<!-- 以下部分为弹窗  -->
<!-- 领取成功-->
<div class="pop pop-lq" id="getGiftSuc">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h2 class="pop-tit">领取成功</h2>
		<p class="pop-txt">前往<i class="red">大理（172，122）<br>龚彩云</i>处领取</p>
	</div>
</div>
<!-- 封停-->
<div class="pop pop-lq" id="forbidden">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<!-- <h2 class="pop-tit">提示</h2> -->
		<p class="pop-txt1 pop-txt">您的账号已停权</p>
	</div>
</div>
<div class="pop pop-lq" id="tips">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<!-- <h2 class="pop-tit">提示</h2> -->
		<!--- pop-txt1  一行文字-->
		<p class="pop-txt1 pop-txt"></p>
	</div>
</div>
<!-- 领取失败1-->
<div class="pop pop-lq" id="pop-get-fail">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h2 class="pop-tit tit">领取失败</h2>
		<p class="pop-txt">您还未成功组队，请先完成组队！</p>
	</div>
</div>
<!--
        您的小队中还有成员未达到50级！
        您的小队未满2人，无法领取奖励！
        礼包领取暂未开放，请先完成组队，<br>并成新服开启后达到指定要求，再来领取。
        -->
<!-- 加入小队成功-->
<div class="pop pop-join" id="pop-join-succ">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h2 class="pop-tit tit">加入小队成功</h2>
		<p class="pop-txt">请前往个人中心查看小队成员信息哦~<br>小队人数≥2人即可获得1次抽奖机会；小队<br>满员还可以额外获得1次抽奖机会哦。</p>
	</div>
</div>
<!--加入小队失败1-->
<div class="pop pop-join" id="pop-join-fail">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h2 class="pop-tit tit">加入小队失败</h2>
		<p class="pop-txt">您已经有相同身份证账号在小队里了，<br>快去邀请其他小伙伴一起组队吧！</p>
	</div>
</div>
<!--组队失败-->
<div class="pop pop-join" id="pop-creat-fail">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h2 class="pop-tit tit">组队失败</h2>
		<p class="pop-txt">您的账号已经绑定过小队，请前往个人中心查看组队情况</p>
	</div>
</div>
<!-- 您已经有相同认证手机账号在小队里了，快去邀请其他小伙伴一起组队吧！
        您所申请的小队已满员，请换个小队试试吧！
        您的账号已经绑定一个小队，请至个人中心查看。 -->

<!--申请成功-->
<div class="pop pop-apply" id="pop-apply-succ">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h2 class="pop-tit tit">申请成功</h2>
		<p class="pop-txt">申请信息已发送~请耐心等待队长回复哟~</p>
	</div>
</div>
<!--申请失败-->
<div class="pop pop-apply" id="pop-apply-fail">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h2 class="pop-tit tit">申请失败</h2>
		<p class="pop-txt">您已经申请了<i class="red">3个小队</i>，请等等队长的回复吧！若队长始终未回复，2日后您可重新申请其他小队。</p>
	</div>
</div>
<!-- 通用 审核 -->
<div class="pop pop-apply" id="pop-agree-fail">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h3 class="pop-tit"></h3>
		<p class="pop-txt"></p>
	</div>
</div>
<!-- 创建小队-->
<div class="pop pop-bg1 pop-team" id="creatGroup">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h2 class="pop-tit">创建小队</h2>
		<div class="online-time clearfix">
			<div class="online onlinetop">
				<em>您的在线时间：</em>
				<span class="fn-select">
					<select id="creatStartTime">
						<option>00:00</option>
						<option>01:00</option>
						<option>02:00</option>
						<option>03:00</option>
						<option>04:00</option>
						<option>05:00</option>
						<option>06:00</option>
						<option>07:00</option>
						<option>08:00</option>
						<option>09:00</option>
						<option>10:00</option>
						<option>11:00</option>
						<option>12:00</option>
						<option>13:00</option>
						<option>14:00</option>
						<option>15:00</option>
						<option>16:00</option>
						<option>17:00</option>
						<option>18:00</option>
						<option>19:00</option>
						<option>20:00</option>
						<option>21:00</option>
						<option>22:00</option>
						<option>23:00</option>
						<option>24:00</option>
					</select>
				</span>
				<span class="sign">~</span>
				<span class="fn-select">
					<select id="creatEndTime">
						<option>00:00</option>
						<option>01:00</option>
						<option>02:00</option>
						<option>03:00</option>
						<option>04:00</option>
						<option>05:00</option>
						<option>06:00</option>
						<option>07:00</option>
						<option>08:00</option>
						<option>09:00</option>
						<option>10:00</option>
						<option>11:00</option>
						<option>12:00</option>
						<option>13:00</option>
						<option>14:00</option>
						<option>15:00</option>
						<option>16:00</option>
						<option>17:00</option>
						<option>18:00</option>
						<option>19:00</option>
						<option>20:00</option>
						<option>21:00</option>
						<option>22:00</option>
						<option>23:00</option>
						<option>24:00</option>
					</select>
					</select>
				</span>
			</div>
			<div class="online">
				<em>原大区_服务器_角色(必填)：</em>
			</div>
			<div class="online">
				<span class="fn-select">
					<select class="serselset" id="serverName">
						<option>原大区_服务器_角色</option>
					</select>
				</span>
			</div>
			<div class="online onlinew">
				<em>游戏意向(最多3个)：</em>
			</div>
			<ul class="yxyix">
				<!--已选择，添加类名“ yxz ”-->
				<li><i>外观截图党</i></li>
				<li><i>一条龙副本</i></li><br>
				<li><i>卡级</i></li>
				<li><i>生活</i></li>
				<li><i>打架</i></li>
			</ul>
			<div class="online onlinew1">
				<em>联系方式:</em>
				<span class="fn-select">
					<select id="contactType">
						<option value="0">QQ</option>
						<option value="1">微信</option>
						<option value="2">手机号</option>
						<option value="3">我不想填写联系方式</option><!-- 我不想填写联系方式 -->
					</select>
				</span>
				<input id="contactValue_0" num="true" contact maxlength="12"
					 oninput="value=value.replace(/[^\d]/g,'')" />
				<input id="contactValue_1" contact maxlength="20" style="display:none;" />
				<input id="contactValue_2" num="true" contact maxlength="11"  oninput="value=value.replace(/[^\d]/g,'')"
					style="display:none;" />
			</div>
		</div>
		<p class="sm_p">您的联系方式仅队友可获得</p>
		<a href="javascript:;" class="refer-btn com" id="creatTeam"><i>提交</i></a>
	</div>
</div>
<!-- 申请小队-->
<div class="pop pop-bg1 pop-team" id="aplayTeam">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h2 class="pop-tit tit titsq">申请小队</h2>
		<div class="online-time clearfix">
			<div class="online onlinetop">
				<em>原大区(必填):</em>
			</div>
			<div class="online">
				<span class="fn-select">
					<select id="serverName2" class="serselset">
						<option>原大区_服务器_角色</option>
					</select>
				</span>
			</div>
			<div class="online onlinew">
				<em>游戏意向(最多3个)：</em>
			</div>
			<ul class="yxyix">
				<!--已选择，添加类名“ yxz ”-->
				<li><i>外观截图党</i></li>
				<li><i>一条龙副本</i></li><br>
				<li><i>卡级</i></li>
				<li><i>生活</i></li>
				<li><i>打架</i></li>
			</ul>
			<div class="online onlinew1">
				<em>联系方式:</em>
				<span class="fn-select">
					<select id="contactType2">
						<option value="0">QQ</option>
						<option value="1">微信</option>
						<option value="2">手机号</option>
						<option value="3">我不想填写联系方式</option><!-- 我不想填写联系方式 -->
					</select>
				</span>
				<input id="contactValue2_0" num="true" contact maxlength="12"
					 oninput="value=value.replace(/[^\d]/g,'')" />
				<input id="contactValue2_1" contact maxlength="20" style="display:none;" />
				<input id="contactValue2_2" num="true" contact maxlength="11"
					 oninput="value=value.replace(/[^\d]/g,'')" style="display:none;" />
			</div>
		</div>
		<p class="sm_p">您的联系方式仅队友可获得</p>
		<a href="javascript:;" class="refer-btn com" id="creatTeam2"><i>提交</i></a>
	</div>
</div>
<!-- 创建成功PC-->
<div class="pop pop-bg3 pop-team" id="creatSuc">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<div class="tea-tit1">NO.<span id="teamNo"></span>小队创建成功！</div>
		<div class="tea-tit2"><span>小队验证码：</span><input type="text" id="teamCode" value=""></div>
		<a href="javascript:;" class="copy-btn com" id="teamShare"><i>一键复制</i></a>
		<p class="pop-sm">少侠可<a href="javascript:;" class="apply-info" style="cursor: default;">一键复制</a>发送给想要一起组队的好友哦~
			您可前往<a href="javascript:;" class="apply-info"
				style="cursor: default;">【申请信息】</a>查看/通过其他玩家的入队申请小队人数≥2人即可获得1次抽奖机会；小队满员还可额外获得1次抽奖机会哦~</p>
	</div>
</div>
<!-- 共闯江湖-->
<div class="pop pop-yanz" id="joinTeam">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h2 class="pop-tit">共闯江湖</h2>
		<div class="yanz-box clearfix"><span>小队验证码</span><input type="text" id="teamCodeInput" maxlength="9"
				autocomplete="off" /></div>
		<a href="javascript:;" class="join-team com" id="joinTeamBtn"><i>加入小队</i></a>
		<p class="pop-sm">快输入小队验证码，加入小队与好友共闯江湖吧！</p>
	</div>
</div>
<!-- 我的申请-->
<div class="pop pop-my-apply" id="myApplyBox">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h2 class="pop-tit">我的申请</h2>
		<p class="red-sm">（仅显示待通过状态小队）</p>
		<div class="tab-box">
			<div class="tab-top">
				<table>
					<tr>
						<th width="140">队伍编号</th>
						<th>队长信息</th>
					</tr>
				</table>
			</div>
			<div class="tab-con">
				<table>
					<tbody>
						<!-- 								<tr>
									<td width="140">队伍编号</td>
									<td>队长信息</td>
								</tr>
								<tr>
									<td width="140">队伍编号</td>
									<td>队长信息</td>
								</tr>
								<tr>
									<td width="140">队伍编号</td>
									<td>队长信息</td>
								</tr>
								<tr>
									<td width="140">队伍编号</td>
									<td>队长信息</td>
								</tr>
								<tr>
									<td width="140">队伍编号</td>
									<td>队长信息</td>
								</tr> -->
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>
<!-- 申请信息-->
<div class="pop pop-bg2 pop-apply-info" id="applyInfoPop">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h2 class="pop-tit tit">申请信息</h2>
		<p class="red-sm">（申请信息仅保留2天，请及时查看通过）</p>
		<a href="javascript:;" class="empty-btn" id="clear">一键清空</a>
		<div class="tab-box">
			<div class="tab-top">
				<table>
					<tr>
						<th width="50%">申请账号</th>
						<th>玩家信息</th>
					</tr>
				</table>
			</div>
			<div class="tab-con">
				<table width="100%" id="applyInfoBox">
					<tbody>
						<!-- <tr>
							<td width="50%">Kim···nonKim···nonKim···non</td>
							<td width="50%"><a href="javascript:;">查看信息</a></td>
						</tr>
						<tr>
							<td width="50%">Kim···nonKim···nonKim···non</td>
							<td width="50%"><a href="javascript:;">查看信息</a></td>
						</tr>
						<tr>
							<td width="50%">Kim···nonKim···nonKim···non</td>
							<td width="50%"><a href="javascript:;">查看信息</a></td>
						</tr>
						<tr>
							<td width="50%">Kim···nonKim···nonKim···non</td>
							<td width="50%"><a href="javascript:;">查看信息</a></td>
						</tr>
						<tr>
							<td width="50%">Kim···nonKim···nonKim···non</td>
							<td width="50%"><a href="javascript:;">查看信息</a></td>
						</tr> -->
					</tbody>
				</table>
				<p>暂无申请</p>
			</div>
		</div>
	</div>
</div>
<!-- 申请信息1-->
<div class="pop pop-bg2 pop-bg6">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h2 class="pop-tit tit">申请信息</h2>
		<ul class="applylist">
			<li>玩家账号：<span></span></li>
			<li>原大区：<span></span></li>
			<li>原服务器：<span></span></li>
			<li>原角色名：<span></span></li>
			<li>游戏意向：<span></span></li>
		</ul>
		<a href="javascript:;" class="refer-btn com" title="同意申请"><i>同意申请</i></a>
	</div>
</div>
<!-- 创建小队失败-->
<div class="pop pop-bg2" id="pop-creat-fail">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h2 class="pop-tit">提示</h2>
		<p class="pop-txt">您未填写区服，请先填写原区服方便好友们<br>寻找吧~</p>
	</div>
</div>
<!-- 抽奖弹窗-->
<div class="pop pop-bg2 pop-get" id="dialogGetPrize">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h2 class="pop-tit tit Prizetit">恭喜您获得以下奖励</h2>
		<ul class="pop-list  clearfix">
			<li>
				<img id="priceIcon" src="/act/xtl/newserver/20210820/return/m/img/dj2.png" alt="" />
				<span class="dj-name" id="priceName">功力丹×2</span>
			</li>
		</ul>
		<p class="pop-sm">所有道具奖励仅发放至新服，请在新服开启后前<br>前往<i class="red">大理（172，122）龚彩云</i>处领取</p>
		<!-- <p class="pop-sm">请填写收货地址并保证手机畅通哦~</p> -->
		<a href="javascript:popup($('#PopAddr'));" class="refer-btn com mt10 fillAddress"
			title="填写收货地址"><i>填写收货地址</i></a>
	</div>
</div>
<!-- 新增 -->
<div class="pop pop-bg2 pop-join" id="pop-address-suc">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h2 class="pop-tit tit"></h2>
		<p class="pop-txt">实物奖励将在新服开启后<span class="red">14个工作日内</span>陆续发放，请少侠保证联系手机畅通无阻哦~</p>
	</div>
</div>
<div class="pop pop-bg2 pop-join pop-address" id="pop-address-err">
	<a href="javascript:;" class="closed close hh">关闭</a>
	<div class="pop-con">
		<h2 class="pop-tit tit"></h2>
		<p class="pop-txt pop-txt1"></p>
	</div>
</div>
<!--收货地址-->
<div class="pop pop-bg2 pop-addr" id="PopAddr">
	<a href="javascript:;" class="p-close close t">关闭</a>
	<div class="pop-formbox">
		<div class="pop-sebox">
			<label>收货人</label>
			<input class="input_addr_people" oninput="if(value.length>30)value=value.slice(0,30)" maxlength="30" />
		</div>
		<div class="pop-sebox">
			<label>手机号</label>
			<input class="input_addr_phone" maxlength="11" />
		</div>
		<div class="pop-sebox">
			<label>收货地址</label>
			<input class="input_addr_dec" oninput="if(value.length>100)value=value.slice(0,100)" maxlength="100" />
		</div>
	</div>
	<a href="javascript:;" title="提交" class="pop-tijiao  btn_addr_submit"><i>提交</i></a>
</div>
