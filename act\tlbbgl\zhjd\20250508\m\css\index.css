@charset "utf-8";
body{
    background: #913005;
}

.wrap{
    text-align: center;
    width: 100%;
    height: 100%;
    margin: 0 auto;
    position: fixed;
    overflow: hidden;
    top: 0;
    left: 0;
}
.page{background: no-repeat center center/100% auto; }

/* page1 */
.page1{background-image: url(../img/bg.jpg);}
.login-info{position: absolute; top: 50%; margin-top: -664px; width: 750px; box-sizing: border-box; padding-right: 20px; left: 0; z-index: 5; font-size: 28px; text-align: right; display: none;}

@media screen and (min-aspect-ratio: 750/1334) {
    .pop-poster{
        width: calc(538px * .8);
        height: calc(1020px * .8);
    }
}
.login-info{text-align: right; color: #fdebd7;}
.login-info a{color: #fdebd7; font-size: 28px;}
.start_box_page{width: 100%; height: 1334px; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); }
.start_box{position: absolute; bottom: 24px;  width: 100%;}
.start_tip{display: block; background-image: url("../img/btn_start_txt.png"); width: 196px; height: 23px; margin: 0 auto;}
.btn_start{display: block;background-image: url("../img/btn_start.png"); width: 333px; height: 334px; margin: 0 auto; animation: ani-breath 2s linear infinite;  }
@keyframes ani-breath {
    0% { transform: scale(.92); }
    50% {transform: scale(1); }
    100% { transform: scale(.92); }
}

.wrap{
    height: 100%;
}
.page1{
    height: 1650px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    right: 0;
    min-height: initial;
}
@media screen and (min-aspect-ratio: 375/550) {
    .wrap{
        height: 100%;
    }
}
@media screen and (min-aspect-ratio: 1) {
}
body.browser-vivo .start_box{
    margin-bottom: 64px;
}
.btn_agree{ background: url("../img/checkbox.png") no-repeat 0 0/22px; padding-left: 24px; display: inline-block; height: 21px; margin: 10px auto 0; color: #ffd2c2; font-size: 17px; line-height: 21px; position: relative;}
.btn_agree::before{content: ''; background: url("../img/checkbox_checked.png") no-repeat 0/22px; position: absolute; width: 22px; height: 21px; left: 0;top: 0; display: none;}
.btn_agree.checked::before{display: block;}
.btn_rule{display: inline-block; border-bottom: 1px solid #ffd2c2;}
/* page2 */
.page2{background-image: url("../img/bg_main.jpg"); height: 100%; }
.scence_wrap{width: 750px; height: 100%; position: relative; background: url("../img/bg_main.jpg") no-repeat center center/100% auto; }
.scence_box{box-sizing: border-box; padding-top: 20px; background: url("../img/main_box.png") no-repeat center center/745px auto; width: 750px; height: 1269px; position: absolute; top: 50%; left: 50%; margin-left: -375px; margin-top: -610px;}
.scence_txt{position: absolute; top: 28px; left: 0; right: 0; margin: 0 auto; opacity: 0;}
.scence_role{position: absolute; left: 0; opacity: 0;}
.swiper-slide{overflow: hidden;}
.swiper-slide-active .scence_txt{animation: ani-fade-up-in .8s forwards;}
.swiper-slide-active .scence_role{animation: ani-fade-up-in .8s .1s forwards;}
@keyframes ani-fade-up-in {
    from{ opacity: 0; transform: translateY(140px);}
    to{opacity: 1; transform: translateY(0);}
}
.scence1 .scence_txt{background-image: url("../img/txt1.png"); width: 614px; height: 512px; margin-top: 40px;}
.scence1 .scence_role{background-image: url("../img/role1.png"); width: 698px; height: 725px; top: 510px; left: 52px;}
.scence2 .scence_txt{background-image: url("../img/txt2.png"); width: 547px; height: 310px; top: 68px;}
.scence2 .scence_role{background-image: url("../img/role2.png"); width: 750px; height: 872px; top: 416px;}
.scence3 .scence_txt{background-image: url("../img/txt3.png"); width: 559px; height: 320px; top: 68px;}
.scence3 .scence_role{background-image: url("../img/role3.png"); width: 744px; height: 833px; top: 320px; left: 0; }
.scence4 .scence_txt{background-image: url("../img/txt4.png"); width: 416px; height: 374px; top: 54px;}
.scence4 .scence_role{background-image: url("../img/role4.png"); width: 665px; height: 777px;  top: 370px; left: 64px;}
.btn_next{position: absolute; left: 0; bottom: 64px; z-index: 2; width: 100%; text-align: center; color: #83746a; font-size: 24px;}
.btn_begin_box{position: absolute; bottom: 58px;  left: 0; width: 100%;}
.btn_begin{position: absolute; bottom: 14px; left: 0; right: 0; margin: 0 auto; background-image: url("../img/btn_jianding.png"); width: 240px; height: 257px; opacity: 0;}
.begin_tip{font-size: 28px; text-align: center; color: #c34606; width: 100%; position: relative; z-index: 2; pointer-events: none; opacity: 0;}

.swiper-slide-active .btn_begin{animation: ani-fade-up-in .8s .1s forwards;}
.swiper-slide-active .begin_tip{animation: ani-fade-up-in .8s .2s forwards;}

/* page3 */
@font-face {
    font-family: 'hy';
    src: url("../fonts/hyqh45.ttf");
}

.btn_rule_link {
    position: absolute;
    top: 50%;
    margin-top: -678px;
    background-image: url("../img/btn_rule_link.png");
    width: 200px;
    height: 71px;
    box-sizing: border-box;
    left: 36px;
    z-index: 10;
}
.page3{background-image: url("../img/bg_main.jpg"); height: 100%;}
.result{width: 1170px; height: 1250px; background: no-repeat center center/100%; position: absolute; left: -28px; top: 50%; margin-top: -590px;}
.page2 .result{transform-origin: 32% center;}
.txt_red{color: #ad3928;}
.page2 .result1{background-image: url("../img/result1.png")}
.page2 .result2{background-image: url("../img/result2.png")}
.page2 .result3{background-image: url("../img/result3.png")}
.page2 .result4{background-image: url("../img/result4.png")}
.page2 .result5{background-image: url("../img/result5.png")}
.result .page_mid{padding-top: 140px; box-sizing: border-box; font-size: 28px; color: #57361b;letter-spacing: -1px; line-height: 36px; width: 750px; left: 38px; overflow: initial;}
.logo_result{background: url("../img/logo.png") no-repeat center/100%; width: 160px; height: 92px; position: absolute; top: -50px; left: 50%; margin-left: -80px;}

.tip_txt {
    width: 600px;
    margin: 0 auto;
    text-align: left;
    text-indent: 54px;
    font-family: 'hy';
    animation-delay: .1s;
}
.tip_txt2{margin-top: 346px; animation-delay: .2s;}
.share_btns{display: flex; justify-content: center; align-items: center; margin-top: 34px;  animation-delay: .4s;}
.share_btns a{width: 276px; height: 79px;}
.share_guide_txt{ animation-delay: .6s;font-size: 22px; color: #88776b; line-height: 32px; text-align: center; position: absolute; bottom: 10px; left: 0; width: 100%;}
.btn_share_link{background-image: url("../img/btn_copy.png");}
.btn_share_pic{background-image: url("../img/btn_share.png");}
.qr_wrap{margin: 48px auto 0; animation-delay: .3s;}
.qr_box{background: url("../img/qr_box.png") no-repeat center/100%; width: 132px; height: 132px; margin: 0 auto; display: flex; justify-content: center; align-items: center;}
.qr_box img, .qr_box canvas{width: 126px; height: 126px;}

.opacity0{opacity: 0;}
.swiper-slide-active .opacity0{animation: ani-fade-up-in .8s forwards;}


.result_outer{width: 1px; height: 1px; overflow: hidden; position: relative;}
.page4{width: 750px; height: 1334px; position: absolute; left: 0; top: 0; z-index: -1;}
.page4 .qr_wrap{ margin-top: 40px;}
.page4 .qr_box{width: 186px; height: 186px;}
.page4 .qr_box img, .page4 .qr_box canvas{width: 180px; height: 180px;}
.page4 .result{width: 750px; height: 1334px; left: 0; top: 0; margin-top: 0;}
.page4 .result .page_mid{left: 0; padding-top: 158px;}
.page4 .result .tip_txt2{margin-top: 360px;}
.page4 .result1{background-image: url("../img/poster1.jpg")}
.page4 .result2{background-image: url("../img/poster2.jpg")}
.page4 .result3{background-image: url("../img/poster3.jpg")}
.page4 .result4{background-image: url("../img/poster4.jpg")}
.page4 .result5{background-image: url("../img/poster5.jpg")}

.qr_txt {
    font-family: 'hy';
    margin-top: 10px;
}


/* pop */
.pop{ background:#816f57; width:578px; height:auto; color: #fff; font-size: 24px; text-align: center; margin: 0 auto 20px;  position: relative;display:none;}
.btn-close{width: 60px;height: 51px;position: absolute;top: 18px;right: 4px;z-index: 10;background: url(../img/btn-close.png) no-repeat center;background-size: 100% auto;}
/* .pop-home{display: block;} */

.pop-poster{width: 538px; height: 1020px; background: none;}
.poster_img{width: 100%; height: 957px;}

.save_tip {
    font-size: 28px;
    color: #FFFFFF;
    text-align: center;
    font-family: 'hy';
    position: absolute;
    bottom: 0;
    width: 100%;
    left: 0;
}
.pop-poster .btn-close{top: initial; bottom: -60px; left: 50%; margin-left: -30px;}

.pop-login{width: 660px;height:675px;background: none;background-size: 100% auto;}
.pop-login .login-box, .pop-login iframe {width: 100%; height: 100%;}
.pop-login .btn-close{top: -30px; right: -40px;}

.pop-common{width: 608px;height:373px;background: url(../img/pop-write.png) no-repeat center;background-size: 100% auto;}
.pop-common .btn-confirm{width:224px;height:71px;background: url(../img/btn_confirm.png) no-repeat center;background-size: 100% auto;}


.pop-shareWX{width:100%;height:100%;background:rgba(0,0,0,0.8) url(../img/pop-shareWX.png) 224px 10px/482px 412px  no-repeat ; position:fixed;top:0;left:0;z-index:999;display:none;}

.pop-share{width: 608px;height:393px;background: url(../img/pop-share.png) no-repeat center;background-size: 100% auto;}
.pop-share .txt1{width: 540px; line-height: 34px;}
.pop-share .ipt-copy{width: 479px;height:67px;background: url(../img/input.png) no-repeat center;background-size: 100% auto;font-size: 22px;color: #fff;line-height: 40px;box-sizing: border-box;padding: 10px 30px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
.pop-share .ipt-copyBtn{width: 479px;height:67px;background: url(../img/input.png) no-repeat center;background-size: 100% auto;font-size: 30px;color: #fff;line-height: 40px;padding: 10px 0;margin-top: 20px;}

.pop-rule{width: 608px;height:1058px;background: url(../img/pop-rule.png) no-repeat center;background-size: 100% auto;}
.pop-rule .pop-tit{background: url("../img/pop-tit4.png") no-repeat center/auto 100%; width: 100%; height: 35px; padding-top: 0; margin-top: 54px;}
.pop-rule .txt-box{height: 900px;overflow-y: auto;margin-top: 30px;}
.pop-rule .txt-box strong{font-weight: bold;}
.pop-rule .txt-box p{text-align: start;font-size: 18px;color: #000;line-height: 30px; margin-bottom: 12px;}
.pop-rule .txt-box p i{text-align: center;font-size: 18px;color: #fff;line-height: 30px;text-align: center;width:30px;height:30px;background: url(../img/numBg.png) no-repeat center;background-size: 100% auto;position: absolute;}
.pop-rule .txt-box p span{padding-left: 32px;}

.pop-home{width: 608px;height:761px;background: url(../img/pop-home.png) no-repeat center;background-size: 100% auto;}
.pop-home .txt2{padding: 120px 50px 0;}
.pop-home .txt2,.pop-home ul li{font-size: 22px;color: #000;text-align: start;}
.pop-home ul {width: 508px;height:380px;margin: 150px auto 0;overflow-y: scroll;}
.pop-home ul li{display: flex;flex-direction: row;justify-content: start;align-items: center;margin: 10px 0; border-bottom: 1px dashed #cccccc;}
.pop-home h1{font-size: 18px;padding: 0;width: 160px;}
.pop-home h1{line-height: 48px;}
.pop-home h2{line-height: 28px;margin-left: 10px;}