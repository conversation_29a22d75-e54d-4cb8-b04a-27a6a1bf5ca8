//移动端
var isH5 = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
//适配
function elementFit(ele, isScale) {
    var windowWidth = $(window).width();
    var windowHeight = $(window).height();
    var _s = windowWidth / (isH5 ? 750 : 1920);
    _s = Math.max(_s, windowHeight / (isH5 ? 1650 : 1080));
    if (isScale) {
        $('body').css({
            zoom: _s,
        });
    } else {
        $('.wrap').css({
            zoom: _s,
        });
    }
    //				$(".main").height($(window).height() - 42)
    //	$(".container-last").height(100)
}
elementFit();
$(window).on('resize', function () {
    setTimeout(elementFit, 100);
});

// 奖品列表
var prizeList = [
    {code: 'R1'},// 随机门派立牌
    {code: 'R2'},// 武当武器抱枕
    {code: 'R7'},// 天龙手办
    {code: 'R4'},// 呆呆牛钥匙扣
    {code: 'R5'},// 大蛋蛋鸡
    {code: 'R6'},// 蓝牙音响
    {code: 'R3'},// 天山T恤
    {code: 'CODE'},// 测试资格
    {code: 'N'} // 谢谢参与
];

//swiper切换各个页面的组件样式
function swiperFn(idx) {
    // console.log(idx,'idx ')
    setTimeout(() => {
        if(idx!==0){$('#cyou_top').hide();}else{$('#cyou_top').show();}
    }, 100);
    if (idx === 4 || idx === 5) {
        $('.nav_list').show();
        $('.nav_list_activate').show();
        $('.scroll-down').hide();
    }else if (idx === 0) {
        $('.nav_list').hide();
        $('.nav_list_activate').hide();
        $('.scroll-down').show();
        $('.scroll-down').addClass('page0');
        $('.bufflybox').hide();
        isH5 && $('.nav_list').removeClass('on');
        isH5 && $('.nav_list_activate').removeClass('on');
    } else {
        $('.nav_list').show();
        $('.nav_list_activate').show();
        $('.scroll-down').show();
        $('.scroll-down').removeClass('page0');
        $('.bufflybox').show();
    };
}
//主体切换
var navCnames = ['典藏服预约', '全新内容', '内测资格抽奖', '呼朋唤友', '新闻资讯', ''];
var swiper = new Swiper('.swiper-container-m',{
    hashNavigation: true,
    watchState: true,
    noSwiping: true,
    noSwipingClass: 'stop-swiping',
    direction: 'vertical',
    fadeEffect: {
        crossFade: false,
    },
    initialSlide:0,
    slidesPerView: 'auto',
    speed: 500,
    observer: true,
    observeParents: true,
    // preventInteractionOnTransition: true,
    simulateTouch: false, // 触控拖拽
    mousewheel: true,
    pagination: {
        el: '.nav_list',
        clickable: true,
        renderBullet: function (index, className) {
            return `<div class="${className} nav_item">
                <a href="javascript:;" class="nav_cname">${navCnames[index]}</a>
            </div>`;
        }
    },
    
    lazy: {
        loadPrevNext: true,
    },
    on: {
        slideChange: function () {
            swiperFn(this.realIndex);
        },
        init: function () {
            swiperFn(this.realIndex);
            var checkBottom = setInterval(() => {
                // console.log(isH5)
                var cy=isH5?'body > #cy_bot':'body > #cyou_bottom';
                if ($(cy).length > 0) {
                    $(".ss_bot").html($(cy)).show();
                    clearInterval(checkBottom);
                }
            }, 100);
        },
    }
    });

$('.snav_c a').on('click', function () {
    $('html,body').animate({ 'scrollTop': $('.mod-item').eq($(this).index()).offset().top }, 300);
});
$('.sidebar .backtop').on('click', function () {
    $('html,body').animate({ 'scrollTop': 0 }, 300);
});

var snowflakeURl = [
    '/act/tldc/collection/20250427/pc/img/icon_petal_1.png',
    '/act/tldc/collection/20250427/pc/img/icon_petal_2.png',
    '/act/tldc/collection/20250427/pc/img/icon_petal_3.png',
    '/act/tldc/collection/20250427/pc/img/icon_petal_4.png',
    '/act/tldc/collection/20250427/pc/img/icon_petal_5.png',
    '/act/tldc/collection/20250427/pc/img/icon_petal_6.png',
    '/act/tldc/collection/20250427/pc/img/icon_petal_7.png',
    '/act/tldc/collection/20250427/pc/img/icon_petal_8.png'
]
var container = $(".wrap");
visualWidth = container.width();
visualHeight = container.height();
//获取content的宽高
function snowflake() {
    // 雪花容器
    var $flakeContainer = $('#buffly');

    // 随机八张图
    function getImagesName() {
        return snowflakeURl[[Math.floor(Math.random() * 8)]];
    }
    // 创建一个雪花元素
    function createSnowBox() {
        var url = getImagesName();
        return $('<div class="snowbox" />').css({
            'width': 100,
            'height': 100,
            'position': 'absolute',
            'backgroundRepeat': 'no-repeat',
            'zIndex': 2,
            'top': '-41px',
            'backgroundImage': 'url(' + url + ')'
        }).addClass('snowRoll');
    }
    // 开始飘花
    setInterval(function() {
        // 运动的轨迹
        var startPositionLeft = Math.random() * visualWidth - 100,
            startOpacity = 1,
            endPositionTop = visualHeight - 40,
            endPositionLeft = startPositionLeft - 100 + Math.random() * 2000,
            duration = visualHeight * 10 + Math.random() * 5000;
        // 随机透明度，不小于0.5
        // var randomStart = Math.random();
        // randomStart = randomStart < 0.5 ? startOpacity : randomStart;
        randomStart = startOpacity;
        // 创建一个雪花
        var $flake = createSnowBox();
        // 设计起点位置
        $flake.css({
            left: startPositionLeft,
            opacity: randomStart
        });
        // 加入到容器
        $flakeContainer.append($flake);
        // 开始执行动画
        $flake.transition({
            top: endPositionTop,
            left: endPositionLeft,
            opacity: .7
        }, duration, 'ease-out', function() {
            $(this).remove() //结束后删除
        });
    }, 800);
}
snowflake()
//执行函数


//复制
var clipboard = new ClipboardJS('.copyBtn', {
    text: function (trigger) {
        return $(trigger).attr('data-key');
    }
});
clipboard.on('success', function (e) {
    // alert('复制成功');
    window.example.tips('复制成功');
});
clipboard.on('error', function (e) {
    // alert('复制失败，请手动复制');
    window.example.tips('复制失败，请手动复制');
});

// 内页-1
var swiper1 = new Swiper('.swiper-page1', {
    loop: true,
    initialSlide:isH5? 2:3,
    effect: 'coverflow',
    coverflowEffect: {
        rotate: 0,
        stretch: -10,
        depth: 100,
        modifier: 6,
        slideShadows: true
    },
    centeredSlides: true,
    slidesPerView: 'auto',
    spaceBetween: 300,
    speed: 500,
    observer: true,
    simulateTouch: false, //禁止鼠标模拟
    pagination: {
        clickable: true,
        el: '.page1 .swiper-pagination',
    },
    navigation: {
        nextEl: '.page1 .swiper-button-next',
        prevEl: '.page1 .swiper-button-prev',
    },
    autoplay: {
        delay: 5000,
        stopOnLastSlide: false,
        disableOnInteraction: true,
    },
});













// 奖励hover
const prizeArr={
    0:[
        { name: '天灵丹×1',img: '/act/tldc/collection/20250427/pc/img/dj5.png' },
        { name: '金丹葫芦×1',img: '/act/tldc/collection/20250427/pc/img/dj6.png' },
        { name: '玉液净瓶×1',img: '/act/tldc/collection/20250427/pc/img/dj7.png' },
    ],
    1:[
        { name: '天灵丹×1',img: '/act/tldc/collection/20250427/pc/img/dj5.png' },
        { name: '地灵丹×1',img: '/act/tldc/collection/20250427/pc/img/dj8.png' },
        { name: '珍兽回春丹×1',img: '/act/tldc/collection/20250427/pc/img/dj9.png' },
    ],
    2:[
        { name: '天灵丹×1',img: '/act/tldc/collection/20250427/pc/img/dj5.png' },
        { name: '麟木箭×1',img: '/act/tldc/collection/20250427/pc/img/dj10.png' },
        { name: '回天神石×1',img: '/act/tldc/collection/20250427/pc/img/dj11.png' },
    ],
    3:[
        { name: '天灵丹×1',img: '/act/tldc/collection/20250427/pc/img/dj5.png' },
        { name: '金蚕丝×2',img: '/act/tldc/collection/20250427/pc/img/dj3.png' },
        { name: '真元珀×2',img: '/act/tldc/collection/20250427/pc/img/dj12.png' },
    ]
}
const $prizePop = $('.prize-pop-box');
const $homePrizePop = $('.pop-home-prize');
const positions =isH5? ['-30px', '130px', '-80px', '100px']: ['90px', '370px', '50px', '320px'];
$('body').on('mouseenter touchstart', '.team-prize', function (e) {
    // $prizePop.css('left', positions[$(this).index()] || 'auto');
    // $prizePop.find('.prize-pop').html(prizeArr[$(this).index()].map(item => `
    //     <li class="item">
    //         <img src="${item.img}" alt="">
    //         <p>${item.name}</p>
    //     </li>
    // `).join(''));
    // $prizePop.show();
});
$('body').on('mouseleave touchend', '.team-prize', function (e) {
    // $prizePop.hide();
});
// 立即预约-悬浮窗
$('body').on('mouseenter', '.btn-order', function () {
    $homePrizePop.show(); 
});

// 内页-4
var swiper4 = new Swiper('.swiper-page4', {
    autoplay: true,
    simulateTouch: true,
    preventClicksPropagation : true,
    autoplay: {
        delay: 5000,
        stopOnLastSlide: false,
        disableOnInteraction: true,
    },
    pagination: {
        clickable: true,
        el: '.page4 .swiper-pagination',
    },
});



// tab切换
const tabArr=['最新','新闻','公告'];
function updateTypeTexts() {
    tabArr.forEach((text, index) => {
        const $pane = $(`#tab${index + 1}`);
        if ($pane.length) {
            $pane.find('p.type').each(function () {
                $(this).text(text);
            });
        }
    });
}
updateTypeTexts();

$('.btn-tab').click(function () {
    const tabId = $(this).data('tab');
    $(this).addClass("active").siblings().removeClass("active");
    $('.news-pane').removeClass('active').filter(`#${tabId}`).addClass('active');
});

//弹窗-个人中心-tab切换
$('.userInfo .btn-tab').click(function () {
    const tabId = $(this).data('tab');
    if (tabId == 'tab2'){
        window.example.activationRecord();
    }
    $(this).addClass("active").siblings().removeClass("active");
    $('.user-pane').removeClass('active').filter(`#${tabId}`).addClass('active');
});

$(document).ready(function () {
    $('.user-pane').each(function () {
        var tableBody = $(this).find('table');
        if (tableBody.find('tr').length === 0) {
            var columnCount = $(this).find('thead th').length;
            tableBody.append('<tr><td colspan="' + columnCount + '" style="text-align: center;height: 248px;">暂无</td></tr>');
        }
    });
});

//内页4 tab滑动-禁止全屏滑动
$('.news-content').on('mouseenter', function () {
    swiper.mousewheel.disable();
}).on('mouseleave', function () {
    swiper.mousewheel.enable();
});


//移动端小图swiper
function swiperH5() {
    var swiperThumbs = new Swiper('.swiper-thumbs', {
        loop: true, // 开启循环
        slidesPerView: 'auto',
        spaceBetween: 10,
        centeredSlides: true,
        watchSlidesVisibility: true,
        watchSlidesProgress: true,
        freeMode: true,
    });

    $('.swiper-thumbs .swiper-slide').on('click', function () {
        const index = $(this).index();
        if (index >= 0) {
            swiper1.slideTo(index);
        }
    });
    function syncThumbsToMain(index) {
        $('.swiper-thumbs .swiper-slide').removeClass('swiper-slide-thumb-active');
        $('.swiper-thumbs .swiper-slide').eq(index).addClass('swiper-slide-thumb-active');
        swiperThumbs.slideTo(index);
    }
    // 主图切换时同步缩略图
    swiper1.on('slideChange', function () {
        const realIndex = this.realIndex;
        syncThumbsToMain(realIndex);
    });

    // 页面加载后，手动同步一次初始状态
    $(document).ready(function () {
        const initialIndex = swiper1.realIndex || 0;
        syncThumbsToMain(initialIndex);
    });
}
// 移动端
function navH5() {
    $('.nav_list').append(
        `<a class="btn-off db">收起</a><a class="btn-on db">展开</a>
					<a href="/xtl/collection/20250427/public/m/index.shtml#slide5" class="btn-activate-go"><span>资格激活</span></a>`
    )
    $('.btn-on').click(function () {
        $('.nav_list').addClass('on');
    })
    $('.btn-off').click(function () {
        $('.nav_list').removeClass('on');
        $('.btn-activate-go').removeClass('active');
    })

    swiperH5();

}
isH5&&navH5();




//音乐播放
var musicArr = '/act/xtl/collection/20250427/pc/video/music0.mp3';
var audio = document.getElementById('bgm');
var musicIndex = 0;
$('.btn-music').on('click', function() {
	if ($(this).hasClass('on')) {
		audio.pause();
		$(this).removeClass('on');
		audio.loop = false;
	} else {
		musicIndex = $(this).index();
		audio.src = musicArr;
		audio.play();
		$(this).addClass('on').siblings().removeClass('on');
		audio.loop = true;
	};
});
audio.addEventListener('ended', function() {
	musicIndex++;
	if (musicIndex > 3) {
		musicIndex = 0;
	};
	audio.src = musicArr;
	$('.btn-music').removeClass('on').eq(musicIndex).addClass('on');
	audio.play();
});

audio.addEventListener('error', () => {
  console.error('音频加载失败:', audio.error);
});

$(document).ready(function () {
    $('.bkTop').on('click', function () {
        swiper.slideTo(0);
    });
});

$('btn-home-play').click(()=>{

})


// 内页-2
//预约抽奖
//开始抽奖lottery->js
function callJsToStart() {
    if (!window.example.commHandle()) {
        lottery.enable(); // 重置抽奖按钮为可用
        return
    };
    if (window.example.reserveStatus == 0) {
        window.example.tips('请先完成预约');
        // alert('请先完成预约');
        lottery.enable(); // 重置抽奖按钮为可用
        return;
    }
    if (window.example.drawNum == 0 && window.example.reserveStatus == 1 && window.example.inviteCount > 4) {
        window.example.tips('抽奖次数已用尽，请看看其他活动吧');
        lottery.enable();
        return;
    }
    if (window.example.drawNum == 0) {
        var url = window.location.href.split('#slide')[0];
        var shareUrl = `${url.split('?activityCode=')[0]}?activityCode=1|${example.inviteCode}`
        $('#pop-copyNum .copy').html(shareUrl);
        $('#pop-copyNum .copyBtn').attr('data-key', shareUrl);
        popup('#pop-copyNum');
        lottery.enable();
        return;
    };
    if (window.example.throttle(1000)) {
        $.ajax({
            url: window.example.base_url + 'draw',
            type: 'POST',
            success: function (res) {
                if (res.code == 10000){
                    // {"code":10000,"data":{"prizeCode":"R2","prizeName":"武当武器抱枕","prizeType":"R"}}
                    const {prizeName, prizeCode, prizeType, activationCode } = res.data;
                    window.example.prizeType = prizeType;
                    var index = prizeList.findIndex(function (item){
                      return item.code == prizeCode;
                    });
                    if (prizeType == 'R'){
                        $('#prizeName').html(prizeName);
                    }
                    if (prizeType == 'C'){
                        $('#pop-getcs .copy').html(activationCode);
                        $('#pop-getcs .copyBtn').attr('data-key', activationCode);
                    }
                    calllotteryToRoll(index);
                }else if (window.example.commonErrPop(res)) {
                    window.example.tips(res.message);
                } else {
                    // 已处理所有情况
                }
            }
        });
    }
    //PTTSendClick('lottery','start','抽奖_开始');
    // amsSubmit(136209,434241);
    //alert("抽奖暂未开放哦！");
}
//开发获得抽奖结果 通知lottery开始播放效果 js->lottery
function calllotteryToRoll(id) {
    // PTTSendClick('lottery','reward_'+id,'抽奖_奖品_'+id);
    if (lottery) lottery.stopRoll(id);
}
//动画完成通知js  lottery->js
function callJsToComplete() {
    // PTTSendClick('lottery','end','抽奖_完毕');
    //alert(msg);
    if (window.example.prizeType == 'R'){
        popup('#pop-lottery');
    }
    if (window.example.prizeType == 'C'){
        popup('#pop-getcs');
    }
    window.example.getDrawNum();
}
//初始化抽奖的 可以不知道内部
// 资格激活按钮跳转
$('.nav_list_activate, .btn-activate-go').on('click', function () {
    $('.nav_item').removeClass('swiper-pagination-bullet-active');
    window.location.href = `/tldc/collection/20250427/public/${isH5?'m':'pc'}/activate.shtml#slide5`;
    $(this).addClass('active');
    // console.log($(this))
});

if(typeof Lottery !== 'undefined'){
  var lottery =isH5? new Lottery({
    'lighturl': '/act/xtl/collection/20250427/m/img/luck-cur.png',
    'total': 9,
    'width': 750,
    'height': 638,
    'sbtnx': 243,
    'sbtny': 638,
    'sbtnw': 262,
    'sbtnh': 74,
    'boxw': 236,
    'boxh': 204,
    'position': "20_12,256_12,492_12,12_216,256_216,492_216,12_420,256_420,492_420",
    'contentId': 'lotterycontent',
    'onClickRollEvent': callJsToStart,
    'onCompleteRollEvent': callJsToComplete,
    'isResponsive': 0
}): new Lottery({
    'lighturl': '/act/tldc/collection/20250427/pc/img/luck-cur.png',
    'total': 9,
    'width': 834,
    'height': 488,
    'sbtnx': 286,
    'sbtny': 488,
    'sbtnw': 262,
    'sbtnh': 74,
    'boxw': 269,
    'boxh': 161,
    'position': "0_6,283_6,565_6,0_166,283_166,565_166,0_321,283_321,565_321",
    'contentId': 'lotterycontent',
    'onClickRollEvent': callJsToStart,
    'onCompleteRollEvent': callJsToComplete,
    'isResponsive': 0
});
}