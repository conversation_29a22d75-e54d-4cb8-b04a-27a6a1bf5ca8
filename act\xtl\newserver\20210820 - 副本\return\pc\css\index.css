/*public*/
body { font: 12px/1.5 '\5FAE\8F6F\96C5\9ED1'; background: #000; overflow-x: hidden; min-width: 1200px; }
em, i { font-style: normal; }
.hh { overflow: hidden; text-indent: -999em; display: block; }
.pr { position: relative; }
.pa { position: absolute; }
.fl { float: left; }
.fr { float: right; }
img { display: block; }
input[type="text"], input[type="password"], select, input[type="search"] { -webkit-appearance: none; -moz-appearance: none; appearance: none; outline: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); border-radius: 0; box-sizing: border-box; }
.wrap { color: #619185; font-size: 14px; overflow-x: hidden; position: relative; }
.mid { width: 1200px; margin: 0 auto; position: relative;background:url("../img/bg1cont.jpg") no-repeat center top;height:4183px;padding-top:67px;}
.com,.icon,.huil-wj i, .huoy-wj i, .team-peo i { background: url("../img/icon.png") no-repeat; display: block;text-indent:-9999em; }
.logo { background: url("http://i0.cy.com/xtl/main/20200506/logo_20200506.png") no-repeat; width: 176px; height: 76px; display: block; position: absolute; left: 0; top: 24px }
.btn{transition:filter 0.5s linear}
.btn:hover{-webkit-filter:brightness(1.15)}
.content-bg { background: url("../img/bg1.jpg") top center no-repeat; width: 100%;}
.slg-box { width: 760px; height: 415px; position: absolute; top: 112px; left: 219px; }
.s-img { width: 647px; height: 254px; position: absolute; left: 78px; top: 32px; }
.s-img img { width: 100%; height: 100%; }
.s-bjy { width: 49px; height: 110px; position: absolute; left: 10px; top: 100px; background: url("../img/gsh.png") no-repeat }
/**content**/
.tag-box .dl { background: url("../img/dl.png") no-repeat; width: 214px; height: 218px; top: -52px; position: absolute; }
.tag-box .dl i { background: url("../img/dl-light.png") no-repeat; width: 214px; height: 218px; position: absolute; left: 0; top: 0; z-index: 1; }
.tag-box .dl1 { left: -114px; }
.tag-box .dl2 { right: -117px; }
.tag-box .butterfly { background: url("../img/butterfly.png") no-repeat; width: 98px; height: 100px; display: block; position: absolute; }
.tag-box .fly1 { left: -36px; top: -224px; }
.tag-box .fly2 { right: -104px; top: 596px; background-position: 0 -123px; }
.tag-box .fly3 { left: -206px; top: 914px; background-position: 0 -250px; }
.tag-box .fly4 { left: -62px; top: 1028px; background-position: 0 -392px; }
.tag-box .fly5 { right: -122px; top: 1431px; background-position: 0 -535px; }
.tag-box .fly6 { right: -206px; top: 1376px; background-position: 0 -676px; }
/**cont2**/
.tit{width:617px;height:90px;background:url("../img/tit.png") no-repeat;margin-left:313px;text-indent:-99999em;}
.c-time {font-size:24px;line-height:28px;color:#23313a;text-align:center;position:relative;margin-top:5px;}
.red { color: #af432a; }
.tips { width: 1060px; margin: 1px auto 0; line-height: 24px; text-align: center; color: #23313a;font-size:18px;}
.tips span{color:#f2d76f;}
.lbbox{height:500px;position:relative;width:100%;padding-top:45px;}
.lbbox:after{content:'';width:100%;height:100%;background:url("../img/lbimg.png") no-repeat;position:absolute;left:0;top:0;}
.fl-list {position: relative; z-index: 2;height:376px;}
.fl-list li {width: 257px;float:left;position:relative;padding-top:57px;}
.fl-list li.li1box{padding:57px 33px 0 62px;}
.fl-list li.li3box{padding:57px 0 0 33px;width:494px;}
.lbname{color:#eeeff3;font-size:18px;text-align:center;height:27px;}
.lbnamemid{padding:35px 0 3px;}
.lblist{width:100%;text-align:center;height:99px;}
.lblist dd{display:inline-block;vertical-align:top;width:117px;}
.li3box .lblist dd{width:114px;}
.lblist img{margin:0 auto;width:47px;display:block;padding-top: 17px;}
.lblist p{font-size: 16px;color: #eeeff3;margin-top:5px;position: relative;z-index: 2;}
.lqbtn{width:257px;height:90px;background-position:-601px 0;margin:63px auto 0;}
.lqbtn.ylq{background-position:-590px -102px;}
.lqbtn.gray{background-position:-585px -204px;}
.teambk{margin:41px auto 0;height:995px;width:1123px;background:url(../img/team-k.png) no-repeat;}
.team-a-box {padding:43px 0 0 109px;height:52px;}
.team-a-box a { float: left;width:185px;height:52px;display:inline;transition:all .2s;}
.team-a-box a:hover,.change-btn:hover,.apply-btn:hover,.cjbtn:hover{filter:brightness(105%);}
.join-btn { background-position: 0 -79px; }
.set-btn { background-position: -209px -320px; margin-left:176px;}
.yllq-btn { background-position: -230px -160px; margin-left:175px;}
.team-apply-box {margin:20px auto 0; }
.apply-top { width: 980px; margin: 0 auto;height:32px;}
.search-form {height: 32px; position: relative;}
.search-form span { float: left; line-height: 32px;color:#23313a;font-size:18px;padding-right:10px;}
.search-form input { background: none; border: none; font-size: 18px; background:#86898c; border: 1px #6b7385 solid; color: #23313a; width: 262px; padding: 0 5px; height: 30px; line-height: 30px; display: block; outline: none;float: left;border-radius:4px;}
.search-form input::-webkit-input-placeholder { color: #23313a; }
.sea-ico { width: 36px; height: 32px; background: url("../img/search.png");float:right;}
.my-apply {background-position: -209px -455px;margin-left:89px;}
.my-applyinf{background-position:-209px -386px;margin-left:89px;}
.huil-wj i, .huoy-wj i, .team-peo i { width: 25px; height: 40px;display: inline-block; vertical-align: middle;background-position:-47px -369px; }
.huil-wj { color: #37787e; font-size: 16px; margin-top: 7px; }
.huil-wj i { background-position: -93px -369px; margin-right: 10px; }
.huoy-wj { margin: 0 96px 0 44px; }
.huoy-wj { color: #d55515; font-size: 16px; margin-top: 7px; }
.huoy-wj i { background-position: -47px -369px; margin-right: 10px; }
.apply-list { margin: 0 0 0 87px; }
.apply-list li { background: url("../img/team-bg.png") no-repeat; width: 271px; height: 325px; float: left;position: relative; font-size: 14px;margin:20px 23px 0;color:#eeeff3;}
.apply-list .rank { position: absolute; left: 18px; top: 20px; width: 130px;color: #eeeff3;font-size: 18px;}
.apply-list .apply-btn { width: 115px; height: 34px; background-position:0 -421px;position: absolute; top: 15px; right:13px;text-indent:-9999em;}
.team-con { padding: 56px 16px 0; }
.hg-tag { width: 43px; height: 22px; line-height: 22px; background-position: -371px -239px; text-align: center; color: #eeeff3; position: absolute; top:63px; right: 14px; font-size: 14px; display: none; text-indent: -9999em; }
.team-con p { margin-top:6px; line-height: 18px;color:#eeeff3;font-size:14px;}
.team-con .t-name span { width: 156px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block; vertical-align: top; }
.team-peo { overflow: hidden; margin-top:9px; }
.team-peo i { float: left;display:inline;margin:0 7px;}
.team-peo i.huoy-ico,.team-peo i.huig-ico { background-position:0 -369px; }
.team-peo i.huig-ico{background-position:-104px -367px;}
.change-btn { background-position: -580px -310px;margin:20px auto 0;width:257px;height:90px;}
.more-btn {font-size: 14px; color: #875932; text-decoration: underline; padding-bottom: 3px; margin-top: 10px; text-align: center; display: block; }
.lqlb-btn.ylq, .team-a-box .yllq-btn.ylq { background-position: -1px 0; text-indent: -999em; }
/**ani**/
.com.ylq:hover, .lqlb-btn.gray:hover { filter: brightness(1); -webkit-filter: brightness(1); }
.pt-bg { transition: .4s cubic-bezier(0.175, 0.885, 0.32, 1.275); }
.pint-box .pt-bg:hover { transform: translate(0, -6px); }
.s-img { animation: slogaIn .5s ease-in; -webkit-animation: slogaIn .5s ease-in; }
@-webkit-keyframes slogaIn {
	0% { -webkit-transform: scale(2); opacity: 0; }
	100% { -webkit-transform: scale(1); opacity: 1; }
}
@keyframes slogaIn {
	0% { transform: scale(2); opacity: 0; }
	100% { transform: scale(1); opacity: 1; }
}
.addtit{margin:91px 0 0 313px;width:617px;height:90px;background-position:0 -521px;}
.jltip{text-align:center;padding:1px 0 32px;}
.jlbox{width:872px;height:729px;background:url(../img/jlbg.png) no-repeat;margin:0 auto;text-align:center;padding-top:27px;}
.jlbg0{background:url(../img/jlbg0.png) no-repeat;}
.jlbg1{background:url(../img/jlbg1.png) no-repeat;}
.jlbg2{background:url(../img/jlbg2.png) no-repeat;}
.jlbg3{background:url(../img/jlbg3.png) no-repeat;}
.jlbg4{background:url(../img/jlbg4.png) no-repeat;}
.jlbg5{background:url(../img/jlbg5.png) no-repeat;}
.jlbg6{background:url(../img/jlbg6.png) no-repeat;}
.jlbg7{background:url(../img/jlbg7.png) no-repeat;}
.jlbg8{background:url(../img/jlbg8.png) no-repeat;}
.jlbg9{background:url(../img/jlbg9.png) no-repeat;}
.jlbg10{background:url(../img/jlbg10.png) no-repeat;}
.jlbg11{background:url(../img/jlbg11.png) no-repeat;}
.shaozi{width:703px;height:703px;margin:0 auto;background:url(../img/shaozi.png) no-repeat;}
.cishu{color:#23313a;left:0;width:100%;top:774px;font-size:30px;font-family:simsun;font-weight:bold;}
.cjbtn{top:833px;left:50%;width:318px;height:111px;margin-left:-159px;background-position:-235px 0;}
.cjbtn.gray{background-position:0 -652px;}
.cjtishi{text-align:center;padding-top:191px;color:#23313a;font-size:18px;}
.addlblist{width:1200px;text-align:center;height:265px;position:relative;margin-top:28px;}
.addimg{position:absolute;top:0;left:6px;}
.gztit{width:351px;height:90px;background-position:0 -116px;margin:92px 0 0 442px;}
.rule{color:#23313a;font-size:18px;line-height:24px;padding:18px 15px 0;}
.rule span{color:#851e1b;}
.rule p{padding:13px 0;font-weight:700;}
.rule li{padding:0 0 12px 35px;width:1130px;position:relative;}
.rule li em{top:-3px;left:-3px;position:absolute;width:26px;height:26px;text-align:center;line-height:26px;color:#d4efff;background-position:-491px -665px;text-indent:0;}
input[type="number"] {
    -moz-appearance: textfield;
}