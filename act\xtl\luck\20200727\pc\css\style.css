@charset "utf-8";
@import url(http://tl.changyou.com/logo/xtl_logo.css);
.xtl_logo_bg, .xtl_logo_lk a{
    width: px;
    top: 30px;
    left: 30px;
    width: 170px;
}
/***public***/
*{margin: 0;padding: 0;}
address,caption,cite,code,dfn,em,strong,th,var{font-style:normal;font-weight:normal;}
body,html{-webkit-tap-highlight-color:transparent;-webkit-text-size-adjust:none; }
ol,ul,li{list-style:none;}
caption,th{text-align:left;}
h1,h2,h3,h4,h5,h6{font-size:100%;font-weight:normal;}
input[type="text"], input[type="password"], select { -webkit-appearance: none;appearance:none; outline: none;-webkit-tap-highlight-color: rgba(0,0,0,0); border-radius:0;box-sizing: border-box;}
a{text-decoration:none; outline:none;}
img{ display: block;}
.c:after{content:'\20';display:block;height:0;line-height:0;visibility:hidden;clear:both;}
.hh{display:block;text-indent:-999em;overflow:hidden;}
.hide{width:0;height:0;overflow:hidden;display:none;}
.pr{position: relative;}
.pa{ position: absolute;}
html{font-size:100px;}
body,html{width: 100%; height:100%; }
body{font: 14px/1.75 -apple-system, "Helvetica Neue", Helvetica, Arial, sans-serif; background:#000; font-size:0.24rem; color: #fff; position: relative; overflow-x: hidden;}
a{text-decoration:none;blr:expression(this.onFocus=this.blur());outline:none;}
html{-webkit-tap-highlight-color:rgba(0,0,0,0);}
.g-fl {float: left; }
.g-fr {float: right; }
.t{text-indent: -999em; display: block}
/* index */
.bg1{width: 100%; height: 931px; background: url(../img/bg1.jpg) no-repeat center;}
.com_tent{
    width: 1200px;
    margin: 0 auto;
    position: relative;
}
.com_tent h2.bt_bg{
    width: 301px;
    height: 422px;
    background: url(../img/title_t.png) no-repeat center;
    margin: 0 auto;
    padding-top: 109px;
    background-position: -10px 52px;
    animation: btnbox .8s .1s ease both;animation: btnbox .8s .1s ease both;
    -webkit-animation: btnbox .8s .1s ease both;animation: btnbox .8s .1s ease both;
    -moz-animation: btnbox .8s .1s ease both;animation: btnbox .8s .1s ease both;
}
.sur{
    background: url(../img/tl_sur.png) no-repeat center;
}
.logo_tl{
    display: block;
    position: absolute;
    left: 0;
    top: 34px;
    width: 213px;
    height: 90px;
    background-position: -17px -14px;
}
.yun_bg1{
    width: 326px;
    height: 165px;
    position: absolute;
    top: 112px;
    left: 130px;
    z-index: 55;
    animation:downUp 5s linear infinite;
    -webkit-animation:downUp 5s linear infinite;
    -moz-animation:downUp 5s linear infinite;
}
.dl_bg1{
    width: 1153px;
    height: 453px;
    position: absolute;
    top: 74px;
    left: 195px;
    animation:downUp 3s linear infinite;
    -webkit-animation:downUp 3s linear infinite;
    -moz-animation:downUp 3s linear infinite;
}
.dl_bg2{
    width: 114px;
    height: 108px;
    position: absolute;
    top: 273px;
    left: -50px;
    animation: downUp 6s linear infinite;
    -webkit-animation: downUp 6s linear infinite;
    -moz-animation: downUp 6s linear infinite;
}
@keyframes btnbox{
    0%{opacity:0;transform: translateY(30px);}
    100%{opacity:1;transform: translateY(-2px);}
}
@-webkit-keyframes btnbox{
    0%{opacity:0;transform: translateY(30px);}
    100%{opacity:1;transform: translateY(-2px);}
}
.page_font{
    text-align: center;
    font-size: 14px;
    margin-top: 157px;
    color: #56427f;
}
.my_gb{margin-top: 25px;}
.my_gb a{
    width: 254px;
    height: 64px;
    background-position: -19px -104px;
    margin: 0 auto;
}
.my_gb a:hover{
    filter: brightness(1.05);
    -webkit-filter: brightness(1.05);
    filter: alpha(opacity=90);
}
.my_login{
    text-align: center;
    font-size: 20px;
    letter-spacing: 5px;
}
.my_login a:link,
.my_login a:visited{
    color: #fff;
    /* text-decoration: underline; */
}
.page_font span{width: 74px; height: 17px; background: #56427f;display: inline-block; line-height: 18px; border-radius: 5px; color: #fff;}
@keyframes downUp{0%{ transform:translateY(0);}25%{ transform:translateY(-5px);}50%{ transform:translateY(0);}75%{ transform:translateY(3px);}100%{ transform:translateY(0);}}
@-webkit-keyframes downUp{0%{ -webkit-transform:translateY(0);}25%{ -webkit-transform:translateY(-5px);}50%{ -webkit-transform:translateY(0);}75%{ -webkit-transform:translateY(3px);}100%{ -webkit-transform:translateY(0);}}
.bg2{width: 100%; height: 1910px; background: url(../img/bg2.jpg) no-repeat center}
.bg3{width: 100%; height: 656px; background: url(../img/bg3.jpg) no-repeat center; overflow: hidden}
.bg4{width: 100%; height: 434px; background: url(../img/bg4.jpg) no-repeat center}
.bg5{width: 100%; height: 718px; background: url(../img/bg5.jpg) no-repeat center;overflow: hidden}

.com_content{
    width: 1000px;
    margin: 0 auto;
    padding: 68px 25px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.title_con{
    width: 207px;
    height: 66px;
    background-position: -326px -108px;
}
.jgIcon{
    width: 18px;
    height: 20px;
    background-position: -550px -130px;
    display: inline-block;
    vertical-align: middle;
}
.upData_sc{
    font-size: 18px;
    color: #4f4b56;
    margin-top: 18px;
}
.nrUp_title{
    padding-bottom: 14px;
    border-bottom: 1px solid #ffd2ee;
}
.list_con{margin-top: 15px; height: 1500px;}
.list_con ul li{
    width: 307px;
    height: 484px;
    background: url(../img/list_cen.png) no-repeat center;
    padding: 21px 31px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    color: #2e115b;
    font-size: 12px;
    float: left;
    margin-right: 13px;
    margin-bottom: 13px;
}
.list_con ul li:nth-child(3n){
    float: right;
    margin-right: 0;
}
.search label{
    display: inline-block;
    padding: 0px 4px;
    font-size: 14px;
    color: #3d1f11;
}
.search select,.search input{
    background: #ffd7d0;
    height: 23px;
    border: 0;
    border-radius: 5px;
    padding-left: 8px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    color: #f36b78;
    font-size: 12px;
    width: 132px;
    vertical-align: middle;
}
.search select{
    border: 0;
    /* 清除默认的箭头样式 */
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    /* 右侧添加小箭头的背景图 */
    background: url('../img/xl_pic.jpg') #ffd7d0 right no-repeat;
}
#province{
    width: 105px;
}
#city{
    width: 104px;
}
a.btn_search_04,a.btn_search_01{
    width: 42px;
    height: 23px;
    background: #ff959d;
    display: inline-block;
    font-size: 14px;
    color: #ffffff;
    margin-left: -10px;
    vertical-align: middle;
    text-align: center;
    border-top-right-radius: 5px;
    -moz-border-top-right-radius: 5px;
    -webkit-border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    -moz-border-bottom-right-radius: 5px;
    -webkit-border-bottom-right-radius: 5px;
}
.btn_search_02,.btn_search_03{
    width: 52px;
    height: 23px;
    background: #ff959d;
    border-radius: 5px;
    font-size: 14px;
    color: #ffffff;
    display: inline-block;
    text-align: center;
    vertical-align: middle;
}
.xin_dia i{
    width: 18px;
    height: 15px;
    display: inline-block;
    background-position: -591px -133px;
    vertical-align: middle;
    margin-right: 5px;
}
.tu_list{
    width: 252px;
    height: 154px;
    background: #ffe8df;
    margin-top: 13px;
    position: relative;
    overflow: hidden;
}
.list_p{
    line-height: 15px;
    height: 164px;
    color: #000000;
    margin-top: 10px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    /* -webkit-line-clamp: 7; */
    word-break: break-all;
    overflow: hidden;
}
.list_name{
    text-align: right;
    margin-top: 15px;
}
.pages {
    padding-top: 24px;
    text-align: center;
    color: #fa6f76;
    font-size: 18px;
}
.pages a{
    color: #fa6f76;
}
.pages a:hover, .pages a.current {
    text-decoration: underline;
    color: #7e1314;
}
.gy_1000{
    width: 1000px;
    margin: 0 auto;
}
.title_880{
    width: 880px;
    height: 86px;
    margin: 0 auto;
}
.page3_title{
    background-position: -35px -198px;
    margin: 0 auto 40px;
}
.page4_title{
    background-position: -35px -285px;
}
.mc_list ul{margin-top: 22px}
.mc_list ul li{
    width: 308px;
    height: 302px;
    color: #cf4260;
    text-align: center;
    font-size: 24px;
    float: left;
}
.mc_list ul li+li{ margin-left: 38px}
.mc_list ul li h2{margin-top: 40px}
.mc_list ul li h2 .sur_y{
    width: 10px;
    height: 10px;
    display: inline-block;
    background-position: -295px -135px;
    vertical-align: middle;
    margin: 0 8px;
}
.page5_title{
    background-position: -35px -367px;
    margin-top: 20px;
}
.hg_hui{
    font-size: 14px;
    color: #5c5c5c;
    padding:50px 30px;
}
.hg_hui .red{color: #f00;}
.zuoP{
    width: 910px;
}
.zuoPN{
    width: 910px;
}
.lott{
    width: 1000px;
    margin: 20px auto 0;
    position: relative;
}
.lott ul li{
    width: 242px;
    height: 135px;
    background: url(../img/cj_bg.png) no-repeat center;
    float: left;
    margin-right: 10px;
    margin-bottom: 15px;
}
.lott ul li.tb_sy{
    float: right;
    margin-right: 0;
}
.lott ul li p{
    line-height: 32px;
    color: #ffffff;
    font-size: 14px;
    text-align: center;
}

.up_dao{
    overflow: hidden;
    height: 103px;
}
.lott ul li.play_sp{
    background: none;
    margin-left: 15px;
}
.play_sp a:hover{
    filter: brightness(1.05);
    -webkit-filter: brightness(1.05);
    filter: alpha(opacity=90);
}
.play_sp a{
    width: 455px;
    height: 132px;
    background: url(../img/play_cj.png) no-repeat center;
}
.lott-cover {
    width: 240px;
    height: 135px;
    background: rgba(255,133,145,.5);
    display: none;
    position: absolute;
    z-index: 99;
}

.bang,.commonPop{
    width: 654px;
    height: 447px;
    background: url(../img/bang_d.png) no-repeat center;
}
.btn-close{
    width: 47px;
    height: 47px;
    background-position: -868px -117px;
    position: absolute;
    right: 36px;
    top: 9px;
}
.layer_com{
    width: 555px;
    text-align: center;
    padding: 64px 0;
    margin-left: 34px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.layer_com h3{
    font-size: 30px;
    color: #f36b78;
}
.layer_com p{
    color: #4f4b56;
    font-size: 14px;
    margin: -5px 0 6px 0;
}
.layer_com .jp_show{padding-top: 110px;}
.sel_xz label{
    width: 201px;
    font-size: 18px;
    color: #1b1b1b;
    display: inline-block;
    text-align: left;
}
.sel_xz select{
    font-size: 16px;
    width: 488px;
    height: 36px;
    padding-left: 8px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    color: #4f4b56;
    border: 1px solid #f36b78;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    background: url(../img/tc_xiala.jpg) #fffbfa right no-repeat;
    margin-bottom: 10px;
}
.phone_box{margin-bottom: 5px}
.phone_box label{
    font-size: 18px;
    color: #4f4b56;
    vertical-align: middle;
    display: inline-block;
    height: 36px;
    line-height: 40px;
}
.phone_box input{
    width: 291px;
    height: 36px;
    border: 1px solid #f1cdd1;
    padding-left: 7px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.yzm_box{
    width: 291px;
    height: 36px;
    border: 1px solid #f1cdd1;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.yzm_box{
    width: 291px;
    display: inline-block;
}
.yzm_box input{
    width: 183px;
    height: 99%;
    border: 0;
    vertical-align: top;
    font-size: 12px;
    padding-left: 7px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.yzm_box a{
    width: 96px;
    height: 17px;
    line-height: 17px;
    color: #f36b78;
    text-align: center;
    font-size: 12px;
    display: inline-block;
    vertical-align: middle;
    margin-top: -8px;
    border-left: 1px solid #eaadb3;
}

.subimt a{
    width: 203px;
    height: 52px;
    margin: 4px auto;
}
.up_shang{
    width: 642px;
    height: 830px;
    background: url(../img/up-data.png) no-repeat center;
}
.font_sc{
    color: #4f4b56;
    font-size: 18px;
    text-align: left;
    padding-left: 68px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    margin-bottom: 5px;
}
.font_sc img{
    width: 84px;
    height: 58px;
    display: inline-block;
    vertical-align: middle;
}
.font_sc .sc_input{
    width: 93px;
    height: 31px;
    background: #f36b78;
    border-radius: 31px;
    color: #fff2ff;
    display: inline-block;
    position: relative;
    vertical-align: middle;
    margin-left: 5px;
}
.font_sc .sc_input input{
    width: 93px;
    height: 31px;
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 44;
}
.font_sc .sc_input span{
    position: absolute;
    width: 100%;
    display: inline-block;
    top: 0;
    left: 0;
    text-align: center;
    font-size: 16px;
    height: 31px;
    line-height: 31px;
}
#text_wb{
    display: block;
    width: 410px;
    height: 85px;
    border: 1px solid #bdadbd;
    padding: 10px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    margin-top: 10px;
    color: #858585;
}
.p_bing{
    width: 400px;
    margin: 0 auto;
    font-size: 16px;
    text-align: left;
    color: #4f4b56;
}

.font_sc.bx_data{text-align: center;padding-left: 0; margin-top: 3px}
.tj_a{
    background-position: -372px -30px;
}
.tj_sy{
    background-position: -583px -30px;
}
.my_zx{
    width: 654px;
    height: 560px;
    background: url(../img/gr_zhong.png) no-repeat center;
}
.zy_renz{
    padding:0 60px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;

}
.zy_renz p{font-size: 18px; margin-top: 2px}
.zy_renz a{
    width: 122px;
    height: 31px;
    background: #f36b78;
    display: block;
    color: #ffffff;
    font-size: 16px;
    line-height: 31px;
    border-radius: 31px;
    vertical-align: middle;
}
.lay_mc_list{
    width: 456px;
    margin: 15px auto;
}
.lay_mc_list ul li{
    width: 222px;
    height: 198px;
    background: url(../img/x_listbg.png) no-repeat center;
}
.lay_mc_list ul{margin-top: 2px}
.lay_mc_list ul li+li{
    margin-left: 6px;
}
.lay_mc_list ul li h2{
    font-size: 18px;
    margin-top: 20px;
}
.zy_renz a.hui_gao{
    background: #868686;
}
.layer_com>p{
    font-size: 20px;
}
.bx_bg{
    width: 66px;
    height: 65px;
    background-position: -801px -17px;
    position: absolute;
    top: -3px;
    left: -5px;
    display: none;
}
.pop{display: none;}
.page3_bg{
    position: relative;
}
.login_box{
    position: absolute;
    right: 0;
    top: 85px;
}
.login_btm{
    width: 89px;
    height: 38px;
    background-position: -271px -39px;
}
.hou_login{
    padding-top: 5px;
    font-size: 14px;
    color: #e22e57;
}
.hou_login a{color: #e22e57}
#name{width: 100px; display: inline-block;
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
    vertical-align: middle; margin: 0 5px;
}
.font_sc input{
    width: 291px;
    height: 36px;
    border: 1px solid #f1cdd1;
    padding-left: 7px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.tu_list img{
    width: 100%;
    height: 100%;}
.xin_dia{cursor: pointer}
.cg_link{
    background-position: -372px -30px;
}
.sm_duan{
    width: 433px;
    margin: 20px auto 10px;
    text-align: left;
    font-size: 20px;
}
.fr_span{
    width: 400px;
}
.formSu{
    width: 384px;
    margin: 0 auto;
}
.formSu_in{margin-bottom: 10px;}
.formSu_in label{
    text-align: left;
    color: #4f4b56;
    vertical-align: middle;
    font-size: 20px;
}
.formSu_in input{
    width: 317px;
    height: 36px;
    border: 2px solid #ffe4e6;
    padding-left: 10px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    color: #f36b78;
    font-size: 18px;
}
.city_tjBox label{
    vertical-align: top;
}
.formSu_in textarea#city_tj{
    width: 317px;
    height: 90px;
    border: 2px solid #ffe4e6;
    padding-left: 10px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    color: #f36b78;
    font-size: 18px;
}
.formSu .subimt{
    margin-top: 21px;
}
.tj_gc{
    width: 655px;
    height: 225px;
    background: url(../img/xx_cg.png) no-repeat center;
}
.wan_s{
    padding: 85px 0;
}
.x_ping{
    width: 65px;
    height: 65px;
    margin: 20px auto 0;
}
.lay_mc_list ul li em{
    font-size: 16px;
    margin-top: 5px;
    display: block;
}
.lay_mc_list ul li em p{    color: #cf4260;}
.bom_icon img{
    width: 64px;
    height: 64px;
    margin: 32px auto 10px;
}
.bom_icon p{
    font-size: 16px;
}
.lay_mc_list ul li a{
    width: 103px;
    height: 31px;
    background: #f36b78;
    font-size: 16px;
    color: #ffffff;
    border-radius: 35px;
    line-height: 31px;
    display: none;
    margin: 0 auto;
}
.swiper_lb{
    width: 716px;
    height: 460px;
    background: url(../img/swiper_lb.png) no-repeat center;
}
.swiper_layer{
    width: 523px;
    height: 284px;
    margin-left: 72px;
    margin-top: 81px;
}
.swiper-container{height: 300px;}
.swiper_layer img{
    width:100%;
    height: 100%;
    position: relative;
    z-index: 555;
}
.sx_link{
    width: 100%;
    position: absolute;
    left: -24px;
    top: 200px;
}
.sx_link a{
    width: 44px;
    height: 44px;
}
.sx_link a.pevr_s{
    background-position: -900px -12px;
}
.sx_link a.next_x{
    background-position: -900px -61px;
}

.pagination{
    text-align: center;
    margin-top: -.03rem;
}
.pagination span{
    width: 12px;
    height: 12px;
    background: #555;
    display: inline-block;
    border-radius: 12px;
    margin: 0 5px;
    border: 1px solid #fff;
    cursor: pointer;
}
.pagination span.swiper-active-switch {
    background: #ff959d;
}
.jpFont-zi{
    font-size: 14px;
    color: #e22e57;
    text-align: center;
}
.jpFont-zi a{
    color: #e22e57;
}

.floatPop {
    width: 320px;
    height: 174px;
    position: fixed;
    z-index: 10;
    top: 70%;
    right: 0;
    -webkit-transition: all .3s ease;
    transition: all .3s ease;
}
.floatPop .popClose {
    position: absolute;
    top: -23px;
    right: 0;
    -webkit-transition: all .3s ease;
    transition: all .3s ease;
    width: 30px;
    height: 30px;
    background: url(../img/popClose_806d87b.png) no-repeat;
    background-size: 100% 100%;
}
.floatPop .imgWrap {
    display: block;
    width: 100%;
    height: 100%;
}
.floatPop .imgWrap img {
    display: block;
    width: 100%;
}
.floatPop .popClose:hover {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
}

.lott-list li:nth-last-of-type(1) .x_ping{
    width: auto;
    height: auto;
    margin-top: 32px;
}
.one_updata  s,.two_updata s{position: relative; display: inline-block; }
.one_updata .del,.two_updata .del{display: none;position: absolute; width: 22px; height: 22px; top: -10px; right: -4px; background: url(../img/delebtn.png); cursor: pointer;}
.longmen{display: none; width: 80px;position: absolute;top: 110px;left: 60px;z-index: 10;color: white;}
.lookPrize{color: #e22e57; text-decoration: underline;}