var sign = 2;

$(function() {
	var isLongmen = location.href.indexOf('/longmen/') !== -1,
        qqNumber = isLongmen ? '1234567890' : '746754120';
        date = isLongmen ? '即日起至X月X日X点' : '即日起至2月1日12点', //  活动时间
		payNumber = 'X', // 当前预充点数
		isBaned = false,
		hasCount = 'X',
        startTime = 0,//预充值开放时间
        endTime = 0,//预充值结束时间
        preChargeUrl = '',//预充值地址
		isClick = true,
		commPath = '/changyou/xtl/precharge/',
		typeName = isLongmen ? 'longmen' : 'public',
		commonPath = '/xtl/newserver/********/common/',
        keyPrecharge = {},
		shareLink = location.origin + '/xtl/newserver/********/public'+ (isLongmen ? '/longmen' : '') +'/precharge/m/index.shtml?CYSTID=share',
		groupShareLink = location.origin + '/xtl/newserver/********/public'+ (isLongmen ? '/longmen' : '') +'/precharge/m/index.shtml?flag=1&CYSTID=share',
		isFlag = location.href.indexOf('flag=1') !== -1,
		url = {
			cover: ja.utils.urlAddSearch('./../../cover/m/index.shtml'), // 首页
			precharge: ja.utils.urlAddSearch('./../../precharge/pc/index.shtml'), // 新服预约
			appoint: ja.utils.urlAddSearch('./../../appoint/m/index.shtml'), // 新服预约
			return: ja.utils.urlAddSearch('./../../return/m/index.shtml') // 江湖同归
		};
	// ja.config({
	// 	app: 'xtl', // 必填，应用标识
	// 	activity: 'newservertopic', // 必填，活动标识
	// 	version: actVersion, // 必填，活动版本标识
	// 	platform: 'changyou', // 必填，登陆平台标识
	// 	type: 1,
	// 	isWxLogin: true,
	// 	ready: function(glob) { // 非必填，初始化完成回调
    //         console.log(glob);
	// 		getGroupInit();
	// 		glob.code === 1 ? loginAfter() : loginBefore();
	// 		wexinShare('precharge', shareLink);
	// 		if (isFlag) {
	// 			ja.backTop(3231);
	// 		}
	// 	}
	// });
	// ja 初始化
	(function() {
        $.get(commonPath + 'key.json', function(resKey) {
            keyPrecharge = resKey['precharge'][typeName];
            var json = resKey.precharge[typeName];
            ja.config({
                app: json.APP,
                activity: json.ACTIVITY,
                version: json.VERSIONCODE,
                platform: json.PLAT,
                isWxLogin: true,
                type: 1,
                ready: function(glob) {
                    getGroupInit();
                    glob.code === 1 ? loginAfter() : loginBefore();
                    wexinShare('precharge', shareLink);
                    if (isFlag) {
                        ja.backTop(2500);
                    }
                }
            });
        });
	})();

	init();
	/**
	 * ************* 函数封装 **************
	 * */
	function init() {
		$('.qq_number').text(qqNumber);
		$('.act_date').text(date);
		$('.pay_num').text(payNumber); // 当前预充点数
		$('.has_count').text(hasCount);

		// $('.url_cover').attr('href', url.cover);     // 首页链接
		// $('.url_precharge').attr('href', url.precharge);
		// $('.url_appoint').attr('href', url.appoint);     // 首页链接
		// $('.url_return').attr('href', url.return);    // 江湖同归
		if (isLongmen) {
			var obj = {
				top: "100px",
				left: "40px",
				color: "white"
			}
			ja.utils.longmen(obj, $(".head"));
            alert('亲爱的玩家您好，本次活动为测试，奖励仅在测试服可用，感谢您的支持！');
		}
	}
	// 登录前
	function loginBefore() {
		isWxBind();
		$('.login_box').show();
		$('.logout_box').hide();
	}

	// 登录后
	function loginAfter() {
		$('.login_box').hide();
		$('.logout_box').show();
		$('#userName').text(ja.glob.userInfo.openid);
		getUserInfo();
		getHasCnt();
        getPreCharge();
	}

	// 统一点击
	function $click(fun) {
		switch (ja.glob.code) {
			case 0:
				alert('网络繁忙，请稍后再试');
				break;
			case 1:
				if (fun && ja.utils.objType(fun) === 'function') fun();
				break;
			case 2:
				ja.glob.isWechat ? popShow('pop-loginBox') : ja.login();
				break;
			case 1106:
				alert('活动未开始');
				break;
			case 1102:
				alert('活动无效');
				break;
			case 1103:
				alert('活动已结束');
				break;
		}
	}

	// 查询微信是否绑定
	function isWxBind() {
        // console.log('查询是否为微信环境', ja.glob.isWechat);
		if (ja.glob.isWechat) {
			ja.user.wxIsBind(function(res) {
				var isBind = false;
				$('#wxLogin .text').text('');
				if (res.code === 10000 && res.data) {
					$('#wxLogin .text').html(res.data + '<br>已绑定');
					$('#wxLogin a .btn').hide();
					isBind = true;
				} else {
					isBind = false;
				}
				$('#wxLogin').click(function() {
					if (isBind) {
						ja.user.wxAutoLogin(function(res) {
							if (res.code === 10000) {
								ja.user.getUserInfo(function(res) {
									if (res.code === 10000) {
										loginAfter();
										popHide()
									}
								})
							} else {
								alert(res.message);
							}
						})
					} else {
						location.href = 'http://tlus.changyou.com/xtl/wxBind/********/mobile/index.html'
					}
				})
			})
		}
	}

	//微信分享
	function wexinShare(key, url) {
		ja.glob.isWechat && ja.wx.init(function() {
			$.get('/xtl/newserver/********/common/share.json', function(res) {
				// console.log(ja.glob.cystid);
				ja.share($.extend(res[key], {
					link: url,
					success: function() {
						if (key == 'group') {
							$('.sharetip').fadeOut();
						}
					}
				}));
			})
		})
	}

	$('.login_account').click(ja.login);


    // 获取预充值开放时间&地址
    function getPreCharge() {
        ja.post('/changyou/xtl/precharge/preCharge', function (res) {
            // console.log(res)
            if (res.code === 10000 && res.data) {
                var data = res.data;
                startTime = data.beginTime;
                endTime = data.endTime;
                mLink = data.preChargeUrl;
            } else if (res.code && res.code == 3103) {
                accountNumberStatus = false;
                $('#btn_myCenter').off("click");
                $('#btn_myCenter').click(function () {
                    alert(res.message);
                })
            }
        })
    }
	// 获取用户信息
	function getUserInfo() {
		ja.post(commPath + 'chargeCount', function(res) {
			if (res.code === 10000 && res.data) {
                // console.log(res);
				payNum = res.data.chargecount; //充值点数
                // uName = res.data.cn, //用户cn
                // uMaster = res.data.cnmaster, //用户cnmaster
                // uValidate = res.data.validate; //用户validate
                // mLink = 'http://chong.changyou.com/bank/tlPrePay.do?cn=' + uName + '&cnmaster=' +
                // uMaster + '&validate=' + uValidate; //移动端充值链接
				//充值点数
				$('.pay_num').text(payNum);
				if (payNum >= 12000 && payNum < 30000) {
					$('.fir_box').prepend('<em style="display:block;" class="icon compe pa">已达成</em>');
				}
				if (payNum >= 30000 && payNum < 60000) {
					$('.fir_box,.sec_box').prepend(
						'<em style="display:block;" class="icon compe pa">已达成</em>');
				}
				if (payNum >= 60000) {
					$('.fir_box,.sec_box,.thi_box').prepend(
						'<em style="display:block;" class="icon compe pa">已达成</em>');
				}


			} else if (res.code && res.code == 3103) {
				isBaned = true;
				$('.btn_pay').off("click");
				$(".btn_pay").click(function() {
					alert(res.message);
				});
				alert(res.message);
			}
		})
	}

	function getHasCnt() {
		ja.get(commPath + 'myHasCnt', function(res) {
			//回调
			if (res.code === 10000 && res.data) {
				hasCount = res.data.hasCnt;
				$('.has_count').text(hasCount);
				var groupArr = [res.data.groupno_1, res.data.groupno_2, res.data.groupno_3];
				for (var i = 0; i < groupArr.length; i++) {
					if (groupArr[i] == 0 || groupArr[i] == undefined) {
						$('.my-pt-btn').eq(i).removeClass('gray');
					} else {
						$('.my-pt-btn').eq(i).addClass('gray');
					}
				}
			}
		})
	}

	//拼团初始化数据
	function getGroupInit() {
		if (isClick) {
			isClick = false;
			ja.get(commPath + 'groupInit', function(res) {
				isClick = true;
				//回调
				if (res.code === 10000 && res.data) {
					$('.curr_count1').text(res.data.currentCount_1);
					$('.target_count1').text(res.data.targetCount_1);
					$('.curr_count2').text(res.data.currentCount_2);
					$('.target_count2').text(res.data.targetCount_2);
					$('.curr_count3').text(res.data.currentCount_3);
					$('.target_count3').text(res.data.targetCount_3);
				}
				if (res.code && res.code == 3103) {
					isBaned = true;
				}
			})
		}
	}
	//参加拼团
	function postGroup(num) {
		ja.post(commPath + 'group', {
			groupno: num
		}, function(res) {
			//回调
			if (res.code === 10000) {
				popShow('pop-success');
				//$('.my-pt-btn').eq(num-1).addClass('gray');
				getHasCnt();
				getGroupInit();
			} else if (res.code == 3103) {
				popShow('pop-tips', '您的账号已停权，请您更换参与活动的账号，或解权本账号后再来参与活动~');
			} else if (res.code == 2121) {
				popShow('pop-tips', '您的拼团次数不足');
			} else if (res.code == 1024) {
				popShow('pop-tips', '您已拼过该团');
			} else if (res.code == 1105) {
				popShow('pop-tips', '拼团活动已结束');
			} else {
				popShow('pop-tips', res.message);
			}
		})
	}

	// 加载组件
	function getComponent(path, fun) {
		// 加载js
		function popMycenter() {
			$.getScript(path + 'index.js');
			fun && fun();
		}

		// 加载css
		// if($('#MycenterCss').length === 0) {
		//     $('<link>').attr({
		//         id: 'MycenterCss',
		//         rel: "stylesheet",
		//         type: 'text/css',
		//         href: path + 'index.css',
		//     }).appendTo($('head'));
		// }

		// 加载html
		if ($('#pop-mycenter').length === 0) {
			$('<div />').load(path + 'index.html', function() {
				popMycenter();
			}).appendTo($('body'));
		} else {
			popMycenter();
		}
	}


	/**
	 * ************* 事件绑定 **************
	 * */

	// 登录
	$('#login').click($click);

	// 登出
	$('#logout').click(ja.logout);

	// 按钮“个人中心”
	$('#centerBtn').on('click', function() {
		$click(function() {
			if (isBaned == false) {
				getComponent('/xtl/newserver/********/common/myCenter/', function() {
					popShow('pop-mycenter');
				});
			} else {
				popShow('pop-tips', '您的账号已停权，请您更换参与活动的账号，或解权本账号后再来参与活动~');
			}
		});
	});

	//我要拼团
	$('.my-pt-btn').on('click', function() {
		var _this = $(this);
		$click(function() {
			if (isBaned == false) {
				if (_this.hasClass('gray') == false) {
					if (hasCount > 0) {
						//alert(_this.text());
						postGroup(_this.attr('data-name'));
					} else {
						popShow('pop-fail');
					}
				} else {
					return
				}
			} else {
				popShow('pop-tips', '您的账号已停权，请您更换参与活动的账号，或解权本账号后再来参与活动~');
			}
		})
	});
	// 按钮“我要充值”
	$('.btn_pay').click(function(e) {
		//某个时间段内跳转有效  活动开始时间：x月x日00：00  预充值结束：x月x 00:00
		//结束 预充活动已结束，新服开启后可进游戏内消费领奖!
		//未开始 预充值尚未开始！
        $click(function(){
            var curTime = new Date().getTime();
            var start = new Date(startTime.replace(/-/g, "/")).getTime();
            var end = new Date(endTime.replace(/-/g, "/")).getTime();
            if (curTime > start && curTime < end) {
                // 跳转充值页面
                window.location.href = mLink;
            } else if (curTime > end) {
                alert("预充活动已结束，新服开启后可进游戏内消费领奖!");
            } else if (curTime < start) {
                alert("预充值尚未开始！");
            }
            e.preventDefault();
        });
	});

	//拼团道具弹窗
	$('.pint-box .li1 .pt-bg').on('click', function() {
		popShow("pop-dj1");
	});
	$('.pint-box .li2 .pt-bg').on('click', function() {
		popShow("pop-dj2");
	});
	//拼团时装弹窗
	$('.pint-box .li3 .pt-bg').on('click', function() {
		popShow("pop-fashion2");
	});

	//邀请好友
	$(".yaoq-btn").on("click", function() {
		if (ja.glob.isWechat) {
			$('.sharetip').fadeIn();
			popHide();
			wexinShare('group', groupShareLink);
		} else {
			var clipboard = new ClipboardJS('.yaoq-btn', {
				text: function() {
					shareHref = location.href + '#cont2';
					return shareHref;
				}
			});
			clipboard.on('success', function(e) {
				alert("复制成功");
			})
		}
	});
	//分享提示关闭
	$('.sharetip').on('click', function() {
		$(this).fadeOut();
	});

});

//弹窗
function popShow(id, msg) {
	msg = msg || '';
	popHide();
	popup($('#' + id));
	$('#' + id + ' .mesg_con').text(msg);
}

function popHide() {
	hideMask($('.pop'));
}
// 按钮“弹窗关闭”
$('.closed, .p-close, .pop-close,.pop .closed').click(popHide);
