<!DOCTYPE HTML>
<html>
<head>
    <meta charset="utf-8">
    <meta name="keywords" content="天龙,新天龙,天龙八部,新天龙八部,天龙八部3,tl,tlbb,tl3,xtl,网络游戏,网游,武侠,浪漫武侠,武侠游戏,金庸,<PERSON><PERSON>,天龙八部OL,萧峰,段誉,虚竹,王语嫣,天龙八部钟汉良版,天龙八部黄日华版,8周年,八周年,周年庆,全民代言,纵情四海" />
    <meta name="description" content="《新天龙八部》第十三门派桃花岛11月1日上线！高爆强控，单兵之王，箫中剑+离人偶，双武器作战方式颠覆江湖！许嵩全新桃花岛主题曲发布！" />
    <title>江湖同归-《天龙八部·归来》官方网站</title>
    <meta name="author" content="Design:CP; Web Layout:CP;" />
    <meta name="applicable-device" content="mobile">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no">
    <meta content="yes" name="apple-mobile-web-app-capable" />
    <meta content="black" name="apple-mobile-web-app-status-bar-style" />
    <meta content="telephone=no" name="format-detection" />
    <link type="image/x-icon" href="//xtl.changyou.com/favicon.ico" rel="icon"/>
    <link type="image/x-icon" href="//xtl.changyou.com/favicon.ico" rel="shortcut icon"/>
    <link type="text/css" rel="stylesheet" href="/act/all/css/reset.css" />
    <link type="text/css" rel="stylesheet" href="/act/xtl/betatest/20200107/m/css/share.css" />
    <script>
      //移动端版本兼容
      var phoneWidth = parseInt(window.screen.width);
      var phoneScale = phoneWidth/750;
    
      var ua = navigator.userAgent;
      if (/Android (\d+\.\d+)/.test(ua)){
        var version = parseFloat(RegExp.$1);
        // andriod 2.3
        if(version>2.3){
          document.write('<meta name="viewport" content="width=750, minimum-scale = '+phoneScale+', maximum-scale = '+phoneScale+', target-densitydpi=device-dpi">');
          // andriod 2.3以上
        }else{
          document.write('<meta name="viewport" content="width=750, target-densitydpi=device-dpi">');
        }
        // 其他系统
      } else {
        document.write('<meta name="viewport" content="width=750, user-scalable=no, target-densitydpi=device-dpi">');
      }
    </script>
    <script type="text/javascript">
      var deviceWidth = document.documentElement.clientWidth;
      if (deviceWidth > 750) {
        deviceWidth = 750
      };
      document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
    </script>
</head>
<body class="beshared">
<!--逍遥sharebg0 峨眉sharebg1 明教sharebg2 武当sharebg3 天山sharebg4 丐帮sharebg5-->
<div class="wrapper sharebg5">
	<a href="//m.tl.changyou.com/" class="logo midpa com">天龙八部归来</a>
	<a href="rule.shtml?id=1" class="btn rulebtn midpa">规则说明</a>
	<div class="sharebox midpa">
		<div class="fribox pr">
			<img src="/act/xtl/betatest/20200107/m/img/share/friicon.jpg" width="120" height="120" alt="头像">
			<p>微信名微信名</p>
		</div>
		<p class="friinf">我准备加入<span>xx门派</span>，请响应我的号召同归<br>江湖，助我赢取不删档测试资格，iPhone手机，Switch，神秘周边等多重好礼。</p>
	</div>
	<div class="hzbtnbox midpa">
		<a href="javascript:popShow('pop_sus');" class="btn xybtn">响应号召</a>
		<a href="javascript:;" class="btn yybtn">我要预约</a>
	</div>
</div>
<!--弹窗-->
<!--还有同归记录-->
<!--无法响应该好友号召-->
<div class="pop pop2" id="pop_norespond">
	<div class="pop_cont">
		<h2 class="tctit tctit3 com">无法响应该好友号召</h2>
		<p class="com_tip">每人最多可响应三名好友的号召<br>您响应的号召已经太多啦<br>适当的歇息一下吧~</p>
		<a href="javascript:popHide();" class="btn btn-know">知道了</a>
	</div>
	<a href="javascript:popHide();" class="btn close">x</a>
</div>
<!--请勿重复响应号召-->
<div class="pop pop3" id="pop_norepeat">
	<div class="pop_cont">
		<h2 class="tctit tctit4 com">请勿重复响应号召</h2>
		<a href="javascript:popHide();" class="btn btn-know">知道了</a>
	</div>
	<a href="javascript:popHide();" class="btn close">x</a>
</div>
<!--响应号召成功-->
<div class="pop pop3" id="pop_sus">
	<div class="pop_cont">
		<h2 class="tctit tctit5 com">响应号召成功</h2>
		<a href="javascript:popHide();" class="btn btn-know">知道了</a>
	</div>
	<a href="javascript:popHide();" class="btn close">x</a>
</div>
<script type="text/javascript" src="/act/all/js/jquery-1.8.3.min.js"></script>
<script src="/act/xtl/betatest/20200107/m/js/commshare.js"></script>
<!--#include virtual="/act/all/nav/cy_public_js_dark.html"-->
<!--#include virtual="/act/all/dma/dma_activity.html"-->
</body>
</html>
