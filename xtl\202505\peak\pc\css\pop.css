.pop {
  position: relative;
  width: 737px;
  height: 482px;
  background: url(../img/pop.png) no-repeat;
  transform: translate3d(0, 0, 0);
  display: none;
}

.popclosebtn {
  width: 31px;
  height: 31px;
  position: absolute;
  background: url(../img/close.png) no-repeat;
  text-indent: -9999em;
  top: 2px;
  right: -10px;
  z-index: 2;
  cursor: pointer;
}
.pop2 {
  height: 483px;
  background: url(../img/pop2.png) no-repeat;
}

.popcont {
  padding: 46px 30px 0 30px;
}

.sqlist {
  width: 608px;
  margin: 0 auto;
  padding-top: 18px;
}

.sqlist li {
  float: left;
  width: 152px;
  height: 156px;
  background: url(../img/libg.jpg) no-repeat center top;
  padding-top: 36px;
  position: relative;
}

.sqlist li span {
  position: absolute;
  left: 0;
  width: 100%;
  font-size: 20px;
  font-weight: bold;
  color: #632800;
  /* background-image: -webkit-linear-gradient(top, #fbf8f5, #e8ca90);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent; */
  text-align: center;
  top: 5px;
  line-height: 30px;
}

.sqlist li p {
  line-height: 33px;
  text-align: center;
  color: #913a00;
  background-image: -webkit-linear-gradient(top, #913a00,#491b00);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 18px;
  font-weight: bold;
  position: relative;
  width: 132px;
  margin: 0 auto;
}

.sqlist li p.win::after {
  content: '';
  position: absolute;
  width: 25px;
  height: 25px;
  background: url(../img/win.png) no-repeat;
  top: -6px;
  right: -5px;
}

.pop3 {
  height: 524px;
  background: url(../img/pop3.png) no-repeat;
}

.pop3 .sqlist {
  padding-top: 20px;
  width: 592px;
}

.pop3 .sqlist li {
  width: 148px;
  height: 176px;
  background: url(../img/libg.png) no-repeat center top;
  padding-top: 40px;
}

.pop3 .sqlist li span {
  top: 0;
  font-size: 20px;
  line-height: 32px;
}

.pop3 .sqlist li p {
  height: 28px;
  line-height: 28px;
  margin: 0 auto 8px;
  background: url(../img/sqlistp.png) no-repeat center top;
  width: 123px;
  background-position: -10px -10px;
}

.pop3 .sqlist li em {
  display: inline-block;
  color: #cb8027;
  background-image: -webkit-linear-gradient(top, #ba4f00,#cb8027 );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 20px;
  font-weight: bold;
  width: 110px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pop3 .sqlist p.win {
  background-position: -10px -58px;
}

.pop3 .sqlist p.win em {
  color: #913a00;
  background-image: -webkit-linear-gradient(top,#913a00, #491b00 );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.pop4 {
  height: 491px;
  background: url(../img/pop4.png) no-repeat;
}

.pop4 .sqlist {
  padding-top: 17px;
  width: 612px;
}

.pop4 .sqlist li {
  width: 153px;
  height: 190px;
  background: url(../img/libg1.png) no-repeat center top;
  padding-top: 9px;
}

.pop4 .sqlist li p {
  height: 83px;
  line-height: 83px;
  width: 130px;
  margin: 0 auto;
}

.pop4 .sqlist li p.win::after {
  width: 58px;
  height: 58px;
  background: url(../img/win1.png) no-repeat;
  top: -17px;
  right: -21px;
}

.pop4 .sqlist li p.p2.win::after {
  /* bottom: -17px; */
  /* top: auto; */
}

.pop5 {
  height: 491px;
  background: url(../img/pop5.png) no-repeat;
}

.pop5 .popcont {
  padding-top: 62px;
}

.pop5 .sqlistl {
  width: 600px;
  padding: 0px 0 0 2px;
}

.pop5 .sqlist li {
  float: left;
  width: 300px;
  height: 198px;
  padding-top: 0px;
  background: url(../img/libg2.jpg) no-repeat center top;
}

.pop5 .sqlist li p {
  line-height: 90px;
  height: 90px;
  width: 184px;
}

.pop5 .sqlist li em,
.pop6 .sqlist li em {
  font-weight: bold;
  color: #913a00;
  background-image: -webkit-linear-gradient(top, #913a00, #491b00);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.pop5 .sqlist li p.win {
  background: url(../img/winpbg.png) no-repeat center center;
}

.pop5 .sqlist li p.win em {
  color: #9a3802;
  background-image: -webkit-linear-gradient(top, #9a3802, #9a3802);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.pop5 .sqlist li p.win::after {
  width: 58px;
  height: 58px;
  background: url(../img/win2.png) no-repeat;
  top: -2px;
  right: -22px;
}

.pop5 .sqlistr {
  float: left;
  padding: 17px 0 0 50px;
  width: 241px;
}

.pop5 .sqlistr li {
  width: 241px;
  height: 180px;
  padding-top: 8px;
  background: url(../img/libg3.jpg) no-repeat center top;
}

.pop6 {
  width: 738px;
  height: 583px;
  background: url(../img/pop6.png?v01) no-repeat;
}

.pop6 .popcont {
  padding: 59px 59px 0;
}

.pop6 .popclosebtn {
  top: 94px;
}

.pop6 .sqlist {
  padding: 140px 0 0 8px;
  width: 600px;
  height: 110px;
  margin: 0;
}

.pop6 .sqlist li {
  position: relative;
  float: left;
  padding-top: 0;
  height: 170px;
  width: 300px;
  background: url(../img/linebg.jpg) center top no-repeat;
}
.pop6 .sqlist li::after{
  content: '';
  position: absolute;
  top: -22px;
  left: 0;
  width: 76px;
  height: 64px;
  background: url(../img/win3.png)no-repeat;
}
.pop6 .sqlist li.gj::after{
  background-position: -10px -10px;
}
.pop6 .sqlist li.yj::after{
  background-position: -106px -10px;
}
.pop6 .sqlist li.jj::after{
  background-position: -10px -94px;
}
.pop6 .sqlist li.dj::after{
  background-position: -106px -94px;
}
.pop6 .sqlist li p {
  width: 280px;
  margin: 0 auto;
  height: 150px;
  line-height: 150px;
  font-size: 32px;
}

.pop7,
.pop8 {
  width: 512px;
  height: 254px;
  background: url(../img/pop7.png) no-repeat;
}

.pop8 {
  background: url(../img/pop8.png) no-repeat;
}
.pop7 .popclosebtn,
.pop8 .popclosebtn{
  top: -30px;
  right: -20px;
}
.pop9,
.pop10,
.pop11{
  width: 566px;
  height: 536px;
}
.pop9{
  background: url(../img/fzdt1.png) no-repeat;
  background-size: 100% 100%;
}

.pop10 {
  background: url(../img/fzdt2.png) no-repeat;
  background-size: 100% 100%;
}

.pop11 {
  background: url(../img/fzdt3.png) no-repeat;
  background-size: 100% 100%;
}

.pop9 .popclosebtn,
.pop10 .popclosebtn,
.pop11 .popclosebtn {
  top: 48px;
  right: 50px;
}
