<!DOCTYPE HTML>
<html>
<head>
    <meta charset="utf-8">
    <title>公会争霸-天龙八部·归来官方网站</title>
    <meta name="keywords" content="天龙八部，新天龙八部，轻松天龙，天龙八部归来，天龙八部怀旧，天龙八部·归来，轻松天龙，天龙八部荣耀版，天龙减负，天龙养老，天龙八部轻松版，天龙轻松版，宝石天龙，趣味天龙，网络游戏，
    海量福利、宝石福利、网游，武侠，轻松武侠，武侠游戏，金庸，MMO，天龙八部OL，萧峰，段誉，虚竹，王语嫣，天龙八部钟汉良版，天龙八部黄日华版" />
    <meta name="description" content="天龙八部·归来官方网站 限号测试来啦" />
    <meta content="width=750,minimum-scale=0,maximum-scale=5,user-scalable=no" name="viewport" />
    <meta content="yes" name="apple-mobile-web-app-capable" />
    <meta content="black" name="apple-mobile-web-app-status-bar-style" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="author" content="" />
    <meta name="robots" content="index, follow" />
    <meta name="googlebot" content="index, follow" />
    <link type="image/x-icon" href="//tlbbgl.changyou.com/favicon.ico" rel="icon" />
    <link type="image/x-icon" href="//tlbbgl.changyou.com/favicon.ico" rel="shortcut icon" />
    <link href="/act/all/css/reset.css" rel="stylesheet" type="text/css" />
    <link href="/act/tlbbgl/ghzb/20250407/m/css/index.css?1.6" rel="stylesheet" type="text/css" />
    <script class="external" src="//zhcdn01.xoyo.com/xassets/lib/vconsole/v3/vconsole.min.js"></script>
    <script>
        //移动端版本兼容
        var phoneWidth = parseInt(window.screen.width);
        var phoneScale = phoneWidth/750;

        var ua = navigator.userAgent;
        if (/Android (\d+\.\d+)/.test(ua)){
            var version = parseFloat(RegExp.$1);
            // andriod 2.3
            if(version>2.3){
                document.write('<meta name="viewport" content="width=750, minimum-scale = '+phoneScale+', maximum-scale = '+phoneScale+', target-densitydpi=device-dpi">');
            // andriod 2.3以上
            }else{
                document.write('<meta name="viewport" content="width=750, target-densitydpi=device-dpi">');
            }
            // 其他系统
        } else {
            document.write('<meta name="viewport" content="width=750, user-scalable=no, target-densitydpi=device-dpi">');
        }
        window.onload = function () {
            var popRule = document.getElementById("popRule");
            if (popRule && document.documentElement.clientHeight < 1076) {
                console.log("window.screen.height:" + document.documentElement.clientHeight)
                popRule.style.transform = "scale(" + (document.documentElement.clientHeight / 1076) + ")";
            }
        };
    </script>
    <script type="text/javascript">
        var deviceWidth = document.documentElement.clientWidth;
        if (deviceWidth > 750) {
            deviceWidth = 750
        };
        document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
    </script>
</head>

<body>
    <div class="container">
        <!--  首页  -->
        <div class="page page0 on">
            <div class="wrap">
                <div class="t logo">天龙 归来</div>
                <div class="t slogan">公会争霸
                    拉兄弟冲榜·赢百万元宝</div>
                <a href="javascript:;" class="btn btn-login">登录</a>
                <div class="flex justify-center">
                    <a href="javascript:;" class="btn btn-rule">活动规则</a>
<!--                    <a href="javascript:;" class="btn btn-center">个人中心</a>-->
                </div>
            </div>
        </div>
        <!--  创建公会  -->
        <div class="page page1">
            <div class="wrap">
                <a href="javascript:;" class="btn btn-logout">退出</a>
                <a href="javascript:;" class="btn btn-create">创建公会</a>
                <a href="javascript:;" class="btn btn-join">加入其他公会</a>
                <div class="flex justify-center">
                    <a href="javascript:;" class="btn btn-rule">活动规则</a>
                    <!--                    <a href="javascript:;" class="btn btn-center">个人中心</a>-->
                </div>
            </div>
        </div>
        <!--  公会首页  -->
        <div class="page page2">
            <div class="wrap">
                <a href="javascript:;" class="btn btn-logout">退出</a>
                <div class="flex justify-center">
                    <a href="javascript:;" class="btn btn-rule btn-rule2">活动规则</a>
                    <a href="javascript:;" class="btn btn-center btn-center2">个人中心</a>
                </div>
                <a href="javascript:;" class="btn btn-follow">关注</a>

                <div class="guild-code">
                    <p class="J_nickname guild-nickname">-</p>
                    <p>您是公会<span class="red J_ganghuiName">-</span>的<span class="isLeader"></span>，已有<span class="red J_memberNum">-</span>人加入本公会</p>
                    <div class="guild-line J_qqAccount">
                        <p>公会QQ群</p>
                        <input type="text" readonly value="没有QQ群号">
                        <a href="javascript:;" class="btn btn-copy">复制</a>
                        <a href="javascript:;" class="btn btn-modify">修改</a>
                    </div>
<!--                    <div class="guild-line J_wxAccount">-->
<!--                        <p>会长微信号</p>-->
<!--                        <input type="text" readonly value="没有微信号">-->
<!--                        <a href="javascript:;" class="btn btn-copy">复制</a>-->
<!--                        <a href="javascript:;" class="btn btn-modify">修改</a>-->
<!--                    </div>-->
                    <div class="guild-line J_ghId">
                        <p>公会码</p>
                        <input type="text" readonly>
                        <a href="javascript:;" class="btn btn-copy">复制</a>
                    </div>
                    <a href="javascript:;" class="btn btn-detail">查看成员详情</a>
                </div>
                <p class="tip">您可以通过分享公会码或者通过链接集结您的公会成员</p>
                <a href="javascript:;" class="btn btn-guild">集结公会成员</a>
                <div class="t tit">公会争霸</div>
                <div class="guild-num">有效成员数量达到<span class="red J_personTotal">-</span>人，有效排名<span class="red J_personRank">-</span>，消费排名<span class="red J_buyRank">-</span></div>
                <div class="rank">
                    <div class="rank-tab">
                        <a href="javascript:;" class="btn on">有效数量排名</a>
                        <a href="javascript:;" class="btn">消费排名</a>
                    </div>
                    <div class="rank-con rank-con0">
                        <table>
                            <tr>
                                <th>排名</th>
                                <th>公会名</th>
                                <th>有效数</th>
                            </tr>
                        </table>
                        <div class="rank-table">
                            <table id="rankData1">
                                <tr>
                                    <td>1</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>4</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>5</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>6</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>7</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>8</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>9</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>10</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="rank-con rank-con1" style="display: none;">
                        <table>
                            <tr>
                                <th>排名</th>
                                <th>公会名</th>
                            </tr>
                        </table>
                        <div class="rank-table">
                            <table id="rankData2">
                                <tr>
                                    <td>1</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>4</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>5</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>6</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>7</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>8</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>9</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>10</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <!--  公会列表   -->
        <div class="page page3">
            <div class="wrap">
                <a href="javascript:;" class="btn btn-back">返回</a>
                <div class="guild-num guild-num2">
                    已有<span class="red J_memberNum">-</span>人加入本公会，有效成员数量达到<span class="red J_personTotal">-</span>人。<br/>
                    有效成员数量排名<span class="red J_personRank">-</span>，消费排名<span class="red J_buyRank">-</span>
                </div>
                <div class="rank rank2">
                    <div class="rank-tab rank-tab2">
                        <a href="javascript:;" class="btn on">有效数量排名</a>
                        <a href="javascript:;" class="btn">消费排名</a>
                    </div>
                    <div class="rank-con rank-con0">
                        <table>
                            <tr>
                                <th>序号</th>
                                <th>昵称</th>
                                <th>是否有效</th>
                            </tr>
                        </table>
                        <div class="rank-table">
                            <div class="user-list">
                                <table id="userList1">
<!--                                    <tr>-->
<!--                                        <td>1</td>-->
<!--                                        <td>公会名公会名</td>-->
<!--                                        <td>21463</td>-->
<!--                                    </tr>-->
                                </table>
                            </div>
                        </div>
                        <div class="paging">
                            <a href="javascript:;" class="btn btn-prev">上一页</a>
                            <p><span id="pageNo1">1</span>/<span id="pageTotal1">1</span></p>
                            <a href="javascript:;" class="btn btn-next">下一页</a>
                            <a href="javascript:;" class="btn btn-up">返回顶部</a>
                            <div>跳转<input type="number">页</div>
                        </div>
                    </div>
                    <div class="rank-con rank-con1" style="display: none;">
                        <table>
                            <tr>
                                <th>排名</th>
                                <th>昵称</th>
                            </tr>
                        </table>
                        <div class="rank-table">
                            <div class="user-list">
                                <table id="userList2">
<!--                                    <tr>-->
<!--                                        <td>1</td>-->
<!--                                        <td>公会名公会名</td>-->
<!--                                        <td>21463</td>-->
<!--                                    </tr>-->

                                </table>
                            </div>
                        </div>
                        <div class="paging">
                            <a href="javascript:;" class="btn btn-prev">上一页</a>
                            <p><span id="pageNo2">1</span>/<span id="pageTotal2">1</span></p>
                            <a href="javascript:;" class="btn btn-next">下一页</a>
                            <a href="javascript:;" class="btn btn-up">返回顶部</a>
                            <div>跳转<input type="number">页</div>
                        </div>
                    </div>
                    <p class="rank-tip">到达68级且在福利大厅解锁了高级成长密令的账号为有效用户</p>
                </div>
            </div>
        </div>
        <!--  被邀请加入公会  -->
        <div class="page page4">
            <div class="wrap">
                <a href="javascript:;" class="btn btn-logout">退出</a>
                <div class="flex justify-center">
                    <a href="javascript:;" class="btn btn-rule btn-rule2">活动规则</a>
<!--                    <a href="javascript:;" class="btn btn-center btn-center2">个人中心</a>-->
                </div>
                <div class="invite-text">
                    <span class="red" id="J_inviterUser">-</span>邀请您加入公会<span class="red" id="J_inviterGh">-</span>,<br>
                    您是否加入
                </div>
                <a href="javascript:;" class="btn btn-joins">加入此公会</a>
                <a href="javascript:;" class="btn btn-create">创建公会</a>
                <a href="javascript:;" class="btn btn-join">加入其他公会</a>
            </div>
        </div>

        <!-- ↓↓↓弹窗↓↓↓ -->
        <!-- 通用弹窗 -->
        <div class="pop" id="popCom">
            <a href="javascript:;" class="btn btn-close">×</a>
            <div class="pop-com">
                通用弹窗通用弹窗通用弹窗通用弹窗通用弹窗通用弹窗通用
            </div>
            <div class="flex justify-center">
                <a href="javascript:;" class="btn btn-confirm">确认</a>
            </div>
        </div>
        <!-- 通用弹窗 -->
        <div class="pop" id="popCom2">
            <a href="javascript:;" class="btn btn-close">×</a>
            <div class="pop-com">
                通用弹窗通用弹窗通用弹窗通用弹窗通用弹窗通用弹窗通用
            </div>
            <div class="flex justify-center">
              <a href="javascript:;" class="btn btn-confirm">确认</a>
              <a href="javascript:;" class="btn btn-cancel">取消</a>
            </div>
        </div>
        <!-- 登录 -->
        <div class="pop pop-login" id="popLogin">
            <a href="javascript:;" class="btn btn-close closeAll">×</a>
            <div class="login-box">

            </div>
        </div>
        <!-- 活动规则 -->
        <div class="pop pop2" id="popRule">
            <a href="javascript:;" class="btn btn-close closeAll">×</a>
            <ul class="rule">
                <li>需在本页面指定充值入口预充值至指定额度，</li>
                <li>道具奖励需在11月7日24:00前消费到相应点数后，前往大理(165,171)陈星福处领取(消费达标后，将会通过邮件提醒少快前去领奖。可别错过哟!)</li>
                <li>首月快速通道充值至3000元即可在新服开启后立即享受~若同一时间进入人数过多使排队人数达到上限,还请耐心等待后重新尝试排队。首月快速通道最终失效时间:2024年11月25日更新维护后。</li>
                <li>首月快速通道充值至3000元即可在新服开启后立即享受~若同一时间进入人数过多使排队人数达到上限,还请耐心等待后重新尝试排队。首月快速通道最终失效时间:2024年11月25日更新维护后。</li>
                <li>首月快速通道充值至3000元即可在新服开启后立即享受~若同一时间进入人数过多使排队人数达到上限,还请耐心等待后重新尝试排队。首月快速通道最终失效时间:2024年11月25日更新维护后。</li>
                <li>需在本页面指定充值入口预充值至指定额度，</li>
                <li>道具奖励需在11月7日24:00前消费到相应点数后，前往大理(165,171)陈星福处领取(消费达标后，将会通过邮件提醒少快前去领奖。可别错过哟!)</li>
                <li>首月快速通道充值至3000元即可在新服开启后立即享受~若同一时间进入人数过多使排队人数达到上限,还请耐心等待后重新尝试排队。首月快速通道最终失效时间:2024年11月25日更新维护后。</li>
                <li>首月快速通道充值至3000元即可在新服开启后立即享受~若同一时间进入人数过多使排队人数达到上限,还请耐心等待后重新尝试排队。首月快速通道最终失效时间:2024年11月25日更新维护后。</li>
                <li>首月快速通道充值至3000元即可在新服开启后立即享受~若同一时间进入人数过多使排队人数达到上限,还请耐心等待后重新尝试排队。首月快速通道最终失效时间:2024年11月25日更新维护后。</li>
            </ul>
        </div>
        <!-- 创建公会 -->
        <div class="pop pop4" id="popCreate">
            <a href="javascript:;" class="btn btn-close closeAll">×</a>
            <ul class="ipt-list">
                <li>
                    <div class="ipt">
                        <i class="icon1"></i><p><span class="red">*</span>公会名称：</p><input type="text" maxlength="8" placeholder="创建的公会名称" id="name">
                    </div>
                </li>
                <li>
                    <div class="ipt">
                        <i class="icon5"></i><p><span class="red">*</span>公会前缀：</p><input type="text" maxlength="4" placeholder="成员可使用此前缀命名昵称" id="namePre">
                    </div>
                </li>
                <li>
                    <div class="ipt">
                        <i class="icon6"></i><p><span class="red">*</span>我的昵称：</p><input type="text" maxlength="8" placeholder="可以使用公会前缀+其他命名" id="nickname">
                    </div>
                </li>
<!--                <li>-->
<!--                    <div class="ipt">-->
<!--                        <i class="icon7"></i><p>微信号：</p><input type="text" placeholder="选填，展示给成员方便联系" maxlength="20" minlength="6" id="wxAccount">-->
<!--                    </div>-->
<!--                </li>-->
                <li>
                    <div class="ipt">
                        <i class="icon8"></i><p>QQ群号：</p><input type="text" placeholder="选填，展示给成员方便联系" id="qqAccount">
                    </div>
                </li>
                <li>
                    <div class="ipt">
                        <i class="icon3"></i><p><span class="red">*</span>手机号：</p><input type="phone" id="phone">
                    </div>
                </li>
                <li>
                    <div class="ipt ipt2">
                        <i class="icon4"></i><p><span class="red">*</span>短信验证码：</p><input type="text" maxlength="6" id="vCode">
                    </div>
                    <a href="javascript:;" class="btn-getcode">获取验证码</a>
                </li>
            </ul>
            <div class="flex justify-center">
                <a href="javascript:;" class="btn btn-confirm">确认</a>
                <a href="javascript:;" class="btn btn-cancel">取消</a>
            </div>
        </div>
        <!-- 加入公会 -->
        <div class="pop pop3" id="popJoin">
            <a href="javascript:;" class="btn btn-close closeAll">×</a>
            <ul class="ipt-list ipt-list2">
                <li id="ghId">
                    <div class="ipt">
                        <i class="icon1"></i><p><span class="red">*</span>公会码：</p><input type="text">
                    </div>
                </li>
                <li>
                    <div class="ipt">
                        <i class="icon6"></i><p><span class="red">*</span>我的昵称：</p><input type="text" id="nickname2" maxlength="8">
                    </div>
                </li>
                <li class="radio-li">
<!--                    <div class="radio checked"></div>-->
                    <p style="font-size: 20px;"><span id="J_ghName">-</span>公会邀请您加入，您可以使用此公会前缀<span class="J_namePre">-</span>+其他命名自己的昵称</p></li>
            </ul>

            <div class="flex justify-center">
                <a href="javascript:;" class="btn btn-confirm">确认</a>
                <a href="javascript:;" class="btn btn-cancel">取消</a>
            </div>
        </div>
        <!-- 修改QQ -->
        <div class="pop pop1" id="modifyQQ">
            <div class="t tit1">修改公会QQ群</div>
            <a href="javascript:;" class="btn btn-close closeAll">×</a>

            <ul class="ipt-list" style="margin: 46px 0">
                <li>
                    <div class="ipt">
                        <i class="icon8"></i><p>QQ群号：</p><input type="text" placeholder="展示给成员方便联系" id="qqAccount2">
                    </div>
                </li>
            </ul>

            <div class="flex justify-center">
                <a href="javascript:;" class="btn btn-confirm">确认</a>
                <a href="javascript:;" class="btn btn-cancel">取消</a>
            </div>
        </div>
        <!-- 修改微信 -->
        <div class="pop pop1" id="modifyWX">
            <a href="javascript:;" class="btn btn-close closeAll">×</a>
            <div class="t tit2">修改微信号</div>
            <ul class="ipt-list" style="margin: 46px 0">
                <li>
                    <div class="ipt">
                        <i class="icon7"></i><p>微信号：</p><input type="text" placeholder="展示给成员方便联系" maxlength="20" minlength="6" id="wxAccount2">
                    </div>
                </li>
            </ul>

            <div class="flex justify-center">
                <a href="javascript:;" class="btn btn-confirm">确认</a>
                <a href="javascript:;" class="btn btn-cancel">取消</a>
            </div>
        </div>
        <!-- 分享引导 -->
        <div class="pop-share" onclick="$(this).hide();"></div>
        <!-- 复制链接 -->
        <div class="pop pop5" id="popCopy">
            <a href="javascript:;" class="btn btn-close closeAll">×</a>
            <div class="copy-text">
                <input type="text" readonly>
                <i></i></div>
            <a href="javascript:;" class="btn-copy2">复制链接</a>
        </div>
        <!-- 个人中心 -->
        <div class="pop pop6" id="popCenter">
            <a href="javascript:;" class="btn btn-close closeAll">×</a>
            <div class="record-text">
                <span class="J_cn">-</span>，您好 <br>
                您已成功获得以下奖励，请开服后前往游戏内领取
            </div>
            <div class="record">
                <table id="giftRecord">
                    <tr>
                        <td>XXX</td>
                        <td>XXXXX</td>
                    </tr>
                    <tr>
                        <td>XXX</td>
                        <td>XXXXX</td>
                    </tr>
                    <tr>
                        <td>XXX</td>
                        <td>XXXXX</td>
                    </tr>
                    <tr>
                        <td>XXX</td>
                        <td>XXXXX</td>
                    </tr>
                    <tr>
                        <td>XXX</td>
                        <td>XXXXX</td>
                    </tr>
                    <tr>
                        <td>XXX</td>
                        <td>XXXXX</td>
                    </tr>
                    <tr>
                        <td>XXX</td>
                        <td>XXXXX</td>
                    </tr>
                    <tr>
                        <td>XXX</td>
                        <td>XXXXX</td>
                    </tr>
                    <tr>
                        <td>XXX</td>
                        <td>XXXXX</td>
                    </tr>
                    <tr>
                        <td>XXX</td>
                        <td>XXXXX</td>
                    </tr>
                    <tr>
                        <td>XXX</td>
                        <td>XXXXX</td>
                    </tr>
                    <tr>
                        <td>XXX</td>
                        <td>XXXXX</td>
                    </tr>
                </table>
            </div>
        </div>
        <!--  关注  -->
        <div class="pop pop7" id="popFollow">
            <a href="javascript:;" class="btn btn-close closeAll">×</a>
        </div>
    </div>
    <script type="text/javascript" src="//res.wx.qq.com/open/js/jweixin-1.4.0.js"></script>
    <script type="text/javascript" src="//www.changyou.com/cyouFile/shareToWx/js/share.wx.join.js"></script>
    <script src="/act/all/cdn/jquery/3.4.1/jquery.min.js"></script>
    <script src="/act/tlbbgl/ghzb/20250407/m/js/clipBordCopy.min.js"></script>
    <script src="/act/tlbbgl/ghzb/20250407/m/js/jquery.lay.js?1.7"></script>
    <script src="/act/tlbbgl/ghzb/20250407/m/js/tools.js"></script>
    <script src="/act/tlbbgl/ghzb/20250407/m/js/cdc_js_sdk_1.1.1.js"></script>
    <script src="/act/tlbbgl/ghzb/20250407/m/js/tracker.js" data-project="gonghui"></script>
    <script>document.write('<script class="external" src="/act/tlbbgl/ghzb/20250407/m/js/common.js?r='+ (new Date().getTime()) +'"><\/script>')</script>
    <script class="external" src="/act/all/cdn/vConsole/3.3.0/vconsole.min.js"></script>
    <script>
      new VConsole();
    </script>
    <script>
      var loaderInit = function(config) {
        var a = {
          path: '/act/tlbbgl/ghzb/20250407/m/img/',
          source: [],
          delay: 100,
          mode: 2,
          handle: function(count, len) {},
          complete: function() {}
        };
        for (var k in config)
          a[k] = config[k];
        var count = 0,
            len = a.source.length,
            array = [],
            intv,
            time = 0;
        for (var i = 0; i < len; i++) {
          array.push(new Image());
          array[i].loaded = false;
          array[i].onload = function() {
            this.loaded = true;
          };
          array[i].onerror = function() {
            this.loaded = true;
          };
          array[i].src = a.path + a.source[i];
        }
        intv = setInterval(function() {
          time += 20;
          for (var i = 0; i < array.length; i++) {
            if (array[i].loaded && time >= a.delay) {
              count++;
              array.splice(i, 1);
              a.handle(count, len);
              if (a.mode !== 1) {
                time = 0;
                break;
              }
            }
          }
          if (count === len) {
            clearInterval(intv);
            a.complete();
          }
        }, 20);
      };
      var imagePath = "/act/tlbbgl/ghzb/20250407/m/img/";
      loaderInit({
        source: ['pop.png','pop1.png','pop2.png','pop3.png','pop4.png','pop5.png','pop6.png','pop-share.png',
        ],
        handle: function(count, max) {
          // loadClick = false;
          // var per = Math.floor(count / max * 100) + '%';
          // $('.loader-prob i').width(per);
          // $('.loader-txt span').text(per);
        },
        complete: function() {
          // console.log('complete');
        }
      });
    </script>
		<!-- # include virtual="/all/dma/dma_activity.html"-->
		<!-- # include virtual="/all/nav/cy_public_js_dark.html"-->
</body>
</html>