﻿@charset "utf-8";
.none{display:none;}
.clearfix:after{content:'\20';display:block;height:0;line-height:0;visibility:hidden;clear:both;}
.yy-pop-btn,.yy-pop-tit,.yy-pop-close,.yy-pop-qr dd,.yy-pop-ln i,.yy-qwqq{background:url(../img/yy_pop.png) no-repeat;}
.yy-pop-btn{width:172px;font-size:22px;color:#fff;text-align:center;line-height:56px;background-position:-100px 0;display:block;margin:0 auto;}
.yy-pop-btn:hover{background-position:-100px -58px;}
.yy-pop{text-align:center;font-size:16px;color:#000;background:#fbfdfd;outline:none;display:none;position:relative;}
.yy-pop-app,.yy-pop-app:before,.yy-pop-app:after{width:756px;background:url(//i0.cy.com/tlgl/main/2020/0205/yy_pop_app.png) no-repeat;}
.yy-pop-app{background-color:#fdfdfd;background-position:left bottom;}
.yy-pop-app:before,.yy-pop-app:after{content:'';height:12px;background-position:-756px 0;position:absolute;top:-8px;left:0;}
.yy-pop-app:after{background-position:-756px -12px;top:auto;bottom:-8px;}
.yy-pop-tip,.yy-pop-tip:before,.yy-pop-tip:after{width:608px;background:url(../img/yy_pop_tip.png) no-repeat;}
.yy-pop-tip{background-color:#fdfdfd;background-position:left bottom;}
.yy-pop-tip:before,.yy-pop-tip:after{content:'';height:12px;background-position:-608px 0;position:absolute;top:-8px;left:0;}
.yy-pop-tip:after{background-position:-608px -12px;top:auto;bottom:-8px;}
.yy-pop-bd{border:1px solid #c0ccdc;margin:3px;}
.yy-pop-app .yy-pop-bd{min-height:320px;padding-top:48px;}
.yy-pop-tip .yy-pop-bd{min-height:160px;padding:40px 0;}
.yy-pop-app .yy-pop-btn{margin-top:48px;}
.yy-pop-tip .yy-pop-btn{margin-top:20px;}
.yy-pop-close{width:45px;height:45px;text-indent:-999em;background-position:0 -4px;position:absolute;top:3px;right:13px;cursor:pointer;z-index:9;}
.yy-pop-tit{height:64px;text-indent:-999em;margin:0 auto;}
.yy-pop-tit1{width:694px;background-position:0 -137px;}
#yy-pop-1 h3{height: 37px;}
.yy-pop-tit2{width:255px;background-position:-295px -48px; margin-top: 27px;font-size: 30px;}
.yy-pop-tit3{width:64px;background-position:-295px -1px;}
.yy-pop-ln{height:1px;background:#a1b4cb;background:-webkit-linear-gradient(left,rgba(161,180,203,0),rgba(161,180,203,1) 20%,rgba(161,180,203,1) 80%,rgba(161,180,203,0));position:relative;margin-top:54px;}
.yy-pop-ln i{width:36px;height:14px;background-position:0 -76px;position:absolute;top:50%;left:50%;margin:-7px -18px;}
.yy-pop-que{line-height:135px;padding-top:5px;}
.yy-pop-que p{width:432px;font-size:18px;line-height:1.5;display:inline-block;vertical-align:middle;}
.yy-pop-que b{color:#ff3000;font-weight:normal;}
.yy-pop-que .yy-pop-btn{background-position:-190px -179px;display:inline-block;vertical-align:middle;margin:0 98px 0 0;}
.yy-pop-que .yy-pop-btn:hover{background-position:-190px -237px;}
.yy-form{font-size:16px;text-align:left;padding:34px 108px 0;}
.yy-form-login a{color:#a68421;}
.yy-form-item{line-height:48px;padding-top:18px;}
.yy-form-label{width:100px;white-space:nowrap;font-weight:normal;overflow:hidden;float:left;}
.yy-form-input{width:274px;line-height:46px;height:46px;background:transparent;border:1px solid #dbdbdb;padding:0 10px;outline:none;float:left;}
.yy-form-input1{width:198px;}
.yy-form-check{float:right;}
.yy-form-check img{width:122px;height:48px;float:left;margin-right:10px;}
.yy-form-check a{color:#666;float:left;}
.yy-form-check a:hover{text-decoration:underline;}
.yy-form-yzm{width:118px;color:#fff;text-align:center;line-height:48px;background:#739cc0;background:-webkit-radial-gradient(center bottom,#82b4d6,#7391b3);white-space:nowrap;overflow:hidden;cursor:pointer;float:right;}
.yy-form-yzm:hover{background:#70adde;background:-webkit-radial-gradient(center bottom,#6cb2e2,#729bca);}
.yy-form-yzm.disable{background:#cacaca;cursor:default;}
.yy-pop-txt{line-height:81px;padding:6px 36px;}
.yy-pop-txt p{font-size:18px;line-height:1.5;display:inline-block;vertical-align:middle; transform: translateY(10px);}
.yy-pop-qr{width:320px;margin:40px 0 60px;display:inline-block;vertical-align:top;}
.yy-pop-qr dt{width:24px;font-size:18px;color:#738fb1;line-height:18px;display:inline-block;vertical-align:top;}
.yy-pop-qr dt.t:before,.yy-pop-qr dt.t:after{content:'';width:1px;height:12px;background:#738fb1;margin:6px auto;display:block;}
.yy-pop-qr dd{width:179px;height:179px;background-position:0 -179px;overflow:hidden;display:inline-block;vertical-align:top;}
.yy-pop-qr dd img{width:171px;height:171px;margin:4px;display:block;}
.yy-qwqq{width:24px;height:50px;text-indent:-999em;background-position:-61px -4px;display:block;margin-top:4px;}
/* 修改预约弹框的高度 */
#yy-pop-1{
    height: 553px;
}
/* 预约成功 */
#yy-pop-2{
    height: auto;
}
.yy-pop-app b {
    color: #ff3000;
    font-weight: normal;
}
.yy-pop-qr {
    transform: scale(.7);
    width: 230px;
    margin: 1px 0 60px;
    display: inline-block;
    vertical-align: top;
}