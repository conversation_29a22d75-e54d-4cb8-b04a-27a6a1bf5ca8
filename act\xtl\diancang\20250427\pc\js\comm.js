//弹窗
var popIsShow = false;
var popDom = null;
function popShow(id) {
            popHide();
            var p = $('#'+id);
            popDom = p;
            if (p) {
                p.show().css({
                    position: 'fixed',
                    top: '50%',
                    left: '50%',
                    marginTop: -popDom.height() / 2 + 'px',
                    marginLeft: -popDom.width() / 2 + 'px',
                    zIndex: 9998
                });
                p.attr('for', 'pop');
                popIsShow = true;
                $('.overlay').show();
            }
        }
        function popHide() {
            $('.overlay').hide();
            $('.pop').hide();
        };
//分享
var share = new Cyoushare('target', {
	offset: ['10px', '10px']
	, icon: 'small'
	, title: '天龙八部归来'
	, fontSize: '14px'
	, fontFamily: '微软雅黑'
	, notShareTo: 's_renren,s_kaixin,s_qzone'
	, url: '' // 分享的地址
	, content: '天龙八部归来'
	, pic: ''//分享图片
	, showTitle: '天龙八部归来' 	
});
//抽奖
var LottOBJ = function (o) {
    var conf = {
      obj: '',
      cover: '',
      count: 0,
      position: '',
      complete: function () { }
    }
    conf = $.extend(conf, o);
    conf.cover.hide();
    var pos = [], timer = null, spd = 100, curr = 0, round = 0;
    for (var i = 0; i < conf.position.split(',').length ; i++) {
      var s = conf.position.split(',')[i];
      pos.push({ left: parseInt(s.split('_')[0]), top: parseInt(s.split('_')[1]) });
    }
    if (pos.length != conf.count) return;
    this.startRoll = function (n) {
      if (typeof (n) != 'number' && n > pos.length) n = 0;
      spd = 100;
      round = Math.ceil(Math.random() * 4) + 1;
      conf.cover.show().css({ left: pos[curr].left, top: pos[curr].top });
      timer = setTimeout(function () { roll(n); }, spd);
    };
    function roll(e) {
      clearTimeout(timer);
      if (curr == pos.length - 1) {
        if (round > 0) {
          round--;
          curr = 0;
        }
      } else
        curr++;
      conf.cover.css({ left: pos[curr].left, top: pos[curr].top });
      if (curr == e && round == 0) {
        setTimeout(conf.complete, 500);
        return;
      }
      if (round == 0) spd += 80;
      timer = setTimeout(function () { roll(e); }, spd);
    }
  };
  var lottObj = new LottOBJ({
    obj: $('#lottcon'),
    cover: $('#lottcon .lott-cover'),
    count: 8,
    position: '0_10,376_10,752_10,752_214,752_418,376_418,0_418,0_214',
    complete: function () {
      //抽奖完成
      //测试资格
	  //popShow('pop_com');
	  //$('#com_tip').html('恭喜您获得《天龙八部·归来》不删档测试资格！');
	  //实物奖励
	  popShow('pop_lot');
    }
  });
  //开始抽奖 参数为抽中的序号
  function lottStart() {
    lottObj.startRoll(0);
  };
 //侧导航
$('a[href*=#],area[href*=#]').click(function () {
   if (location.pathname.replace(/^\//, '') == this.pathname.replace(/^\//, '') && location.hostname == this.hostname) {
     var $target = $(this.hash);
     $target = $target.length && $target || $('[name=' + this.hash.slice(1) + ']');
     if ($target.length) {
       var targetOffset = $target.offset().top;
       $('html,body').animate({
         scrollTop: targetOffset
       }, 500);
       return false;
     }
   }
 });
 $(function () {
   var offsets = [$('#section0').offset().top - 200, $('#section1').offset().top - 200, $('#section2').offset().top - 200, $('#section3').offset().top - 200, $('#section4').offset().top - 200], len = offsets.length;
   $(window).bind('load scroll', function () {
     var t = $(window).scrollTop();
     navsett(t);
   });
   function navsett(t) {
     var fnav = $('.s_con a');
     fnav.removeClass('on');
     if (t >= offsets[len - 1])
       fnav.eq(len - 1).addClass('on');
     else {
       for (var i = 0; i < len; i++) {
         if (t >= offsets[i] && t < offsets[i+1]) fnav.eq(i).addClass('on');
       }
     }
   }
 });
var cp_index;//门派全局变量
//选择门派
$('.xzbtn').on('click',function(){
	if($(this).hasClass('xzstyle')){
		$(this).removeClass('xzstyle');
	}else{
		cp_index=$(this).parent().index();
		$('.tgbox li').find('.xzbtn').removeClass('xzstyle');
		$('.tgbox li').eq(cp_index).find('.xzbtn').addClass('xzstyle');
	};
});
 //实物收货地址
 function __changeUserName(of){
   var username=$('#'+of).val();
   if(username=='' || !isMobilePhone(username)) {
 	  $('.tj_tip p').show();
	  $('.tj_tip p').html('请输入正确的手机号码!');
 	return;
   }
 };
 function checkMobilePhone(telphone) {
 	if(telphone=='' || !isMobilePhone(telphone)) {
 	$('.tj_tip p').show();
	$('.tj_tip p').html('请输入正确的手机号码!');
   }else{
 	$('.tj_tip p').hide();
	$('.tj_tip p').html('您还有必填项未填写!');
   }
 };
 if($('#mobile').val()=='' || !isMobilePhone($('#mobile').val())) {            
 	  $('.tj_tip p').html('手机号码不正确!');
 	  ckh_result = false;
   };
 function isMobilePhone(value) {
 	if(value.search(/^(\+\d{2,3})?\d{11}$/) == -1)
 	return false;
 	else
 	return true;
 };
 function __changeCom(comname){
 	if (comname == "") {
 		$('.tj_tip p').show();
		$('.tj_tip p').html('您还有必填项未填写!');
 	}else {
 		$('.tj_tip p').hide();
 	}
 };
 function check() {
 	$('.tj_tip p').hide();
 	var ckh_result = true;     
   if($('#mobile').val()=='' || !isMobilePhone($('#mobile').val())) {            
 	  $('.tj_tip p').show();
	   $('.tj_tip p').html('请输入正确的手机号码!');
 	  ckh_result = false;
   }
   if ($('#usename').val() == '') {
 	$('.tj_tip p').show();
	$('.tj_tip p').html('您还有必填项未填写!');
 	ckh_result = false;
   }
   if ($('#address_input').val() == '') {
   	$('.tj_tip p').show();
	$('.tj_tip p').html('您还有必填项未填写!');
   	ckh_result = false;
   }
   return ckh_result;
 };
 $('#tjbtn').on('click',function(){
 	check();
 });
 //视频
$('.v_btn').on('click',function(){
    $('#pop_video').find('video').attr('src',$(this).attr('data-video'));
    popShow('pop_video');
    document.getElementById('videoTz').play();
});
$('#pop_video .close').click(function(){
	popHide();
    document.getElementById('videoTz').pause();
});
//背景视频
var canPlayVideo = document.createElement('video').canPlayType, isMob = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    if(!isMob &&canPlayVideo) {
		var video = document.getElementById('videoBg');
		video.style.display="none";
		video.src='/act/xtl/betatest/20200107/pc/video/video.mp4';
//      video.loop=1;
        video.addEventListener("canplaythrough", function(){
            var sTime = 4;
			video.style.display="block";
            video.play();
            setInterval(function(){
                if( video.currentTime >= 8 ){
                    video.currentTime = sTime;
                }
            },40);
        });
    };
$(window).on('load scroll',function (){
        var sTop = $(window).scrollTop(),wh = $(window).height();
        $('.mid').each(function() {
            if(sTop >= $(this).offset().top - $(window).height()/1.1) {
                $(this).addClass('animate');
            }
        });
});
