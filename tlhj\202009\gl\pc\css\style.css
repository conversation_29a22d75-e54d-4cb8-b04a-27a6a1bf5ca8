@charset "utf-8";
body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,form,fieldset,input,textarea,p,blockquote,th,td{margin:0;padding:0;}
table{border-collapse:collapse;border-spacing:0;}
address,caption,cite,code,dfn,em,strong,th,var{font-weight:normal;font-style:normal;}
ol,ul{list-style:none;}
caption,th{text-align:left;}
h1,h2,h3,h4,h5,h6{font-weight:normal;font-size:100%;}
q:before,q:after{content:'';}
fieldset,img,abbr,acronym{border:0;}
/*页面样式*/
.c:before,.c:after {content:"";display:table;}
.c:after{clear:both;}
.c{zoom:1;}
.none{display:none;}
.pr{position:relative;}
.pa{position:absolute;}
.t{font-size: 0;}
body{font:12px/1.5 '\5FAE\8F6F\96C5\9ED1';padding-top:55px;text-align:justify;min-width:1200px;}
a{text-decoration:none;overflow:visible;}
a:hover{text-decoration:none;}
.com, .tab-i, .tit, .tit-s{background:url(../img/com.png) no-repeat;display:block;overflow:hidden;text-align:center;}
.btn{transition:filter 0.5s linear;}
.btn:hover{-webkit-filter: brightness(1.15);}
.xtl_logo_lk{top:20px;left:4px;z-index:101; width: 154px; height: 103px; background-position: -0px -510px;}
.xtl_logo_lk a{display:block; width:100%; height: 100%; z-index:999;}
.bar{width:1200px;margin:0 auto;position:relative;}
.wrap{color:#fff;}
.head{height:726px;background:url(../img/bg-head.jpg) no-repeat center top;z-index:1;position:relative;}
.cont{height:auto;background:#3a2522 url(../img/bg-con.jpg) no-repeat center top;z-index:2;position:relative; padding-bottom:90px;}
.slogan{ background: url("../img/slogan.png"); width:328px; height: 506px; top: 44px; left:50%; margin-left: -164px; }

.main-wrap{min-height: 600px; position: relative;}
.main{display:none; opacity: 0; transition: all 0.5s; padding-top: 1px; background: url("../img/bg-main.png"); width:1200px; height: auto; padding-bottom: 20px; margin: 0 auto;}
.main.show{opacity: 1; transition: all 0.3s;}
.main-con{width: 1060px; min-height: 1300px; margin: 0 auto;}
.main-con::-webkit-scrollbar{
    width: 5px;
    border-radius: 10px;
    background-color: #fef4de;
}
.main-con::-webkit-scrollbar-track{
    border-radius: 10px;
    background-color: #fef4de;
}
.main-con::-webkit-scrollbar-thumb{
    border-radius: 10px;
    background-color: #885c49;
}
.main-con::-webkit-scrollbar-thumb:hover {
    border-radius: 10px;
    background: #885c49;
}

.tab-wrap{ width:100%; height: auto; margin: 0 auto; }
.tab{width: 1200px; height: 79px; margin: 0 auto; position: relative;}
.tab .tab-i{width:300px; height: 79px; background-position-y: -304px; display: inline-block; float:left;}
.tab .tab-i:hover, .tab .tab-i.active{background-position-y: -412px; }
.tab .tab-i:nth-child(1){background-position-x:-0px;}
.tab .tab-i:nth-child(2){background-position-x:-300px; }
.tab .tab-i:nth-child(3){background-position-x:-600px; }
.tab .tab-i:nth-child(4){background-position-x:-900px; }


.tit{ width:554px; height: 39px; display: block; margin: 40px auto 0px;}
.main1 .tit{ background-position: 0 0;}
.main2 .tit{ background-position: 0 -62px;}
.main3 .tit{ background-position: 0 -124px;}
.main4 .tit{ background-position: 0 -186px;}

.tit-s{ height: 48px; background-position: -725px 0; font-size: 30px; color: #702607; text-indent: 45px; line-height: 48px; text-align: left; margin-top: 54px;}
.tit-s2{background-position-y: -51px;}
.tit-s3{background-position-y: -101px;}
.tit-s4{background-position-y: -151px;}

.art-con{margin: 10px 45px 10px 12px; font-size: 16px; color:#431c0c; line-height: 30px;}
.etrLink{line-height: 36px; border-bottom: 2px solid #ca9675; color:#b13805; display: inline-block;}
.etrLink:hover{color: #b13e23; border-color:#c2743b; }

.table thead td{background-color: #a34f2b; height: 40px; line-height: 40px; text-align: center; color:#f8eacd; font-size: 20px; width: 184px; border: 1px solid #c3b3a4;}
.table tbody td{background-color: #fbedd3; height: 40px; line-height: 40px; text-align: center; color:#7b371a; font-size: 16px; border: 1px solid #c3b3a4; }

.pop{position:relative;width:513px;height:288px;background:transparent no-repeat;color:#3f3e45; display:none;}

.ani .slogan{-webkit-animation:sloganAni .4s 0s linear both;}

@-webkit-keyframes fadeInUp{
    0%{opacity:0; -webkit-transform:translateY(20px)}
    100%{opacity:1; -webkit-transform:translateY(0)}
}
@-webkit-keyframes sloganAni{
    0%{-webkit-transform:scale(1.8);opacity:0;}
    60%{-webkit-transform:scale(1);opacity:1;}
    65%{-webkit-transform:translate(-4px,-4px);}
    70%{-webkit-transform:translate(0,0);}
    75%{-webkit-transform:translate(4px,4px);}
    80%{-webkit-transform:translate(0,0);}
    85%{-webkit-transform:translate(-4px,4px);}
    90%{-webkit-transform:translate(0,0);}
    95%{-webkit-transform:translate(4px,-4px);}
    100%{-webkit-transform:translate(0,0);opacity:1;}
}
/* 新添加 */
.disabled{pointer-events: none;}
