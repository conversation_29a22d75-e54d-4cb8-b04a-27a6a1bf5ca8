
(function () {
    var JoinActivity = function () {
        // -----------------全局变量----------------
        this.options = {
            app: undefined,
            activity: undefined,
            version: undefined,
            platform: undefined,
            host: location.origin,
            isWxInit: false,            // 微信初始化
            isUserInit: false,          // 获取用户信息初始化

            token: undefined,           // token
            cyscid: undefined,          // 地址标识
            isInit: 0,                  // 初始化状态：0 未初始化 ； 1 初始完成 ； 2 初始失败
            appInfo: undefined,         // 应用信息
            activityInfo: undefined,    // 活动信息
            clientInfo: undefined,      // 客户端信息
            userInfo: undefined,        // 用户信息

            ready: function() {},       // 初始化完成后返回
        };
    };
    JoinActivity.fn = JoinActivity.prototype = {
        // -----------------初始化----------------
        config: function(options) {
            $.extend(this.options, options);
            var _this = this;
            ja.client.api.get("/landing/init", {}, function (res) {
                if(res.code === 10000) {
                    var info = res.data;
                    $.extend(_this.options, {
                        token: info.token,
                        cyscid: ja.utils.getQueryString("CYSCID"),
                        isInit: 1,
                        appInfo: info.app,
                        activityInfo: info.activity,
                        clientInfo: info.client
                    });
                    if(_this.options.isWxInit) ja.wx.init();
                    if(_this.options.isUserInit) {
                        ja.items.getUserInfo(_this.options.ready);
                    }else {
                        _this.options.ready();
                    }
                }else {
                    $.extend(_this.data, { isInit: 2 });
                    alert(res.msg);
                }
            })
        },
        // -----------------全局设置----------------
        common: {
            // 常用表单长度限制，class名要对应
            input: function() {
                $('.input_phone').keyup(function() { if($(this).val().length > 11) $(this).val($(this).val().slice(0, 11)) });        // 手机号
                $('.input_phone_code').keyup(function() { if($(this).val().length > 6) $(this).val($(this).val().slice(0, 6)) });     // 手机验证码
            },
            // 移动端初始化
            mobile: function() {
                $('input').blur(function() { window.scrollTo(0, 0) });  // 解决 H5 在输入弹框后页面留白问题
            }
        },
        // -----------------表单验证----------------
        verify: {
            reg: {
                phone: /^1[3456789]\d{9}$/,
                email: /^(\w-*\.*)+@(\w-?)+(\.\w{2,})+$/,           // 邮箱
                userName: /^[a-z0-9_-]{3,16}$/,                     // 用户名
                chineseName: /^[\u4E00-\u9FA5]{2,4}$/,              // 中文姓名
            },
            // 手机号
            phone: function (val) {
                if (!val) {
                    return '请填写手机号！';
                } else if (!val.match(this.reg.phone)) {
                    return '请输入正确的手机号！';
                } else {
                    return false;
                }
            },
            // 验证码
            code: function (val) {
                if (!val) {
                    return '请填写验证码！';
                } else if (val.length !== 6) {
                    return '验证码错误！'
                } else {
                    return false;
                }
            }
        },
        // ----------------- 工具类 ----------------
        utils: {
            // 截取url参数
            getQueryString: function(name) {
                var reg = new RegExp("(^|&)"+ name +"=([^&]*)(&|$)");
                var r = window.location.search.substr(1).match(reg);
                if(r!=null)return  unescape(r[2]); return null;
            },
            // 获取十六进制随机色
            getRandomColor: function() {
                return '#' + (function(h) {
                    return new Array(7 - h.length).join("0") + h;
                })((Math.random() * 0x1000000 << 0).toString(16));
            },
            // 单数变双数
            getTwo: function(val) {
                n = n.toString();
                return n[1] ? n : "0" + n;
            },
            // 是否是移动端
            isMobile: function() {
                return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
            },
            // 是否是微信浏览器
            isWechat: function() {
                var ua = window.navigator.userAgent.toLowerCase();
                return ua.match(/MicroMessenger/i) == 'micromessenger'
            },
            cookie: {
                get: function() {
                    var cookieValue = "";
                    var search = name + "=";
                    if (document.cookie.length > 0) {
                        offset = document.cookie.indexOf(search);
                        if (offset != -1) {
                            offset += search.length;
                            end = document.cookie.indexOf(";", offset);
                            if (end == -1)
                                end = document.cookie.length;
                            cookieValue = unescape(document.cookie.substring(offset, end))
                        }
                    }
                    return cookieValue;
                },
                set: function() {
                    var d = new Date();
                    d.setDate(d.getDate() + a);
                    document.cookie = b + "=" + escape(c) + ((a == null) ? "": "; expires=" + d.toGMTString()) + "; path=/; domain=changyou.com";
                },
                del: function(name) {
                    setCookie(name, "", -1);
                }
            }
        },
        // -----------------ajax请求----------------
        client: {
            ajax: function (type, url, data, fun) {
                $.ajax({
                    type: type,
                    url: url,
                    data: data,
                    headers: {
                        "Authorization": ja.options.token,
                        "APP": ja.options.app,
                        "ACTIVITY": ja.options.activity,
                        "VERSIONCODE": ja.options.version,
                        "CYSCID": ja.utils.getQueryString('CYSCID')
                    },
                    success: function(res) {
                        if(res.code === 10000) {
                            fun({
                                code: 10000,
                                data: res.data
                            })
                        }else {
                            fun({
                                code: res.code,
                                data: res.data,
                                msg: res.message
                            })
                        }
                    }
                })
            },
            api: {
                base: location.origin,       // 默认地址（现在是取同域名）
                get: function (url, data, fun) {
                    ja.client.ajax('GET', this.base + '/' + ja.options.platform + url, data, fun);
                },
                post: function (url, data, fun) {
                    ja.client.ajax('POST', this.base + '/' + ja.options.platform +  url, data, fun);
                }
            },
            apiWx: {
                base: location.origin + '/wechat',
                get: function (url, data, fun) {
                    ja.client.ajax('GET', this.base + url, data, fun);
                },
                post: function (url, data, fun) {
                    ja.client.ajax('POST', this.base +  url, data, fun);
                }
            }
        },
        // -----------------公用业务----------------
        items: {
            url: {
                userInfoPath: "/user/info",      // 用户详情
                phoneCode: '/core/zhpt/sendcode'    // 登陆发送手机号验证码
            },
            getUrl: function() {
                return  '/core/login/' + ja.options.app + '/' + ja.options.activity + '/' + ja.options.version
            },
            // 获取用户信息
            getUserInfo: function(fun) {
                ja.client.api.get(this.url.userInfoPath, {}, function(res) {
                    fun(res);
                    if(res.code === 10000) {
                        ja.options.userInfo = res.data;
                    }
                })
            },
            // 获取验证码
            getCode: function(phone, fun) {
                ja.client.api.post(this.url.phoneCode, {
                    phone: phone
                }, fun);
            },
            // 手机号与验证码验证
            phoneLogin: function(phone, code, fun) {
                ja.client.api.post(this.getUrl() + '/phonelogin', {
                    phone: phone,
                    code: code
                }, fun);
            },
            // 登出
            logout: function (fun) {
                ja.client.api.post(this.getUrl() + '/logout', {}, fun);
            },
            // 登陆（星际战甲专用登陆弹窗）
            loginXJZJ: function(back) {
                ja.utils.cookie.del("CASGTC");
                return location.origin + "/" + ja.options.platform + this.getUrl() + "?callback=" + encodeURI(back);
            }
        },
        // -----------------微信业务----------------
        wx: {
            // 微信初始化
            init: function() {
                $.ajax({
                    url: location.origin + "/wechat/web/" + ja.options.activityInfo.wxId + "/config",
                    success: function(data) {
                        if(data.config) {
                            $.getScript("https://res.wx.qq.com/open/js/jweixin-1.4.0.js", function () {
                                wx.config({
                                    debug: false ,                      // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                                    appId: data.config.appId,           // 必填，公众号的唯一标识
                                    timestamp: data.config.timestamp,   // 必填，生成签名的时间戳
                                    nonceStr: data.config.nonceStr,     // 必填，生成签名的随机串
                                    signature: data.config.signature,   // 必填，签名，见附录1
                                    jsApiList: [                        // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
                                        "onMenuShareTimeline",
                                        "onMenuShareAppMessage",
                                        "onMenuShareQQ",
                                        "onMenuShareWeibo",
                                        "hideMenuItems",
                                        "hideAllNonBaseMenuItem",
                                        "onMenuShareQZone"
                                    ]
                                });
                            })
                        }else {
                            alert('微信初始化失败，' + data.msg);
                        }
                    }
                });
            },
            // 分享
            share: function(options) {
                var _options = {
                    type: "link",
                    shareList: [
                        "onMenuShareTimeline",      // 分享到朋友圈
                        "onMenuShareAppMessage",    // 分享给朋友
                        "onMenuShareQQ",            // 分享到QQ
                        "onMenuShareWeibo",         // 分享到腾讯微博
                        "onMenuShareQZone"          // 分型到QQ空间
                    ]
                };
                _options = $.extend(_options, options);
                var list = _options.shareList;
                delete _options["shareList"];
                wx.ready(function() {
                    for(var i in list) {
                        var shareType = list[i];
                        if($.isFunction(wx[shareType])) {
                            wx[shareType](_options);
                        }
                    }
                })
            }
        }
    };
    window.ja = new JoinActivity();
}());
