<!DOCTYPE HTML>
<html>

<head>
	<meta charset="utf-8">
	<title>第三届新全球争霸赛-新天龙八部官方网站</title>
	<meta name="applicable-device" content="mobile">
	<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no">
	<meta content="yes" name="mobile-web-app-capable">
	<meta content="yes" name="apple-mobile-web-app-capable">
	<meta content="telephone=no" name="format-detection">
	<meta name="keywords" content="天龙,新天龙,天龙八部,新天龙八部,天龙八部3,tl,tlbb,tl3,xtl,网络游戏,网游,武侠,浪漫武侠,武侠游戏,金庸,MMO,天龙八部OL,萧峰,段誉,虚竹,王语嫣,天龙八部钟汉良版,天龙八部黄日华版,8周年,八周年,周年庆,全民代言,纵情四海" />
	<meta name="description" content="《新天龙八部》十三周年，全新副本观山海即将上线，画面颠覆性升级！唯美山河场景、国韵音乐大作、全新副本玩法登场！海量周年福利上线领取。" />
	<link type="image/x-icon" href="//tl.changyou.com/favicon.ico" rel="icon" />
	<link type="image/x-icon" href="//tl.changyou.com/favicon.ico" rel="shortcut icon" />
	<link type="text/css" rel="stylesheet" href="/all/css/reset.css" />
	<link type="text/css" rel="stylesheet" href="css/swiper.min.css" />
	<link type="text/css" rel="stylesheet" href="css/index.css" />
	<link type="text/css" rel="stylesheet" href="/xtl/202407/peak/pc/css/pop.css" />
	<script>
		(function () {
			if (!/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)) {
				window.location.href = "../pc/live.shtml"
			};
		})();
		var phoneWidth = parseInt(window.screen.width);
		var phoneScale = phoneWidth / 750;
		var ua = navigator.userAgent;
		if (/Android (\d+\.\d+)/.test(ua)) {
			var version = parseFloat(RegExp.$1);
			// andriod 2.3
			if (version > 2.3) {
				document.write('<meta name="viewport" content="width=750, minimum-scale = ' + phoneScale +
					', maximum-scale = ' +
					phoneScale + ', target-densitydpi=device-dpi">');
				// andriod 2.3
			} else {
				document.write('<meta name="viewport" content="width=750, target-densitydpi=device-dpi">');
			}
		} else {
			document.write('<meta name="viewport" content="width=750, user-scalable=no, target-densitydpi=device-dpi">');
		}
	</script>
	<script type="text/javascript">
		var deviceWidth = document.documentElement.clientWidth;
		if (deviceWidth > 750) {
			deviceWidth = 750
		};
		document.documentElement.style.fontSize = deviceWidth / 750 + 'px';
	</script>
</head>

<body class="pg5">
	<h1 class="none">第三届新全球争霸赛</h1>
	<div class="wrap">
		<div class="head">
			<a href="http://tl.changyou.com/main.shtml" title="《新天龙八部》官方网站" target="_blank" class="logo">新天龙八部官方网站</a>
			<a href="javascript:;" title="第三届新全球争霸赛" class="logo2">第三届新全球争霸赛</a>
			<p class="arr">向下滑动鼠标浏览更多</p>
		</div>
		<div class="cont">
			<h2 class="tit tit4">赛事直播</h2>
			<div class="livehd">
				<a href="javascript:;" class="cur"><span>主播主播名</span></a>
				<a href="javascript:;"><span>主播主播名</span></a>
				<a href="javascript:;"><span>主播主播名</span></a>
				<a href="javascript:;"><span>主播主播名</span></a>
			</div>
			<div class="livebox">
				<a href="https://v.douyin.com/ihdLU5eW/ <EMAIL> :3pm" id="live_btn">进入直播间</a>
			</div>
			<p class="livetip"><span>提示：若您观看直播时出现界面异常，直播间加载较慢的情况。</span> 建议您：1.尝试刷新当前页面；2.尝试更换浏览器打开当前页面；3.更新至最新版本Flash；4.检查您的网络；</p>
		</div>
	<!--#include virtual="/xtl/202505/peak/pc/inc/flo.shtml"-->

	</div>
	<script src="/all/js/jquery-1.8.3.min.js"></script>
	<script src="/all/js/swiper-4.5.0.min.js"></script>
	<script src="/all/js/popout.js"></script>
	<script src="js/index.js"></script>
	<!--#include virtual="/all/nav/cy_public_js_dark.html"-->
	<!--#include virtual="/all/dma/dma_static.html"-->
</body>

</html>
