$(function () {
    // 龙门服还是正式服
    window.urlName = location.href.indexOf('longmen') > -1 ? 'longmen' : 'public';
    var example = {
        // 公共路径前缀
        base_url: '/tldc/dc/',
        // 初始化用户参数
        configParams: {
            app: 'tldc', // 必填，应用标识
            activity: 'dc', // 必填，活动标识
            version: window.urlName == 'longmen' ? '2021080901' : '20250427', // 必填，版本标识
            platform: 'changyou', // 必填，平台标识
        },
        // 活动首页 分享
        indexShare: {
            link: location.origin + '/tldc/vote/20210823/' + window.urlName + '/m/' + (window.urlName == 'longmen' ? 'index' : 'index') + '.shtml',
            title: '你的好友邀你一起当策划',
            desc: '《新天龙八部》卡级服定制活动开启，点击参与，即可定制天龙首个卡！',
            imgUrl: 'http://i0.cy.com/tlpt/vote/20210823/share.jpg'
        },
        // 防抖标识
        clickFlag: false,
        // 当前微信是否有绑定账号
        wxUser: null,
        // 等级定制列表
        levelList: null,
        // 玩法定制列表
        playList: null,
        // 当前账号选择的等级类型
        myLevel: null,
        // 当前账号选择的玩法类型
        myPlay: [],
        // 当前账号是否提交新服名
        isSubmitName: 0,
        // 当前账号选择的玩法类型是否小于5
        isMyPlay: 1,
        // 定制等级对应
        levelObj: {
            'level_A': 69,
            'level_B': 79,
            'level_C': 89,
        },
        // 是否可以提交新服名
        isName: 0,
        // 等级是否是30级
        isLevel: true,
        // 动态定制玩法数量
        playLimitNum: 0,
        // 已发奖
        isAwards: 0,
        // 是否预约 1-已预约 0-未预约
        reserveStatus: 0,
        // 是否弹框激活码 1-弹窗 0-不弹窗
        isPopup: 0,
        // 激活码
        activationCode: '',
        // 邀请码
        inviteCode: '',
        // 抽奖次数
        drawNum: 0,
        // 邀请人数
        inviteCount: 0,
        // 邀请人邀请码
        userInviteCode: '',
        // 抽奖码
        prizeType: '',
        // 小队编号
        teamNumber: '',
        // 是否入队
        hasJoined: 0,
        // 邀请队伍编号
        inviteTeamNum: '',
        // 小队角色
        userRole: 0,
        // 当前登录信息
        cnMaster: '',
        // 申请列表红点状态
        isRead: 1,
        // 是否满员
        isTeamFull: 0,
        // 页面初始化
        init: function () {
            /*
             * 初始化
             * config配置项详见 表 3.1
             */
            ja.config({
                app: example.configParams.app,
                activity: example.configParams.activity,
                version: example.configParams.version,
                platform: example.configParams.platform,
                isWxLogin: true,
                isWxStatus: true,
                // isWxInit: true,
                // isVconsole: true
            });
            /*
              * 初始化完成后执行函数
              * 返回全局对象 glob,详见 表 3.2
              */
            ja.ready(function (glob) {
                $('.name-input').val('');
                // example.getList();
                example.getPeopleNum();
                var searchObj = example.getUrlParameters();
                var url = window.location.href.split('#slide')[0];
                if (searchObj.activityCode){
                    var activityArr = searchObj.activityCode.split('|');
                    if (activityArr[0] == '1'){
                        example.userInviteCode = activityArr[1];
                    }else if (activityArr[0] == '2'){
                        $('#invitationUser').html(activityArr[2]);
                        $('#invitationTeam').html(activityArr[1]);
                        example.inviteTeamNum = activityArr[1];
                        popup('#pop-invitation');
                    }
                }
                // 用户是否登录
                glob.isLogin ? example.loginAfter() : example.loginBefore();
                // mock ? example.loginAfter() : example.loginAfter();
                // 龙门添加样式
                if (window.urlName == 'longmen') {
                    // 龙门添加样式
                    ja.utils.longmen({
                        top: '100px',
                        left: '5px',
                    });
                    if (ja.code == 10000 && glob.isLogin) {
                        example.tips('此活动页面为龙门测试地址,<br/>活动奖励发放到龙门服,请各位玩家注意');
                    };
                };
                if (ja.isWechat) {
                    // 微信端
                    example.wxShareConfig();
                };
                // 登录
                $('#login').on('click', function () {
                    example.commHandle();
                });
                // 注销
                $('#logout').on('click', function () {
                    $('.login_err').show();
                    $('.login_succ').hide();
                    ja.logout();
                });

                // 点击预约
                $('#reverse').on('click', function () {
                    if (!example.commHandle()) return;
                    if ($(this).hasClass('gray')) return;
                    popup('#pop-order');
                });
                // 点击查看申请
                $('#viewApplication').on('click', function () {
                   example.applyingList();
                });
                // 点击查看个人中心
                $('#user_info').on('click', function () {
                    example.getDrawRecord();
                });
                $('#toUserInfo').on('click', function () {
                    example.getDrawRecord();
                });
                $('.btn-userInfo').on('click', function () {
                    example.getDrawRecord();
                });
                $('#submitReverse').on('click', function () {
                   example.onReverse();
                });
                $('#addCopy').on('click', function () {
                    if(!example.commHandle()) return;
                    var url = window.location.href.split('#slide')[0];
                    $('#pop-copy .copy').html(`${url.split('?activityCode=')[0]}?activityCode=1|${example.inviteCode}`);
                    $('#pop-copy .copyBtn').attr('data-key', `${url.split('?activityCode=')[0]}?activityCode=1|${example.inviteCode}`);
                    popup('#pop-copy');
                });
                // 获取验证码按钮点击事件
                $('.btn-code').click(function() {
                    var phone = $(".form #phone").val();
                    if (phone == "") {
                        // alert("请输入您的手机号");
                        $('#pop-order .address-tips').html('请输入您的手机号');
                        return;
                    } else if (!/^1[3-9]\d{9}$/.test(phone)) {
                        // alert("请输入正确的手机号");
                        $('#pop-order .address-tips').html('请输入正确的手机号');
                        return;
                    };
                    example.getPhoneCode(phone);
                });
                // 提交地址信息
                $('#pop-address .btn-confirm').on('click', function () {
                    var phone = $(".form #addrPhone").val();
                    var name = $(".form #addrUserName").val();
                    var address = $(".form #addressInfo").val();
                    if (name == ''){
                        // alert('请输入姓名');
                        $('#pop-address .address-tips').html('请输入姓名');
                        return;
                    }
                    if (phone == "") {
                        // alert('请输入手机号');
                        $('#pop-address .address-tips').html('请输入正确的手机号');
                        return;
                    }
                    if (address == '') {
                        // alert('请输入地址信息');
                        $('#pop-address .address-tips').html('请输入地址信息');
                        return;
                    }
                    example.saveAddr({phone, name, address});
                });
                $('.btn-createSteam').on('click', function () {
                    if (!example.commHandle()) return;
                    popup('#pop-createTeam');
                });
                $('.btn-join').on('click', function () {
                    if (!example.commHandle()) return;
                    popup('#pop-teamNumber');
                })
                $('#pop-createTeam .btn-confirmTeam').on('click', function () {
                    example.createTeam();
                });
                $('#pop-teamNumber .btn-applyJoin').on('click', function () {
                    var num = $(".input #teamNum").val();
                    if (!/^[A-Z0-9]{6}$/.test(num)) {
                        // alert("请输入正确的队伍编号");
                        example.tips('请输入正确的队伍编号');
                        return;
                    };
                    example.applicationTeam(num);
                });
                // 组队头像点击
                $('#firstUser').on('click', function () {
                    if (!example.commHandle()) return;
                    if (example.hasJoined == 0){ // 未入队处理
                        popup('#pop-createTeam');
                        return;
                    }
                });
                $('#secondUser').on('click', function () {
                    var imgDom = $('#secondUser img');
                    if (!example.commHandle()) return;
                    if (example.hasJoined == 0) {
                        popup('#pop-teamNumber');
                        return;
                    }
                    if (imgDom.attr('src') == '') { // 邀请好友弹框
                        $('#pop-copyUser .copy').html(`${url.split('?')[0]}?activityCode=2|${example.teamNumber}|${ja.userInfo.openid}`);
                        $('#pop-copyUser .copyBtn').attr('data-key', `${url.split('?')[0]}?activityCode=2|${example.teamNumber}|${ja.userInfo.openid}`);
                        popup('#pop-copyUser');
                    }
                });
                $('#thirdUser').on('click', function () {
                    var imgDom = $('#thirdUser img');
                    if (!example.commHandle()) return;
                    if (example.hasJoined == 0) {
                        popup('#pop-teamNumber');
                        return;
                    }
                    if (imgDom.attr('src') == '') { // 邀请好友弹框
                        $('#pop-copyUser .copy').html(`${url.split('?')[0]}?activityCode=2|${example.teamNumber}|${ja.userInfo.openid}`);
                        $('#pop-copyUser .copyBtn').attr('data-key', `${url.split('?')[0]}?activityCode=2|${example.teamNumber}|${ja.userInfo.openid}`);
                        popup('#pop-copyUser');
                    }
                });
                // 确认加入队伍
                $('.btn-confirmJoin').on('click', function () {
                    if (!example.commHandle()) return;
                    example.applicationTeam(example.inviteTeamNum);
                });
                $('.btn-cancel').on('click', function () {
                    window.hideMask('#pop-invitation');
                });
                // 离队
                $('.btn-inviteChange:not(.out)').on('click', function () {
                    var id = $(this).attr('data-user');
                    var name = $(this).attr('data-name');
                    if ($(this).hasClass('out')){ // 踢出队伍
                        $('#pop-out .p-tips span').html(name);
                        $('#pop-out .p-tips span').attr('data-user', id);
                        popup('#pop-out');
                    }else { // 离队
                        if (example.userRole == 1){ // 队长离队
                            popup('#pop-leaderOut');
                        }else { // 队员离队
                            popup('#pop-memberOut');
                        }
                    }
                });
                // 确认离队
                $('.btn-leaderOut').on('click', function () {
                    example.leaveTeam();
                });
                // 去激活-跳转激活页面
                $('.btn-goActivate').on('click', function () {
                    $('.nav_item').removeClass('swiper-pagination-bullet-active');
                    window.location.href = `/tldc/collection/20250427/public/${isH5?'m':'pc'}/activate.shtml#slide5`;
                    $(this).addClass('active');
                    example.closePopup();
                });
                // 确认踢出队员
                $('.btn-confirmOut').on('click', function () {
                    var id = $('#pop-out .p-tips span').attr('data-user');
                   example.kickMember(id);
                });
                // 监听激活页面input输入
                $('.page5 .key').on('input', function (){
                    var regex = /^[A-Z0-9]{16}$/;
                    this.value = this.value.replace(/[^A-Z0-9]/g, '');
                    var inputText = $(this).val();
                    if ($('.page5 a').hasClass('activated')) return;
                    if (regex.test(inputText)) {
                        $('.page5 a').removeClass('gray');
                    }else {
                        $('.page5 a').addClass('gray');
                    }
                });
                // 监听收货地址input输入
                $('#pop-address .form').on('input', ':input', function() {
                    const $input = $(this);
                    let id = $input.attr('id');
                    if (id == 'addrPhone'){
                        this.value = this.value.replace(/[^0-9]/g, '');
                        if (this.value.length > 11){
                            $('#pop-address .address-tips').html('只能输入11位手机号');
                            return;
                        }else if (!/^1[3-9]\d{9}$/.test(this.value)){
                            $('#pop-address .address-tips').html('请输入正确的手机号');
                            return;
                        }
                    }
                    $('#pop-address .address-tips').html('');
                });
                // 监听预约手机号
                $('#pop-order .form').on('input', ':input', function() {
                    const $input = $(this);
                    let id = $input.attr('id');
                    if (id == 'phone'){
                        this.value = this.value.replace(/[^0-9]/g, '');
                        if (this.value.length > 11){
                            $('#pop-order .address-tips').html('只能输入11位手机号');
                            return;
                        }else if (!/^1[3-9]\d{9}$/.test(this.value)){
                            $('#pop-order .address-tips').html('请输入正确的手机号');
                            return;
                        }
                    }
                    if (id == 'code'){
                        if (!/^[A-Za-z0-9]{6}$/.test(this.value)){
                            $('#pop-order .address-tips').html('请输入正确的验证码');
                            return;
                        }
                    }
                    $('#pop-order .address-tips').html('');
                });
                // 激活按钮
                $('.page5 .btn-activate').on('click', function (){
                   if ($(this).hasClass('gray') || $(this).hasClass('activated')) { // 已激活及置灰
                       return;
                   }
                   if (!example.commHandle()) return;
                   let remaining = 10;
                    var timer = setInterval(function() {
                        remaining--;
                        $('.btn-activate span').text(remaining);
                        if (remaining <= 0) {
                            clearInterval(timer);
                            $('.btn-activate').addClass('btn-timeOver');
                            $('.btn-activate span').css('display', 'none');
                        }
                    }, 1000);
                   $('#pop-confirmActivate .p-tips span:last').html(ja.userInfo.openid);
                   popup('#pop-confirmActivate');
                });
                // 我要切换账号
                $('.btn-changeUser').on('click', function (){
                    $('.login_err').show();
                    $('.login_succ').hide();
                    ja.logout();
                });
                // 仍要激活
                $('#timeOver').on('click', function (){
                    if ($(this).hasClass('btn-timeOver')){
                        example.activation();
                    };
                });
                $('.content').on('click', '.btn-rule', function (){
                   popup('#pop-rule');
                });
                // 重新输入
                $('.btn-retype').on('click', function (){
                    window.hideMask('#pop-codeError')
                })
            });
        },
        /**
         * 登录前执行
         */
        loginBefore: function () {
            if (ja.isWechat) {
                example.wxUser = ja.wxBindName;
                if (example.wxUser) {
                    $('.w-txt').show() && $('#wxLogin #isBind').text(example.wxUser).show();
                    $('#wxLogin #unBind').hide();
                };
            };
        },
        /**
         * 登录后执行
         */
        loginAfter: function () {
            $('.login_err').hide();
            $('#userName').text(ja.userInfo.openid);
            // $('#userName').text('无谋')
            $('.login_succ').show();
            example.getUserInfo(); //初始化用户信息
            // example.initTeamUser(); // 获取初始小队信息
        },
        /**
         * 通用错误码处理
         * param {*} data 返回的对象
         */
        commonErrPop: function (data) {
            var flag = false;
            switch (parseInt(data.code)) {
                case 1202:
                case 1209:
                case 1207:
                case 1007: //1202请求参数缺失或者为空  1209操作频繁
                    example.tips('系统繁忙，请稍后重试！');
                    break;
                case 1019:
                case 1012:
                    ja.isLogin = false; // 重置下ja的登录状态标识以触发登录操作
                    $('#login').click();
                    break;
                case 3301:
                    // 用户创建失败
                    example.tips('用户创建失败');
                    break;
                case 3304:
                    // 用户信息更新失败，请刷新后重试
                    example.tips('用户信息更新失败，请刷新后重试');
                    break;
                case 3011:
                    // 未实名
                    popup('#pop-registered');
                    break;
                case 1210:
                    // 数据异常，请重试
                    example.tips('数据异常，请重试');
                    break;
                case 3103:
                    // 账号封停
                    popup('#pop-stop');
                    break;
                case 2131:
                    example.isLevel = false;
                    // 等级不符合
                    popup('#pop-unable');
                    break;
                case 1106:
                    // 活动未开始
                    ja.code = 1106;
                    example.commHandle();
                    break;
                case 1103:
                    ja.code = 1103;
                    // 活动已结束
                    example.commHandle();
                    break;
                default:
                    flag = true;
                    break;
            }
            return flag;
        },
        /**
         * 通用的前置处理方法
         */
        commHandle: function () {
            var flag = false;
            if (ja.code == 1102) {
                example.tips('活动无效');
            } else if (ja.code == 1106) {
                if (window.urlName == 'longmen') {
                    example.tips('活动未开始');
                } else {
                    example.tips('活动未开始');
                };
            } else if (ja.code == 1103) {
                example.tips('活动已结束');
            } else if (ja.code == 1008) {
                example.tips('配置错误，请联系管理员');
            } else if (!ja.isLogin) {
                ja.login();
            } else {
                flag = true;
            }
            return flag;
        },
        /**
        * 通用提示
         */
        tips: function (msg) {
            $('#pop-tips p.p-tips').html(msg);
            popup('#pop-tips');
        },
        /*
        * 时间轴转化
        * */
        getFormatTime: function (timestamp) {
            var date = new Date(timestamp);
            return date.getFullYear() + '-' +
                (date.getMonth() + 1).toString().padStart(2, '0') + '-' +
                date.getDate().toString().padStart(2, '0') + ' ' +
                date.getHours().toString().padStart(2, '0') + ':' +
                date.getMinutes().toString().padStart(2, '0');
        },
        /**
         * 倒计时
         * */
        startCountdown: function (seconds) {
            var remaining = seconds;
            $('.btn-code').text(remaining + '秒后重新获取').prop('disabled', true);

            var timer = setInterval(function() {
                remaining--;
                $('.btn-code').text(remaining + '秒后重新获取');

                if (remaining <= 0) {
                    clearInterval(timer);
                    $('.btn-code').text('获取验证码').prop('disabled', false);
                }
            }, 1000);
        },
        /**
         * 邀请弹框事件
        * */
        getUrlParameters:function() {
            var urlParams = {};
            var match,
                regex = /([^&=]+)=([^&]*)/g;
            var searchString = window.location.search.substring(1);
            while ((match = regex.exec(searchString))) {
                var key = decodeURIComponent(match[1]);
                var value = decodeURIComponent(match[2]);
                urlParams[key] = value;
            }
            return urlParams;
        },
        /**
        * 通用节流
        */
        throttle: function (msc) {
            var oldtime;
            function preventclick(msc) {
                if (!oldtime) {
                    oldtime = new Date().getTime();
                    return true;
                } else {
                    var newtime = new Date().getTime();
                    if (newtime - oldtime > msc) {
                        oldtime = new Date().getTime();
                        return true;
                    } else {
                        return false;
                    }
                }
            }
            return preventclick(msc);
        },
        /**
         * 登录后用户信息初始化
         */
        getUserInfo: function () {
            $.ajax({
                type: "POST",
                url: this.base_url + 'init',
                async: false,
                success: function (res) {
                    if (res.code == 10000) {
                        example.reserveStatus = res.data.reserveStatus;
                        example.inviteCount = res.data.inviteCount > 5 ? 5 : res.data.inviteCount;
                        if (res.data.reserveStatus === 1) {
                            $('#reverse').addClass('gray');
                            example.getDrawNum(); //获取抽奖次数
                            $('#reserveSuc').html(2);
                            $('#inviteCount').html(example.inviteCount);
                            // example.activationRecord(); // 获取激活记录
                        };
                        example.activationCode = res.data.activationCode;
                        example.inviteCode = res.data.inviteCode;
                        example.isPopup =  res.data.isPopup;
                        $('.playLimitNum').text(example.playLimitNum);
                        if (res.data.isPopup == 1){
                            $('#pop-getCode .copy').html(res.data.activationCode);
                            $('#pop-getCode .copyBtn').attr('data-key', res.data.activationCode);
                            popup('#pop-getCode');
                        }
                        // example.listState();
                        // example.canAwards();
                        example.initTeamUser(); // 获取初始小队信息
                    } else if (example.commonErrPop(res)) {
                        example.tips(res.message);
                    } else {
                        // 已处理所有情况
                    }
                }
            });
        },
        /**
         * 获取定制列表
         */
        getList: function () {
            $.ajax({
                type: "GET",
                url: this.base_url + 'voteList',
                async: false,
                success: function (res) {
                    if (res.code == 10000) {
                        example.levelList = res.data.levelList;
                        example.playList = res.data.playList;
                        example.listStatic();
                    } else if (example.commonErrPop(res)) {
                        example.tips(res.message);
                    } else {
                        // 已处理所有情况
                    }
                }
            });
        },
        /**
         * 渲染定制列表
         */
        listStatic: function () {
            var $levelList = $('#levelList li'),
                $playList = $('#playList li'),
                levelList = example.levelList,
                playList = example.playList;
            $.each($levelList, function (i, v) {
                $(v).find('.level-btn').attr('type-code', levelList[i].typeCode);
                $(v).find('.level-btn').attr('index', i);
                $(v).find('.level-num').text(levelList[i].num);
            });
            $.each($playList, function (i, v) {
                $(v).find('.play-btn').attr('type-code', playList[i].typeCode);
                $(v).find('.play-btn').attr('index', i);
                $(v).find('.play-num').text(playList[i].num);
            });
        },
        /**
         * 等级选中状态
         */
        levelState: function () {
            var $levelList = $('#levelList .level-btn'),
                myLevel = example.myLevel,
                $levelIcon = $('.level-icon');
            if (myLevel) {
                example.canAwards();
                $levelList.each(function () {
                    if ($(this).attr('type-code') == myLevel) {
                        $(this).addClass('gray');
                    } else {
                        $(this).addClass('hui');
                    }
                });
                $levelIcon.addClass('light');
            } else {
                $levelIcon.removeClass('light');
            };
        },
        /**
         * 玩法选中状态
         */
        playState: function () {
            var $playList = $('#playList .play-btn'),
                myPlay = example.myPlay,
                $playIcon = $('.play-icon'),
                playLimitNum = example.playLimitNum;
            if (myPlay.length > 0) {
                $playList.each(function () {
                    var typeCode = $(this).attr('type-code');
                    if (myPlay.length == playLimitNum) {
                        example.canAwards();
                        $playIcon.addClass('light');
                        example.isMyPlay = 0;
                        if (myPlay.indexOf(typeCode) > -1) {
                            $(this).addClass('gray');
                        } else {
                            $(this).addClass('hui');
                        };
                    };
                    if (myPlay.length < playLimitNum) {
                        $playIcon.removeClass('light');
                        example.isMyPlay = 1;
                        if (myPlay.indexOf(typeCode) > -1) {
                            $(this).addClass('gray');
                        };
                    };

                });
            } else {
                $playIcon.removeClass('light');
            };
        },
        /**
         * 名称选中状态
         */
        nameState: function () {
            var isSubmitName = example.isSubmitName,
                $nameBtn = $('.name-btn');
            if (isSubmitName) $nameBtn.addClass('gray');
        },
        /**
         * 等级定制投票
         */
        levelVote: function (param) {
            var typeCode = $(param).attr('type-code');
            $.ajax({
                type: "POST",
                url: this.base_url + 'levelVote',
                data: {
                    'typeCode': typeCode
                },
                success: function (res) {
                    if (res.code == 10000) {
                        var $levelNum = $(param).parent('li').find('.level-num');
                        $levelNum.text(Number($levelNum.text()) + 1);
                        example.myLevel = typeCode;
                        example.levelState();
                        var title = example.levelObj[typeCode];
                        $('#djdz').text(title);
                        popup('#pop-select');
                    } else if (example.commonErrPop(res)) {
                        example.tips(res.message);
                    } else {
                        // 已处理所有情况
                    }
                }
            });
        },
        /**
         * 玩法定制投票
         */
        playVote: function (param) {
            var typeCode = $(param).attr('type-code');
            $.ajax({
                type: "POST",
                url: this.base_url + 'playVote',
                data: {
                    'typeCode': typeCode
                },
                success: function (res) {
                    if (res.code == 10000) {
                        var $playNum = $(param).parent('li').find('.play-num');
                        $playNum.text(Number($playNum.text()) + 1);
                        example.myPlay.push(typeCode);
                        example.playState();
                        var title = $(param).parent('li').find('strong').text();
                        $('#wfdz').text(title);
                        popup('#pop-tags');
                    } else if (example.commonErrPop(res)) {
                        switch (parseInt(res.code)) {
                            case 2102:
                                example.tips('已点赞该玩法');
                                break;
                            default:
                                example.tips(res.message);
                                break;
                        }
                    } else {
                        // 已处理所有情况
                    }
                }
            });
        },
        /**
         * 提交新服名
         */
        submitName: function (param) {
            $.ajax({
                type: "POST",
                url: this.base_url + 'submitName',
                data: {
                    'serverName': param
                },
                success: function (res) {
                    if (res.code == 10000) {
                        $('.name-input').val('');
                        $('.name-input').blur();
                        $(".name-tips").css({
                            opacity: 0,
                        }).text('');
                        example.isSubmitName = 1;
                        example.nameState();
                        popup('#pop-name');
                    } else if (example.commonErrPop(res)) {
                        switch (parseInt(res.code)) {
                            default:
                                example.tips(res.message);
                                break;
                        }
                    } else {
                        // 已处理所有情况
                    }
                }
            });
        },
        /**
         * 是否发奖
         */
        canAwards: function () {
            var myLevel = example.myLevel,
                myPlay = example.myPlay,
                playLimitNum = example.playLimitNum;
            if (myLevel && myPlay && myPlay.length == playLimitNum) {
                example.isAwards = 1;
                $('span.brand').show();
            } else {
                example.isAwards = 0;
                $('span.brand').hide();
            };
        },
        /**
         * 微信分享设置
         * param {*} isShare 是否是引导分享
         */
        wxShareConfig: function () {
            ja.share({
                link: example.indexShare.link,//分享链接
                title: example.indexShare.title,//分享标题
                desc: example.indexShare.desc,  //分享描述
                imgUrl: example.indexShare.imgUrl,//分享 icon
                hideMenuList: [
                    'menuItem:share:qq',
                    'menuItem:favorite',
                    'menuItem:share:QZone',
                    'menuItem:openWithSafari',
                    'menuItem:share:weiboApp',
                    'menuItem:openWithQQBrowser',
                ],
                success: function () {
                    example.shareLog(2);
                }
            })
        },
        // 记录分享日志
        shareLog: function (type) {
            $.ajax({
                url: '/shareLog',
                type: 'POST',
                data: {
                    event: type
                },
                success: function (data) {
                    console.log(data.code);
                }
            })
        },
        // 获取预约人数
        getPeopleNum: function () {
          $.ajax({
              type: "GET",
              url: this.base_url + 'reverseCnt',
              success: function (res) {
                  if (res.code == 10000) {
                      $('#peopleNum').text(res.data.reverseCnt);
                  } else if (example.commonErrPop(res)) {
                      switch (parseInt(res.code)) {
                          default:
                              example.tips(res.message);
                              break;
                      }
                  } else {
                      // 已处理所有情况
                  }
              }
          });
        },
        /**
        * 预约
        * */
        onReverse: function () {
            var phone = $(".form #phone").val();
            var code = $(".form #code").val();
            var data = {
                phone, code
            }
            if (phone == "") {
                // alert("请输入您的手机号");
                $('#pop-order .address-tips').html('请输入您的手机号');
                return;
            }
            if (code == "") {
                // alert("请输入您收到的短信随机码");
                $('#pop-order .address-tips').html('请输入您收到的短信验证码');
                return;
            }
            if (example.userInviteCode) {
                data.inviteCode = example.userInviteCode;
            }
            $.ajax({
                type: 'POST',
                url: this.base_url + 'reverse',
                data: data,
                success: function (res) {
                    if (res.code == 10000){
                        $("#reverse").addClass('gray');
                        example.getUserInfo(); //初始化用户信息
                        popup('#pop-orderSuccess');
                    }else if (res.code == 3002) {
                        example.tips(res.message);
                    }else if (example.commonErrPop(res)) {
                        example.tips(res.message);
                        return
                    }else {

                    }
                }
            });
        },
        /**
         * 获取抽奖次数
         * */
        getDrawNum: function (){
            $.ajax({
                type: 'GET',
                url: this.base_url + 'drawNum',
                success: function (res) {
                    if (res.code == 10000) {
                        $('#drawNum').text(res.data.count);
                        example.drawNum = res.data.count;
                    } else if (example.commonErrPop(res)) {
                        switch (parseInt(res.code)) {
                            default:
                                example.tips(res.message);
                                break;
                        }
                    } else {
                        // 已处理所有情况
                    }
                }
            });
        },
        /**
         * 获取获奖记录
         * */
        getDrawRecord: function (){
            var tab1 = $('.user-content #tab1');
            tab1.empty();
            var html = `<table>
                                <tr>
                                    <th>奖品内容</th>
                                    <th>奖品数量</th>
                                    <th>中奖时间</th>
                                </tr>`;
            if (example.reserveStatus){
                $.ajax({
                    type: 'GET',
                    url: this.base_url + 'drawRecord',
                    success: function (res) {
                        if (res.code == 10000) {
                            var { hasAddress, isWinInKind, drawRecord, activationCodeGetRecord, phone, receiver, address } = res.data;
                            var arr = drawRecord;
                            if (activationCodeGetRecord && Object.keys(activationCodeGetRecord).length > 0){
                                arr.push({winTime: activationCodeGetRecord.createTime, prizeContent: activationCodeGetRecord.activationCode, prizeType: 'C'})
                            }
                            html += arr.map(item => {
                                var { winTime, prizeContent, prizeType } = item;
                                var prizeName = prizeContent == '门派立牌' ? '随机门派立牌' : prizeContent;
                                return `<tr>
                                        <td>${prizeType == 'R' ? (prizeName) : `激活码\n ${prizeContent}`}</td>
                                        <td>1</td>
                                        <td>${example.getFormatTime(winTime)}</td>
                                    </tr>`;
                            }).join('');
                            html += '</table>';
                            if (drawRecord.length == 0){
                                html += '暂无中奖记录';
                            }
                            if (hasAddress == 1){
                                html += `
                                    <span class="addrSuc">您已填写过领奖地址</span>
                                    <a href="javascript:popup('#pop-address');" class="btn-address">重新填写领奖地址</a>
                                `;
                                $(".form #addrPhone").val(phone);
                                $(".form #addrUserName").val(receiver);
                                $(".form #addressInfo").val(address);
                            }else if (drawRecord.length > 1){
                                html += `<a href="javascript:popup('#pop-address');" class="btn-address">填写中奖收获地址</a>`;
                            }
                            tab1.html(html);
                            popup('#pop-userInfo');
                        } else if (example.commonErrPop(res)) {
                            switch (parseInt(res.code)) {
                                default:
                                    example.tips(res.message);
                                    break;
                            }
                        } else {
                            // 已处理所有情况
                        }
                    }
                });
            }else {
                html += '</table>';
                html += '暂无中奖记录';
                tab1.html(html);
                popup('#pop-userInfo');
            }

        },
        /**
        *  小队初始化请求
        * */
        initTeamUser:  function (){
            $.ajax({
                type: 'POST',
                url: this.base_url + 'initTeamUser',
                success: function (res) {
                    if (res.code === 10000) {
                        example.teamNumber = res.data.teamId;
                        example.hasJoined = res.data.hasJoined;
                        example.userRole = res.data.userRole;
                        example.cnMaster = res.data.cnMaster;
                        example.isTeamFull = res.data.isTeamFull;
                        var $avatars = $('.invite-box .item');
                        $('.user-status span').html(res.data.userIdentity == '2' ? '老友' : '新朋');
                        [1,2,3].forEach(function(user, index) {
                            if (index < $avatars.length) { // 确保不超过3个
                                $avatars.eq(index).find('img').attr('src', '');
                                $avatars.eq(index).find('.btn-inviteChange').css({'display': 'none'});
                            }
                        });
                        if (res.data.hasJoined == 1) {
                            $('.btn-createSteam').css({'display': 'none'});
                            $('.btn-join').css({'display': 'none'});
                            $('.number').css({'display': 'block'});
                            $('#teamNum').html(res.data.teamId);
                            if (res.data.userRole == 1) {
                                $('.btn-check').css({'display': 'block'});
                                if (res.data.isTeamFull !== 1) example.getIsRead();
                            };
                            example.getTeamMember();
                            example.getTeamPrizeStatus();
                        }else {
                            $('#teamNum').html('');
                            $('.number').css({'display': 'none'});
                            $('.btn-createSteam').css({'display': 'block'});
                            $('.btn-join').css({'display': 'block'});
                            $('.btn-check').css({'display': 'none'});
                        }
                    }else if (example.commonErrPop(res)) {
                        switch (parseInt(res.code)) {
                            default:
                                example.tips(res.message);
                                break;
                        }
                    } else {
                        // 已处理所有情况
                    }
                }
            });
        },
        /**
         * 获取组队小队
        * */
        getTeamMember: function (){
            $.ajax({
                url: this.base_url + 'teamMember',
                type: 'GET',
                success: function (res) {
                    var $avatars = $('.invite-box .item');
                    if (res.code == 10000) {
                        // 获取小队信息
                        if(res.data.length !== 0) {
                            res.data.forEach(function(user, index) {
                            if (index < $avatars.length) { // 确保不超过3个
                                $avatars.eq(index).find('img').attr('src', `/act/tldc/collection/20250427/pc/img/${user.avatar}.jpg` || '');
                                if (example.cnMaster == user.cnmaster || example.userRole == 1){
                                    $avatars.eq(index).find('.btn-inviteChange').css({'display': 'block'});
                                }
                                $avatars.eq(index).find('.btn-inviteChange').attr('data-user', user.id);
                                $avatars.eq(index).find('.btn-inviteChange').attr('data-name', user.cnmaster);
                                if (example.userRole == 1 && user.isLeader == 0) {
                                    $avatars.eq(index).find('.btn-inviteChange').addClass('out');
                                }
                                $avatars.eq(index).addClass('active');
                            }
                        })
                        };
                    } else if (example.commonErrPop(res)) {
                        switch (parseInt(res.code)) {
                            default:
                                example.tips(res.message);
                                break;
                        }
                    } else {
                        // 已处理所有情况
                    }
                }
            });
        },
        /**
         * 获取申请列表
         * */
        applyingList: function (){
            $.ajax({
                url: this.base_url + 'applyingList',
                type: 'GET',
                success: function (res) {
                    if (res.code == 10000) {
                        var ul = $('#pop-apply .scrollable');
                        ul.empty();
                        if(res.data.length === 0) {
                            var html = '暂无申请';
                            ul.append(html);
                            popup('#pop-apply');
                            return;
                        };
                        var html = res.data.map(item => {
                            var { applicantAvatar, applicantCnmaster, id } = item;
                            return `<li>
                                        ${(!example.isRead) ? '' : '<span class="applyNew"></span>'}
                                        <p>申请人</p>
                                        <img src='/act/tldc/collection/20250427/pc/img/${applicantAvatar}.jpg' alt="">
                                        <p class="name">${applicantCnmaster}</p>
                                        <a href="javascript:;"  class="btn btn-agree" onclick="window.example.checkApply({type: '1', id: ${id}})">同意</a>
                                        <a href="javascript:;" class="btn btn-ignore" onclick="window.example.checkApply({type: '2', id: ${id}})">忽略</a>
                                    </li>`;
                        }).join('\n');
                        ul.append(html);
                        popup('#pop-apply');
                        if (example.isRead == 1) example.updateIsRead(); // 更新红点状态
                    }
                }
            });
        },
        /**
         * 激活记录
        * */
        activationRecord: function (){
            var tab = $('.user-content #tab2');
            tab.empty();
            var html = `<table>
                                                <tr>
                                                    <th>激活账号</th>
                                                    <th>激活码</th>
                                                    <th>激活时间</th>
                                                </tr>`;
            if (example.reserveStatus){
                $.ajax({
                    url: this.base_url + 'activationRecord',
                    type: 'GET',
                    success: function (res) {
                        if (res.code == 10000) {
                            var { activationCode, activationTime, activatorCnmaster } = res.data;
                            if (Object.keys(res.data).length !== 0){
                                $('.page5 a').removeClass('gray');
                                $('.page5 a').addClass('activated');
                                html += `<tr>
                                    <td>${activatorCnmaster}</td>
                                    <td>${activationCode}</td>
                                    <td>${example.getFormatTime(activationTime)}</td>
                                </tr>`;
                                html += '</table>';

                            }else {
                                html += '</table>';
                                html += '您尚未有过激活记录';
                            }
                            tab.html(html);
                        } else if (example.commonErrPop(res)) {
                            switch (parseInt(res.code)) {
                                default:
                                    example.tips(res.message);
                                    break;
                            }
                        } else {
                            // 已处理所有情况
                        }
                    }
                });
            }else {
                html += '</table>';
                html += '您尚未有过激活记录';
                tab.html(html);
            }

        },
        /**
         * 获取验证码
         * */
        getPhoneCode: function (phone) {
            $.ajax({
                url: '/changyou/core/zhpt/sendcode',
                type: 'POST',
                data: { phone: phone },
                success: function(res) {
                    if (res.code == 10000) {
                        alert('验证码已发送');
                        // example.tips('验证码已发送');
                        example.startCountdown(60); // 开始60秒倒计时
                    } else if (example.commonErrPop(res)) {
                        // alert('发送失败: ' + res.message);
                        example.tips(res.message);
                    }else {

                    }
                }
            });
        },
        /**
         * 保存地址
         * */
        saveAddr: function (body) {
            $.ajax({
                url: this.base_url + 'saveAddr',
                type: 'POST',
                data: body,
                success: function (res) {
                    if (res.code == 10000) {
                        example.getDrawRecord();
                    } else if (example.commonErrPop(res)) {
                        switch (parseInt(res.code)) {
                            default:
                                example.tips(res.message);
                                break;
                        }
                    } else {
                        // 已处理所有情况
                    }
                }
            });
        },
        /**
         * 创建小队
         * */
        createTeam: function (){
            $.ajax({
                url: this.base_url + 'createTeam',
                type: 'POST',
                success: function (res) {
                    if (res.code == 10000){
                        hideMask('#pop-createTeam');
                        // example.init();
                    }else if (example.commonErrPop(res)) {
                        switch (parseInt(res.code)) {
                            default:
                                example.tips(res.message);
                                break;
                        }
                    } else {
                        // 已处理所有情况
                    }
                    example.initTeamUser();
                }
            });
        },
        /**
         * 申请入队
        * */
        applicationTeam: function (teamId){
          $.ajax({
              url: this.base_url + 'applicationTeam',
              type: 'POST',
              data: {teamId},
              success: function (res) {
                  if (res.code == 10000) {
                      var urlParts = window.location.href.split('?');
                      window.history.replaceState({}, document.title, urlParts[0]);
                      window.hideMask('#pop-teamNumber');
                      // example.init();
                  }
                  if (example.commonErrPop(res)) {
                      switch (parseInt(res.code)) {
                          default:
                              example.tips(res.code == 10000 ? '申请信息已发送~<br />请耐心等待队长回复哟~' : res.message);
                              break;
                      }
                  } else {
                      // 已处理所有情况
                  }
              }
          });
        },
        /**
         * 入队审核
         * */
        checkApply: function (params) {
            $.ajax({
                url: this.base_url + 'checkApply',
                type: 'POST',
                data:  params,
                success: function (res) {
                    if (res.code == 10000) {
                        example.applyingList();
                        // example.init();
                        example.getTeamMember();
                    }if (example.commonErrPop(res)) {
                        switch (parseInt(res.code)) {
                            default:
                                example.tips(res.message);
                                break;
                        }
                    } else {
                        // 已处理所有情况
                    }
                }
            });
        },
        /**
         * 离队
         * */
        leaveTeam: function (){
            $.ajax({
                url: this.base_url + 'leaveTeam',
                type: 'POST',
                success: function (res) {
                    if (res.code == 10000) {
                        window.hideMask('#pop-leaderOut');
                        window.hideMask('#pop-memberOut');
                        // example.init();
                        example.initTeamUser();
                    }else if (example.commonErrPop(res)) {
                        example.tips(res.message);
                        return;
                    }else {

                    }
                }
            });
        },
        /**
        * 提出队员
        * */
        kickMember: function (id){
            $.ajax({
                url: this.base_url + 'kickMember',
                type: 'POST',
                data: {id},
                success: function (res) {
                    if (res.code == 10000) {
                        window.hideMask('#pop-out');
                        // example.init();
                        example.initTeamUser();
                    }
                }
            });
        },
        /**
         * 激活
         * */
        activation:function (){
            var code = $('.page5 .key').val();
            $.ajax({
                url: this.base_url + 'activation',
                type: 'POST',
                data: {code},
                success: function (res) {
                    if (res.code == 10000) {
                        example.activationRecord();
                        $('#pop-activateSuccess .p-tips span').html(ja.userInfo.openid);
                        popup('#pop-activateSuccess');
                    }else if (res.code == 5125){
                        // 激活码不存在
                        popup('#pop-codeError');
                    }else if(example.commonErrPop(res)) {
                        example.tips(res.message);
                        return;
                    }else {

                    }
                }
            });
        },
        /**
         * 关闭弹窗
         * */
        closePopup: function () {
            $.ajax({
                url: this.base_url + 'closePopup',
                type: 'POST',
                success: function (res) {
                    if (res.code == 10000) {

                    }
                }
            });
        },
        /**
         * 获取申请红点
         * */
        getIsRead: function (){
            $.ajax({
                url: this.base_url + 'getIsRead',
                type: 'GET',
                success: function (res) {
                    if (res.code == 10000) {
                        example.isRead = res.data.isRead;
                    }else if (example.commonErrPop(res)) {
                        example.tips(res.message);
                        return;
                    }else {

                    }
                }
            });
        },
        /**
         * 更新红点状态
         * */
        updateIsRead:  function (){
            $.ajax({
                url: this.base_url + 'updateIsRead',
                type: 'POST',
                success: function (res) {
                    if (res.code == 10000) {
                        example.isRead = 0;
                    }
                }
            });
        },
        /**
         * 获取组队奖励
         * */
        getTeamPrizeStatus: function (){
            $.ajax({
                url: this.base_url + 'getTeamPrizeStatus',
                type: 'GET',
                success: function (res) {
                    if (res.code == 10000){
                        var {isTitle, isTeamSuccessPrize, isTeamPrize90, isTeamPrize70, isTeamPrize50} = res.data;
                        $('.prize-box .team-prize:nth-child(1)')[isTeamSuccessPrize ? 'addClass' : 'removeClass']('done');
                        $('.prize-box .team-prize:nth-child(2)')[isTeamPrize50 ? 'addClass' : 'removeClass']('done');
                        $('.prize-box .team-prize:nth-child(3)')[isTeamPrize70 ? 'addClass' : 'removeClass']('done');
                        $('.prize-box .team-prize:nth-child(4)')[isTeamPrize90 ? 'addClass' : 'removeClass']( 'done');
                    }
                }
            });
        }
    };
    window.example = example;
    example.init();
});


