//制衣坊图纸选择
var cloth_flage = false;//选择图纸参数
var cur_cloth;//当前制衣
$('#plist li.clothli').on('click', function () {
	if ($click()) return;
	if (!th.methodForPhase_2()) return;

	if ($(this).hasClass('dis_btn')) {
		popShow('pop_cloth');
		return;
	} else if ($(this).hasClass('btn-huih')) {
		th.tips('您已经制作完成这张图纸了')
		return;
	} else {
		if ($(this).hasClass('cur')) {
			$(this).removeClass('cur');
			cloth_flage = false;
		} else {
			$(this).addClass('cur').siblings('.clothli').removeClass('cur');
			cur_cloth = $(this).index();
			cloth_flage = true;
		}
	}
});
//制衣坊布料选择
$('#plist li.buliao').on('click', function () {
	if ($click()) return;
	if (!th.methodForPhase_2()) return;

	if ($(this).hasClass('btn-huih')) {
		popShow('pop_buliao');
	} else {
		$(this).addClass('cur')
	}
});
//转动纺车
var angle = 0;
var timer, timerForLeft;
touch.on('#target', 'touchstart', function (ev) {
	if ($click()) return;
	if (!th.methodForPhase_2()) return;
	if ($('#plist li.buliao').hasClass('btn-huih')) {
		popShow('pop_buliao');
		return;
	}

	ev.preventDefault();
	ev.startRotate();
	$('.tishi').fadeOut();

	if (cloth_flage) {
		timer = setTimeout(function () {
			angle = 0;
			$('#target')[0].style.webkitTransform = 'rotate(0deg)';
			document.getElementById('daro').style.webkitTransform = 'rotate(0deg)';
			document.getElementById('xiaoro').style.webkitTransform = 'rotate(0deg)';
			th.tips('你需要<span>顺时针拖动手柄转动转盘一圈</span>才可以完成制作哟~');
			$('.tishi').fadeIn()
		}, 500);
	} else if ($('.btn-huih').length >= 4) {
		th.tips('恭喜您已经制作完成了全部图纸~')
	} else {
		th.tips('点击圆圈，选中图纸和布料，<br>转动纺车即可开始制作时装！');
	};
});

var rotateFlag = false;
touch.on('#target', 'rotate', function (ev) {
	if ($click()) return;
	if (!th.methodForPhase_2()) return;
	if (ev.fingerStatus === 'end') {
		rotateFlag = false;
	}
	if (cloth_flage) {
		if (ev.rotation == 0) return;
		if (ev.direction === 'right') {
			clearTimeout(timer);
			var totalAngle = angle + ev.rotation;
			if (ev.fingerStatus === 'end') {
				angle = angle + ev.rotation;
				return;
			}
			// console.log('totalAngle：' + totalAngle, 330 < totalAngle && totalAngle < 360, 'rotateFlag：' + rotateFlag);

			if (330 < totalAngle && totalAngle < 390) {
				if (rotateFlag) return;
				rotateFlag = true;
				var taskid = cur_cloth + 1;

				th.toStep(taskid, 4, function () {
					$('#hd_pic').attr('src', '/act/xtl/cloth/20200317/m/img/jl' + cur_cloth + '.jpg');
					popShow('cloth_sus');
					// 置灰这张图纸的按钮
					$('#plist li.clothli:eq(' + cur_cloth + ')').removeClass('cur').addClass('btn-huih');
					th.ReduceProp(true); //更新下材料数量
					th.getAwards() // 更新礼包数量
					totalAngle = 0;
					angle = 0;
					$('.tishi').fadeIn();
					return;
				}, function () {
					totalAngle = 0;
					angle = 0;
					$('.tishi').fadeIn();
					return
				})
			} else if (totalAngle > 360) {
				totalAngle = 0;
				angle = 0;
			}
			this.style.webkitTransform = 'rotate(' + totalAngle + 'deg)';
			document.getElementById('daro').style.webkitTransform = 'rotate(' + totalAngle + 'deg)';
			document.getElementById('xiaoro').style.webkitTransform = 'rotate(' + totalAngle + 'deg)';
		} else {
			angle = 0;
			$('#target')[0].style.webkitTransform = 'rotate(0deg)';
			document.getElementById('daro').style.webkitTransform = 'rotate(0deg)';
			document.getElementById('xiaoro').style.webkitTransform = 'rotate(0deg)';
			th.tips('你需要<span>顺时针拖动手柄转动转盘一圈</span>才可以完成制作哟~');
			$('.tishi').finish().fadeIn();
		}
	}
});

//完成制衣弹窗关闭
$('#cloth_sus_close').on('click', function () {
	popHide();
	angle = 0;
	document.getElementById('target').style.webkitTransform = 'rotate(0deg)';
	document.getElementById('daro').style.webkitTransform = 'rotate(0deg)';
	document.getElementById('xiaoro').style.webkitTransform = 'rotate(0deg)';
});
//查看大图
$('.whislist li').on('click', function () {
	if ($(this).hasClass('wjsli')) {
		return;
	} else {
		$('#datu_pic').attr('src', '/act/xtl/cloth/20200317/m/img/adtu_' + $(this).data('n') + '.jpg');
		popShow('pop_datu');
	};
});
