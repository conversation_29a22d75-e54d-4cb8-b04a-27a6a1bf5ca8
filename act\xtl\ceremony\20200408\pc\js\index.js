var cp_h,cp_w,scroll_h,_s,_ss;
$(window).on('load resize',function(){
	cp_h=$(window).height()-55;
	cp_w=$(window).width();
	_s = cp_w / 1920;
	_ss = Math.min(_s, cp_h / 860);
	if(cp_h>1025){
		cp_h=1025;
	};
	$('.wrapn').css('height',cp_h);
	$('.wrapi').addClass('active');
	scroll_h=cp_h-158;
	$('.scrollbox').css('height',scroll_h);
	if(cp_w<=1200){
		_s= 1200 / 1920;
		_ss=_s;
	};
	$('.wrap').css("zoom", _s);
	//$('.i_nr').css("zoom", _ss);
});
//pop(弹窗)
var popIsShow = false;
var popDom = null;
function popShow(id) {
	popHide();
	var p = $('#' + id);
	popDom = p;
	if (p) {
		p.show().css({
			position: 'fixed',
			top: '50%',
			left: '50%',
			marginTop: -popDom.height() / 2 + 'px',
			marginLeft: -popDom.width() / 2 + 'px',
			zIndex: 998
		});
		p.attr('for', 'pop');
		popIsShow = true;
		if ($('[for="' + id + '"]').length >= 1) return;
		$('body').append('<div name="overlay" for=' + id +
			' style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:997;background:rgba(0,0,0,0.8);"></div>');
	}
}
function popHide() {
	$('[for="pop"]').hide().attr('style', '');
	$('[name="overlay"]').remove();
}
window.addEventListener("resize", function() {
	if (!popIsShow)
		return;
	setTimeout(function() {
		popDom.css({
			marginTop: -popDom.height() / 2 + 'px',
			marginLeft: -popDom.width() / 2 + 'px',
			zIndex: 998
		});
	}, 400)
});
