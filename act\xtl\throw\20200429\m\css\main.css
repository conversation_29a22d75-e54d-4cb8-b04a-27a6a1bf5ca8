/* bg.png */
* {-webkit-tap-highlight-color: rgba(255,255,255,0)}
.wrap *{line-height: 1;box-sizing: border-box; 
  -moz-box-sizing: border-box;
-webkit-box-sizing: border-box;
-o-box-sizing: border-box;
-ms-box-sizing: border-box;
box-sizing: border-box; font-size: 0;}
.wrap{position: relative;}
.sp{width: 1000px;height: 1000px;background: url(../img/sp.png) no-repeat;background-size: 1000px 1000px}
.clearfix:after {content: ".";display: block;height: 0;clear: both;visibility: hidden;}
.clearfix {display: inline-block;}  /* for IE/Mac */
.clearfix {zoom: 1;display: block;   }
.fl{float: left;}
.fr{float: right;}
.bg{position: relative; max-width: 750px;width: 750px; height: 3250px;margin: auto;background: url(../img/bg.jpg)top center no-repeat; background-size: 750px 3250px;overflow: hidden;}
.top{width: 750px;height: 141px;background: url(../img/top.png) top center no-repeat;background-size: 100%}
.top a{float: right;margin: 34px 39px 0 0; width: 250px;height: 75px;}
.xtl_logo_bg{top: 29px;left: 361px;}
.head_box{height:1233px;width: 750px;position:relative;padding-top:945px;}
.head{position:absolute;top:0;left:0;width:100%;}
.head .title{width: 609px; height: 274px;animation:bounceInDown 1s 0s;position:absolute;top:23px;left:95px;}
.head .title .min_title{position: absolute;width:494px; height: 42px;background-position:0 -330px;top:280px;left:38px;}
.head .title .text{display: none;color:#73300f;font-size:22px;width:494px;left:38px;text-align:center;position:absolute;line-height:28px;top:327px;}
@keyframes bounceInDown {
  from,
  60%,
  75%,
  90%,
  to {-webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);}
  0% {opacity: 0;-webkit-transform: translate3d(0, -3000px, 0);transform: translate3d(0, -3000px, 0);}
  60% {opacity: 1;-webkit-transform: translate3d(0, 25px, 0);transform: translate3d(0, 25px, 0);}
  75% {-webkit-transform: translate3d(0, -10px, 0);transform: translate3d(0, -10px, 0);}
  90% {-webkit-transform: translate3d(0, 5px, 0);transform: translate3d(0, 5px, 0);}
  to {-webkit-transform: translate3d(0, 0, 0);transform: translate3d(0, 0, 0);}
}
@keyframes shake {
  from,
  to {-webkit-transform: translate3d(0, 0, 0);transform: translate3d(0, 0, 0);}
  10%,
  30%,
  50%,
  70%,
  90% {-webkit-transform: translate3d(-10px, 0, 0);transform: translate3d(-10px, 0, 0);}
  20%,
  40%,
  60%,
  80% {-webkit-transform: translate3d(10px, 0, 0);transform: translate3d(10px, 0, 0);}
}
@keyframes bounceIn {
  from,
  20%,
  40%,
  60%,
  80%,
  to {
    -webkit-animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }

  0% {
    opacity: 0;
    -webkit-transform: scale3d(0.3, 0.3, 0.3);
    transform: scale3d(0.3, 0.3, 0.3);
  }

  20% {
    -webkit-transform: scale3d(1.1, 1.1, 1.1);
    transform: scale3d(1.1, 1.1, 1.1);
  }

  40% {
    -webkit-transform: scale3d(0.9, 0.9, 0.9);
    transform: scale3d(0.9, 0.9, 0.9);
  }

  60% {
    opacity: 1;
    -webkit-transform: scale3d(1.03, 1.03, 1.03);
    transform: scale3d(1.03, 1.03, 1.03);
  }

  80% {
    -webkit-transform: scale3d(0.97, 0.97, 0.97);
    transform: scale3d(0.97, 0.97, 0.97);
  }

  to {
    opacity: 1;
    -webkit-transform: scale3d(1, 1, 1);
    transform: scale3d(1, 1, 1);
  }
}
/* 切换 */
.tabs{text-align: center;width: 100%;}
.tabs a{display: inline-block; width:136px; height:223px;font-size: 0;margin: 0 39px}
.tabs a.gift{background-position: 0 -427px;}
.tabs a.login{background-position: -213px -427px}
.tabs a.donload{background-position: -616px -180px}
.tabs a.lottery_{background-position: -419px -427px}
/* 抽奖 */
.lottery_box{position: relative;width:750px;height: auto;margin-top:5px;}
.lottery_box .t_1{ width: 713px; height: 63px; margin:0 auto; background-position:0 -704px;}
.lott_box li {position: relative; margin: auto;height:144px;}
.lott_box li div{width:144px;height:100%;float:left;position:relative;}
.lott_box li div.fr{float:right;}
.lott_box li img{position:absolute;top:50%;left:50%;transform:translate(-50%, -90%);}
.lott_box .l_1{width:496px;height:209px;}
.lott_box .l_1 .left,.lott_box .l_1 .right{margin-top:65px;}
.lott_box .l_1 .center{margin:0 32px;}
.lott_box .l_2{width:686px;margin-top:19px;}
.lott_box .l_3{width:620px;margin-top:41px;}
.lott_box .l_4{width: 332px;margin-top:-25px;}
.lottery .btn-lott{position: absolute;top:262px;left: 255px; width: 212px; height: 211px; background: url(../img/lottery_btn.png) no-repeat; background-size: 212px 211px;}
/* .lott_box li span{color: #6381b5;font-size: 21px;width: 100%;display: inline-block;text-align: center;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;}
.lott_box li img{margin: 29px 0 29px 0;}
.lott_box li.l_1,.lott_box li.l_4{width: 516px; }
.lott_box li.l_2{width: 694px;margin:0 auto 20px;height: 143px;}
.lott_box li.l_3{width: 670px;height: 133px;}
.lott_box li.l_4{width: 487px}
.lott_box li.l_4 div.last{margin: 47px 0 0 9px;}

.l_1 .left img{margin: -7px -3px 0px 0px;}
.l_1 .right img{margin: 20px 1px 0px 0px;}
.l_1 .left span{margin: -22px -3px 0px 0px;}
.l_1 .right span{margin: 30px -11px 0px 0px;}


.l_2 .left img{margin: -4px -20px 0px 0px}
.l_2 .right img{margin: 30px 3px 0px 0px;}
.l_2 .left span{margin: -3px 6px 0px -2px;}
.l_2 .right span{margin: 32px -11px 0px 0px;}

.l_3 .left img{margin: 29px 3px 0px 0px;}
.l_3 .right img{margin: 26px -1px 0px 0px;}
.l_3 .left span{margin: 31px -7px 1px -2px;}
.l_3 .right span{margin: 28px -11px 0px 0px;}

.l_4 .left img{margin: 26px -2px 0px 0px;}
.l_4 .right img{margin:16px -1px 0px 0px}
.l_4 .left span{margin: 27px -7px 1px -2px;}
.l_4 .right span{margin:16px -12px 5px -6px} */
input::placeholder{color: #7fa1c3;}
.login_{height:60px;display:flex;justify-content:center;align-items:center;}
.login_ *{font-size: 24px;}
.login_{color: #9e3b20;text-align: center; }
.login_ a{color: #9e3b20;}
/*  */
.lott_box li>div{height:166px;text-align:center}
.lottery_box p{display: block;  color: #9e3b20;font-size: 21px;line-height: 0px;padding-right:16px;margin-top:10px;}
.lottery_box p b{position: relative; color: #9e3b20;font-size: 21px; font-weight: normal;display: block;padding-left:33px;line-height: 30px;}
.lottery_box p b em{position: absolute;left: 0px;top: 3px; color: #9e3b20;font-size: 21px; font-weight: normal;}
.lottery_box h5{color:#9e3b20;font-size:24px;text-align:center;font-weight:bold;padding:21px 0 44px;}
/* 切换 */
.toggle_box{ height: 65px; width: 100%;text-align: center;}
.toggle_box a{height: 65px;width: 237px; display: inline-block; margin: 0 21px;}
.toggle_box a.new{ background-position: -763px -268px;}
.toggle_box a.old{background-position: -763px 0;}
.toggle_box .new.active{background-position: -763px -72px;}
.toggle_box .old.active{background-position: -763px -159px;}
.lottery_box h5 em{color: #7b2c13;font-size: 24px;font-weight:bold;}
/* .lottery_box a.sp{position: relative;top: 13px; z-index: 9; display: block; margin: auto; width: 192px;height: 58px;background-position: -20px -798px;font-size: 0;animation: as 1s infinite alternate linear;} */
@keyframes as{
  0%{
    transform: scale(1);
    opacity: .3;
  }
  100%{
    transform: scale(1.1);
    opacity: 1;
  }
}
.lottery{position: relative;height: 704px;width: 711px; background: url(../img/lottery_bg.png)top center no-repeat;background-size: 711px 704px; margin:16px auto 0;padding-top:13px;}
.lott-cover{position: absolute; width: 157px;height:157px;background:url(../img/this.png) no-repeat;background-size: 157px 157px;}
.lottery p{line-height:22px;position:absolute;left:0;width:100%;text-align:center;bottom:30px;padding-right:0;height:44px;display:flex;justify-content:center;align-items:center;font-size:20px;}




/* 轮播图 */
.wiper_box{position: relative; width: 750px}
.wiper_box h2{ width:618px; height: 63px; margin:88px auto 0; background-position:0 -802px;}
.my_siper{width: 620px;height: 341px; margin: auto; background: url(../img/siper_box.png)top center no-repeat; padding-top: 10px;}
.swiper-container{width: 566px; height: 326px;}
.swiper-button-prev,.swiper-button-next{ width: 60px;height: 59px; margin-top: 49px !important;}
.swiper-button-prev{background-position: -916px -252px;}
.swiper-button-next{background-position: -901px -191px;}
.swiper-pagination{width: 100%; text-align: center; margin-top: 46px;}
.swiper-pagination span{width: 12px;height: 12px;border: solid 1px #c9915d; margin: 0 12px;}
.swiper-pagination-bullet-active{background-color: #c9915d !important;}



#cy_bot{box-sizing: border-box;padding: 0 !important}

.act_dec {margin-left:37px;}
/*评论*/
.pl_box{margin:17px auto 0;width:702px;height:405px;background:url(../img/plbg.png) no-repeat;background-size:100%;padding-top:85px;}
.srollbox{height:238px;width:505px;overflow-x:hidden;overflow-y:auto;margin:0 auto;}
.srollbox img{display:block;width:100%;}
/* 弹窗 */
.pop_box p{text-align: center;color: #9e3b20;font-size: 22px;line-height: 26px;padding:10px 57px 0;}
.sbumit_btn,.pop8 .write{display: block;margin:0 auto; background-position: -763px -446px; width: 237px;height: 65px;font-size: 28px;color: #fcdfa6;text-align: center; line-height: 64px;}
.sbumit_btn span,.pop8 .write span{background-image:-webkit-linear-gradient(top,#f5d28f,#fae8c3);-webkit-background-clip:text;-webkit-text-fill-color:transparent;font-weight:bold;}
.pop{position: relative; width: 620px;}
.off{position: absolute;top:39px;right:-1px; width: 59px; height: 59px;background-position: -941px -529px;font-size: 0}
/* 填写信息 */
.pop1{ height: 425px;background: url(../img/pop4.png) no-repeat;background-size: 100% 100%;width: 712px; box-sizing: border-box;padding-top:41px;}
.pop h3{color:#f3d691;font-size:.3rem;text-align:center;width:432px;height:46px;background-position:0 -889px;margin:0 auto;padding-bottom:3px;}
.pop h3 span{font-weight:bold;background-image:-webkit-linear-gradient(top,#efc96e,#f7e2b2);-webkit-background-clip:text;-webkit-text-fill-color:transparent;display:inline-block;vertical-align:top;margin-top:1px;}
.pop1 .dj_box{width:132px;text-align:center;margin:20px 0 0 55px;height:124px;position:relative;display:flex;justify-content:center;align-items:center;}
.pop1 .user {padding-top:17px;font-size:0;height:167px;}
.pop1 .user li{margin-top:10px;height:38px;}
.pop1 .user label{color: #9e3b20;font-size: 22px;width:83px;line-height:38px;float:left;text-align:right;}
.pop1 .user input{width: 271px;height: 38px;border: solid 1px #9e3b20;padding-left: 10px;font-size: 22px; color: #9e3b20; background: transparent;}
.pop1 .msg{padding:0 57px;}
.pop1 .msg img{margin: 0px auto;display:block;}
.pop1 .msg p{text-align:center;color:#9e3b20;font-size:22px;padding:4px 0 0 0;position:absolute;width:100%;top:124px;left:0;}
/* 获得价值9999元的礼包一份 */
.pop2{width:702px; height:264px;background:url(../img/pop3.png) no-repeat;background-size:100% 305px;padding-top:41px;}
.pop2 .dj_box{width:115px;height:124px;text-align:center;display: flex; justify-content: center; align-items: center;margin:12px auto 0;}
/* 请不要心急哦 */
.pop3, .pop7{ height: 195px;background: url(../img/pop1.png) no-repeat;}
.pop3 p, .pop7 p{color:#9e3b20;font-size:22px;padding-top:18px;height:166px;display:flex;justify-content:center;align-items:center;}
.pop3, .pop7{width: 712px; height: 195px;}
/* pop2.png */
.pop4{height: 384px;width: 712px; background: url(../img/pop2.png) no-repeat;background-size: 712px 425px;padding-top:41px;}
.pop4 p.dec{height:84px; display:flex;justify-content:center;align-items:center;}
.pop4 input{height: 36px;border: solid 1px #9e3b20;background: transparent;font-size: 20px;color: #9e3b20;padding-left: 10px;line-height:36px;}
.pop4 p.input_wrap { text-align: left;padding:0;width:411px;margin:10px auto 0;}
.pop4 input.input_phone { width: 396px; }
.pop4 input.input_phone_code { width: 266px; }
.pop4 .btn_phone_code { display: inline-block; width: 108px; line-height: 38px; background-color:#8d270d; color: #f6dfb9; border-radius: 5px; text-align: center; margin-left:18px;line-height:38px;background-image:-webkit-linear-gradient(top,#8d270d,#c7550d);}
.pop4 .btn_phone_bind { margin-top: 10px;}
.pop4 .btn_phone_bind .active{background-position: -366px -809px; color: #fff;}
.pop4 .sbumit_btn{margin-top:39px;}
.pop4 input::placeholder{color: #9e3b20;}
/* 登录游戏账号 */
.pop5{height: 278px;background: url(../img/pop2.png) no-repeat;}
.pop5 p{margin: 0 0 33px;padding-top: 37px;}
.pop5 ul{width: 323px;margin: auto}
.pop5 ul li{margin-bottom: 17px;}
.pop5 label{color: #7fa1c3;font-size: 20px;}
.pop5 input{width: 263px;	height: 32px;border: solid 1px #97badc;color: #7fa1c3;font-size: 20px;padding-left: 10px;background: transparent}
/* 拆礼包 */
.pop6{width: 341px;height: 354px;}
.pop6 .gift_box{display: block;width: 341px;height: 354px; background: url(../img/gift.png) no-repeat;}
/* 获奖记录 */
.pop8{width: 596px;height: 348px;background: url(../img/pop3.png) no-repeat;background-size: 100% 348px;}
.pop8 table td{color: #9e3b20;font-size: 22px;text-align: center;width: 425px;height: 44px;border: solid 1px #9e3b20;}
.pop8>.gift_list{width:425px; height: 130px; margin:22px auto 0; box-sizing: border-box;}
.pop8>.gift_list::-webkit-scrollbar{width: 0px;height: 0px;}
.pop8>.gift_list::-webkit-scrollbar-thumb{border-radius: 0px;-webkit-box-shadow: inset 0 0 0px #9e3b20; background: #9e3b20;;}
.pop8>.gift_list::-webkit-scrollbar-track{-webkit-box-shadow: inset 0 0 0px #9e3b20;border-radius: 4px;background: #9e3b20;}
.pop8 .write.active{background-position: -763px -350px;color: #fff;}











