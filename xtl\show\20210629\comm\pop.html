<!--送TA参选-->
<div class="pop pop1" id="enterPop">
	<div class="popcont">
		<div class="frombox">
			<div class="sebox">
				<label>萌宠昵称:</label>
				<input id="nickName" />
			</div>
			<div class="sebox seboxh">
				<label>萌宠故事:</label>
				<div class="textareabox">
					<textarea maxlength="150" id="content" /></textarea>
				</div>
			</div>
			<div class="uploadbox">
				<div class="uploadfl">
					<a href="javascript:;" class="btn acbtn" title="爱宠照片">
						<input type="file" name="file" id="phoneFile" accept="image/*" title="">
					</a>
					<a href="javascript:;" class="ckbtn" title="查看上传规则">查看上传规则</a>
				</div>
				<div class="upimgbox">
					<img src="">
				</div>
			</div>
			<div class="sebox">
				<label>手机号码:</label>
				<input class="wduan" type="phone" maxlength="11" id="phone" />
				<!--置灰，添加类名gray-->
				<a href="javascript:;" class="btn yzbtn">发送验证码</a>
			</div>
			<div class="sebox">
				<label>验证码:</label>
				<input class="wduan" type="phone" id="code" maxlength="6" />
			</div>
			<a href="javascript:;" class="btn tjbtn" title="提交">提交</a>
		</div>
	</div>
	<a href="javascript:;" class="close" title="点击关闭">x</a>
</div>
<!-- 剪切弹窗 S -->
<div class="pop-container" id="cropperPop">
	<div class="img-container">
		<img id="imageCropper" src="" alt="Picture">
	</div>
	<div class="cropper-btn">
		<a href="javascript:;" class="btn-upload-cirfirm">确定上传</a>
		<a href="javascript:;" class="btn-upload-cancel">取消</a>
	</div>
</div>
<!-- 剪切弹窗 E -->
<!--个人中心-->
<div class="pop pop2" id="personPop">
	<div class="popcont">
		<ul class="inflist">
			<li class="clearfix">
				<label>参选情况：</label>
				<span class="person_state">尚未参选</span>
			</li>
			<li class="clearfix">
				<label>投喂次数：</label>
				<span class="person_currCanVote">-</span>
				<a href="javascript:;" class="btn csbtn" id="csbtntop" title="如何获得投喂次数?">如何获得投喂次数?</a>
			</li>
			<li class="clearfix">
				<label>抽奖次数：</label>
				<span class="person_currLotNum">-</span>
				<a href="javascript:;" class="btn csbtn csbtn1" id="csbtnbop" title="如何获得抽奖次数?">如何获得抽奖次数?</a>
			</li>
			<li class="clearfix">
				<label>我的奖励：</label>
				<div class="person_giftsbox">
					<span class="person_gifts">暂无奖励</span>
				</div>
			</li>
		</ul>
	</div>
	<a href="javascript:;" class="close" title="点击关闭">x</a>
</div>
<div class="pop pop3" id="csPop1">
	<div class="popcont">
		<ul class="tasklist">
			<li>每日首次在活动页面登陆<span>今日投喂次数+1</span><em>今日1/1</em></li>
			<li>每日首次分享活动页面<span>今日投喂次数+1</span><em>今日0/1</em></li>
		</ul>
		<div class="btnbox">
			<a href="javascript:;" class="btn fxbtn" title="分享活动">分享活动</a>
			<a href="javascript:;" onclick="hideMask($('.pop'))" class="btn surebtn" title="确定">确定</a>
		</div>
	</div>
	<a href="javascript:;" class="close" title="点击关闭">x</a>
</div>
<div class="pop pop4" id="csPop2">
	<div class="popcont">
		<ul class="tasklist">
			<li><strong>成功发布参选信息并通过审核</strong><span>抽奖次数+1</span><em>未完成</em></li>
			<li><strong>获得投喂数达到50</strong><span>抽奖次数+2</span><em>未完成</em></li>
		</ul>
		<div class="btnbox">
			<a href="javascript:;" onclick="hideMask($('.pop'))" class="btn surebtn" title="确定">确定</a>
		</div>
	</div>
	<a href="javascript:;" class="close" title="点击关闭">x</a>
</div>
<div class="pop pop5" id="fxPop">
	<div class="popcont">
		<img src="/act/xtl/show/20210629/pc/img/fx.jpg" class="fximg">
		<div class="linkbox">
			<label>活动链接：</label>
			<input value="555" readonly id="foo" />
			<a href="javascript:;" class="btn copybtn" title="点击复制" data-clipboard-target="#foo">点击复制</a>
		</div>
		<div class="sharebox">
			<div id="share"></div>
		</div>
	</div>
	<a href="javascript:;" class="close" title="点击关闭">x</a>
</div>
<div class="pop pop6" id="gzPop">
	<div class="popcont">
		<ul class="gzlistpo">
			<li><span>1、</span>上传图片大小需≤4M。</li>
			<li><span>2、</span>上传照片需为您家中宠物猫的实拍照片，上传其他宠物或其他内容均无法通过审核。</li>
			<li><span>3、</span>上传照片内容需符合我国相关法律法规，不得上传涉及色情、反动等信息的内容，否则审核将无法通过。</li>
		</ul>
		<div class="btnbox btnboxt">
			<a href="javascript:;" onclick="hideMask($('.pop'))" class="btn surebtn" title="确定">确定</a>
		</div>
	</div>
	<a href="javascript:;" class="close" title="点击关闭">x</a>
</div>
<div class="pop pop7">
	<div class="popcont">
		<div class="txtbox">
			您已成功提交参选信息，请耐心等待审核结果。<br>结果可在<a href="javascript:popup($('#personPop'));">【个人中心】</a>中进行查看。
		</div>
		<p class="poptip">提示：投票阶段将于7月16日0:00开始。</p>
		<div class="btnbox btnboxt">
			<a href="javascript:;" onclick="hideMask($('.pop'))" class="btn surebtn" title="确定">确定</a>
		</div>
	</div>
	<a href="javascript:;" class="close" title="点击关闭">x</a>
</div>
<div class="pop pop7" id="noVote">
	<div class="popcont">
		<div class="txtbox txtboxlh">
			您今日已经没有投喂次数啦，明天再来吧!
		</div>
		<div class="btnbox btnboxt">
			<a href="javascript:;" onclick="hideMask($('.pop'))" class="btn surebtn" title="确定">确定</a>
		</div>
	</div>
	<a href="javascript:;" class="close" title="点击关闭">x</a>
</div>
<div class="pop pop7" id="dialogGetPrize">
	<div class="popcont">
		<div class="txtbox txtboxlh">
			恭喜您获得<span class="g-name">道具奖励名称</span>
		</div>
		<div class="btnbox btnboxt">
			<a href="javascript:;" onclick="hideMask($('.pop'))" class="btn surebtn" title="确定">确定</a>
		</div>
	</div>
	<a href="javascript:;" class="close" title="点击关闭">x</a>
</div>
<div class="pop pop7" id="noLotNum">
	<div class="popcont">
		<div class="txtbox">
			您已经没有抽奖机会啦,<br>快去完成任务获得抽奖次数吧~
		</div>
		<div class="btnbox btnboxtup">
			<a href="javascript:;" onclick="hideMask($('.pop'))" class="btn surebtn" title="确定">确定</a>
		</div>
	</div>
	<a href="javascript:;" class="close" title="点击关闭">x</a>
</div>
<div class="pop pop7" id="popAlert">
	<div class="popcont">
		<div class="txtbox">
			<strong>分享成功!</strong>今日投喂次数+1
		</div>
		<div class="btnbox btnboxtup">
			<a href="javascript:;" onclick="hideMask($('#popAlert'))" class="btn surebtn" title="确定">确定</a>
		</div>
	</div>
	<a href="javascript:;" onclick="hideMask($('#popAlert'))" class="pop-close" title="点击关闭">x</a>
</div>
<div class="pop pop8">
	<ul class="login-type">
		<li class="wx-li" id="wxLogin">
			<p>已绑定微信入口</p>
			<a href="javascript:;">
				<strong class="wxicon" id="unBind"><i>点击前往绑定</i></strong>
				<strong class="text" id="isBind"></strong>
			</a>
		</li>
		<li class="no-wx-li" id="plaLogin">
			<p>未绑定微信入口</p>
			<a href="javascript:;" class="login_account"><strong class="text">未在微信绑定过畅游账号的玩家可在此登录</strong></a>
		</li>
	</ul>
	<a href="javascript:;" class="close" title="点击关闭">x</a>
</div>
<div class="pop pop9" id="bindPop">
	<div class="popcont">
		<p class="poptip">小提示：账号一旦绑定，将无法更换哦~</p>
		<p class="bindtip">请选择您要绑定的角色</p>
		<div class="selbox">
			<select id="roleList">
				<option>请选择角色</option>
			</select>
		</div>
		<a href="javascript:;" class="btn tjbtn" title="提交">提交</a>
	</div>
	<a href="javascript:;" class="close" title="点击关闭">x</a>
</div>
