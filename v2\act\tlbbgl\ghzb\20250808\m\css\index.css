@charset "utf-8";
html,body,div,p,ul,li,dl,dt,dd,em,i,span,a,img,input,h1,h2,h3,h4,h5{margin:0;padding:0;}
a,img,input{border:none;outline:none;}
a{text-decoration:none;}
img{display:block;}
ul,li{list-style:none;}
table{border-collapse:collapse;border-spacing:0;}
body{font:24px/1.5 -apple-system,"Helvetica Neue",Helvetica,Arial,sans-serif;-webkit-user-select: none;background-color:#fff;}
.none{display:none;}
input::-webkit-outer-spin-button,input::-webkit-inner-spin-button{-webkit-appearance: none !important;}
input[type="number"]{-moz-appearance:textfield;}
*{-webkit-appearance:none;-webkit-text-size-adjust:none;}
html{-webkit-tap-highlight-color:rgba(0,0,0,0);}
#cy_bot{padding:10px!important;position: relative;z-index: 2}
.cyLogo{margin-top:10px;}
.orientLayer {
    display: none;
    position: fixed;
    z-index: 1000;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, .9);
    color: #fff;
    font-size: 30px;
}

/* 页面样式 */
html,body{width:100%;}
.pr{position: relative;}
.pa{position: absolute;}
.hide{font-size: 0; text-indent: -9999em; width: 0; height: 0;}
.t,.btn{font-size: 0; text-indent: -9999em;display: block; background-repeat: no-repeat; background-color: transparent; background-size: 100% auto;}
.btn{ background-image: url("../img/btn-.png"); background-size: 750px auto; width: 333px; height: 119px;}
.flex{ display: flex;}
.justify-center{ justify-content: center}
.t-l{text-align: left;}
/**/
html,body,.container{ width: 100%; height: 100%; font-size: 28px; background: #fff; color: #1a1919; line-height: 1.5;}
.container{ max-width: 750px; height: 100%; margin: 0 auto; display: flex; align-items: center; justify-content: center;}
.page{ width: 100%; height: 100%; min-height: 13.34rem; overflow: hidden; background: url("../img/bg1.jpg") no-repeat center center/100% auto; display: none; align-items: center; justify-content: center;}
.page.on{ display: flex;}
.page2,.page3,.page4{ background-image: url("../img/bg2.jpg");}
.page4 .btn-join{height: 95px;background-position: -304px -278px;}
.wrap{ height: 1334px; width: 100%; background: rgba(255,0,0,0); box-sizing: border-box; position: relative;}
.logo{ background-image: url("../img/logo.png?1.0"); width: 206px; height: 118px; position: absolute; left: 272px; top: -40px;}
.slogan{ background-image: url("../img/slogan.png"); width: 750px; height: 382px; margin: 658px auto 0;}
.btn-login{ margin: 16px auto 30px;background-position:0 0; width: 305px;}
.btn-login.timeover{filter: grayscale(100%);}
.btn-login:not(.timeover){animation: ani-breath 2s linear infinite both;}
@keyframes ani-breath {
    0%, 100% {transform: scale(1);}
    50% {transform: scale(.9);}
}
.btn-logout{ position: absolute; right: .3rem; top: .2rem; width: 100px; height: 43px; background-position: -96px -227px;}
.btn-rule,.btn-center{ width: 145px; height: 26px; margin: 0 20px; background-position: -213px -749px;}
.btn-center{ background-position-x: -437px;}
.page1 .wrap{ padding-top: 814px;}
.btn-create{width: 440px;height: 100px; background-position-x: -304px; margin: 30px auto;}
.btn-join{width: 440px;height: 100px; background-position:-304px -275px; margin: 30px auto 50px;}
/* .btn-joins{ background-position-y: -258px; margin: 30px auto;} */
.btn-joins{ width: 440px;height: 95px;background-position: -306px -187px; margin: 30px auto;}
.btn-rule2,.btn-center2{ width: 163px; height: 32px; margin-top: 26px;}
.btn-rule2{ background-position: -20px -789px;}
.btn-center2{ background-position: -20px -745px;}
.invite-text{ background: url("../img/text.png") no-repeat; background-size: 100% auto; width: 600px; height: 143px; margin: 198px auto 80px; text-align: center; font-size: 28px; color: #1a1919; box-sizing: border-box; padding-top: 34px;}
.red{ color: #701a1b;}
.ipt .red{ color: red;}
.guild-code{ font-size: 24px; color: #463726; background: url("../img/msg.png") no-repeat center center/100% auto; width: 664px; height: 360px; margin: 20px auto 6px; box-sizing: border-box; padding: 20px 44px 14px; text-align: center; display: flex; flex-direction: column; justify-content: space-evenly;}
.guild-code2{ background-image: url("../img/msg2.png"); padding: 54px 44px 48px;}
.guild-nickname{ font-size: 22px; color: #701a1b; text-decoration: underline; text-underline-offset: 6px;}
.guild-line{ display: flex; align-items: center; margin: 2px 0;}
.guild-code2 .guild-line,.guild-code3 .guild-line{ justify-content: center;}
.guild-code3 .btn-modify{ display: none;}
.guild-line p{ width: 130px; text-align: left;}
.guild-line input{ width: 175px; padding: 0 16px; line-height: 36px; border: 1px solid #d7c7ab; background: #fff; text-align: left; font-size: 22px; color: #701a1b;}
.btn-copy,.btn-modify{ width: 111px; height: 40px; background-position:-144px -355px; display: inline-block; vertical-align: middle; margin-left: 10px;}
.btn-modify{ background-position:-144px -404px;}
.btn-detail{ width: 204px; height: 50px; margin: 10px auto; background-position: -502px -665px; flex-shrink: 0;}
.tip{ text-align: center; font-size: 20px; color: #7e291b;}
.guild-num{ font-size: 22px; font-weight: bold; color: #d5a460; text-align: center;}
.guild-num2{ line-height: 1.3; margin-top: 20px;}
.guild-num2 .red{ color: #fc3d3d;}
.btn-guild{width: 440px;height: 100px; background-position:-306px -374px; margin: 20px auto 0;}
.tit{ background: url("../img/tit.png") no-repeat center center/auto 100%; height: 164px; margin-bottom: 30px;}
.rank{ width: 618px; margin: 16px auto; height: 540px; border-radius: 6px; background: linear-gradient(to bottom, rgba(0,0,0,.6),rgba(0,0,0,0) 90%); padding: 14px 16px;}
.rank2{ height: 1100px;background: linear-gradient(to bottom, rgba(0,0,0,.5) 70%,rgba(0,0,0,0) 90%);}
.rank-tab{ display: flex; justify-content: space-between; margin-bottom: 24px;}
.rank-tab a{ width: 287px; height: 60px;}
.rank-tab a:last-of-type,.rank-tab2 a:last-of-type{ background-position:-320px -570px;}
.rank-tab a:first-of-type,.rank-tab2 a:first-of-type{ background-position:-13px -482px}
.rank-tab a:first-of-type.on,.rank-tab2 a:first-of-type.on{ background-position:-13px -570px;}
.rank-tab a:last-of-type.on,.rank-tab2 a:last-of-type.on{ background-position:-320px -482px;}
.rank-con table{ width: 624px; text-align: center; color: #fff;}
.rank-table{ height: 250px; padding-top: 4px; overflow-x: hidden; overflow-y: auto;}
/*定义滚动条高宽及背景
 高宽分别对应横竖滚动条的尺寸*/
.rank-table::-webkit-scrollbar{
  width: 16px;
}
/*定义滚动条轨道 内阴影+圆角*/
.rank-table::-webkit-scrollbar-track{
  background: url("../img/scrollbar.png") no-repeat center center/100% 100%;
}
.rank2 .rank-table::-webkit-scrollbar-track{
    background: url("../img/scrollbar2.png") no-repeat center center/100% 100%;
}
/*定义滑块 内阴影+圆角*/
.rank-table::-webkit-scrollbar-thumb{
  background: url("../img/scrollbar-thumb.png") no-repeat center center/100% 100%;
}
.rank2 .rank-table::-webkit-scrollbar-thumb{
    background: url("../img/scrollbar-thumb2.png") no-repeat center center/100% 100%;
}
.rank-con th{ width: 28%; height: 40px; background: #000; font-size: 24px; color: #fff; text-align: center;}
.rank-con td{ width: 28%; height: 44px; font-size: 22px; color: #fff;}
.rank-con tr th:nth-of-type(2),.rank-con tr td:nth-of-type(2){ width: 42%;}
.rank-con tr th:nth-of-type(3),.rank-con tr td:nth-of-type(3){ width: 30%;}
.btn-back{ background-position: -121px -267px; width: 138px; height: 65px; margin-left: 52px;}
.rank2 .rank-table{ height: 820px; margin-bottom: 10px;}
.rank2 .rank-con.nonext .rank-table{ height: 858px;}
.rank2 .rank-con.nonext .paging{ display: none;}
.user-list{ min-height: 798px; margin-bottom: 20px;}
.paging{ display: flex; align-items: center; text-align: center; font-size: 24px; color: #fff; }
.paging p{ flex: 1;}
.paging input{ background: transparent; border: none; border-bottom: 1px solid #fff; width: 50px; text-align: center; font-size: 24px; color: #fff;}
.btn-prev,.btn-next,.btn-up{ width: 110px;height: 40px; background-position: -632px -599px;}
.btn-prev{background-position: -6px -298px; width: 110px;}
.btn-next{ background-position: -6px -352px; margin-right: 16px;}
.btn-up{ background-position: -6px -402px; margin-right: 16px;}
.btn-follow{ background: url("../img/ew.png") no-repeat; background-size: 100% auto; width: 49px; height: 252px; position: absolute; right: 0; top: 1rem;}
/* pop */
.lay-msg-con{ font-size: 24px;}
.pop{ display: none; background: url("../img/pop1.png") no-repeat; background-size: 100% auto; width: 750px; height: 508px; box-sizing: border-box; padding: 90px 114px 0; position: relative;}
.pop1{ background-image: url("../img/pop1.png"); height: 508px; padding: 90px 140px 0;}
.pop2{ background-image: url("../img/pop2.png"); height: 1109px;}
.pop3{ background-image: url("../img/pop3.png?1.0"); height: 455px; padding: 100px 140px 0;}
.pop4{ background-image: url("../img/pop4.png?1.0"); height: 761px; padding: 125px 114px 0;}
.pop5{ background-image: url("../img/pop1.png"); height: 508px; padding: 80px 114px 0;}
.pop6{ background-image: url("../img/pop-home.png"); width: 695px; height: 740px; box-sizing: border-box;padding: 80px 110px 0px 94px;}
.pop7{ background-image: url("../img/adp.png"); width: 620px; height: 1065px;}
.pop7 .btn-close{ top: 0; right: 6px;}
.pop6 .btn-close{ right: 6px;}
.pop6 .pop-tit {background: url("../img/pop-tit2.png") no-repeat center; background-size: auto 100%; height: 50px;margin-top: 18px;}
.pop6 h3{font-size: 34px; color: #f59d41; line-height: 36px; text-align: center; margin: 16px auto 20px;}
.pop-alert {padding: 40px 120px 0;}
.pop-alert h3{font-size: 38px; color: #372812; text-align: center; font-weight: bold; line-height: 1.3;}
.pop-share{ display: none; width: 100%; height: 100%; position: fixed; z-index: 9999; top: 0; left: 0; background: rgba(0,0,0,.7) url("../img/pop-share.png") no-repeat center top/100% auto;}
.pop-login { width: 608px; height: 624px; background: none; padding: 1px;}
.login-box{ width: 100%; height: 100%;}
.btn-close{ width: 65px; height: 65px; background-position: -19px -217px; position: absolute; top: -15px; right: 40px;}
.pop3 .btn-close{ top: -20px; right: 40px;}
.pop4 .btn-close{ top: -20px; right: 40px;}
.pop-login .btn-close{ top: -20px; right: -20px;}
.pop-com{ height: 138px; display: flex; justify-content: center; align-items: center; text-align: center; font-size: 30px; color: #fff; line-height: 1.3; margin: 16px;}

.pop-alert .pop-com{height: 220px; margin: 0 auto 8px; font-size: 23px;}
.btn-confirm{ background-position:-24px -659px; }
#popCom .btn-confirm{width: 241px;  height: 74px;background-position:-6px -652px; }
#popJoin .btn-confirm,#popCreate .btn-confirm{width: 241px;  height: 74px;background-position:-15px -647px; }
#popJoin .btn-cancel,#popCreate .btn-cancel{width: 241px;  height: 74px;background-position:-261px -647px; } 
.btn-cancel{ background-position:-24px -659px; }
/* .btn-confirm{ background-position:-24px -659px; } */
.btn-confirm,.btn-cancel,.btn-create2,.btn-fillin{ width: 209px; height: 74px; background-position-y: -437px; margin: 0 6px;}

.btn-cancel{ background-position-x: -234px;}
.btn-create2{ background-position-y: -518px;}
.btn-fillin{ background-position: -234px -518px;}
.rule{ height: 850px; overflow-x: hidden; overflow-y: auto; list-style-type: none; counter-reset: sectioncounter; font-size: 22px; color: #fff; margin: 44px auto;}
.rule .tit-color{color: #f59d41;}
.rule li{ display: flex; margin-bottom: 14px;}
.rule li:before{ width: 30px; height: 30px; flex-shrink: 0; background: #7f6150; text-align: center; line-height: 30px; margin-right: 8px; color: #fff; content: counter(sectioncounter); counter-increment: sectioncounter;}
.copy-tip{font-size: 28px; text-align: center; color: #fff; line-height: 1.3; }
.join-ipt{ display: block; background: #fff url("../img/icon1.png") no-repeat 10px center/24px auto; border-radius: 4px; border: 1px solid #d1c3a5; width: 460px; height: 60px; line-height: 60px; margin: 50px auto; font-size: 24px; color: #3b3b3b; box-sizing: border-box; padding: 0 16px 0 42px;}
.copy-text{ background: url("../img/ipt.png") no-repeat; background-size: 100% auto; width: 479px; height: 67px; font-size: 22px; color: #fff; margin: 46px auto 20px; display: flex; align-items: center; box-sizing: border-box; padding: 0 30px;}
.copy-text input{ flex: 1; font-size: 22px; color: #fff; border: none; background: none;}
.copy-text i{ background: url("../img/icon2.png") no-repeat; background-size: 100% auto; width: 40px; height: 42px; margin-left: 10px;}
.btn-copy2{ display: block; background: url("../img/ipt.png") no-repeat; background-size: 100% auto; width: 479px; height: 67px; line-height: 67px; text-align: center; font-size: 30px; color: #fff; margin: 0 auto;}
.ipt-list{ width: 460px; padding-left: 20px;}/*margin: 42px auto;*/
.ipt-list2{ display: flex; flex-direction: column; justify-content: space-evenly; height: 210px; align-items: center;}/* margin: 20px 0;*/
.ipt-list li{ display: flex; align-items: center; margin: 14px 0; width: 460px; font-size: 24px; color: #3b3b3b;}
.ipt-list2 li{ margin: 6px 0;}
.ipt{ display: flex; align-items: center; flex-grow: 1; height: 60px;  white-space: nowrap; line-height: 60px; background: #fff; border: 1px solid #d1c3a5; border-radius: 4px;}
.ipt input{ flex-grow: 1; padding-right: 10px; font-size: 24px; color: #3b3b3b; height: 100%; max-width: 276px;}
.ipt .namePre{max-width: 228px;}
.ipt2 input{ max-width: 94px;}
.ipt3 input { max-width: 180px;}
::-webkit-input-placeholder{ font-size: 22px;}
.ipt i,.ipt p{ flex-shrink: 0;}
.ipt-list li.radio-li{ align-items: flex-start;}
.radio{ flex-shrink: 0; border: 1px solid #494949; width: 24px; height: 24px; border-radius: 50%; margin-right: 10px; display: flex; align-items: center; justify-content: center; }
.radio.checked:after{ content: ''; width: 16px; height: 16px; border-radius: 50%; background: #7f6150; }
.btn-getcode{ width: 156px; height: 60px; line-height: 60px; text-align: center; font-size: 24px; color: #fff; margin-left: 10px; background: #8f785a; border-radius: 4px; flex-shrink: 0;}
.btn-getcode.disabled{ opacity: .8; pointer-events: none; user-select: none; -webkit-user-select: none;}
.icon1,.icon3,.icon4,.icon5,.icon6,.icon7,.icon8{ background: url("../img/icon1.png") no-repeat; background-size: 100% auto; width: 24px; height: 27px; margin: 0 10px;}
.icon3{ background-image: url("../img/icon3.png");}
.icon4{ background-image: url("../img/icon4.png");}
.icon5{ background-image: url("../img/icon5.png");}
.icon6{ background-image: url("../img/icon6.png");}
.icon7{ background-image: url("../img/icon7.png"); width: 26px; margin: 0 9px;}
.icon8{ background-image: url("../img/icon8.png");}
.record-text{ font-size: 20px; color: #fff; margin: 20px 30px 10px;}
/*.record{ height: 400px; overflow-x: hidden; overflow-y: auto;}*/
/*.record table{ width: 100%; text-align: center; font-size: 22px; color: #000;}*/
/*.record tr td:last-of-type{ width: 53%; height: 50px; padding: .04rem 0;}*/
.pop6 .gift-list-head {width: 466px;margin: 0 auto;}
.pop6 .gift-list-head div{height:54px; color: #ffffff;font-size: 24px; line-height: 54px; text-align: center; text-shadow: 0 2px 8px #bd996b;}
.pop6 .gift-list {width: 466px;height:280px;margin: 10px auto 0;overflow-y: scroll;position: relative;}
.pop .no-data::after{content: '暂无数据~'; position: absolute; top: 50%; left: 50%; transform: translate(-50%,-50%); font-size: 24px;color: #4c4537;}
.pop6 ul li{display: flex;flex-direction: row;justify-content: start;align-items: center;margin: 10px 0; border-bottom: 1px dashed #cccccc; color: #000;}
.pop6 .col1{font-size: 24px; width: 50%; line-height: 48px; display: flex; align-items: center;justify-content: center;color: #fff;}
.pop6 .col2{font-size: 24px; width: 50%; line-height: 28px;margin-left: 10px; min-height: 48px; display: flex; align-items: center;justify-content: center;color: #fff;}
.gift-list-head .col1{background: url("../img/th1.png") no-repeat center;background-size: 100% 100%; width: 170px; justify-content: center;}
.gift-list-head .col2{background: url("../img/th2.png") no-repeat center;background-size: 100% 100%; width: 345px; justify-content: center;}
.tit1{ background: url("../img/tit1.png") no-repeat center center/ auto 100%; height: 41px; }
.tit2{ background: url("../img/tit2.png") no-repeat center center/ auto 100%; height: 36px; }
.rank-tip{ color: #fff; font-size: 20px; text-align: center; margin: 10px 0;}
/*.red{ color: red;}*/
.ewmtn{width: 43px;height: 341px;background: url("../img/ew.png") no-repeat;position: fixed;left: 50%; margin-left: 326px; bottom: 45%;z-index: 9; display: none;}
.pop-ewm{width: 620px;height: 1065px; background: none; padding: 0;}
.pop-ewm img{width: 100%; height: 100%; object-fit: contain;}
.pop-ewm .btn-close{top:4px;right:10px;}
.prize_max{background-image: url("../img/prize_max.png"); width: 210px; height: 69px; position: absolute; bottom: -6px; left: 30px;}
.pop-ewm .qrcode_img{position: absolute; left: 112px; top: 136px; width: 242px; height: 242px;}
.pop-ewm .qrcode1{top: 136px;}
.pop-ewm .qrcode2{top: 500px;}