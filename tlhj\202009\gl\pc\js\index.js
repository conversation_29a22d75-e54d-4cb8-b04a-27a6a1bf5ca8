$(function () {
    $(window).on('load',function () {
        $('.wrap').addClass('ani');
    })

    $('.tab').on('click','.tab-i',function () {
        var $this = $(this), index = $this.index();
        showPage(index);

        var top = $('.tab-wrap').offset().top;
        $('html,body').animate({'scrollTop':(top-10)+'px'});
    });

    function showPage(index) {
        $('.tab .tab-i').eq(index).addClass('active').siblings().removeClass('active');
        var $main = $('.main'+(index+1));
        $('.main').css("display","none").removeClass('show');
        $main.css("display","block");
        setTimeout(function(){
            $main.addClass('show');
        },50);
    }
    showPage(0);


})
