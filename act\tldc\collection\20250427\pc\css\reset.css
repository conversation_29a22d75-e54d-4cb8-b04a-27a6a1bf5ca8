@charset "utf-8";
	/* reset */
body,div,p,ul,li,table,tbody,tr,td,textarea,form,input,h1,h2,h3,h4,h5,h6,dl,dt,dd,img,iframe,header,nav,section,article,footer,figure,figcaption,menu{margin:0;padding:0;list-style:none;font-weight: normal;}
body{-webkit-text-size-adjust:none;}
header,nav,section,article,footer,figure,figcaption{display:block;}
ol,ul{margin:0;padding:0;}
fieldset{border:none;margin:0;padding:0;}
a,li,ul,[onclick]{-webkit-tap-highlight-color:none;-webkit-tap-highlight-color:rgba(0,0,0,0);}
table{border-collapse:collapse;border-spacing:0;}
em,strong,i{font-weight:normal;font-style:normal;}
a {text-decoration:none;}
.hide {display:block;width:0;height:0;overflow:hidden;}
.c:before,.c:after {content:"";display:table;}
.c:after {clear:both;}
.fl {float:left;}
.fr {float:right;}
.pr {position:relative;}
.pa {position:absolute;}
.db,.btn,.comm {display:block;text-indent:-999em;}
html,
body {
	width: 100%;
	font-family: "Microsoft YaHei", "Helvetica Neue", Arial, HelveticaNeue, Helvetica, sans-serif;
}
:focus{outline: none;}
@font-face {
	font-family: 'title';
	src: url(../font/hy.ttf);
	font-display: swap;
}
input{outline:none;border: none;background: none;}
input::-webkit-input-placeholder{color:#999;padding-right: 50px;}
img[src=""] {display: none;}
.scrollable {
	width: 300px;
	height: 200px;
	overflow-y: auto;
}

/* 滚动条整体样式（WebKit 内核浏览器） */
.scrollable::-webkit-scrollbar {
	width: 10px;
}

/* 滚动条轨道样式（WebKit 内核浏览器） */
.scrollable::-webkit-scrollbar-track {
	background: transparent;
	border-radius: 5px;
}

/* 滚动条滑块样式（WebKit 内核浏览器） */
.scrollable::-webkit-scrollbar-thumb {
	background: #888;
	border-radius: 5px;
}

/* 滚动条滑块悬停样式（WebKit 内核浏览器） */
.scrollable::-webkit-scrollbar-thumb:hover {
	background: #555;
}

/* Firefox 浏览器滚动条样式 */
.scrollable {
	scrollbar-width: thin;
	scrollbar-color: #888 transparent;
}

/* 隐藏滚动条箭头（在一些有箭头的情况下） */
.scrollable::-webkit-scrollbar-button {
	display: none;
}


@keyframes scale_video_btn {
  0%, 100% {
    transform: scale(1);
}
50% {
    transform: scale(1.2);
}
}