var pro = ["电信全国一区", "网通二区","华东电信一区", "无双网通", "唯美网通", "唯美双线", "网通专区", "华北网通一区", "至尊电信", "超级双线",
    "西南电信一区", "南部电信", "南部电信二区", "纵横双线", "华南电信一区", "东北网通一区", "中西部电信", "东部电信", "唯美电信", "华中电信一区",
    "华北网通二区", "西北电信一区", "超级网通", "超级电信", "网通全国一区", "周年庆专区", "鸿运大区", "人气专区", "绝世双线", "内测服务器"];
var city = [
    [["琴音小筑", 2202], ["九天惊雷", 3079], ["仙侣情缘", 3016], ["天若有情", 3048], ["听香水榭", 2201]],
    [["锦绣中华", 2093]],
    [["武夷山", 168], ["上海滩", 155], ["雨花台", 149], ["双龙洞", 154], ["太湖仙岛", 137]],
    [["独孤求败", 3192]],
    [["武林至尊", 4036]],
    [["王者", 5038], ["绝杀", 5016], ["冰雪奇缘", 5060], ["三世情缘", 5068], ["至尊", 5024], ["天命", 5013], ["千秋殿", 5057], ["醉沙场", 5065]],
    [["杜康", 85], ["三生石", 1042], ["问情崖", 1043], ["百泉书院", 1138], ["太极剑法", 14]],
    [["紫禁之巅", 101], ["什刹海", 1053], ["北戴河", 118], ["八达岭", 111]],
    [["唯我独尊", 3081], ["天外江湖", 3085]],
    [["天龙", 3200], ["天下", 3202]],
    [["剑门蜀道", 198], ["蜀南竹海", 191], ["乐山大佛", 1171], ["忘忧谷", 1161]],
    [["乾坤大挪移", 65]],
    [["烟雨轩", 9140]],
    [["不见不散", 5076], ["清歌遥望月", 5121], ["惜君青玉裳", 5120], ['愿君共白首', 5098], ['以梦为马', 5099], ['逐梦江湖', 5093],
        ['师门逆徒', 5092], ['宁为我道', 5091], ['铜锣湾', 5090], ['在水一方', 5089], ['守望江湖', 5088],
        ["天下为棋", 5069], ["无法无天", 5072], ["一梦十年", 5083]],
    [["桂林山水", 162], ["罗浮山", 172], ["东坡书院", 1191]],
    [["凤凰山", 134], ["水晶湖", 22]],
    [["一阳指", 68]],
    [["云雾茶", 1015], ["烧刀子", 1010], ["神龙摆尾", 47], ["九阴真经", 38]],
    [["狂战天下", 3145], ["仙人指路", 3144]],
    [["洞庭秋月", 182], ["逐鹿中原", 2021], ["乔峰", 2020], ["庐山飞瀑", 188]],
    [["龙腾九天", 2082]],
    [["昆仑山", 209]],
    [["独步江湖", 4025]],
    [["倾国倾城", 3128], ["天下第一", 3125]],
    [["刀光剑影", 3161]],
    [['鹤舞九霄', 5097], ['瑞鹤千秋', 5096], ['三生三世', 5075], ['十里桃花', 5074]],
    [['鸿运当头', 5095], ['鸿运连年', 5094], ["元夕踏灯", 5129], ["华灯如昼", 5127]],
    [['天下会武', 5087], ["金戌迎瑞", 5084]],
    [["碧海潮生", 5126], ["洛城飞雪", 5125], ["潇潇雨幕", 5124], ["箫中剑", 5123], ["桃花影落", 5122]],
    [["龙腾天下", 9031], ["龙啸苍穹", 9015]]
];

$(function(){
    //初始化大区下拉列表
    for(var i=0;i<pro.length;i++){
        $(".zone").append($("<option>").val(i+1).html(pro[i]));
    }
    //为大区下拉列表绑定change事件
    $(".zone").on('change', function () {
        var box = $(this).parents('.searchbox')
        var index=$(this).val()-1;//获取当前省的下标
        box.find(".server").prop("length",1);//清空原有的数据
        if(index == -1){
            return;
        } else if ($(this).val() == '所在大区') {
            box.find(".server").html('<option>所在服务器</option>')
            return;
        }
        for(var i=0;i<city[index].length;i++){//重新为市赋值
            box.find(".server").append($("<option>").val(i + 1).html(city[index][i][0]).attr('group_No', city[index][i][1]));
        }
    });
})
