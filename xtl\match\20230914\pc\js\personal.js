// $(function () {
/**
 * 门派个人赛
 */
var personal = {
	isShare: location.href.indexOf('share') !== -1,
	isQQ: navigator.userAgent.toLowerCase().indexOf(' qq') != -1 && ua.indexOf('mqqbrowser') != -1,
	isWeChat: navigator.userAgent.toLowerCase().indexOf('micromessenger') != -1,
	/** 作品域名白名单 */
	whiteUrl: [
		// 论坛
		'bbs.tl.changyou.com',
		// 抖音
		'www.douyin.com',
		'v.douyin.com',
		// 畅秀阁
		'cxg.changyou.com',
		'm.cxg.changyou.com'
	],
	isBack: false,
	ready: function (isLogin) {
		personal.share();

		if (common.isPersonal) {
			setTimeout(function () {
				Object.keys(common.sectsList).forEach(function (v, i) {
					personal.getSectsPersonalRank(i);
				})
			})
		}
	},
	init: function () {
		// 报名
		$('#btn_joinSectsPersonal').on('click', function () {
			if (common.commHandle()) return;
			if (common.isBindRol == '9') {
				common.tips('不符合报名条件~')
			} else {
				common.isBindRole && $('#pop_sectsPersonal .poptit span').text('报名参加' + common.sectsList[common.isBindRole] + '门派赛道')
				$('#pop_setAvatar img').removeAttr('src');
				$('#btn_setAvatar img').removeAttr('src');
				$('#pop_sectsPersonal input, #pop_sectsPersonal textarea').val('')
				popup('#pop_sectsPersonal')
			}
		})

		// 设置头像弹窗
		$('#btn_setAvatar').on('click', function () {
			personal.imageCropper();
			popup('#pop_setAvatar')
		})

		// 确认头像
		$('#BtnCroper').on('click', function () {
			personal.cropperDate();
		})

		// 提交报名
		$('#btn_submitSectsPersonalFrom').on('click', function () {
			var obj = {
				'image': $('#btn_setAvatar img').attr('src'),
				'slogan': $('#pop_sectsPersonal input:eq(0)').val(),
				'workTitle': $('#pop_sectsPersonal input:eq(1)').val(),
				'workUrl': $('#pop_sectsPersonal textarea').val()
			}
			if (!obj.image) {
				common.tips('请设置您的头像！', true)
			} else if (!obj.slogan || obj.slogan.length > 50) {
				common.tips(!obj.slogan ? "请填写您的参战宣言！" : "请填写50字以内的参战宣言！", true)
			} else if (!obj.workTitle || obj.workTitle.length > 15) {
				common.tips(!obj.workTitle ? "请填写您的作品标题！" : "请填写15字以内的作品标题！", true)
			} else if (obj.workUrl && !personal.verifyUrl(obj.workUrl)) {
				common.tips("请填写有效的作品链接！", true)
			} else {
				personal.submitFrom(obj);
			}
		})

		// 查看详情
		$('.starbox').on('click', '.avtarbox', function () {
			personal.getDetails($(this).data('id'))
		})

		// 点赞
		$('body').on('click', '.hot', function () {
			if (common.commHandle()) return;

			personal.sendScore($(this));
		})

		// 分享
		$('body').on('click', '.wxbtn, .qqbtn', function () {
			if (common.commHandle()) return;

			if (ja.isMobile) {

			} else {
				ja.shareLog(personal.isQQ ? '4': '1');
				popup($(this).hasClass('wxbtn') ? '#pop8' : popup('#pop8_qq'));
			}
		})

		$('#shareTips').on('click', '.hot', function () {
			personal.share();
		})

		// 添加标签
		$('#pop_detail .tiebtn').on('click', function () {
			if (common.commHandle()) return;

			$('#pop_selectLabel .surebtn').attr('data-id', $(this).data('id'))
			popup('#pop_selectLabel');
		})

		// 确认添加
		$('#pop_selectLabel .surebtn').on('click', function () {
			var index = $('#pop_selectLabel li.on').index();

			if (index != -1) {
				personal.addViewLabel($(this).data('id'), (index + 1));
			} else {
				common.tips('请选择要添加的标签~', true);
			}
		})
	},
	/** 登录后 */
	loginAfter: function () {
		personal.myInfo();
	},
	/** 登录前 */
	loginBefore: function () {

	},
	/**
	 * 图片裁切
	 */
	imageCropper: function (ele, input) {
		//图片裁剪
		var imageCropper = $('#imageCropper');
		var options = {
			aspectRatio: 1 / 1, //图片裁剪比例
			//preview: '.img-preview',
			crop: function (e) {

			}
		};
		imageCropper.cropper(options);
		$('#pop_setAvatar input').on('change', function (event) {
			var eve = event || window.event;
			var file = event.currentTarget.files[0];
			var format = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
			var str = 'jpg,jpeg,png';
			if (str.indexOf(format) == -1) {
				alert('请选择jpeg，jpg，png格式的图片');
				return
			}
			var fileSizeBefter = Math.ceil(file.size / 1024);

			if (fileSizeBefter > 3072) {
				alert('请选择小于3M的图片');
				$('#pop_setAvatar input').val("");
				return;
			}
			var reader = new FileReader();
			reader.onload = function () {
				popup('#pop_imageCropper');
				$('#pop_setAvatar input').val("");
				imageCropper.cropper('destroy').attr('src', reader.result).cropper(options);
			};
			if (file) {
				reader.readAsDataURL(file);
			}
		});
	},
	/**
	 * 再次确认裁切的图片
	 * @returns
	 */
	cropperDate: function () {
		var imageSrc = $('#imageCropper').cropper('getCroppedCanvas').toDataURL('image/jpeg');
		// 去掉base64编码中的前缀 data:image/png;base64,
		var tag = "base64,";
		baseStr = imageSrc.substring(imageSrc.indexOf(tag) + tag.length);
		// 去掉base64编码中的“=”号　　
		var eqTagIndex = baseStr.indexOf("=");
		baseStr = eqTagIndex != -1 ? baseStr.substring(0, eqTagIndex) : baseStr;
		// 计算文件流大小
		var strLen = baseStr.length;
		var fileSize = strLen - (strLen / 8) * 2
		if (fileSize / 1024 > 3072) {
			alert('图片过大请重新上传')
			return false;
		}

		$('#pop_setAvatar img').attr('src', imageSrc);
		$('#btn_setAvatar img').attr('src', imageSrc);
		$('#imageCropper').cropper('destroy').removeAttr('src')
		popup('#pop_setAvatar');
	},
	/**
	 * 验证是否白名单网址
	 * @param {*} url
	 * @returns Boolean
	 */
	verifyUrl: function (url) {
		var start = url.indexOf('//');
		if (start != -1) {
			url = url.substring(start + 2)
		}
		var end = url.indexOf('/');
		return personal.whiteUrl.includes(url.substring(0, end));
	},
	/**
	* 门派个人榜
	* @param {String} a 示例
	*/
	getSectsPersonalRank: function (sectID) {
		$.ajax({
			type: 'GET',
			url: common.base_url + 'getSoloOccRank',
			async: false,
			data: {
				'roleOcc': sectID
			},
			success: function (res) {
				// 前置过滤器 处理通用错误码
				if (common.commonErrPop(res)) return;

				if (res.code == 10000) {

					if (res.data.length) {
						var box = $('[data-sectID="' + sectID + '"]'),
							frontRow = box.find('.qiansbox').children(),
							listRow = box.find('.ranklist'),
							list = ``;

						$.each(res.data, function (i, v) {
							if (i < 3) {
								frontRow.eq(i).find('.avtarbox').attr('data-id', v.soloUuid)
									.find('img').attr('src', v.imgUrl);
								frontRow.eq(i).find('.avtarinf p:eq(0)').text(v.serverName)
								frontRow.eq(i).find('.avtarinf p:eq(1)').text(v.roleName)
								frontRow.eq(i).find('.hot').attr('data-id', v.soloUuid)
								frontRow.eq(i).find('.hotnum span').text(v.scoreGet)
							} else {
								list += `<li>
												<div class="avtarbox" data-id="${v.soloUuid}" >
													<span>${i}</span>
													<img src="${v.imgUrl}"onerror="this.src=common.defaultAvatar">
												</div>
												<div class="avtarinf">
													<p>${v.serverName}</p>
													<p>${v.roleName}</p>
												</div>
												<div class="hotbox">
													<span class="hot"></span>
													<p class="hotnum">热度<span>${v.scoreGet}</span></p>
												</div>
											</li>`
							}
						});

						listRow.html(list);
						new common.initPagination(listRow, 3);
					}

				} else {
					switch (parseInt(res.code)) {
						// case 5111:
						// 	common.tips('创建队伍失败，请刷新页面后重试');
						// 	break;
						default:
							common.tips(res.message);
							break;
					}
				}
			}
		})
	},
	/**
	 * 个人赛参赛信息
	 */
	myInfo: function () {
		$.ajax({
			type: 'POST',
			url: common.base_url + 'mySoloInfo',
			async: false,
			success: function (res) {
				// 前置过滤器 处理通用错误码
				if (common.commonErrPop(res)) return;

				if (res.code == 10000) {
					var data = res.data;

					personal.isBack = data.backFlag;

					if (data.joinPrize == 0) {
						$('#joinPrize').removeClass('gray');
					} else if (data.joinPrize == 1) {
						$('#joinPrize').addClass('ylq');
					} else {
						$('#joinPrize').addClass('gray');
					}

					$('.myServer').text(data.serverName);
					$('.myRoleName').text(data.roleName);
					$('.myVotes').text(data.scoreStock);
					switch (data.status) {
						case 1: // 审核中
							$('#btn_joinSectsPersonal, #myInfo').hide()
							$('.pkinfbox, .examineing').show()
							break;
						case 2: // 通过审核
							$('#myRanking').text(data.rank);
							$('#myInfo img').attr('src', data.imgUrl);
							$('#myInfo .hotnum span').text(data.scoreGet);
							$('#myInfo .hot').attr('data-id', data.soloUuid)
							$('#myInfo .toptxt span').text(data.slogan);
							$('#myInfo .bottxt span').text(data.workTitle);
							$('#myInfo .cklink').attr({
								'href': data.workUrl,
								'target': '_blank'
							});
							data.viewLabel.split('#').forEach(function (v) {
								if (v) {
									$('#myInfo .first-say-list').append('<li><span>' + $('.signlist li:eq(' + ++v + ')').text() + '</li></span>')
								}
							})
							if ($('#myInfo .first-say-list').children().length) {
								$('#myInfo .nosign').hide();
								$('#myInfo .roll').show();
								$('#myInfo .btn-ztgd').show();
							}
							$('#btn_joinSectsPersonal, .examineing').hide()
							$('.pkinfbox, #myInfo').show()
							break;
						case 3: // 重新报名
							$('#btn_joinSectsPersonal').addClass('repeatbtn').show();
							$('.pkinfbox').hide()
							break;
					}

				} else {
					switch (parseInt(res.code)) {
						// case 5111:
						// 	common.tips('创建队伍失败，请刷新页面后重试');
						// 	break;
						default:
							common.tips(res.message);
							break;
					}
				}
			}
		})
	},
	/**
	 * 提交报名信息
	 * @param {*} obj
	 */
	submitFrom: function (obj) {
		$.ajax({
			type: 'POST',
			url: common.base_url + 'saveSolo',
			async: false,
			data: obj,
			success: function (res) {
				// 前置过滤器 处理通用错误码
				if (common.commonErrPop(res)) return;

				if (res.code == 10000) {
					personal.myInfo();
					common.tips('报名成功！');
				} else {
					switch (parseInt(res.code)) {
						// case 5111:
						// 	common.tips('创建队伍失败，请刷新页面后重试');
						// 	break;
						default:
							common.tips(res.message);
							break;
					}
				}
			}
		})
	},
	/**
	 * 获取详情
	 * @param {*} id
	 */
	getDetails: function (id) {
		$.ajax({
			type: 'GET',
			url: common.base_url + 'getSoloInfo',
			async: false,
			data: { 'soloUuid': id },
			success: function (res) {
				// 前置过滤器 处理通用错误码
				if (common.commonErrPop(res)) return;

				if (res.code == 10000) {
					var data = res.data;

					data.rank && $('#detailRanking').text(data.rank);
					$('#pop_detail img').attr('src', data.imgUrl)
					$('#detailServer').text(data.serverName)
					$('#detailRoleName').text(data.roleName)
					$('#pop_detail .hotnum span').text(data.scoreGet)
					$('#pop_detail .textareabox').text(data.slogan)
					$('#pop_detail .inputboxlong p').text(data.workTitle)
					$('#pop_detail .inputboxlong a').attr({
						'href': data.workUrl,
						'target': '_blank'
					});
					data.viewLabel.split('#').forEach(function (v) {
						if (v) {
							$('#pop_detail .first-say-list').append('<li><span>' + $('.signlist li:eq(' + ++v + ')').text() + '</li></span>')
						}
					})
					if ($('#pop_detail .first-say-list').children().length) {
						$('#pop_detail .nosign').hide();
						$('#pop_detail .roll').show();
						$('#pop_detail .btn-ztgd').show();
					}
					$('#pop_detail .tiebtn, #pop_detail .hot, #pop_detail .sharebox a').attr('data-id', id);

					popup('#pop_detail');
				} else {
					switch (parseInt(res.code)) {
						// case 5111:
						// 	common.tips('创建队伍失败，请刷新页面后重试');
						// 	break;
						default:
							common.tips(res.message);
							break;
					}
				}
			}
		})
	},
	/**
	 * 添加标签
	 * @param {*} id
	 */
	addViewLabel: function (id, index) {
		$.ajax({
			type: 'POST',
			url: common.base_url + 'addViewLabel',
			async: false,
			data: {
				'soloUuid': id,
				'viewLabel': index
			},
			success: function (res) {
				// 前置过滤器 处理通用错误码
				if (common.commonErrPop(res)) return;

				if (res.code == 10000) {

					common.tips('添加成功！');

				} else {
					switch (parseInt(res.code)) {
						case 5072:
							common.tips('只能添加一次标签~');
							break;
						default:
							common.tips(res.message);
							break;
					}
				}
			}
		})
	},
	/**
	 * 点赞
	 * @param {*} $ele
	 */
	sendScore: function ($ele) {
		$.ajax({
			type: 'POST',
			url: common.base_url + 'sendScore',
			async: false,
			data: {
				'soloUuid': $ele.data('id'),
				'road': personal.isShare ? '1' : '2'
			},
			success: function (res) {
				// 前置过滤器 处理通用错误码
				if (common.commonErrPop(res)) return;

				if (res.code == 10000) {
					$ele.addClass('on');
					var box = $ele.next('.hotnum').find('span');
					var num = box.text();
					box.text(++num)
					common.tips('赠送成功！');

				} else {
					switch (parseInt(res.code)) {
						case 1001:
							common.tips('火力值不足~');
							break;
						case 5072:
							common.tips('超过给每人可赠送火力值次数~');
							break;
						default:
							common.tips(res.message);
							break;
					}
				}
			}
		})
	},
	share: function (info, onlyLink) {
		if (info) {
			var config = {
				link: location.origin + location.pathname.replace('index', 'share') + '?info=' + info,//分享链接
				title: '引导分享',//分享标题
				desc: '<新天龙八部>门派个人战!',//分享介绍
				imgUrl: 'http://tl.changyou.com/favicon.ico',//分享 icon
			}
			if (onlyLink) {
				return config.link;
			}
			setShareInfo({
				title: config.title, // 分享标题
				summary: config.desc, // 分享内容
				pic: config.imgUrl, // 分享图片
				url: config.link, // 分享链接
			});
			ja.share({
				link: config.link,//分享链接
				title: config.title,//分享标题
				desc: config.desc,//分享介绍
				imgUrl: config.imgUrl,//分享 icon
				hideMenuList: [
					'// 默认展示分享给朋友',
				],
				success: function () {
					//分享成功回调
					!example.hasReceived && example.prize();

					ja.shareLog('2');
					example.share();
				}
			})
		} else {
			var config = {
				link: location.origin + location.pathname,//分享链接
				title: '自主分享',//分享标题
				desc: '<新天龙八部>门派个人战!',//分享介绍
				imgUrl: 'http://tl.changyou.com/favicon.ico',//分享 icon
			}
			setShareInfo({
				title: config.title, // 分享标题
				summary: config.desc, // 分享内容
				pic: config.imgUrl, // 分享图片
				url: config.link, // 分享链接
			});
			ja.share({
				link: config.link,//分享链接
				title: config.title,//分享标题
				desc: config.desc,//分享介绍
				imgUrl: config.imgUrl,//分享 icon
				hideMenuList: [
					'// 默认展示分享给朋友',
				],
				success: function () {
					//分享成功回调
					ja.shareLog('1');
				}
			})
		}
	}
}


// })
