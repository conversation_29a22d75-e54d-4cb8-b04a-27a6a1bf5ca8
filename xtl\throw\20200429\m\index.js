new Vue({
    el: '#boxThrow',
    data: function() {
        return {
            urlPath: '/changyou/xtl/tflottery',
            imgPath: '/act/xtl/throw/20200107/m/img/',
            imgArr: ['img1.jpg', 'img2.jpg', 'img3.jpg'],   // 轮播图片
            defaultGift: '',
            userInfo: {},
            allInfo: {},        // 全服信息 + 预约信息
            newInfo: {},        // 新服信息
            isLogin: undefined,     // 是否登录
            isBaned: undefined,     // 是否封停
            isReceive: undefined,   // 是否拆取礼包
            isActive: undefined,    // 是否在游戏中激活
            isLotting: false,       // 是否在抽奖中...
            isAll: false,           // 是否请求到基础信息
            loading: false,         // 接口请求中...
            status: undefined,      // 1 新服专属 / 2 全服通用
            lotteryData: undefined, // json 数据
            lotteryList: [],        // 抽奖列表（便利用）
            lotteryAll: [],         // 抽奖列表（展开）
            lottObj: undefined,     // 抽奖对象
            giftSelect: undefined,  // 抽中礼品
            giftSelectId: undefined,    // 抽中礼品ID
            formAddr: {             // 地址填写提交内容
                rname: '',
                phone: '',
                address: ''
            }
        }
    },
    created: function() {
        // 轮播
        var timer1 = setTimeout(function () {
            new Swiper('.swiper-container', {
                direction: 'horizontal', // 垂直切换选项
                loop: true, // 循环模式选项
                // 如果需要分页器
                pagination: {
                    el: '.swiper-pagination',
                },
                // 如果需要前进后退按钮
                navigation: {
                    nextEl: '.swiper-button-next',
                    prevEl: '.swiper-button-prev',
                },
            });
            $('.min_title').css('animation', 'shake 1s 0s');
            clearTimeout(timer1);
        }, 1000);

        var timer2 = setTimeout(function () {
            $('.head .title .text').show().css('animation', 'bounceIn 2s 0s');
            clearTimeout(timer2);
        }, 1500);

        this.lottery().init();
    },
    mounted: function() {
        var _this = this;
        ja.config({
            app: 'xtl',
            activity: 'tflottery',
            version: '20191230',
            platform: 'changyou',
            type: 1,
            // isDebug: true,
            ready: function(glob) {
                _this.isLogin = glob.code === 1;
                _this.init();
            }
        })
    },
    methods: {
        // 统一点击事件管理
        $click: function(fun) {
            if(typeof this.isLogin !== 'undefined') {
                switch (ja.glob.code) {
                    case 1:
                        if(typeof this.isBaned !== 'undefined') {
                            if(this.isBaned) {
                                return  alert('您的账号已停用');
                            }
                            if(typeof fun === 'function') {
                                fun();
                            }
                        }
                        break;
                    case 2:
                        ja.login();
                        break;
                    case 1106:
                        alert('活动未开始');
                        break;
                    case 1103:
                        alert('活动已结束');
                        break;
                    case 1102:
                        alert('活动无效');
                        break;
                    default:
                }
            }
        },
        // 弹窗方法 flag 为开关 true/false
        pop: function(flag, num) {
            if(flag) {
                $('.pop').hide();
                popup($('.pop' + num));
            }else {
                hideMask($('.pop'));
            }
        },
        // 初始化执行事件
        init: function() {
            var _this = this;
            this.http().giftList();
            if(this.isLogin) {
                this.userInfo = ja.glob.userInfo;
                ja.user.userStatus(function(res) {
                    _this.isBaned = res.code !== 10000;
                    if(!_this.isBaned) {
                        _this.http().receiveStatus();
                        _this.http().lotStatus();
                    }
                })
            }else {
                this.gift().pop();
                var timer3 = setTimeout(function() {
                    _this.gift().remove();
                    clearTimeout(timer3);
                }, 2000);
            }
        },
        // 登录账号
        login: function() {
            this.$click(function() {
                alert('您已登录');
            })
        },
        // 个人中心
        myCenter: function() {
            this.isAll && this.pop(true, 8);
        },
        // 礼包操作
        gift: function() {
            var _this = this;
            return {
                name: ja.glob.appName + ':' + 'gift',
                set: function() {
                    sessionStorage.setItem(this.name, 1);
                },
                get: function() {
                    return sessionStorage.getItem(this.name);
                },
                remove: function() {
                    sessionStorage.removeItem(this.name);
                },
                // 是否隐藏
                isHide: function() {
                    return Number(this.get()) === 1;
                },
                // 弹出弹层
                pop: function() {
                    _this.pop(true, 6);
                },
                // 显示
                show: function() {
                    _this.$click(function() {
                        if(typeof _this.isReceive !== 'undefined') {
                            if(!_this.isReceive) {
                                _this.gift().pop();
                            }else {
                                alert('您已领取礼包');
                            }
                        }
                    })
                },
                // 关闭
                close: function() {
                    _this.isLogin && this.set();
                },
                // 拆开
                open: function() {
                    if(_this.isLogin === false) {
                        var cyscid = ja.glob.cyscid;
                        var ID = ja.utils.getQueryString('ID');
                        if(cyscid && ID) {
                            // 抖音/快手 统计
                            if(cyscid.indexOf('douyin') !== -1) {
                                meteor.track("form", {convert_id: ID});
                            }
                            if(cyscid.indexOf('kuaishou') !== -1) {
                                _ks_trace.push({event: 'form', convertId: ID, cb: function(){    console.log('Your callback function here!')}})
                            }
                        }
                    }
                    if(!_this.loading) {
                        _this.$click(function() {
                            _this.loading = true;
                            _this.http().gift(function() {
                                _this.appoint().show();
                                _this.http().receiveStatus();
                            });
                        });
                    }
                },
            }
        },
        // 预约操作
        appoint: function() {
            var _this = this;
            return {
                // 弹窗显示
                show: function() {
                    _this.pop(true, 4);
                },
                // 发送验证码
                code: function() {
                    ja.user.getCode();
                },
                // 提交
                submit: function() {
                    if(!_this.loading) {
                        var phone = $('.input_phone').val(),
                            phoneCode = $('.input_phone_code').val(),
                            errPhone = ja.verify.phone(phone),
                            errPhoneCode = ja.verify.code(phoneCode);
                        if(errPhone) return alert(errPhone);
                        if(errPhoneCode) return alert(errPhoneCode);
                        _this.loading = true;
                        _this.http().appoint({ phone: phone,  code: phoneCode }, function() {
                            alert('绑定成功');
                            ja.utils.refresh();
                        })
                    }
                }
            }
        },
        // 地址操作
        addr: function() {
            var _this = this;
            return {
                // 弹窗显示
                show: function(opt) {
                    _this.pop(true, 1);
                },
                // 弹窗再次展示
                supplyAgain: function(opt) {
                    if(opt.dec) {
                        $('.addr_title').html(opt.title);
                        $('.addr_dec').html(opt.dec).css({padding: '0 85px'});
                    }
                    this.show();
                },
                // 提交
                submit: function() {
                    if(!_this.loading) {
                        var errorPhone = ja.verify.phone(_this.formAddr.phone);
                        if(!_this.formAddr.rname) return alert('请填写姓名');
                        if(errorPhone) return alert(errorPhone);
                        if(!_this.formAddr.address) return alert('请填写地址');
                        _this.formAddr.logId = _this.giftSelectId;
                        _this.loading = true;
                        _this.http().addr(_this.formAddr, function(res) {
                            alert('提交成功');
                            _this.pop(false);
                            _this.http().receiveStatus();
                        })
                    }
                }
            }
        },
        // 抽奖操作
        lottery: function() {
            var _this = this;
            return {
                // 初始化
                init: function() {
                    var timer4 = setTimeout(function() {
                        _this.lottObj = new LottOBJ({
                            obj: $('#lottcon'),
                            cover: $('#lottcon .lott-cover'),
                            count: 9,
                            position: ['100_72_0','276_7_0','453_70_0','548_232_0','516_417_0','373_539_0','185_540_0','40_420_0','7_235_0'],
							//新服坐标
							// position: ['96_65_0','274_0_0','452_65_0','546_230_0','513_416_0','368_537_0','178_537_0','33_415_0','0_229_0'],
                            complete: function () {
                                _this.isLotting = false;
                                if(_this.giftSelect) {
                                    $('.gift_select_name').html(_this.giftSelect.name);
                                    switch (_this.giftSelect.prizeType) {
                                        case 'N': _this.pop(true, 7); break;    // 沒有抽中
                                        case 'R': _this.pop(true, 1); break;    // 实物奖
                                        case 'Q': _this.pop(true, 2); break;    // 虚拟奖
                                        default:
                                    }
                                }
                            }
                        });
                        clearTimeout(timer4);
                    })
                },
                // 新服/全服切换
                tab: function(n) {
                    if(!_this.isLotting) _this.status = n;
                },
                // 抽奖
                start: function(n) {
                    _this.$click(function() {
                        if(_this.isLotting) return false;
                        if(_this.lotteryNum > 0) {
                            _this.isLotting = true;
                            _this.http().lottStart(function(res) {
                                var giftSelect = _this.lottery().json(res.data.lotLotteryLog.prizeCode);
                                _this.giftSelect = giftSelect;
                                _this.giftSelectId = res.data.lotLotteryLog.id;
                                _this.status === 1 ? _this.http().lotStatus() : _this.http().receiveStatus();
                                _this.lottObj.startRoll(giftSelect.order);
                            });
                        }else {
                            if(_this.isReceive && !_this.isActive) {
                                _this.pop(true, 3);
                            }else {
                                alert('没有抽奖资格');
                            }
                        }
                    })
                },
                // 根据 Code 查询信息
                json: function(code) {
                    var json = {};
                    _this.lotteryAll.forEach(function(item) {
                        json[item.id] = item;
                    });
                    return json[code];
                }
            }
        },
        // 接口请求
        http: function() {
            var _this = this;
            return {
                // 获取抽奖信息
                giftList: function() {
                    $.get('./data.json', function(res) {
                        _this.lotteryData = res;
                        _this.status = 1;
                    })
                },
                // 查询奖励领取状态
                receiveStatus: function() {
                    ja.get(_this.urlPath + '/receiveStatus', function(res) {
                        if(res.code === 10000) {
                            var info = res.data;
                            _this.isAll = true;
                            _this.allInfo = info;
                            _this.isReceive = Number(info.receiveStatus) === 1;     // 是否已领取
                            _this.isActive = Number(info.activeStatus) === 1;       // 是否在游戏中激活
                            if(!_this.isReceive && !_this.gift().isHide()) {        // 如果没有领取 且 没有主动关闭，显示礼品弹窗
                                _this.gift().show();
                            }
                            if(info.prizeType === 'R') {        // 如果中实物奖励 且 没有填写地址，弹出地址弹窗
                                _this.giftSelectId = info.id;
                                if(!info.hasAddress && !_this.isLotting) {
                                    _this.giftSelect = _this.lottery().json(info.prizeCode);
                                    _this.addr().supplyAgain({
                                        title: '提示',
                                        dec: '请填写您的手机号和联系方式，以便我们及时给您发放奖品！',
                                        name: info.prizeName
                                    });
                                }
                            }
                        }
                    })
                },
                // 查询新服抽奖次数和是否填写地址
                lotStatus: function() {
                    ja.get(_this.urlPath + '/newserver/lotStatus', function(res) {
                        if(res.code === 10000) {
                            _this.newInfo = res.data;
                        }
                    })
                },
                // 抽奖接口调用
                lottStart: function(fun) {
                    var url = _this.status === 1 ? '/newserver/lottery' : '/condition/lottery',
                        type = _this.status === 1 ? 'post' : 'get';
                    ja[type](_this.urlPath + url, function(res) {
                        if(res.code === 10000) {
                            fun && fun(res)
                        }else {
                            alert('请求失败，请稍后再试');
                        }
                    })
                },
                // 提交领取
                gift: function(fun) {
                    ja.post(_this.urlPath + '/start', function(res) {
                        _this.loading = false;
                        if(res.code === 10000) {
                            fun && fun(res);
                        }else {
                            alert('系统繁忙,请稍后再试');
                        }
                    })
                },
                // 提交预约
                appoint: function(params, fun) {
                    ja.post(_this.urlPath + '/phone', params, function(res) {
                        _this.loading = false;
                        if(res.code === 10000) {
                            fun && fun(res);
                        }else {
                            alert('绑定失败，请稍后再试');
                        }
                    })
                },
                // 提交地址
                addr: function(params, fun) {
                    ja.post(_this.urlPath + '/saveAddr', params, function(res) {
                        _this.loading = false;
                        if(res.code === 10000) {
                            fun && fun(res)
                        }else {
                            alert('提交失败，请稍后再试');
                        }
                    })
                }
            }
        }
    },
    computed: {
        // 抽奖次数
        lotteryNum: function() {
            return (this.status === 1 ? this.newInfo.lotteryNum : this.allInfo.lotteryNum) || 0;
        }
    },
    filters: {
        // 时间戳转化时间
        formatDateTime: function(val) {
            function getTwo(n) {
                n = n.toString();
                return n[1] ? n : "0" + n;
            }
            var date = new Date(val),
                y = date.getFullYear(),
                m = getTwo(date.getMonth() + 1),
                d = getTwo(date.getDate()),
                hour = getTwo(date.getHours()),
                minute = getTwo(date.getMinutes()),
                second = getTwo(date.getSeconds());
            return y + '-' + m + '-' + d + ' ' + hour + ':' + minute + ':' + second
        }
    },
    watch: {
        // 状态监听
        status: function(val) {
            this.lotteryList = this.lotteryData[val === 1 ? 'new' : 'all'];
            for(var i in this.lotteryList) {
                var row = this.lotteryList[i];
                for(var j in row) {
                    this.lotteryAll.push(row[j]);
                }
            }
        }
    }
});