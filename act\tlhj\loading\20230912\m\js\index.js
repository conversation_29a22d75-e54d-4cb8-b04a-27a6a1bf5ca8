'use strict';

$(function() {
    var resObj ={}
    ja.config({
        app: 'tlgl',
        activity: 'toufang',
        version: '20231013',
        platform: 'changyou',
        // isDebug: true
    });

    ja.ready(function(glob) {
        if(glob.isLogin) {
            var enPhone = sessionStorage.getItem('enPhone');
            if(enPhone) {
                getReward(enPhone, function(res) {
                    resObj = res;
                    sessionStorage.removeItem('enPhone');
                    if(res.code === 10000) {
                        showDia('inti_pop');
                    }else if(res.code === 3402) {   // 已经领取
                        showDia('got_pop');
                    }else if(res.code == 5001){
                        alert("已达上限");
                        ja.logout();
                    }else {
                        alert(res.message);
                    }
                })
            }
            $('#login_qq_span').html(glob.userInfo.openid);
            $('.wrap').addClass('logged');
            $('.login_after').show();
        }else {
            $('.login_before').show();
        }
    });
    
    function showDia(id) {
        showDialog.show({
            id: id,
            bgcolor: "#000",
            opacity: 70,
        });
    }

    // 获取CDK
    function getCdk(phone, code, fn) {
        $.ajax({
            url: '/toufang/getCdk',
            type: 'POST',
            data: {
                phone: phone,
                code: code
            },
            success: function(res) {
                $('.btn_submit').removeClass('ho')
                if (res.code === 10000) {
                    //window.bilicm.mbase.report_data();
                    $('.success_text').text('恭喜你已获得怀旧服【重返江湖】礼包卡，礼包卡以短信形式发送您手机号，请注意查收手机短信，您还可以登录游戏账号领取梦回天龙礼包哦，更有天马等你拿~ ')
                }else if(res.code === 5075) {
                    $('.success_text').text('您已领取过怀旧服【重返江湖】礼包卡，礼包卡如下，您还可以登录游戏账号领取梦回天龙礼包哦，更有天马等您拿！')
                }
                if(res.code === 10000 || res.code === 5075) {
                    fn && fn(res.data);
                }else if(res.code === 3002){
                    commTip("短信验证码错误!")
                }else {
                    alert(res.message)
                }
            }
        })
    }

    // 领取奖励
    function getReward(enPhone, fun) {
        $.ajax({
            url: '/toufang/receivePrize',
            type: 'POST',
            data: {
                enPhone: enPhone
            },
            success: function(res) {
                fun && fun(res);
            }
        })
    }

    // 统一验证判断活动状态
    function ladingMsg() {
        switch (ja.glob.code) {
            case 1106: return '活动未开始'; break;
            case 1103: return '活动已结束'; break;
            case 1102: return '活动无效'; break;
            default:
                return false;
        }
    }

    // 发送验证码
    $('.got_yz').click(function() {

        if(ladingMsg()) return alert(ladingMsg());
        var $this = $(this),
            $inputPhone = $(this).parents('.input_box').find('.input_phone'),
            errPhone = ja.phone($inputPhone.val());

        if(errPhone) return alert(errPhone);

        if(!$(this).is('.over')) $(this).addClass('over').html('发送中...');

        var httpInfo = ja.getCode($inputPhone.val());

        if(httpInfo.code === 10000) {
            var codeNum = 60;
            $(this).html('倒计时<span class="second">' + codeNum + 's</span>')
            var timer = setInterval(function () {
                $this.find('.second').text(codeNum + 's');
                console.log(codeNum);
                if (codeNum <= 0) {
                    $this.removeClass('over').html('获取验证码')
                    clearInterval(timer);
                }
                codeNum--;
            }, 1000)
        }else {
            $(this).removeClass('over').html('获取验证码')
        }
    });
    function commTip(txt){
        $("#commTip .txt").text(txt)
        showDia("commTip")
    }
    // 预约提交
    $('.btn_submit').click(function() {

        if(ladingMsg()) return alert(ladingMsg());
        var $inputPhone = $(this).parents('.input_box').find('.input_phone'),
            $inputPhoneCode= $(this).parents('.input_box').find('.input_phone_code'),
            errPhone = ja.phone($inputPhone.val()),
            errPhoneCode = ja.phoneCode($inputPhoneCode.val()),
            $this = $(this);

        if(errPhone) return alert(errPhone);

        if(errPhoneCode) return alert(errPhoneCode);
        if(!$(this).parents(".combox").find(".box_ys").hasClass("on")){
            commTip("请勾选同意隐私协议！")
            return;
        }
        if(!$(this).is('.ho')) $(this).addClass('ho');

        getCdk($inputPhone.val(), $inputPhoneCode.val(), function(info) {
            $('.cdk_show').text(info.cdk);
            sessionStorage.setItem('enPhone', info.enPhone);
            showDia('congr_pop');
        });
    });

    // 领取礼包（登录）
    $('.btn_login').click(function() {
        showDialog.hide();
        ja.login();
    });

    // 关闭
    $('.btn_close').click(function() {
        showDialog.hide();
    });

    // 已登录 - 领取礼包
    $('.draw_btn').click(function() {
        var res = resObj
        if(res.code){
            if(res.code === 10000) {
                showDia('inti_pop');
            }else if(res.code === 3402) {   // 已经领取
                showDia('got_pop');
            }else if(res.code == 5001){
                alert("已达上限")
            }else {
                alert(res.message);
            }
        }else{
            showDia('got_pop');
        }
    });

    // 注销
    $('#dologout').click(ja.logout);

    // 渠道转化
    (function() {
        if(ja.glob.isMobile) {
            var params = ja.glob.params;
            switch (params.CYSCID) {
                case 'dy':      // 抖音
                case 'tt':      // 头条
                    if(params.UID) {
                        meteor.track("form", {convert_id: params.ID});
                    }
                    break;
                case 'tx':      // 腾讯
                case 'pyq':     // 朋友圈
                    if(params.UID) {
                        window.gdt && gdt('track_custom', 'CUSTOM_ACTION_NAME', {'id': params.ID});
                    }
                    break;
                case 'uc':      // UC
                    utq('track', 'Other', params.ID);
                    break;
                case 'bd':      // 百度
                    window._agl && window._agl.push(['track', ['success', {t: 3}]]);
                    break;
            }
        }

    })();

});