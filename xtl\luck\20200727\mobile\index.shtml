﻿<!DOCTYPE HTML>
<html>

<head>
  <meta charset="utf-8">
  <title>纸短情长-《新天龙八部》官方网站</title>
  <meta name="keywords"
    content="天龙,新天龙,天龙八部,新天龙八部,天龙八部3,tl,tlbb,tl3,xtl,网络游戏,网游,武侠,浪漫武侠,武侠游戏,金庸,<PERSON><PERSON>,天龙八部OL,年终vip，vip回馈，猪年珍兽" />
  <meta name="description"
    content="《新天龙八部》2019开年大服：鸿运当头、鸿运连年！专属春节限定外观、国风珍兽、女神相伴、全新春节活动、海量福利，更有每天一部iPhone XS等你来拿！金庸正版授权，大英雄武侠世界，等你为兄弟热血而战。" />
  <meta name="author" content="" />
  <meta name="applicable-device" content="mobile">
  <meta name="viewport"
    content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover">
  <meta content="yes" name="apple-mobile-web-app-capable" />
  <meta content="black" name="apple-mobile-web-app-status-bar-style" />
  <meta content="telephone=no" name="format-detection" />
  <link type="image/x-icon" href="http://xtl.changyou.com/favicon.ico" rel="icon" />
  <link type="image/x-icon" href="http://xtl.changyou.com/favicon.ico" rel="shortcut icon" />
  <link rel="stylesheet" href="/act/xtl/luck/20200727/mobile/css/swiper-4.3.3.min.css">
  <link rel="stylesheet" type="text/css" href="/act/xtl/luck/20200727/mobile/css/index.css?1" />
  <link rel="stylesheet" href="/act/all/cdn/layer/2.2/skin/layer.css">
<!--   <script src="http://***********:8081/target/target-script-min.js#anonymous"></script> -->

  <script type="text/javascript">
    //屏幕适应
    (function (win, doc) {
      if (!win.addEventListener) return;

      function setFont() {
        var html = document.documentElement;
        var k = 640;
        html.style.fontSize = html.clientWidth / k * 100 + "px";
      }

      setFont();
      setTimeout(function () {
        setFont();
      }, 300);
      doc.addEventListener('DOMContentLoaded', setFont, false);
      win.addEventListener('resize', setFont, false);
      win.addEventListener('load', setFont, false);
    })(window, document);

    function IsPC() {
      var userAgentInfo = navigator.userAgent;
      var Agents = new Array("Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod");
      var flag = true;
      for (var v = 0; v < Agents.length; v++) {
        if (userAgentInfo.indexOf(Agents[v]) > 0) { flag = false; break; }
      }
      return flag;
    }
    if (IsPC()) {
      window.location = '../pc/index.shtml';
    }
  </script>
</head>

<body>
  <div class="wrap">
    <div class="bg1">
      <div class="com_tent">
        <h2 class="bt_bg t">纸短情长</h2>
        <div class="xtl_logo_lk">
					<a target="_blank" href="http://tl.changyou.com/index.shtml" title="《新天龙八部》官方网站"></a>
				</div>
        <div class="xtl_logo_bg"></div>
        <img src="//i0.cy.com/xtl/logo/longmen_test_white.png" class="longmen" >
        <a href="JavaScript:;" class="logo_tl sur t">logo</a>
        <!-- 文字 -->
        <div class="page_font">
          <p>七夕相会，纸短情长。<span>8.17~8.31 </span> 参与纸短情长活动，写下你的对你另一半的三行情书，并上传你与另一半的截图即可获得甜蜜奖励,
            参与奔现认证还可获得更多抽奖机会。</p>
        </div>
        <!-- 我要写信 -->
        <div class="my_gb">
          <a href="JavaScript:login();" class="sur t">我要写信</a>
        </div>
        <!-- 登录 -->
        <div class="my_login"><a href="JavaScript:login();">马上登录</a></div>
      </div>
    </div>
    <div class="bg2">
      <div class="com_content">
        <div class="nrUp_title c">
          <div class="title_con sur t g-fl">纸短情长</div>
          <div class="upData_sc g-fr">
            <i class="sur jgIcon"></i>
            上传1~3张截图、写下200字内的情书
          </div>
        </div>

        <div class="search">
          <label>大区</label>
          <select id="province">
            <option value="0" selected>所在大区</option>
          </select>
          <label>服务器</label>
          <select id="city">
            <option value="0" selected>所在服务器</option>
          </select>
          <a class="btn_search_02" href="javascript:;" title="按时间搜索">按时间</a>
          <a class="btn_search_03" href="javascript:;" title="按人气搜索">按人气</a>
          <label>编号搜索</label><input type="text" id="idsearch" maxlength="6">
          <a class="btn_input btn_search_04" href="javascript:;" title="搜索">搜索</a>
          <label>玩家昵称搜索</label><input type="text" id="key_words" maxlength="12">
          <a class="btn_input btn_search_01" href="javascript:;" title="搜索">搜索</a>

        </div>

        <div class="list_con">
          <ul class="c">
             
          </ul>
        </div>
        <p class="pages">
          <a class="btn page_prev" title="上一页" href="javascript:;">上一页</a>
          <em id="page_w">1</em>/<span id="page_ys">1</span>
          <a class="btn page_next" title="下一页" href="javascript:;">下一页</a>
        </p>
      </div>
    </div>
    <div class="bg3">
      <div class="page3_bg gy_1000">
        <div class="page3_title title_880 sur t">七夕抽奖</div>
          <div class="login_box"> 
        <!-- 登录前 -->
        <a href="javascript:;" class="login_btm sur t">登录</a>
        <!-- 登录后 -->
        <div class="hou_login" style="display: none">
            欢迎<span id="name"></span>，抽奖机会<span id="cs_na">0</span>次
            <a href="JavaScript:;" class="gr_zx">【个人中心】</a>
            <a href="JavaScript:;" class="logout">【退出】</a>
          </div>
         </div>  
      </div>

      <div class="lott" id="lottcon">
        <ul class="lott-list c">
          <li>
            <div class="up_dao">
              <img src="/act/xtl/luck/20200727/mobile/img/jping1.jpg" alt="" class="x_ping">
            </div>
            <p>天河灵鹊(7天)</p>
          </li>
          <li>
            <div class="up_dao">
              <img src="/act/xtl/luck/20200727/mobile/img/new_jp1.jpg" alt="" class="x_ping">
            </div>
            <p>云霄羽翼(180天)</p>
          </li>
          <li>
            <div class="up_dao">
              <img src="/act/xtl/luck/20200727/mobile/img/new_jp2.jpg" alt="" class="x_ping">
            </div>
            <p>全门派马克杯</p>
          </li>
          <li>
            <div class="up_dao">
              <img src="/act/xtl/luck/20200727/mobile/img/jping1.jpg" alt="" class="x_ping">
            </div>
            <p>天河灵鹊(30天)</p>
          </li>
          <li>
            <div class="up_dao">
              <img src="/act/xtl/luck/20200727/mobile/img/jping2.png" alt="" class="x_ping">
            </div>
            <p>小蛋蛋鸡玩偶</p>
          </li>
          <li class="play_sp sur">
            <a class="play_lott t sur" href="javascript:lottStart();" title="立即抽奖">开始抽奖</a>
          </li>

          <li>
            <div class="up_dao">
              <img src="/act/xtl/luck/20200727/mobile/img/jping3.jpg" alt="" class="x_ping">
            </div>
            <p>愿君共白首(永久)</p>
          </li>
          <li>
            <div class="up_dao">
              <img src="/act/xtl/luck/20200727/mobile/img/jping6.jpg" alt="" class="x_ping">
            </div>
            <p>真元精珀*3</p>
          </li>
          <li>
            <div class="up_dao">
              <img src="/act/xtl/luck/20200727/mobile/img/jping4.jpg" alt="" class="x_ping">
            </div>
            <p>虹耀石*3</p>
          </li>
          <li>
            <div class="up_dao">
              <img src="/act/xtl/luck/20200727/mobile/img/jping5.jpg" alt="" class="x_ping">
            </div>
            <p>缀梦灵石*5</p>
          </li>
          <li>
            <div class="up_dao">
              <img src="/act/xtl/luck/20200727/mobile/img/xx_cy.jpg" alt="" class="x_ping">
            </div>
            <p>谢谢参与</p>
          </li>
        </ul>
        <div class="lott-cover"></div>
      </div>

      <div class="jpFont-zi" style="display: none">
        恭喜您 抽中<span></span>,奖品可到<a href="JavaScript:;" class="gr_zx">【个人中心】</a>查看
      </div>

    </div>
    <div class="bg4">
      <div class="page4_bg gy_1000">
        <div class="page4_title title_880 sur t">活动奖励</div>
        <div class="mc_list ">
          <ul class="flex">
            <li>
              <img src="/act/xtl/luck/20200727/mobile/img/dz_pic1.png" alt="">
            </li>
            <li>
              <img src="/act/xtl/luck/20200727/mobile/img/dz_pic2.png" alt="">
            </li>
            <li>
              <img src="/act/xtl/luck/20200727/mobile/img/dz_pic3.png" alt="">
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="bg5">
      <div class="page5_bg gy_1000">
        <div class="page5_title title_880 sur t">活动规则</div>
      </div>
      <!--#include virtual="inc/rule.shtml"-->
    </div>

    <!-- 弹窗（账号绑定） -->
    <div class="pop bang" id="pop1">
      <a href="javascript:closeDialog();" class="btn-close t sur"></a>
      <div class="pop-con">
        <div class="layer_com">
          <h3>账号绑定</h3>
          <p>小提示：账号一旦绑定，将无法更换哦~</p>
          <div class="inputLayer">
            <form action="">
              <div class="sel_xz">
                <label>请选择您要绑定的角色</label>
              </div>
              <div class="sel_xz">
                <select class="js_select">
                  <option value="0">请选择角色</option>
                </select>
              </div>
              <div class="subimt">
                <a href="JavaScript:closeDialog();" class="sur t tj_a" id="bang_js">提交</a>
              </div>
            </form>

          </div>
        </div>
      </div>
    </div>

    <!-- 上传认证 -->
    <div >
      <div class="pop up_shang new_pop" id="pop2">
        <a href="javascript:closeDialog();" class="btn-close t sur"></a>
        <div class="pop-con">
          <div class="layer_com">
            <h3>纸短情长</h3>
            <!--<p>小提示：账号一旦绑定，将无法更换哦~</p>-->
            <div class="sc_box">
              <div class="upBx_gb bk_bjuP">
                <div class="font_sc">告白对象：<input type="text" id="name_dx" maxlength="8"></div>
                <div class="font_sc one_updata">上传游戏截图/照片：
                  <s><img src="" alt="" id="pic_one" style="display: none">
                    <em class="del"></em></s>
                  <s><img src="" alt="" id="two_pic" style="display: none">
                      <em class="del"></em></s>
                  <s><img src="" alt="" id="san_pic" style="display: none">
                      <em class="del"></em></s>
                  <a href="JavaScript:;" class="sc_input">
                    <input type="file" id="vfile" value="选择视频" placeholder="选择视频">
                    <span>上传图片</span>
                  </a>
                </div>
                <div class="font_sc">
                  告白内容：

                  <textarea id="text_wb" rows="10" cols="30" maxlength="200" placeholder="不超过200字"
                    style="resize: none;"></textarea>
                </div>
              </div>
              <div class="upBx_bx bk_bj">
                <h3>奔现认证（选填）</h3>
                <div class="p_bing">
                  上传与游戏内另一半奔现认证证明，如结婚照、合影等
                  皆可，即可参与奔现认证。奔现认证成功可以获得额外
                  两次抽奖机会，赢取更多奖励（注：奔现认证证明图片不做展示用）
                </div>
                <div class="font_sc bx_data two_updata">
                  <s><img src="" alt="" id="pic_one1" style="display: none">
                    <em class="del"></em></s>
                  <s><img src="" alt="" id="pic_one2" style="display: none">
                    <em class="del"></em></s>
                  <a href="JavaScript:;" class="sc_input">
                    <input type="file" id="vfile1" value="选择视频" placeholder="选择视频">
                    <span>上传图片</span>
                  </a>
                </div>
                <div class="mobile_yz">
                  <div class="phone_box">
                    <label>手机号：</label>
                    <input type="text" id="phone" placeholder="填写联系方式（手机号）" maxlength="11">
                  </div>
                  <div class="phone_box">
                    <label>验证码：</label>
                    <span class="yzm_box">
                      <input type="text" placeholder="验证码" id="yam_code" maxlength="6">
                      <a href="JavaScript:;">获取验证码</a>
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div class="subimt">
              <a href="JavaScript:save1('save');" class="sur t tj_sy" id="shiyan">提交誓言</a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 个人中心（您还没写下您的情书！） -->
    <div class="pop my_zx" id="pop3">
      <a href="javascript:closeDialog();" class="btn-close t sur"></a>
      <div class="pop-con">
        <div class="layer_com">
          <h3>个人中心</h3>
          <div class="zy_renz c">
            <p class="g-fl">您还没写下您的情书！</p>
            <a href="javascript:OpenDialog('pop2');" class="g-fr">我要去写信</a>
        </div>
        <div class="zy_renz c">
            <p class="g-fl">奔现认证得更多奖励！</p>
            <a href="JavaScript:OpenDialog('pop2');" class="g-fr">我要去认证</a>
        </div>
          <div class="mc_list lay_mc_list">
            <ul class="c">
              <li>
                <h2>
                  <i class="sur_y sur"></i>实物奖励<i class="sur_y sur"></i>
                </h2>
                <em class="shiwu"></em>
                <a href="JavaScript:OpenDialog('pop11');">完善信息</a>
              </li>
              <li>
                <h2>
                  <i class="sur_y sur"></i>虚拟奖励<i class="sur_y sur"></i>
                </h2>
                <em class="xuni"></em>
              </li>
            </ul>
          </div>
          <!-- <div class="subimt">
                    <a href="JavaScript:closeDialog();" class="sur t cg_link">完成</a>
                </div> -->
        </div>
      </div>
    </div>

    <!-- 个人中心（审核中！） -->
    <div class="pop my_zx" id="pop4">
      <a href="javascript:closeDialog();" class="btn-close t sur"></a>
      <div class="pop-con">
        <div class="layer_com">
          <h3>个人中心</h3>
          <div>

          </div>
          <!-- <div class="zy_renz c">
                    <p class="g-fl">审核中，请耐心等待！</p>
                    <a href="JavaScript:;" class="g-fr hui_gao">纸短情长</a>
                </div>
                <div class="zy_renz c">
                    <p class="g-fl">奔现认证得更多奖励！</p>
                    <a href="JavaScript:OpenDialog('pop2');" class="g-fr">我要去认证</a>
                </div> -->
          <div class="mc_list lay_mc_list">
            <ul class="c">
              <li>
                <h2>
                  <i class="sur_y sur"></i>实物奖励<i class="sur_y sur"></i>
                </h2>
                <em class="shiwu"></em>
                <a href="JavaScript:OpenDialog('pop11');">完善信息</a>
              </li>
              <li>
                <h2>
                  <i class="sur_y sur"></i>虚拟奖励<i class="sur_y sur"></i>
                </h2>
                <em class="xuni"></em>
              </li>
            </ul>
          </div>
          
        </div>
      </div>
    </div>


    <!-- 个人中心（您已参与写信成功） -->
    <div class="pop my_zx" id="pop5">
      <a href="javascript:closeDialog();" class="btn-close t sur"></a>
      <div class="pop-con">
        <div class="layer_com">
          <h3>个人中心</h3>
          <p>您已参与写信成功！</p>
          <div class="zy_renz c">
            <p class="g-fl">奔现认证得更多奖励！</p>
            <a href="JavaScript:OpenDialog('pop2');" class="g-fr">我要去认证</a>
          </div>
          <div class="mc_list lay_mc_list">
            <ul class="c">
              <li>
                <h2>
                  <i class="sur_y sur"></i>实物奖励<i class="sur_y sur"></i>
                </h2>
                <em class="shiwu"></em>
                <a href="JavaScript:OpenDialog('pop11');">完善信息</a>
              </li>
              <li>
                <h2>
                  <i class="sur_y sur"></i>虚拟奖励<i class="sur_y sur"></i>
                </h2>
                <em class="xuni"></em>
              </li>
            </ul>
          </div>
          <div class="subimt">
            <a href="JavaScript:closeDialog();" class="sur t cg_link">完成</a>
          </div>
        </div>
      </div>
    </div>

    <!-- 个人中心（您已参与写信成功！） -->
    <div class="pop my_zx" id="pop6">
      <a href="javascript:closeDialog();" class="btn-close t sur"></a>
      <div class="pop-con">
        <div class="layer_com">
          <h3>个人中心</h3>
          <p>您已参与写信成功！<br />奔现认证审核中，请耐心等待！</p>
          <div class="mc_list lay_mc_list">
            <ul class="c">
              <li>
                <h2>
                  <i class="sur_y sur"></i>实物奖励<i class="sur_y sur"></i>
                </h2>
                <em class="shiwu"></em>
                <a href="JavaScript:OpenDialog('pop11');">完善信息</a>
              </li>
              <li>
                <h2>
                  <i class="sur_y sur"></i>虚拟奖励<i class="sur_y sur"></i>
                </h2>
                <em class="xuni"></em>
              </li>
            </ul>
          </div>
          <div class="subimt">
            <a href="JavaScript:closeDialog();" class="sur t cg_link">完成</a>
          </div>
        </div>
      </div>
    </div>

    <!-- 个人中心（您已参与写信成功！ 奔现认证审核成功！） -->
    <div class="pop my_zx" id="pop7">
      <a href="javascript:closeDialog();" class="btn-close t sur"></a>
      <div class="pop-con">
        <div class="layer_com">
          <h3>个人中心</h3>
          <p>您已参与写信成功！<br />奔现认证审核成功！</p>
          <div class="mc_list lay_mc_list">
            <ul class="c">
              <li>
                <h2>
                  <i class="sur_y sur"></i>实物奖励<i class="sur_y sur"></i>
                </h2>
                <em class="shiwu"></em>
                <a href="JavaScript:OpenDialog('pop11');">完善信息</a>
              </li>
              <li>
                <h2>
                  <i class="sur_y sur"></i>虚拟奖励<i class="sur_y sur"></i>
                </h2>
                <em class="xuni"></em>
              </li>
            </ul>
          </div>
          <div class="subimt">
            <a href="JavaScript:closeDialog();" class="sur t cg_link">完成</a>
          </div>
        </div>
      </div>
    </div>

    <!-- 个人中心（您已参与写信成功！ 奔现认证审核成功！） -->
    <div class="pop my_zx" id="pop8">
      <a href="javascript:closeDialog();" class="btn-close t sur"></a>
      <div class="pop-con">
        <div class="layer_com">
          <h3>个人中心</h3>
          <p>您已参与写信成功！<br />奔现认证审核成功！</p>
          <div class="mc_list lay_mc_list">
            <ul class="c">
              <li>
                <h2>
                  <i class="sur_y sur"></i>实物奖励<i class="sur_y sur"></i>
                </h2>
                <em class="shiwu"></em>
                <a href="JavaScript:OpenDialog('pop11');">完善信息</a>
              </li>
              <li>
                <h2>
                  <i class="sur_y sur"></i>虚拟奖励<i class="sur_y sur"></i>
                </h2>
                <em class="xuni"></em>
              </li>
            </ul>
          </div>
          <div class="subimt">
            <a href="JavaScript:closeDialog();" class="sur t cg_link">完成</a>
          </div>
        </div>
      </div>
    </div>

    <!-- 个人中心（很抱歉，您参与写信失败！） -->
    <div class="pop my_zx" id="pop9">
      <a href="javascript:closeDialog();" class="btn-close t sur"></a>
      <div class="pop-con">
        <div class="layer_com">
          <h3>个人中心</h3>
          <p>很抱歉，您参与写信失败！</p>
          <div class="zy_renz c">
            <p class="g-fl">奔现认证得更多奖励！</p>
            <a href="JavaScript:;" class="g-fr hui_gao">我要去认证</a>
          </div>
          <div class="mc_list lay_mc_list">
            <ul class="c">
              <li>
                <h2>
                  <i class="sur_y sur"></i>实物奖励<i class="sur_y sur"></i>
                </h2>
                <em class="shiwu"></em>
                <a href="JavaScript:OpenDialog('pop11');">完善信息</a>
              </li>
              <li>
                <h2>
                  <i class="sur_y sur"></i>虚拟奖励<i class="sur_y sur"></i>
                </h2>
                <em class="xuni"></em>
              </li>
            </ul>
          </div>
          <div class="subimt">
            <a href="JavaScript:closeDialog();" class="sur t cg_link">完成</a>
          </div>
        </div>
      </div>
    </div>

    <!-- 个人中心（您已参与写信成功！） -->
    <div class="pop my_zx" id="pop10">
      <a href="javascript:closeDialog();" class="btn-close t sur"></a>
      <div class="pop-con">
        <div class="layer_com">
          <h3>个人中心</h3>
          <p>您已参与写信成功！<br />很抱歉，您参与的奔现认证审核未通过！</p>
          <div class="mc_list lay_mc_list">
            <ul class="c">
              <li>
                <h2>
                  <i class="sur_y sur"></i>实物奖励<i class="sur_y sur"></i>
                </h2>
                <em class="shiwu"></em>
                <a href="JavaScript:OpenDialog('pop11');">完善信息</a>
              </li>
              <li>
                <h2>
                  <i class="sur_y sur"></i>虚拟奖励<i class="sur_y sur"></i>
                </h2>
                <em class="xuni"></em>
              </li>
            </ul>
          </div>
          <div class="subimt">
            <a href="JavaScript:xinxi();" class="sur t cg_link">完成</a>
          </div>
        </div>
      </div>
    </div>
    <!-- 恭喜您抽中实物礼品！ -->
    <div class="pop my_zx" id="pop11">
      <a href="javascript:closeDialog();" class="btn-close t sur"></a>
      <div class="pop-con">
        <div class="layer_com">
          <h3>恭喜您抽中<span class="shiwu_box">实物礼品</span>！</h3>
          <div class="sm_duan">
            <p class="c">1、请务必填写真实信息，<span style="color: #f04f53">提交后不可修改</span></p>
            <p class="c"><span class="g-fl">2、</span><span class="g-fl fr_span">因个人原因导致奖品无法签收，而被快递退回的奖品
                将视为自动放弃获奖资格。</span></p>
          </div>
          <div class="formSu">
            <form action="">
              <div class="formSu_in">
                <label>姓名：</label>
                <input type="text" id="name_tj" maxlength="5">
              </div>
              <div class="formSu_in">
                <label>电话：</label>
                <input type="text" id="phone_tj" maxlength="11">
              </div>
              <div class="formSu_in city_tjBox">
                  <label>地址：</label>
                  <textarea  id="city_tj" maxlength="120"></textarea>
              </div>
              <div class="subimt">
                <a href="JavaScript:xinxi();" class="sur t cg_link">完成</a>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- 恭喜您信息提交成功 -->
    <div class="pop tj_gc" id="pop12">
      <a href="javascript:closeDialog();" class="btn-close t sur"></a>
      <div class="pop-con">
        <div class="layer_com">
          <h3>信息提交成功！</h3>
          <p>我们将于活动结束后15个工作日内通过快递将奖品送出</p>
        </div>
      </div>
    </div>

    <!-- 您的信息已完善！ -->
    <div class="pop tj_gc" id="pop13">
      <a href="javascript:closeDialog();" class="btn-close t sur"></a>
      <div class="pop-con">
        <div class="layer_com wan_s">
          <h3>您的信息已完善！</h3>
        </div>
      </div>
    </div>

    <!-- 奔现被拒绝！ -->
    <div class="pop tj_gc" id="pop15">
      <a href="javascript:closeDialog();" class="btn-close t sur"></a>
      <div class="pop-con">
        <div class="layer_com wan_s">
          <h3>奔现认证被拒绝，请重新填写！</h3>
        </div>
      </div>
    </div>
    <!-- swiper 轮播 -->
    <div class="pop swiper_lb" id="pop14">
      <a href="javascript:closeDialog();" class="btn-close t sur" id="remove_swiper"></a>
      <div class="pop-con">
          <div class="swiper_layer">
              <div class="swiper-container">
                  <div class="swiper-wrapper" id="weyi_sw">
                      <!--<div class="swiper-slide"><img src="/act/xtl/luck/20190702/mobile/img/bg1.jpg" alt="灯笼"></div>-->
                      <!--<div class="swiper-slide"><img src="/act/xtl/luck/20190702/mobile/img/bg1.jpg" alt="灯笼"></div>-->
                  </div>
              </div>
              <div class="pagination"></div>
          </div>
          <div class="sx_link c">
              <a href="JavaScript:;" class="pevr_s t sur g-fl">上一页</a>
              <a href="JavaScript:;" class="next_x t sur g-fr">下一页</a>
          </div>
      </div>
  </div>
  <!-- 通用中奖虚拟奖提示 -->
  <div class="pop commonPop" id="pop17">
    <a href="javascript:closeDialog();" class="btn-close t sur"></a>
    <div class="pop-con">
        <div class="layer_com">
            <h3></h3>
            <p class="jp_show"></p>
        </div>
    </div>
  </div>

    <!--图片裁剪框 start-->
    <div class="yc-upload-wrap" style="display: none">
      <div class="yc-upload-box">
        <div class="container">
          <div class="row">
            <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0;">
              <div class="ycupload-mainbox">
                <div class="ycupload-line"></div>
                <div style="height:30px;"></div>
                <div style="min-height:1px;">
                  <div class="container">
                    <div class="row">
                      <div class="col-md-12 col-sm-12 col-xs-12" style="padding-right:0;padding-left:36px;">
                        <!--<a href="javascript:void(0);" class="cover-uploadBtn">
                                            <img src="img/yc_uploadimg_06.png">
                                            <div id="clipArea"></div>
                                            <input type="file" id="file">
                                            <button id="clipBtn">截取</button>
                                        </a>
                                        <div id="view"></div>-->
                        <div style="min-height:1px;line-height:160px;text-align:center;position:relative;"
                          ontouchstart="">
                          <div class="cover-wrap"
                            style="display:none;position:fixed;left:0;top:0;width:100%;height:100%;background: rgba(0, 0, 0, 0.4);z-index: 10000000;text-align:center;">
                            <div class=""
                              style="width:100%;height:7.5rem;margin:100px auto;background-color:#FFFFFF;overflow: hidden;border-radius:4px;">
                              <div id="clipArea" style="margin:10px;height: 6.3rem;">
                              </div>
                              <div class="one_but"
                                style="height:56px;line-height:36px;text-align: center;padding-top:8px; ">
                                <button id="clipBtn"
                                  style="width:120px;height: 36px;border-radius: 4px;background-color:#ff8a00;color: #FFFFFF;font-size: 14px;text-align: center;line-height: 36px;outline: none;border: none">保存截图</button>
                                <button class="quxiao"
                                  style="width:120px;height: 36px;border-radius: 4px;background-color:#ff8a00;color: #FFFFFF;font-size: 14px;text-align: center;line-height: 36px;outline: none;border: none">取消</button>
                              </div>
                              <div class="two_but"
                                style="height:56px;line-height:36px;text-align: center;padding-top:8px; display: none">
                                <button id="clipBtn1"
                                  style="width:120px;height: 36px;border-radius: 4px;background-color:#ff8a00;color: #FFFFFF;font-size: 14px;text-align: center;line-height: 36px;outline: none;border: none">保存截图</button>
                                <button class="quxiao"
                                  style="width:120px;height: 36px;border-radius: 4px;background-color:#ff8a00;color: #FFFFFF;font-size: 14px;text-align: center;line-height: 36px;outline: none;border: none">取消</button>
                              </div>
                            </div>
                          </div>
                          <div id="view" style="width:214px;height:160.5px;" title="请上传 428*321 的封面图片"></div>
                          <div style="height:10px;"></div>

                        </div>


                      </div>
                    </div>
                  </div>

                </div>
                <div style="height:25px;"></div>
              </div>

            </div>
          </div>
        </div>

      </div>
    </div>

    <!--图片裁剪框1 start-->
    <div class="yc-upload-wrap1" style="display: none">
      <div class="yc-upload-box">
        <div class="container">
          <div class="row">
            <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0;">

              <div class="ycupload-mainbox">
                <div class="ycupload-line"></div>
                <div style="height:30px;"></div>
                <div style="min-height:1px;">
                  <div class="container">
                    <div class="row">
                      <div class="col-md-12 col-sm-12 col-xs-12" style="padding-right:0;padding-left:36px;">
                        <!--<a href="javascript:void(0);" class="cover-uploadBtn">
                                            <img src="img/yc_uploadimg_06.png">
                                            <div id="clipArea"></div>
                                            <input type="file" id="file">
                                            <button id="clipBtn">截取</button>
                                        </a>
                                        <div id="view"></div>-->
                        <div style="min-height:1px;line-height:160px;text-align:center;position:relative;"
                          ontouchstart="">
                          <div class="cover-wrap"
                            style="display:none;position:fixed;left:0;top:0;width:100%;height:100%;background: rgba(0, 0, 0, 0.4);z-index: 10000000;text-align:center;">
                            <div class=""
                              style="width:100%;height:7.5rem;margin:100px auto;background-color:#FFFFFF;overflow: hidden;border-radius:4px;">
                              <div id="clipArea1" style="margin:10px;height: 6.3rem;">
                              </div>
                              <div class="one_but"
                                style="height:56px;line-height:36px;text-align: center;padding-top:8px; ">
                                <button id="clipBtn"
                                  style="width:120px;height: 36px;border-radius: 4px;background-color:#ff8a00;color: #FFFFFF;font-size: 14px;text-align: center;line-height: 36px;outline: none;border: none">保存截图</button>
                                <button class="quxiao"
                                  style="width:120px;height: 36px;border-radius: 4px;background-color:#ff8a00;color: #FFFFFF;font-size: 14px;text-align: center;line-height: 36px;outline: none;border: none">取消</button>
                              </div>
                              <div class="two_but"
                                style="height:56px;line-height:36px;text-align: center;padding-top:8px; display: none">
                                <button id="clipBtn2"
                                  style="width:120px;height: 36px;border-radius: 4px;background-color:#ff8a00;color: #FFFFFF;font-size: 14px;text-align: center;line-height: 36px;outline: none;border: none">保存截图</button>
                                <button class="quxiao"
                                  style="width:120px;height: 36px;border-radius: 4px;background-color:#ff8a00;color: #FFFFFF;font-size: 14px;text-align: center;line-height: 36px;outline: none;border: none">取消</button>
                              </div>
                            </div>
                          </div>
                          <div id="view" style="width:214px;height:160.5px;" title="请上传 428*321 的封面图片"></div>
                          <div style="height:10px;"></div>

                        </div>


                      </div>
                    </div>
                  </div>

                </div>
                <div style="height:25px;"></div>
              </div>

            </div>
          </div>
        </div>

      </div>
    </div>
    
    <script src="/act/xtl/luck/20200727/mobile/js/jquery-1.8.3.min.js"></script>
    <script src="/act/all/cdn/layer/2.2/layer.js"></script>
    <script type="text/javascript" src="/act/xtl/luck/20200727/pc/js/base64.min.js"></script>
    <script src="/js/join-activity-1.0.0.js"></script>
    <script src="/act/xtl/luck/20200727/mobile/js/swiper.min.js"></script>
    <script src="/act/xtl/luck/20200727/pc/js/menu.js"></script>
    <script src="/act/xtl/luck/20200727/mobile/js/comm.js"></script>
    <script src="/act/xtl/luck/20200727/mobile/js/data.js"></script>
    <script src="/act/xtl/luck/20200727/mobile/js/iscroll-zoom.js?" type="text/javascript" charset="utf-8"></script>
    <script src="/act/xtl/luck/20200727/mobile/js/hammer.js?" type="text/javascript" charset="utf-8"></script>
    <script src="/act/xtl/luck/20200727/mobile/js/lrz.all.bundle.js?" type="text/javascript" charset="utf-8"></script>
    <script src="/act/xtl/luck/20200727/mobile/js/jquery.photoClip.min.js?" type="text/javascript"
      charset="utf-8"></script>

    <script type="text/javascript">
      // OpenDialog('pop14');

      //挡住inputf
      var inp = document.querySelector('#yam_code');
      var bodyHeight = document.body.offsetHeight;
      inp.onclick = function (ev) {
        document.querySelector('body').style.height = '9999px';
        setTimeout(function () {
          document.body.scrollTop = document.documentElement.scrollTop = inp.getBoundingClientRect().top + pageYOffset - 5;
        }, 50);
        window.addEventListener('touchmove', fn, false);
      }

      inp.onblur = function () {
        document.querySelector('body').style.height = "auto"
        document.querySelector('body').removeAttribute('style')
        window.removeEventListener('touchmove', fn, false)
      }
      //触摸取消blur
      function fn(ev) {
        var _target = ev.target || ev.srcElement;
        if (_target.nodeName != 'INPUT') { inp.blur(); }
        ev.preventDefault()
      };

      //swiper
      function swiper() {
        mySwiper = new Swiper('.swiper-container', {
          navigation: {
            nextEl: '.next_x',
            prevEl: '.pevr_s',
          },
          paginationClickable: true,
          observer: true,//修改swiper自己或子元素时，自动初始化swiper
          observeParents: true,//修改swiper的父元素时，自动初始化swiper
          pagination: {
            el: '.pagination',
          },
          on: {
            slideChangeTransitionStart: function () {
              $('.pagination span').eq(this.activeIndex).addClass('swiper-pagination-bullet-active').siblings().removeClass('swiper-pagination-bullet-active');
            },
          }
        });
      }
      swiper();
      $(document).on("click", ".tu_list", function (e) {
        $('#weyi_sw').html(' ')
        var img = $(this).find('img');
        if (img.length > 1) {
          OpenDialog('pop14');
          img.each(function (index) {
            var root_img = $(this).context.currentSrc;
            $('#weyi_sw').append('<div class="swiper-slide"><img src="' + root_img + '" alt="灯笼"></div>')
            // $('.swiper-wrapper .swiper-slide').eq(index).find('img').attr('src',root_img);
          });
          setTimeout(function () {
            swiper();
          }, 10);
        } else {
          alert('该用户上传的图片小于两张')
        }
      });

      $('#remove_swiper').click(function () {
        mySwiper.slideTo(0);
        $('#weyi_sw').html(' ')
      });


      $('#vfile').click(function () {
        $('.two_but').hide();
        $('.one_but').show();
      });
      $('#vfile1').click(function () {
        $('.two_but').show();
        $('.one_but').hide();
      });
      $('.quxiao').click(function () {
        $('.cover-wrap').fadeOut();
      });
      //上传封面
      //document.addEventListener('touchmove', function (e) { e.preventDefault(); }, false);
      new bjj.PhotoClip("#clipArea", {
        size: [390, 284],// 截取框的宽和高组成的数组。默认值为[260,260]
        outputSize: [360, 284], // 输出图像的宽和高组成的数组。默认值为[0,0]，表示输出图像原始大小
        outputType: "jpg", // 指定输出图片的类型，可选 "jpg" 和 "png" 两种种类型，默认为 "jpg"
        file: "#vfile", // 上传图片的<input type="file">控件的选择器或者DOM对象
        ok: "#clipBtn", // 确认截图按钮的选择器或者DOM对象
        loadStart: function (file) {
          var Fsize = file.size / 1024;
          if (Fsize >= 5120) {
            //window.alert('请上传5M以下图片')
            layer.msg('请上传5M以下图片')
            return false;
          }
          $('.yc-upload-wrap').show()
          $('.cover-wrap').fadeIn();
          $('.photo-clip-rotateLayer img').attr('src', '');
          console.log("照片读取中");
        },
        loadComplete: function (d) {
          // 加载完成的回调函数。this指向图片对象，并将图片地址作为参数传入
          console.log("照片读取完成");
        },
        clipFinish: function (dataURL) {
          // 裁剪完成的回调函数。this指向图片对象，会将裁剪出的图像数据DataURL作为参数传入
          $('.cover-wrap').fadeOut();
          $('.yc-upload-wrap').hide();
          $('.photo-clip-rotateLayer img').attr('src', '');
          if ($('#pic_one').is(":visible") && $('#two_pic').is(":visible") && $('#san_pic').is(":visible")) {
            alert('纸短情长最多只能上传三张图片');
            return false;
          }
          if ($('#pic_one').is(":hidden")) {
            $('#pic_one').show();
            $('#pic_one').next('em').show();
            $("#pic_one").prop("src", dataURL);//显示为图片的形式
          } else if ($('#two_pic').is(":hidden")) {
            $('#two_pic').show();
            $('#two_pic').next('em').show();
            $("#two_pic").prop("src", dataURL);//显示为图片的形式
          } else {
            $('#san_pic').show();
            $('#san_pic').next('em').show();
            $("#san_pic").prop("src", dataURL);//显示为图片的形式
          }

        }
      });

      new bjj.PhotoClip("#clipArea1", {
        size: [390, 284],// 截取框的宽和高组成的数组。默认值为[260,260]
        outputSize: [360, 284], // 输出图像的宽和高组成的数组。默认值为[0,0]，表示输出图像原始大小
        outputType: "jpg", // 指定输出图片的类型，可选 "jpg" 和 "png" 两种种类型，默认为 "jpg"
        file: "#vfile1", // 上传图片的<input type="file">控件的选择器或者DOM对象
        ok: "#clipBtn2", // 确认截图按钮的选择器或者DOM对象
        loadStart: function (file) {
          var Fsize = file.size / 1024;
          if (Fsize >= 5120) {
            layer.msg('请上传5M以下图片')
            return false;
          }

          $('.yc-upload-wrap1').show();
          $('.cover-wrap').fadeIn();
          $('.photo-clip-rotateLayer img').attr('src', '');
          console.log("照片读取中");
        },
        loadComplete: function () {
          // 加载完成的回调函数。this指向图片对象，并将图片地址作为参数传入
          console.log("照片读取完成");
        },
        //loadError: function(event) {}, // 加载失败的回调函数。this指向 fileReader 对象，并将错误事件的 event 对象作为参数传入
        clipFinish: function (dataURL) {
          console.log(this);
          // 裁剪完成的回调函数。this指向图片对象，会将裁剪出的图像数据DataURL作为参数传入
          $('.cover-wrap').fadeOut();
          $('.yc-upload-wrap1').hide();
          $('.photo-clip-rotateLayer img').attr('src', '');
          if ($('#pic_one1').is(":visible") && $('#pic_one2').is(":visible")) {
            alert('奔现认证只允许上传两张图');
            return false;
          }
          if ($('#pic_one1').is(":visible")) {
            $('#pic_one2').show();
            $('#pic_one2').next('em').show();
            $("#pic_one2").prop("src", dataURL);//显示为图片的形式
          } else {
            $('#pic_one1 ').show();
            $('#pic_one1 ').next('em').show();
            $("#pic_one1").prop("src", dataURL);//显示为图片的形式
          }
        }
      });
      $("em.del").click(function(){
            $(this).prev("img").attr("src",'').hide();
            $(this).hide();
        })
    </script>
    <!--#include virtual="/all/dma/dma_activity.html"-->
    <!-- 监测代码文件碎片-->
</body>

</html>