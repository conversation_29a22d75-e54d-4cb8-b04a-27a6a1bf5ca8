<!DOCTYPE HTML>
<html>

<head>
	<meta charset="utf-8">
	<title>《天龙八部·归来》账号鉴定</title>
	<meta name="keywords" content="天龙八部归来，天龙八部端游，新天龙八部，经典天龙八部，怀旧天龙八部，福利，兄弟，黄金，宝石，账号鉴定，称号，" />
	<meta name="description" content="来看看你的天龙账号究竟什么水平，点击领取账号奖励和专属称号！" />
	<meta content="width=750,minimum-scale=0,maximum-scale=1,user-scalable=no" name="viewport" />
	<meta name="referrer" content="no-referrer-when-downgrade">
	<meta content="yes" name="apple-mobile-web-app-capable" />
	<meta content="black" name="apple-mobile-web-app-status-bar-style" />
	<meta content="telephone=no" name="format-detection" />
	<meta name="author" content="" />
	<meta name="robots" content="index, follow" />
	<meta name="googlebot" content="index, follow" />
	<link type="image/x-icon" href="//tlbbgl.changyou.com/favicon.ico" rel="icon" />
	<link type="image/x-icon" href="//tlbbgl.changyou.com/favicon.ico" rel="shortcut icon" />
	<link href="/act/all/css/reset.css" rel="stylesheet" type="text/css" />
	<link href="/act/all/cdn/Swiper/4.5.0/css/swiper.min.css" rel="stylesheet" type="text/css" />
	<link href="/act/tlbbgl/zhjd/********/m/css/common.css" rel="stylesheet" type="text/css" />
	<link href="/act/tlbbgl/zhjd/********/m/css/index.css" rel="stylesheet" type="text/css" />
<!--	<script class="external" src="//zhcdn01.xoyo.com/xassets/lib/vconsole/v3/vconsole.min.js"></script>-->
<!--	<script>-->
<!--		new VConsole();-->
<!--	</script>-->

	<script>
		//移动端版本兼容
		var phoneWidth = parseInt(window.screen.width);
		var phoneScale = phoneWidth / 750;

		var ua = navigator.userAgent;
		if (/Android (\d+\.\d+)/.test(ua)) {
			var version = parseFloat(RegExp.$1);
			// andriod 2.3
			if (version > 2.3) {
				document.write('<meta name="viewport" content="width=750, minimum-scale = ' + phoneScale +
						', maximum-scale = ' + phoneScale + ', target-densitydpi=device-dpi">');
				// andriod 2.3以上
			} else {
				document.write('<meta name="viewport" content="width=750, target-densitydpi=device-dpi">');
			}
			// 其他系统
		} else {
			document.write('<meta name="viewport" content="width=750, user-scalable=no, target-densitydpi=device-dpi">');
		}
	</script>
	<script type="text/javascript">
		var deviceWidth = document.documentElement.clientWidth;
		if (deviceWidth > 750) {
			deviceWidth = 750
		};
		document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
	</script>
</head>

<body>
<div class="wrap " id="main">

	<!-- page1 首屏-->
	<div class="page page1 contbox" id="page1">

		<!-- 加载+动画 -->
		<div class="start_box_page">
			<div class="start_box">
				<span class="start_tip bg t"></span>
				<a href="javascript:;" class="btn_start bg t"></a>
				<!--					<a href="javascript:;" class="btn_rule t">活动规则</a>-->

				<div class="btn_agree_wrap">
					<a href="javascript:" class="btn_agree bg ">请您认真阅读并勾选同意<span class="btn_rule">活动规则</span>后再参与活动</a>
				</div>
			</div>
		</div>
	</div>


	<div class=" swiper-container page page2 " id="page2">
		<div class="swiper-wrapper">
			<!-- 场景页、鉴定页 -->
			<div class="scence_wrap swiper-slide">
				<div class="scence_box scence1 ">
					<a href="javascript:;" class="btn_rule_link bg t no-swiping">活动规则</a>
					<div class="login-info no-swiping">
						<span class="userinfo J_username">-</span>，<a href="javascript:app.logout();" class="btn-logout">退出</a>
					</div>
					<div class="scence_txt"></div>
					<div class="scence_role bg"></div>
					<a href="javascript:" class="btn_next">点击进入下一页</a>
				</div>
			</div>

			<!-- 场景页、鉴定页 -->
			<div class="scence_wrap swiper-slide">
				<div class=" scence_box scence2">
					<a href="javascript:;" class="btn_rule_link bg t no-swiping">活动规则</a>
					<div class="login-info no-swiping">
						<span class="userinfo J_username">-</span>，<a href="javascript:app.logout();" class="btn-logout">退出</a>
					</div>
					<div class="scence_txt"></div>
					<div class="scence_role bg"></div>
					<a href="javascript:" class="btn_next">点击进入下一页</a>
				</div>
			</div>

			<!-- 场景页、鉴定页 -->
			<div class="scence_wrap swiper-slide">
				<div class="scence_box scence3">
					<a href="javascript:;" class="btn_rule_link bg t no-swiping">活动规则</a>
					<div class="login-info no-swiping">
						<span class="userinfo J_username">-</span>，<a href="javascript:app.logout();" class="btn-logout">退出</a>
					</div>
					<div class="scence_txt"></div>
					<div class="scence_role bg"></div>
					<a href="javascript:" class="btn_next">点击进入下一页</a>
				</div>
			</div>

			<!-- 场景页、鉴定页 -->
			<div class="scence_wrap swiper-slide">
				<div class=" scence_box scence4">
					<a href="javascript:;" class="btn_rule_link bg t no-swiping">活动规则</a>
					<div class="login-info no-swiping">
						<span class="userinfo J_username">-</span>，<a href="javascript:app.logout();" class="btn-logout">退出</a>
					</div>
					<div class="scence_txt"></div>
					<div class="scence_role bg"></div>
					<div class="btn_begin_box">
						<a href="javascript:app.jianding()" class="btn_begin bg t">鉴定</a>
						<div class="begin_tip">鉴定账号年限 领取归来豪礼</div>
					</div>
				</div>
			</div>


			<!-- 分享页 -->
			<div class="scence_wrap swiper-slide">
				<!-- 1年以内	称号：潜龙在渊（限时90天），血上限+2000 -->
				<!-- 2~4年	称号：青云独步（限时90天），血上限+2200 -->
				<!-- 5~9年	称号：中流砥柱（限时90天），血上限+2400 -->
				<!-- 10~14年	称号：江湖名士（限时90天），血上限+2600 -->
				<!-- 15年以上	称号：天龙传奇（限时90天），血上限+3000 -->
				<div class="result result1 ">
					<a href="javascript:;" class="btn_rule_link bg t no-swiping">活动规则</a>
					<div class="login-info no-swiping">
						<span class="userinfo J_username">-</span>，<a href="javascript:app.logout();" class="btn-logout">退出</a>
					</div>
					<div class="page_mid">
						<div class="logo_result"></div>
						<div class="tip_txt opacity0">经鉴定，您于<span class="regedit_time txt_red">【2020年12月20日】</span>踏上天龙江湖，您的注册时长共计<span class="regedit_days txt_red">【3265天】</span>，在天龙世界度过了<span class="regedit_seasons txt_red">【17】</span>个春秋，领先了<span class="beyond_percent txt_red">【90%】</span>的大侠，鉴定满足<span class="account_step txt_red">【1年内】</span>档位，您在天龙江湖已经达到<span class="step_name  txt_red">【潜龙在渊】</span>的成就! </div>
						<div class="tip_txt tip_txt2 opacity0">恭喜您获得豪华限定奖励：<span class="step_prize_name txt_red">【】</span>和<span class="regedit_gift txt_red">【】</span>，《天龙八部·归来》新征程<span class="start_time">X月XX日</span>开启，邀请大侠重归江湖!</div>

						<div class="result_bot">
							<div class="qr_wrap opacity0">
								<div class="qr_box J_qr_box">
									<img src="/act/tlbbgl/zhjd/********/m/img/ewm.png" alt="">
								</div>
							</div>
							<div class="share_btns opacity0">
								<a href="javascript:app.share();" class="btn_share_link bg t">分享链接</a>
								<a href="javascript:;" class="btn_share_pic bg t">分享图片</a>
							</div>
						</div>

						<div class="share_guide_txt opacity0">
							（可分享此处链接或者图片给好友）<br />
							（奖励已发放至游戏账户内，请在开服后前往大理龚彩云处领取）
						</div>

					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="result_outer">
		<div class=" page4" id="poster">
			<div class="result result1 ">
				<div class="page_mid">
					<div class="tip_txt">经鉴定，您于<span class="regedit_time txt_red">【2020年12月20日】</span>踏上天龙江湖，您的注册时长共计<span class="regedit_days txt_red">【3265天】</span>，在天龙世界度过了<span class="regedit_seasons txt_red">【17】</span>个春秋，领先了<span class="beyond_percent txt_red">【90%】</span>的大侠，鉴定满足<span class="account_step txt_red">【1年内】</span>档位，您在天龙江湖已经达到<span class="step_name txt_red">【潜龙在渊】</span>的成就! </div>
					<div class="tip_txt tip_txt2">恭喜您获得豪华限定奖励：称号<span class="step_prize_name txt_red">【】</span>和<span class="regedit_gift txt_red">【】</span>，《天龙八部·归来》新征程<span class="start_time">X月XX日</span>开启，邀请大侠重归江湖!</div>

					<div class="result_bot">
						<div class="qr_wrap">
							<div class="qr_box J_qr_box">
								<img src="/act/tlbbgl/zhjd/********/m/img/ewm.png" alt="">
							</div>
							<div class="qr_txt">扫码参与活动</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>


<!-- 弹窗 -->


<!-- 引导分享（微信内 -->
<div class="pop-shareWX" id="shareWX"></div>

<!-- 引导分享（非微信-->
<div class="pop pop-share" id="share">
	<a href="javascript:;" class="btn-close"></a>
	<p class="txt1">你的天龙账号值钱吗？点击测试，还有账号专属称号-《天龙八部·归来》！</p>
	<input type="text" class="ipt-copy lianjie"
		   value="Http://www.SDFASDFASDFAFASSDFASDFASDFAFASSDFASDFASDFAFASSDFASDFASDFAFAS" />
	<input type="button" class="ipt-copyBtn" value="复制链接" />
</div>


<div class="pop pop-poster" id="popPoster">
	<a href="javascript:;" class="btn-close"></a>
	<img id="poster_img" src="" alt=" " class="poster_img">
	<div class="save_tip">长按保存图片</div>
</div>

<!-- 通用 -->
<div class="pop pop-common" id="common">
	<a href="javascript:;" class="btn-close"></a>
	<h1>提示</h1>
	<div class="txt-box">
		<p></p>
	</div>
	<div class="btn-boxPop">
		<a href="javascript:;" class="btn-confirm t">确认</a>
	</div>
</div>

<!-- 通用 -->
<div class="pop pop-login" id="popLogin">
	<a href="javascript:;" class="btn-close"></a>
	<div class="login-box">

	</div>
</div>

<!-- 活动规则 -->
<div class="pop pop-rule" id="rule">
	<a href="javascript:;" class="btn-close"></a>
	<h1 class="pop-tit"></h1>
	<div class="txt-box">
		<p><strong>活动时间：</strong><br>2025年6月11日至2025年8月31日</p>
		<p><strong>活动内容：</strong><br>在活动期间内，玩家可以参加《天龙八部·归来》账号鉴定活动，根据玩家账号注册时间进行账号鉴定。</p>
		<p><strong>活动奖励：</strong><br>服务器开启后至2025年8月31日，玩家可前往大理（169，122）NPC龚彩云领取对应的绑定奖励。
			<br>1、注册时间在2025年的玩家可获得获得【1年内】档位奖励：称号：潜龙在渊（90天），血上限+2000，以及金蚕丝*6。
			<br>2、注册时间在2022年至2024年的玩家可获得获得【2~4年】档位奖励：称号：青云独步（90天），血上限+2200，以及金蚕丝*8。
			<br>3、注册时间在2017年至2021年的玩家可获得获得【5~9年】档位奖励：称号：中流砥柱（90天），血上限+2400，以及金蚕丝*10。
			<br>4、注册时间在2012年至2016年的玩家可获得获得【10~14年】档位奖励：称号：江湖名士（90天），血上限+2600，以及金蚕丝*12。
			<br>5、注册时间在2011年（含）之前的玩家将可获得【15年以上】档位奖励：称号：天龙传奇（90天），血上限+3000，以及金蚕丝*15。</p>
		<p><strong>活动须知：</strong><br>当您自愿参加本次畅游组织的游戏账号鉴定活动时，畅游会收集您注册畅游通行证的时间，作为本次活动奖励的发放依据。</p>
	</div>
</div>


<script src="/act/all/cdn/jquery/3.4.1/jquery.min.js"></script>
<script src="/act/all/cdn/Swiper/4.5.0/js/swiper.min.js"></script>
<script src="/act/all/cdn/html2canvas/1.0.0-rc.7/html2canvas.min.js"></script>
<script src="/act/all/cdn/qrcode/1.0/qrcode.min.js"></script>
<script src="/act/tlbbgl/zhjd/********/m/js/clipboard.min.js"></script>
<script src="/act/tlbbgl/zhjd/********/m/js/tools.js"></script>
<script src="/act/tlbbgl/zhjd/********/m/js/jquery.lay.js"></script>
<script src="/act/tlbbgl/rlr/20250507/m/js/cdc_js_sdk_1.1.1.js"></script>
<script src="/act/tlbbgl/zhjd/********/m/js/tracker.js" data-project="zhjd"></script>
<script src="/act/tlbbgl/zhjd/********/m/js/common.js"></script>
<script>
	var loaderInit = function(config) {
		var a = {
			path: '/act/tlbbgl/zhjd/********/m/img/',
			source: [],
			delay: 100,
			mode: 1,
			handle: function(count, len) {},
			complete: function() {}
		};
		for (var k in config)
			a[k] = config[k];
		var count = 0,
				len = a.source.length,
				array = [],
				intv,
				time = 0;
		for (var i = 0; i < len; i++) {
			array.push(new Image());
			array[i].loaded = false;
			array[i].onload = function() {
				this.loaded = true;
			};
			array[i].onerror = function() {
				this.loaded = true;
			};
			array[i].src = a.path + a.source[i];
		}
		intv = setInterval(function() {
			time += 20;
			for (var i = 0; i < array.length; i++) {
				if (array[i].loaded && time >= a.delay) {
					count++;
					array.splice(i, 1);
					a.handle(count, len);
					if (a.mode !== 1) {
						time = 0;
						break;
					}
				}
			}
			if (count === len) {
				clearInterval(intv);
				a.complete();
			}
		}, 20);
	};
	var imagePath = "/act/tlbbgl/zhjd/********/m/img/";
	loaderInit({
		path: imagePath,
		source: ['pop-common.png', 'pop-home.png', 'pop-rule.png',
			'pop-share.png', 'pop-shareWX.png', 'pop-userinfo1.png',
			'pop-userinfo2.png'
		],
		delay: 100,
		mode: 2,
		handle: function(count, max) {
			// loadClick = false;
			// var per = Math.floor(count / max * 100) + '%';
			// $('.loader-prob i').width(per);
			// $('.loader-txt span').text(per);
		},
		complete: function() {
			console.log('complete');
		}
	});
</script>