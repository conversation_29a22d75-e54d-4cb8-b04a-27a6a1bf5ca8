@charset "utf-8";

@font-face {
    font-family: 'hy';
    src: url("../fonts/hydsj.ttf");
}
.wrap{
    text-align: center;
    width:750px;
    height: 100%;
    margin: 0 auto;
}

.page {
    background: no-repeat center center/100% auto;
    width: 750px;
}
.logo{background: url("../img/logo.png") no-repeat center center/100% auto; width: 160px; height: 92px; position: absolute; top: 50%; left: 50%; margin-top: -646px; margin-left: -80px;}
.loading {
    width: 750px;
    position: fixed;
    left: 50%;
    margin-left: -375px;
    z-index: 90;
    background: url("../img/loading_bg.jpg") no-repeat center center/100%;
}

.loading_box {
    width: 633px;
    height: 13px;
    position: absolute;
    top: 50%;
    margin-top: -190px;
    left: 50%;
    margin-left: -316px;
    background: url("../img/loading.png") no-repeat center/100%;
}

.loading_box::before, .loading_box::after {
    content: '';
    background: url("../img/loading_icon_left.png") no-repeat center center/100%;
    position: absolute;
    width: 123px;
    height: 31px;
    left: -50px;
    top: -10px;
    z-index: 2;
}

.loading_box::after {
    left: initial;
    right: -50px;
    background-image: url("../img/loading_icon_right.png");
}

.loading_percent {
    position: absolute;
    top: 35px;
    right: 0;
    transform: translateX(50%);
    font-size: 30px;
    color: #4e3323;
    /*font-family: 'hy';*/
}

.loading_icon{
    position: absolute;
    bottom: -20px;
    right: -140px;
    background: url("../img/ani/loading/machexulz_00000.png") no-repeat center center/100%;
    width: 280px;
    height: 120px;
}

.loading_con {
    width: 80%;
    height: 9px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
}

.loading_inner {
    background: url("../img/loading_con.png") repeat-x center/auto 100%;
    width: 10%;
    height: 9px;
    position: relative;
    transition: all .3s;
}

.loading_tip{
    position: absolute;
    top:100px;
    width: 100%;
    left: 0;
    text-align: center;
    font-size: 26px;
    color: #4e3323;
    font-family: 'hy';
}

/* page1 */
.page1{background-image: url(../img/bg.jpg);}
.login-info{position: absolute; top: 60px; right: 20px; z-index: 5; font-size: 28px; text-align: right;}
.login-info{text-align: right; color: #fdebd7;}
.login-info a{color: #fdebd7; font-size: 28px;}
.page1 .page_mid{display: flex; flex-direction: column; justify-content: flex-end; align-items: center;}
.slogan{background-image: url("../img/slogan.png"); width: 589px; height: 328px;}
.btn_start{background-image: url("../img/btn_enter.png"); width: 591px; height: 69px; margin: .48rem auto;}
.btn_start.timeover{filter: grayscale(1);}
.btn_rule_index{width: 134px;height: 26px;background: url(../img/btn_rule_index.png) no-repeat center/ 100% auto; z-index: 2; margin: 0 auto;}


/* page2 */
.page2{background-image: url("../img/bg_main.jpg"); }
.scence_wrap{width: 100%; height: 1172px; position: relative;}
.scence_box{display: none; width: 100%; height: 1680px; position: absolute; top: 50%; left: 0; transform: translateY(-50%);}
.scence_box.show{display: block;}
.scence_box canvas{width: 100%; height: 100%;}
.btn_top{position: absolute; top:50%; margin-top: -529px; width: 84px; height: 80px;}
.btn_muted{background-image: url("../img/btn_vol.png"); left: 24px;}
.btn_muted.muted::after{content: ''; background: url("../img/btn_vol_muted.png") no-repeat center/100%; width: 100%; height: 100%; position: absolute; left: 0; top: 0;}
.btn_skip{background-image: url("../img/btn_skip.png");  left: 115px;}
.video_continue{position: absolute; top: 50%; left: 50%;}
.video_continue1{width: 301px; height: 240px; margin-top: -96px; margin-left: -142px; animation: ani-fade-up-in 2s ;}
.video_continue2{width: 477px; height: 266px; margin-top: -196px; margin-left: -244px; animation: ani-fade-up-in 1.4s ; }
.video_continue3{width: 216px; height: 270px; margin-top: -205px; margin-left: -118px; animation: ani-fade-up-in 2s ;}
.video_continue4{width: 216px; height: 220px; margin-top: -302px; margin-left: -110px; animation: ani-fade-up-in 2s ;}
.video_continue5{width: 477px; height: 295px; margin-top: -314px; margin-left: -244px; animation: ani-fade-up-in 2s ;}

@keyframes ani-fade-up-in {
    from{ opacity: 0; transform: translateY(100px); }
    to{opacity: 1; transform: translateY(0px);}
}
.video_continue1::after{content: ''; display: block; width: 100%; height: 100%; background:  url("../img/btn_video1.png") no-repeat 0 0/100%; animation: ani-breath 2s linear 2s infinite;}
@keyframes ani-breath {
    0%, 100% {transform: scale(1);}
    50% {transform: scale(.9);}
}
.video_continue2::after{content: ''; display: block; width: 100%; height: 100%; background:  url("../img/btn_video2.png") no-repeat 0 0/100%;}

.video_continue3::before{content: ''; display: block; width: 100%; height: 100%; background:  url("../img/btn_video3.png") no-repeat 0 0/100%; }

.video_continue3::after {
    content: '';
    position: absolute;
    left: 160px;
    top: 167px;
    width: 220px;
    height: 98px;
    background: url("../img/btn_video3_guide.png") no-repeat 0 0/100%;
    animation: ani-breath 1s linear 2s infinite;
}
.video_continue4::before{content: ''; display: block; width: 100%; height: 100%; background:  url("../img/btn_video4.png") no-repeat 0 0/100%; }

.video_continue4::after {
    content: '';
    position: absolute;
    left: 160px;
    top: 167px;
    width: 277px;
    height: 98px;
    background: url("../img/btn_video4_guide.png") no-repeat 0 0/100%;
    animation: ani-breath 1s linear 2s infinite;
}
.video_continue5::before{content: ''; display: block; width: 100%; height: 100%; background:  url("../img/btn_video5.png") no-repeat 0 0/100%; }

.video_continue5::after {
    content: '';
    position: absolute;
    left: 334px;
    top: 237px;
    width: 176px;
    height: 102px;
    background: url("../img/btn_video5_guide.png") no-repeat 0 0/100%;
    animation: ani-breath 1s linear 2s infinite;
}


/* page3 */
.page3{overflow: auto; top: 0; margin-top: 0; transform-origin: top center;}
.page3_cont{background-image: url("../img/bg_main.jpg"); height: 5056px;}
.btns_top_left{position: absolute; top: 40px; left: 0; z-index: 6; display: flex; justify-content: flex-start; flex-wrap: nowrap;}
.btns_top_left a{width: 141px; height: 50px;}
.btn_top_right{position: absolute; top: 47px; right: 28px; z-index: 6; display: flex; justify-content: flex-end; flex-wrap: nowrap;}
.btn_top_right a{width: 130px; height: 42px; margin-left: 12px;}
.btn_review{background-image: url("../img/btn_repeat.png");}
.btn_logout{background-image: url("../img/btn_logout.png");}
.btn_rule{background-image: url("../img/btn_rule.png");}
.btn_usercenter{background-image: url("../img/btn_usercenter.png");}
.icon_scroll_top{background-image: url("../img/icon_down.png"); width: 85px; height: 54px; position: fixed; right: 0; bottom: 30%; z-index: 9;}
.foot_menu{position: fixed; left:0; right: 0; margin: auto; bottom: 40px; width: 750px; height: 74px; background: #fff; display: none; z-index: 9;}
.foot_menu.show{display: flex;}
.foot_menu a{flex: 1; background: url("../img/nav.png") no-repeat 0 0/auto 100%; height: 74px; width: 74px;}
.foot_menu a.on{background-image: url("../img/nav_on.png")}
.foot_menu .btn_menu1{background-position: 0 0;}
.foot_menu .btn_menu2{background-position: -149px 0;}
.foot_menu .btn_menu3{background-position: -298px 0;}
.foot_menu .btn_menu4{background-position: -449px 0;}
.foot_menu .btn_menu5{background-position: -600px 0;}
.part{position: relative; box-sizing: border-box;}
.part1{height: 670px; padding-top: 424px;}
.btn_reserve{background-image: url("../img/btn_reserve.png"); width: 380px; height: 171px; margin: 0 auto;}
.btn_reserve.reserved{background-image: url("../img/btn_reserved.png");}
.part2{height: 592px;}
.part3{height: 1070px;}
.part4{height: 1370px;}
.part .tit{ height: 72px; background: no-repeat center center/auto 100% ; z-index: 2; margin: 0 auto;}
.part2 .tit{background-image: url("../img/tit2.png"); margin-bottom: 10px;}
.part3 .tit{background-image: url("../img/tit3.png");}
.part4 .tit{background-image: url("../img/tit4.png");}
.part5 .tit{background-image: url("../img/tit5.png");}
.tip{color: #ffedd5; font-size: 20px; text-align: center;}
.part2 .top{margin-top: 20px;}
.pro{background: url("../img/pro.png") no-repeat center/100%; width: 632px; height: 39px; margin: 210px auto 0; position: relative;}
.step1{background-image: url("../img/pro1.png");}
.step2{background-image: url("../img/pro2.png");}
.step3{background-image: url("../img/pro3.png");}
.pro_step{width: 1px; height: 94px; position: absolute; top: 0;}
.pro_step1{left: 94px;}
.pro_step2{left: 315px;}
.pro_step3{left: 536px;}
.pro_step .pro_icon{position: absolute; background: url("../img/box_normal.png") no-repeat center/100%; width: 105px; height: 104px; left: 50%; margin-left: -53px; top: -50px;
    /*pointer-events: none;*/
}
.pro_step.get .pro_icon{background-image: url("../img/box_get.png"); pointer-events: auto;}
.pro_step.got .pro_icon{background-image: url("../img/box_got.png");}
.pro_txt{position: absolute; top: 56px; background: url("../img/pro_txt_bg.png") no-repeat center/100%; width: 106px; height: 36px; margin-left: -53px; color: #ffffff; font-size: 20px; text-indent: 4px;}
.pro_step.get .pro_txt, .pro_step.got .pro_txt{background-image: url("../img/pro_txt_bg_on.png"); width: 131px; height: 39px; margin-left: -70px; text-indent: 40px;}
.pro_prize{position: absolute; top: -194px; left: 50%; margin-left: -112px; width: 219px; height: 156px; background: no-repeat center/100%;}
.pro_step1 .pro_prize{background-image: url("../img/pro_prize1.png");}
.pro_step2 .pro_prize{background-image: url("../img/pro_prize2.png");}
.pro_step3 .pro_prize{background-image: url("../img/pro_prize3.png");}
.btn_invite{background-image: url("../img/btn_invite.png"); width: 244px; height: 85px; margin: 74px auto 0;}
.prize_role{background-image: url("../img/prize_role.png"); width: 370px; height: 107px; margin: 634px auto 20px;}
.btn_get{background-image: url("../img/btn_get.png"); width: 371px; height: 114px; margin: 0 auto;}
.btn_get.got{background-image: url("../img/btn_got.png"); pointer-events: none;}
.part_cont{position: relative;}
.btn_music{background-image: url("../img/icon_music.png"); width: 46px; height: 55px; position: absolute; right: 28px; top: 4px;}
.btn_music.muted{background-image: url("../img/icon_music_muted.png");}
.map_pro{background-image: url("../img/map_txt.png"); width: 272px; height: 72px; text-align: center;color: #ffffff; font-size: 32px; text-indent: -8px; line-height: 68px; margin: 40px auto 0; position: relative; left: 10px;}
.map_pro span{font-size: 38px;}
.map_pro .txt-red{color: #ff3400;}
.valign-bottom{ vertical-align: bottom;}

.map_wrap{margin: 40px auto 0; width: 713px; height: 886px; position: relative;}
.map-list{width: 713px; height: 886px; background-image: url("../img/map.png"); position: absolute; left: 0; top: 0; z-index: 2;}
.map-list li{position: absolute; }
.map-list li{background-image: url("../img/map_on.png"); background-size: 713px auto; display: none;}
.map-list li:nth-child(1){width: 131px; height: 164px; left: 0; top: 16px; background-position: 0 -16px;}
.map-list li:nth-child(2){width: 56px; height: 101px; left: 171px; top: 49px; background-position: -171px -49px;}
.map-list li:nth-child(3){width: 100px; height: 161px; left: 277px; top: 0px; background-position: -277px -0px;}
.map-list li:nth-child(4){width: 55px; height: 105px; left: 422px; top: 7px; background-position: -422px -7px;}
.map-list li:nth-child(5){width: 96px; height: 106px; left: 546px; top: 10px; background-position: -546px -10px;}
.map-list li:nth-child(6){width: 55px; height: 101px; left: 639px; top: 127px; background-position: -639px -127px;}
.map-list li:nth-child(7){width: 100px; height: 153px; left: 503px; top: 106px; background-position: -503px -106px;}
.map-list li:nth-child(8){width: 54px; height: 101px; left: 384px; top: 178px; background-position: -384px -178px;}
.map-list li:nth-child(9){width: 74px; height: 101px; left: 265px; top: 210px; background-position: -265px -210px;}
.map-list li:nth-child(10){width: 74px; height: 102px; left: 136px; top: 149px; background-position: -136px -149px;}
.map-list li:nth-child(11){width: 54px; height: 102px; left: 8px; top: 224px; background-position: -8px -224px;}
.map-list li:nth-child(12){width: 73px; height: 102px; left: 67px; top: 335px; background-position: -67px -335px;}
.map-list li:nth-child(13){width: 54px; height: 103px; left: 192px; top: 353px; background-position: -192px -353px;}
.map-list li:nth-child(14){width: 54px; height: 104px; left: 308px; top: 369px; background-position: -308px -369px;}
.map-list li:nth-child(15){width: 100px; height: 152px; left: 422px; top: 337px; background-position: -422px -337px;}
.map-list li:nth-child(16){width: 54px; height: 104px; left: 583px; top: 380px; background-position: -583px -380px;}
.map-list li:nth-child(17){width: 74px; height: 103px; left: 639px; top: 495px; background-position: -639px -495px;}
.map-list li:nth-child(18){width: 54px; height: 101px; left: 522px; top: 516px; background-position: -522px -516px;}
.map-list li:nth-child(19){width: 54px; height: 102px; left: 382px; top: 506px; background-position: -382px -506px;}
.map-list li:nth-child(20){width: 74px; height: 102px; left: 219px; top: 528px; background-position: -219px -528px;}
.map-list li:nth-child(21){width: 54px; height: 102px; left: 96px; top: 490px; background-position: -96px -490px;}
.map-list li:nth-child(22){width: 55px; height: 101px; left: 17px; top: 608px; background-position: -17px -608px;}
.map-list li:nth-child(23){width: 74px; height: 101px; left: 116px; top: 655px; background-position: -116px -655px;}
.map-list li:nth-child(24){width: 95px; height: 106px; left: 230px; top: 673px; background-position: -230px -673px;}
.map-list li:nth-child(25){width: 189px; height: 150px; left: 356px; top: 623px; background-position: -356px -623px;}
.map-list li:nth-child(26){width: 55px; height: 105px; left: 605px; top: 650px; background-position: -605px -650px;}
.map-list li:nth-child(27){width: 73px; height: 102px; left: 631px; top: 770px; background-position: -631px -770px;}
.map-list li:nth-child(28){width: 54px; height: 105px; left: 543px; top: 775px; background-position: -543px -775px;}
.map-list li:nth-child(29){width: 53px; height: 103px; left: 429px; top: 783px; background-position: -429px -783px;}
.map-list li:nth-child(30){width: 74px; height: 103px; left: 307px; top: 780px; background-position: -307px -780px;}
.day1 .map-list li:nth-child(-n+1) {display: block;}
.day2 .map-list li:nth-child(-n+2) {display: block;}
.day3 .map-list li:nth-child(-n+3) {display: block;}
.day4 .map-list li:nth-child(-n+4) {display: block;}
.day5 .map-list li:nth-child(-n+5) {display: block;}
.day6 .map-list li:nth-child(-n+6) {display: block;}
.day7 .map-list li:nth-child(-n+7) {display: block;}
.day8 .map-list li:nth-child(-n+8) {display: block;}
.day9 .map-list li:nth-child(-n+9) {display: block;}
.day10 .map-list li:nth-child(-n+10) {display: block;}
.day11 .map-list li:nth-child(-n+11) {display: block;}
.day12 .map-list li:nth-child(-n+12) {display: block;}
.day13 .map-list li:nth-child(-n+13) {display: block;}
.day14 .map-list li:nth-child(-n+14) {display: block;}
.day15 .map-list li:nth-child(-n+15) {display: block;}
.day16 .map-list li:nth-child(-n+16) {display: block;}
.day17 .map-list li:nth-child(-n+17) {display: block;}
.day18 .map-list li:nth-child(-n+18) {display: block;}
.day19 .map-list li:nth-child(-n+19) {display: block;}
.day20 .map-list li:nth-child(-n+20) {display: block;}
.day21 .map-list li:nth-child(-n+21) {display: block;}
.day22 .map-list li:nth-child(-n+22) {display: block;}
.day23 .map-list li:nth-child(-n+23) {display: block;}
.day24 .map-list li:nth-child(-n+24) {display: block;}
.day25 .map-list li:nth-child(-n+25) {display: block;}
.day26 .map-list li:nth-child(-n+26) {display: block;}
.day27 .map-list li:nth-child(-n+27) {display: block;}
.day28 .map-list li:nth-child(-n+28) {display: block;}
.day29 .map-list li:nth-child(-n+29) {display: block;}
.day30 .map-list li:nth-child(-n+30) {display: block;}


.map_path{background: url("../img/map_path.png") no-repeat 0/100%; width: 713px; height: 886px;}
.map_path li{background-image: url("../img/map_path_on.png"); background-size: 713px auto; display: none; position: absolute;}
.map_path li:nth-child(1){width: 192px; height: 161px; left: 0; top: 0px; background-position: 0 -0px;}
.map_path li:nth-child(2){width: 116px; height: 161px; left: 192px; top: 0px; background-position: -192px -0px;}
.map_path li:nth-child(3){width: 143px; height: 161px; left: 307px; top: 0px; background-position: -307px -0px;}
.map_path li:nth-child(4){width: 133px; height: 150px; left: 450px; top: 0px; background-position: -450px -0px;}
.map_path li:nth-child(5){width: 130px; height: 194px; left: 583px; top: 0px; background-position: -583px -0px;}
.map_path li:nth-child(6){width: 130px; height: 102px; left: 583px; top: 194px; background-position: -583px -194px;}
.map_path li:nth-child(7){width: 174px; height: 102px; left: 409px; top: 194px; background-position: -409px -194px;}
.map_path li:nth-child(8){width: 107px; height: 117px; left: 302px; top: 194px; background-position: -302px -194px;}
.map_path li:nth-child(9){width: 131px; height: 117px; left: 171px; top: 194px; background-position: -171px -194px;}
.map_path li:nth-child(10){width: 171px; height: 102px; left: 0px; top: 194px; background-position: -0px -194px;}

.map_path li:nth-child(11){width: 96px; height: 177px; left: 0px; top: 296px; background-position: -0px -296px;}
.map_path li:nth-child(12){width: 123px; height: 104px; left: 96px; top: 369px; background-position: -96px -369px;}
.map_path li:nth-child(13){width: 116px; height: 104px; left: 219px; top: 369px; background-position: -219px -369px;}
.map_path li:nth-child(14){width: 147px; height: 104px; left: 335px; top: 369px; background-position: -335px -369px;}
.map_path li:nth-child(15){width: 128px; height: 104px; left: 482px; top: 369px; background-position: -482px -369px;}
.map_path li:nth-child(16){width: 103px; height: 178px; left: 610px; top: 369px; background-position: -610px -369px;}
.map_path li:nth-child(17){width: 168px; height: 70px; left: 545px; top: 547px; background-position: -545px -547px;}
.map_path li:nth-child(18){width: 134px; height: 70px; left: 411px; top: 547px; background-position: -411px -547px;}
.map_path li:nth-child(19){width: 155px; height: 70px; left: 256px; top: 547px; background-position: -256px -547px;}
.map_path li:nth-child(20){width: 147px; height: 70px; left: 116px; top: 547px; background-position: -116px -547px;}
.map_path li:nth-child(21){width: 116px; height: 118px; left: 0px; top: 547px; background-position: -0px -547px;}
.map_path li:nth-child(22){width: 150px; height: 118px; left: 0px; top: 665px; background-position: -0px -665px;}
.map_path li:nth-child(23){width: 127px; height: 118px; left: 150px; top: 665px; background-position: -150px -665px;}
.map_path li:nth-child(24){width: 172px; height: 118px; left: 278px; top: 665px; background-position: -278px -665px;}
.map_path li:nth-child(25){width: 180px; height: 115px; left: 451px; top: 665px; background-position: -451px -665px;}
.map_path li:nth-child(26){width: 82px; height: 115px; left: 631px; top: 665px; background-position: -631px -665px;}
.map_path li:nth-child(26)::after{content: ''; position: absolute; top: 114px; right: 0; background: url("../img/map_path_on.png") no-repeat -668px -780px/713px; width: 46px; height: 107px;}
.map_path li:nth-child(27){width: 98px; height: 103px; left: 570px; top: 783px; background-position: -570px -783px;}
.map_path li:nth-child(28){width: 120px; height: 106px; left: 450px; top: 783px; background-position: -450px -783px;}
.map_path li:nth-child(29){width: 173px; height: 103px; left: 278px; top: 783px; background-position: -278px -783px;}


.day2 .map_path li:nth-child(-n+1) {display: block;}
.day3 .map_path li:nth-child(-n+2) {display: block;}
.day4 .map_path li:nth-child(-n+3) {display: block;}
.day5 .map_path li:nth-child(-n+4) {display: block;}
.day6 .map_path li:nth-child(-n+5) {display: block;}
.day7 .map_path li:nth-child(-n+6) {display: block;}
.day8 .map_path li:nth-child(-n+7) {display: block;}
.day9 .map_path li:nth-child(-n+8) {display: block;}
.day10 .map_path li:nth-child(-n+9) {display: block;}
.day11 .map_path li:nth-child(-n+10) {display: block;}
.day12 .map_path li:nth-child(-n+11) {display: block;}
.day13 .map_path li:nth-child(-n+12) {display: block;}
.day14 .map_path li:nth-child(-n+13) {display: block;}
.day15 .map_path li:nth-child(-n+14) {display: block;}
.day16 .map_path li:nth-child(-n+15) {display: block;}
.day17 .map_path li:nth-child(-n+16) {display: block;}
.day18 .map_path li:nth-child(-n+17) {display: block;}
.day19 .map_path li:nth-child(-n+18) {display: block;}
.day20 .map_path li:nth-child(-n+19) {display: block;}
.day21 .map_path li:nth-child(-n+20) {display: block;}
.day22 .map_path li:nth-child(-n+21) {display: block;}
.day23 .map_path li:nth-child(-n+22) {display: block;}
.day24 .map_path li:nth-child(-n+23) {display: block;}
.day25 .map_path li:nth-child(-n+24) {display: block;}
.day26 .map_path li:nth-child(-n+25) {display: block;}
.day27 .map_path li:nth-child(-n+26) {display: block;}
.day28 .map_path li:nth-child(-n+27) {display: block;}
.day29 .map_path li:nth-child(-n+28) {display: block;}
.day30 .map_path li:nth-child(-n+29) {display: block;}

.btn_sign{background-image: url("../img/btn_sign.png"); width: 3.04rem; height: .93rem; margin: 30px auto 0;}
.btn_sign.signed{background-image: url("../img/btn_signed.png"); pointer-events: none;}
.btn_sign.signed_all{background-image: url("../img/btn_signed_all.png"); pointer-events: none;}

.lott_count{background-image: url("../img/lott_count.png"); width: 442px; height: 81px; text-align: center;color: #ffffff; font-size: 32px; text-indent: -8px; line-height: 80px; margin: 40px auto 0; position: relative; left: 10px;}
.lott_count em{color: #ff3400; font-size: 48px; vertical-align: bottom;}
.lott_tip{font-size: 22px; color: #2b1d1b; text-align: center;}
.lott_wrap{margin: 46px auto 0; background: url("../img/lott_bg.png") no-repeat center/100%; width: 674px; height: 674px; position: relative; display: flex; justify-content: center; align-items: center;}
.lott_wrap ul{width: 630px; height: 638px; position: relative;}
.lott_wrap li{position: absolute; width: 202px; height: 205px;}
.lott_wrap li img{
    width: 100%; height: 100%; position: relative; z-index: 2;}
.lott_wrap li:nth-child(1){left: 0; top: 0;}
.lott_wrap li:nth-child(2){left: 214px; top: 0px;}
.lott_wrap li:nth-child(3){left: 428px; top: 0px;}
.lott_wrap li:nth-child(4){left: 428px; top: 216px;}
.lott_wrap li:nth-child(5){left: 428px; top: 433px;}
.lott_wrap li:nth-child(6){left: 214px; top: 433px;}
.lott_wrap li:nth-child(7){left: 0px; top: 433px;}
.lott_wrap li:nth-child(8){left: 0px; top: 216px;}
.lott_wrap li.active::after{content: ''; background: url("../img/lott_item_bor.png") no-repeat center center/100%; width: 204px; height: 208px; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);}
.btn_lott{background-image: url("../img/btn_lott.png"); width: 202px; height: 204px; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); }
.btn_lott.disabled{background-image: url("../img/btn_lott_disabeld.png");}
.btn_lott:not(.disabled){animation: ani-breath-light 2s infinite;}
@keyframes ani-breath-light {
    0%{filter: brightness(1)}
    50%{filter: brightness(1.2)}
    100%{filter: brightness(1)}
}
/* pop */
.pop{ background:#816f57; width:578px; height:auto; color: #fff; font-size: 24px; text-align: center; margin: 0 auto 20px;  position: relative;display:none; padding-top: 1px;}
.btn-close{width: 60px;height: 51px;position: absolute;top: 18px;right: 4px;z-index: 10;background: url(../img/btn-close.png) no-repeat center;background-size: 100% auto;}
.tit_pop{margin: .58rem auto 0;}


.pop-poster{width: 538px; height: 1020px; background: none;}
.poster_img{width: 100%; height: 957px;}
.save_tip{font-size: 28px; color: #FFFFFF; text-align: center; position: absolute; bottom: 0; width: 100%; left: 0;}
.pop-poster .btn-close{top: initial; bottom: -60px; left: 50%; margin-left: -30px;}

.pop-login{width: 608px;height:624px;background: none;background-size: 100% auto;}
.pop-login iframe {width: 600px; height: 630px;}
.pop-login .btn-close{top: 0; right: -60px;}

.pop-common{width: 608px;height:551px;background: url(../img/pop-common.png) no-repeat center;background-size: 100% auto;}
.pop-common .btn-confirm{width:224px;height:71px;background: url(../img/btn_confirm.png) no-repeat center;background-size: 100% auto;}
.pop-common .btn-bind{width:196px;height:65px;background: url(../img/bind.png) no-repeat center;background-size: 100% auto;margin: 0 auto;}

.pop-promote{background: url("../img/pop-write.png") no-repeat center;background-size: 100% auto; width: 608px; height: 373px;}
.pop-promote h1{background: url("../img/pop-tit1.png") no-repeat center center/auto 100%; height: 39px; padding: 0; margin: 104px auto 0;}
.pop-promote .btn-boxPop{bottom: 76px;}
.btn_continue_reserve{background: url("../img/btn_continue.png") no-repeat center;background-size: 100% auto; width: 224px; height: 71px;}
.btn_go_bind{background: url("../img/btn_write_phone.png") no-repeat center;background-size: 100% auto; width: 224px; height: 71px;}

.pop-phone{background: url("../img/pop-common.png") no-repeat center;background-size: 100% auto; width: 608px; height: 551px;}
.pop-phone h1{background: url("../img/pop-tit3.png") no-repeat center center/auto 100%; height: 36px; padding: 0; margin: 70px auto 50px;}
.pop-phone .tip-box{font-size: 24px; color: #372812; text-align: center; line-height: 36px; width: 464px; height: 72px; margin: 0 auto;}
.pop-phone .tip-box span{color: #df2f00;}
.phone-input{background: url("../img/input2.png") no-repeat center center/auto 100%; width: 462px; height: 61px; padding: 0; margin: 30px auto 60px; position: relative;}
.phone-input input{width: 332px; height: 100%; background: none; border: none; appearance: none; margin-left: 130px; font-size: 24px;}
.phone-input label{position: absolute; left: 12px; top: 16px; background: url("../img/icon_phone.png") no-repeat 0 center/24px auto; width: 130px; height: 27px; color: #3b3b3b; font-size: 24px; text-indent: 24px; line-height: 28px;}
.pop-phone .btn_confirm{background-image: url("../img/btn_confirm.png"); width: 224px; height: 71px;}
.pop-phone .btn_cancel{background-image: url("../img/btn_cancel.png"); width: 224px; height: 71px;}

.pop-shareWX{width:100%;height:100%;background:rgba(0,0,0,0.8) url(../img/pop-shareWX.png) right 10% top 188px/482px 412px  no-repeat ; position:fixed;top:0;left:0;z-index:999;display:none;}
.pop-shareWX .btn-close{top: 307px;right: 58px;width: 42px;height: 37px;}

.pop-share{width: 608px;height:393px;background: url(../img/pop-share.png) no-repeat center;background-size: 100% auto;}
.pop-share .txt1{width: 340px;}
.pop-share{width: 608px;height:393px;background: url(../img/pop-share.png) no-repeat center;background-size: 100% auto;}
.pop-share .txt1{width: 340px;}
.pop-share .ipt-copy{width: 479px;height:67px;background: url(../img/input.png) no-repeat center;background-size: 100% auto;font-size: 22px;color: #fff;line-height: 40px;box-sizing: border-box;padding: 10px 30px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
.pop-share .ipt-copyBtn{width: 479px;height:67px;background: url(../img/input.png) no-repeat center;background-size: 100% auto;font-size: 30px;color: #fff;line-height: 40px;padding: 10px 0;margin-top: 20px;}


.pop-state{width: 633px;height:622px;background: url(../img/pop-state.png) no-repeat center;background-size: 100% auto;}

.pop-rule{width: 608px;height:1058px;background: url(../img/pop-rule.png) no-repeat center;background-size: 100% auto;}
.tit_rule{background-image: url("../img/pop-tit4.png"); width: 337px; height: 35px;}
.pop-rule .txt-box{height: 900px;overflow-y: scroll;margin-top: 30px;}
.pop-rule .txt-box p{text-align: start;font-size: 18px;color: #000;line-height: 30px;}
.pop-rule .txt-box p i{text-align: center;font-size: 18px;color: #fff;line-height: 30px;text-align: center;width:30px;height:30px;background: url(../img/numBg.png) no-repeat center;background-size: 100% auto;position: absolute;}
.pop-rule .txt-box p span{padding-left: 32px;}

.pop-home{width: 608px;height:761px;background: url(../img/pop-home.png) no-repeat center;background-size: 100% auto;}
.pop-home .pop-tit{background: url("../img/pop-tit2.png") no-repeat center;background-size: auto 100%; height: 35px; margin-top: 58px;}
.pop-home .txt2{padding: 0 50px 0; margin-top: 40px;}
.pop-home .txt2,.pop-home ul li{font-size: 22px;color: #000;text-align: start;}
.pop-home h3{font-size: 34px; color: #77664e; line-height: 36px; text-align: center; margin: 16px auto 20px;}
.pop-home .gift-list-head {width: 508px;margin: 0 auto;}
.pop-home .gift-list-head div{height:54px; color: #ffffff;font-size: 24px; line-height: 54px; text-align: center; text-shadow: 0 2px 8px #bd996b;}
.pop-home .gift-list {width: 508px;height:340px;margin: 10px auto 0;overflow-y: scroll;position: relative;}
.pop .no-data::after{content: '暂无数据~'; position: absolute; top: 50%; left: 50%; transform: translate(-50%,-50%); font-size: 24px;color: #4c4537;}
.pop-home ul li{display: flex;flex-direction: row;justify-content: start;align-items: center;margin: 10px 0; border-bottom: 1px dashed #cccccc;}
.pop-home .col1{font-size: 24px;width: 160px;flex-shrink: 0; line-height: 48px;justify-content: center; display: flex; align-items: center;}
.pop-home .col2{font-size: 24px;line-height: 28px;margin-left: 10px; min-height: 48px; display: flex; align-items: center;}
.gift-list-head .col1{background: url("../img/th1.png") no-repeat center;background-size: auto 100%; justify-content: center;}
.gift-list-head .col2{background: url("../img/th2.png") no-repeat center;background-size: 100% 100%;; width: 346px; justify-content: center;}
.btn_write_box{text-align: center; margin-top: 14px;font-size: 24px; color: #77664e;}
.btn_write_box a{font-size: 24px; color: #77664e; text-decoration: underline;}