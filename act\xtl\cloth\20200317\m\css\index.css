/***public***/
a { text-decoration: none; }
input[type="text"], input[type="password"], select { -webkit-appearance: none; appearance: none; outline: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); border-radius: 0; box-sizing: border-box; }
html, a, button, input { -webkit-tap-highlight-color: rgba(0, 0, 0, 0); }
i, em { font-style: normal; }
html { font-size: 100px; }
body, html { -webkit-tap-highlight-color: transparent; -webkit-text-size-adjust: none; -webkit-user-select: none; }
a { text-decoration: none; blr: expression(this.onFocus=this.blur()); outline: none; }
a:focus, a:active, a { outline: none; }
.c:after { content: " "; clear: both; height: 0; visibility: hidden; display: block; }
.c { *zoom: 1; }
body { font-size: 22px; color: #494543; text-align: center; position: relative; overflow-x: hidden; }
.pr { position: relative; }
.pa { position: absolute; }
.btn, .com, .sidebar a:after, .tabhd a { background: url(../img/com.png) no-repeat; display: block; overflow: hidden; text-indent: -9999em; background-size: 750px auto; }
.wrap { background: url(../img/wbg.jpg) no-repeat center top; background-size: 100% auto; width: 100%; overflow-x: hidden; position: relative; z-index: 1; }
.head { height: 799px; }
.logo { width: 270px; height: 126px; background: url(../img/logo.png) no-repeat; background-size: 100% auto; text-indent: -9999em; top: 17px; left: 20px; z-index: 2; }
.slogantxt, .slogantxt i { width: 362px; height: 680px; background: url(../img/slogan.png) no-repeat; background-size: 100%; }
.slogan_longmen, i.slogan_longmen { background: url(../img/slogan_longmen.png) no-repeat;  background-size: 100%;}
.slogantxt { top: 71px; left: 271px; }
.slogantxt i { text-indent: -9999em; display: block; }
.fubiao { width: 58px; height: 585px; top: 116px; left: 245px; }
.dlbar { top: 22px; right: 22px; z-index: 3; line-height: 34px; width: 440px; font-size: 22px; text-align: right; }
.dlbar, .dlbar a { color: #fff; }
.sidebar { width: 108px; height: 306px; text-indent: 0;	/* background-position: -84px 0; */ z-index: 2; top: 111px; right: 30px;	/* padding-top: 16px; */ overflow: visible; }
.sidebar a {		/* width: 108px; */

				/* height: 108px; */
display: block; text-indent: -9999em;	/* margin-top: 24px; */ position: relative; background: url(../img/com.png) no-repeat; }
#centerBtn { background-position: -84px -6px; width: 108px; height: 166px; }
#center_rule { background-position: -84px -172px; width: 108px; height: 144px; }
.sidebar a:after { content: ''; width: 114px; height: 114px; position: absolute; background-position: -242px 0; animation: 5s linear 0s normal none infinite fc; }
#centerBtn::after { top: 30px; left: -2px; }
#center_rule::after { top: -4px; left: -2px; }
.active .slogantxt { animation: scaleA 0.6s 0.2s ease both; }
.active .slogantxt i { animation: scaleB 0.5s .6s ease-in both; }
.active .fubiao { animation: fadeInLeft 1s 1s ease both; }
.active .sidebar { animation: fadeInDown 1s 1.2s ease both; }
.active .dlbar { animation: fadeInDown 1s 1.4s ease both; }
.fadeup { animation: fadeInUp 1s .2s ease both; }
.fadeDown { animation: fadeDown 1s .2s ease both; }
@keyframes fadeDown {
	0% { opacity: 1; transform: translateY(0px) }
	100% { opacity: 0; transform: translateY(-20px) }
}
@keyframes fadeInDown {
	0% { opacity: 0; transform: translateY(-20px) }
	100% { opacity: 1; transform: translateY(0) }
}
@keyframes fc {
	from { transform: rotate(0deg) }
	to { transform: rotate(360deg) }
}
@keyframes fadeInLeft {
	0% { opacity: 0; transform: translateX(-20px) }
	100% { opacity: 1; transform: translateX(0) }
}
@keyframes scaleA {
	0% { opacity: 0; transform: scale(1.1, 1.1); }
	80% { transform: scale(.9, .9); }
	100% { opacity: 1; transform: scale(1, 1); }
}
@keyframes scaleB {
	0% { transform: scale(1); opacity: 1; }
	99.9% { transform: scale(2); opacity: 0; }
	100% { transform: scale(1); opacity: 0; }
}
.cont { height: 2031px; }
.taskbox { text-indent: 0; height: 130px; margin: 0 auto; text-align: center; width: 100%; }
.taskbox a { display: inline-block; vertical-align: top; margin: 0 8px; }
.znqbtn { width: 337px; height: 92px; background-position: 0 -1004px; }
.znqbtn.ylq { background-position: -382px -1004px; }
.getbtn { width: 338px; height: 92px; background-position: 2px -1117px; }
.getbtn.ylq { background-position: -371px -1113px; }
.getbtn.dis_btn { background-position: 0 -1226px; }
.tabhd { margin: 0 auto; height: 70px; display: flex; display: -webkit-flex; justify-content: center; -webkit-justify-content: center; }
.tabhd a { width: 344px; height: 70px; margin: 0 1px; }
.tabhd a:nth-child(1) { background-position: -242px -426px; }
.tabhd a:nth-child(2) { background-position: -242px -348px; }
.clothbg .tabhd a:nth-child(1) { background-position: -242px -268px; }
.clothbg .tabhd a:nth-child(2) { background-position: -247px -567px; }
.tit { width: 416px; height: 29px; margin: 27px auto 0; background-position: 0 -714px; }
.tip { padding: 15px 0 20px; }
.swiperbox { height: 274px; width: 644px; margin: 0 auto; }
.swiper-container { width: 100%; height: 100%; }
.swiperbox .swiper-slide { width: 161px; height: 274px; }
.swiperbox .swiper-slide img { width: 156px; display: block; margin: 0 auto; }
.prev, .next, .bxprev, .bxnext { width: 24px; height: 48px; top: 50%; margin-top: -24px; }
.prev, .bxprev { background-position: -652px 0; left: -37px; }
.bxprev { left: -52px; }
.next, .bxnext { background-position: -697px 0; right: -37px; }
.bxnext { right: -52px; }
.bxprev, .bxnext { transform: scale(1.625); margin-top: -47px; }
.swiper-button-disabled { opacity: .4; }
.suobtn { width: 114px; height: 114px; background-position: -636px -68px; top: 50%; margin-top: -57px; left: 50%; margin-left: -57px; animation: blings 2s infinite ease-in-out; transform: scale(.8); }
@keyframes blings {
	0% { filter: brightness(1); }
	50% { filter: brightness(1.1); }
	100% { filter: brightness(1); }
}
@keyframes blings2 {
	0% { filter: brightness(1); transform: rotate(0); }
	50% { filter: brightness(1.3); }
	100% { filter: brightness(1); transform: rotate(360deg); }
}
.dikuang { height: 1214px; width: 750px; background: url(../img/dikuang.png) no-repeat; background-size: 100%; margin-top: 22px; }
.draw_box { width: 750px; height: 1089px; position: relative; }
.stepbox { width: 750px; height: 1089px; background-size: 100% 100%; }
.cloth0_1 { background-image: url(../img/cloth/scloth0_1.png); }
.cloth0_2 { background-image: url(../img/cloth/scloth0_2.png); }
.cloth0_3 { animation: run 3s steps(1, end) both; }
.cloth0_4 { background-image: url(../img/cloth/cloth0_35.jpg); }
.cloth1_1 { background-image: url(../img/cloth/scloth1_1.png); }
.cloth1_2 { background-image: url(../img/cloth/scloth1_2.png); }
.cloth1_3 { animation: run1 3s steps(1, end) both; }
.cloth1_4 { background-image: url(../img/cloth/cloth1_35.jpg); }
.cloth2_1 { background-image: url(../img/cloth/scloth2_1.png); }
.cloth2_2 { background-image: url(../img/cloth/scloth2_2.png); }
.cloth2_3 { animation: run2 3s steps(1, end) both; }
.cloth2_4 { background-image: url(../img/cloth/cloth2_35.jpg); }
.cloth3_1 { background-image: url(../img/cloth/scloth3_1.png); }
.cloth3_2 { background-image: url(../img/cloth/scloth3_2.png); }
.cloth3_3 { animation: run3 3s steps(1, end) both; }
.cloth3_4 { background-image: url(../img/cloth/cloth3_35.jpg); }
@keyframes run {
	0% { background-image: url(../img/cloth/cloth0_0.jpg); }
	2.9% { background-image: url(../img/cloth/cloth0_1.jpg); }
	5.7% { background-image: url(../img/cloth/cloth0_2.jpg); }
	8.6% { background-image: url(../img/cloth/cloth0_3.jpg); }
	11.4% { background-image: url(../img/cloth/cloth0_4.jpg); }
	14.3% { background-image: url(../img/cloth/cloth0_5.jpg); }
	17.1% { background-image: url(../img/cloth/cloth0_6.jpg); }
	20% { background-image: url(../img/cloth/cloth0_7.jpg); }
	22.9% { background-image: url(../img/cloth/cloth0_8.jpg); }
	25.7% { background-image: url(../img/cloth/cloth0_9.jpg); }
	28.6% { background-image: url(../img/cloth/cloth0_10.jpg); }
	31.4% { background-image: url(../img/cloth/cloth0_11.jpg); }
	34.2% { background-image: url(../img/cloth/cloth0_12.jpg); }
	37.1% { background-image: url(../img/cloth/cloth0_13.jpg); }
	40% { background-image: url(../img/cloth/cloth0_14.jpg); }
	42.9% { background-image: url(../img/cloth/cloth0_15.jpg); }
	45.7% { background-image: url(../img/cloth/cloth0_16.jpg); }
	48.6% { background-image: url(../img/cloth/cloth0_17.jpg); }
	51.4% { background-image: url(../img/cloth/cloth0_18.jpg); }
	54.3% { background-image: url(../img/cloth/cloth0_19.jpg); }
	57.1% { background-image: url(../img/cloth/cloth0_20.jpg); }
	60% { background-image: url(../img/cloth/cloth0_21.jpg); }
	62.9% { background-image: url(../img/cloth/cloth0_22.jpg); }
	65.7% { background-image: url(../img/cloth/cloth0_23.jpg); }
	68.6% { background-image: url(../img/cloth/cloth0_24.jpg); }
	71.4% { background-image: url(../img/cloth/cloth0_25.jpg); }
	74.3% { background-image: url(../img/cloth/cloth0_26.jpg); }
	77.1% { background-image: url(../img/cloth/cloth0_27.jpg); }
	80% { background-image: url(../img/cloth/cloth0_28.jpg); }
	82.9% { background-image: url(../img/cloth/cloth0_29.jpg); }
	85.7% { background-image: url(../img/cloth/cloth0_30.jpg); }
	88.6% { background-image: url(../img/cloth/cloth0_31.jpg); }
	91.4% { background-image: url(../img/cloth/cloth0_32.jpg); }
	94.3% { background-image: url(../img/cloth/cloth0_33.jpg); }
	97.1% { background-image: url(../img/cloth/cloth0_34.jpg); }
	100% { background-image: url(../img/cloth/cloth0_35.jpg); }
}
@keyframes run1 {
	0% { background-image: url(../img/cloth/cloth1_0.jpg); }
	2.9% { background-image: url(../img/cloth/cloth1_1.jpg); }
	5.7% { background-image: url(../img/cloth/cloth1_2.jpg); }
	8.6% { background-image: url(../img/cloth/cloth1_3.jpg); }
	11.4% { background-image: url(../img/cloth/cloth1_4.jpg); }
	14.3% { background-image: url(../img/cloth/cloth1_5.jpg); }
	17.1% { background-image: url(../img/cloth/cloth1_6.jpg); }
	20% { background-image: url(../img/cloth/cloth1_7.jpg); }
	22.9% { background-image: url(../img/cloth/cloth1_8.jpg); }
	25.7% { background-image: url(../img/cloth/cloth1_9.jpg); }
	28.6% { background-image: url(../img/cloth/cloth1_10.jpg); }
	31.4% { background-image: url(../img/cloth/cloth1_11.jpg); }
	34.2% { background-image: url(../img/cloth/cloth1_12.jpg); }
	37.1% { background-image: url(../img/cloth/cloth1_13.jpg); }
	40% { background-image: url(../img/cloth/cloth1_14.jpg); }
	42.9% { background-image: url(../img/cloth/cloth1_15.jpg); }
	45.7% { background-image: url(../img/cloth/cloth1_16.jpg); }
	48.6% { background-image: url(../img/cloth/cloth1_17.jpg); }
	51.4% { background-image: url(../img/cloth/cloth1_18.jpg); }
	54.3% { background-image: url(../img/cloth/cloth1_19.jpg); }
	57.1% { background-image: url(../img/cloth/cloth1_20.jpg); }
	60% { background-image: url(../img/cloth/cloth1_21.jpg); }
	62.9% { background-image: url(../img/cloth/cloth1_22.jpg); }
	65.7% { background-image: url(../img/cloth/cloth1_23.jpg); }
	68.6% { background-image: url(../img/cloth/cloth1_24.jpg); }
	71.4% { background-image: url(../img/cloth/cloth1_25.jpg); }
	74.3% { background-image: url(../img/cloth/cloth1_26.jpg); }
	77.1% { background-image: url(../img/cloth/cloth1_27.jpg); }
	80% { background-image: url(../img/cloth/cloth1_28.jpg); }
	82.9% { background-image: url(../img/cloth/cloth1_29.jpg); }
	85.7% { background-image: url(../img/cloth/cloth1_30.jpg); }
	88.6% { background-image: url(../img/cloth/cloth1_31.jpg); }
	91.4% { background-image: url(../img/cloth/cloth1_32.jpg); }
	94.3% { background-image: url(../img/cloth/cloth1_33.jpg); }
	97.1% { background-image: url(../img/cloth/cloth1_34.jpg); }
	100% { background-image: url(../img/cloth/cloth1_35.jpg); }
}
@keyframes run2 {
	0% { background-image: url(../img/cloth/cloth2_0.jpg); }
	2.9% { background-image: url(../img/cloth/cloth2_1.jpg); }
	5.7% { background-image: url(../img/cloth/cloth2_2.jpg); }
	8.6% { background-image: url(../img/cloth/cloth2_3.jpg); }
	11.4% { background-image: url(../img/cloth/cloth2_4.jpg); }
	14.3% { background-image: url(../img/cloth/cloth2_5.jpg); }
	17.1% { background-image: url(../img/cloth/cloth2_6.jpg); }
	20% { background-image: url(../img/cloth/cloth2_7.jpg); }
	22.9% { background-image: url(../img/cloth/cloth2_8.jpg); }
	25.7% { background-image: url(../img/cloth/cloth2_9.jpg); }
	28.6% { background-image: url(../img/cloth/cloth2_10.jpg); }
	31.4% { background-image: url(../img/cloth/cloth2_11.jpg); }
	34.2% { background-image: url(../img/cloth/cloth2_12.jpg); }
	37.1% { background-image: url(../img/cloth/cloth2_13.jpg); }
	40% { background-image: url(../img/cloth/cloth2_14.jpg); }
	42.9% { background-image: url(../img/cloth/cloth2_15.jpg); }
	45.7% { background-image: url(../img/cloth/cloth2_16.jpg); }
	48.6% { background-image: url(../img/cloth/cloth2_17.jpg); }
	51.4% { background-image: url(../img/cloth/cloth2_18.jpg); }
	54.3% { background-image: url(../img/cloth/cloth2_19.jpg); }
	57.1% { background-image: url(../img/cloth/cloth2_20.jpg); }
	60% { background-image: url(../img/cloth/cloth2_21.jpg); }
	62.9% { background-image: url(../img/cloth/cloth2_22.jpg); }
	65.7% { background-image: url(../img/cloth/cloth2_23.jpg); }
	68.6% { background-image: url(../img/cloth/cloth2_24.jpg); }
	71.4% { background-image: url(../img/cloth/cloth2_25.jpg); }
	74.3% { background-image: url(../img/cloth/cloth2_26.jpg); }
	77.1% { background-image: url(../img/cloth/cloth2_27.jpg); }
	80% { background-image: url(../img/cloth/cloth2_28.jpg); }
	82.9% { background-image: url(../img/cloth/cloth2_29.jpg); }
	85.7% { background-image: url(../img/cloth/cloth2_30.jpg); }
	88.6% { background-image: url(../img/cloth/cloth2_31.jpg); }
	91.4% { background-image: url(../img/cloth/cloth2_32.jpg); }
	94.3% { background-image: url(../img/cloth/cloth2_33.jpg); }
	97.1% { background-image: url(../img/cloth/cloth2_34.jpg); }
	100% { background-image: url(../img/cloth/cloth2_35.jpg); }
}
@keyframes run3 {
	0% { background-image: url(../img/cloth/cloth3_0.jpg); }
	2.9% { background-image: url(../img/cloth/cloth3_1.jpg); }
	5.7% { background-image: url(../img/cloth/cloth3_2.jpg); }
	8.6% { background-image: url(../img/cloth/cloth3_3.jpg); }
	11.4% { background-image: url(../img/cloth/cloth3_4.jpg); }
	14.3% { background-image: url(../img/cloth/cloth3_5.jpg); }
	17.1% { background-image: url(../img/cloth/cloth3_6.jpg); }
	20% { background-image: url(../img/cloth/cloth3_7.jpg); }
	22.9% { background-image: url(../img/cloth/cloth3_8.jpg); }
	25.7% { background-image: url(../img/cloth/cloth3_9.jpg); }
	28.6% { background-image: url(../img/cloth/cloth3_10.jpg); }
	31.4% { background-image: url(../img/cloth/cloth3_11.jpg); }
	34.2% { background-image: url(../img/cloth/cloth3_12.jpg); }
	37.1% { background-image: url(../img/cloth/cloth3_13.jpg); }
	40% { background-image: url(../img/cloth/cloth3_14.jpg); }
	42.9% { background-image: url(../img/cloth/cloth3_15.jpg); }
	45.7% { background-image: url(../img/cloth/cloth3_16.jpg); }
	48.6% { background-image: url(../img/cloth/cloth3_17.jpg); }
	51.4% { background-image: url(../img/cloth/cloth3_18.jpg); }
	54.3% { background-image: url(../img/cloth/cloth3_19.jpg); }
	57.1% { background-image: url(../img/cloth/cloth3_20.jpg); }
	60% { background-image: url(../img/cloth/cloth3_21.jpg); }
	62.9% { background-image: url(../img/cloth/cloth3_22.jpg); }
	65.7% { background-image: url(../img/cloth/cloth3_23.jpg); }
	68.6% { background-image: url(../img/cloth/cloth3_24.jpg); }
	71.4% { background-image: url(../img/cloth/cloth3_25.jpg); }
	74.3% { background-image: url(../img/cloth/cloth3_26.jpg); }
	77.1% { background-image: url(../img/cloth/cloth3_27.jpg); }
	80% { background-image: url(../img/cloth/cloth3_28.jpg); }
	82.9% { background-image: url(../img/cloth/cloth3_29.jpg); }
	85.7% { background-image: url(../img/cloth/cloth3_30.jpg); }
	88.6% { background-image: url(../img/cloth/cloth3_31.jpg); }
	91.4% { background-image: url(../img/cloth/cloth3_32.jpg); }
	94.3% { background-image: url(../img/cloth/cloth3_33.jpg); }
	97.1% { background-image: url(../img/cloth/cloth3_34.jpg); }
	100% { background-image: url(../img/cloth/cloth3_35.jpg); }
}
.cailist { margin: 12px 0 0 54px; height: 112px; }
.cailist li { float: left; display: flex; display: -webkit-flex; width: 211px; }
.cailist li:last-child { width: 250px; }
.cailist li div { width: 93px; height: 92px; text-indent: -9999em; align-self: center; -webkit-align-self: center; position: relative; }
.cailist li div, .cailist li.cur div:after { background: url(../img/gongju.png) no-repeat; background-size: 467px 288px; }
.cailist li:nth-child(2) div { background-position: 0 -96px; }
.cailist li:nth-child(3) div { background-position: 0 -196px; }
.cailist li:nth-child(1).dis_btn div { background-position: -104px 0; }
.cailist li:nth-child(2).dis_btn div { background-position: -104px -96px; }
.cailist li:nth-child(3).dis_btn div { background-position: -104px -196px; }
.cailist li.cur div:after { width: 123px; height: 122px; position: absolute; content: ''; z-index: 2; top: -15px; left: -15px; background-position: -308px 0; animation: blings2 2s infinite linear; }
.cailist span { align-self: center; -webkit-align-self: center; padding-left: 6px; }
.anbtnbox { margin-top: 29px; height: 101px; }
.mycail { width: 105px; height: 29px; background-position: -246px -514px; top: 37px; left: 101px; }
.pptbtn { width: 104px; height: 29px; background-position: -370px -514px; top: 37px; right: 101px; }

	/*cloth*/
.clothbg { background: url(../img/clothbg.jpg) no-repeat center top; background-size: 100% auto; }
.clothbg .cont { height: 1521px; }
.fangche { height: 465px; }
.small_crrle { width: 162px; height: 162px; top: 211px; left: 29px; z-index: 1; }
.big_crrle { width: 325px; height: 325px; top: 50px; left: 413px; z-index: 1; }
.fangcqj { width: 706px; height: 489px; top: 34px; left: 10px; z-index: 2; }
.tishi { width: 429px; height: 292px; top: 84px; left: 312px; z-index: 3; animation: blings 2s infinite ease-in-out; }
.shoubing { width: 115px; height: 173px; background: url(../img/shoubing.png) no-repeat; background-size: 100%; top: 195px; left: 559px; z-index: 4; transform-origin: 18px 15px; }
.pic_box { z-index: 2; height: 219px; padding-left: 27px; }
.piclist li { width: 121px; height: 219px; margin: 0 3px; float: left; position: relative; }
.piclist li div, .piclist li.jiahao span { width: 121px; height: 121px; background: url(../img/selcom.png) no-repeat; background-size: 605px auto; text-indent: -9999em; }
.piclist li.jiahao { width: 53px; padding-top: 52px; height: 167px; }
.piclist li.jiahao span { width: 30px; height: 30px; display: block; margin: 0 auto; background-position: 0 -121px; }
.piclist li div.pic0 { background-position: 0 0; }
.piclist li div.pic1 { background-position: -121px 0; }
.piclist li div.pic2 { background-position: -242px 0; }
.piclist li div.pic3 { background-position: -363px 0; }
.piclist li div.pic4 { background-position: -484px 0; }
.piclist li p { color: #fff; line-height: 28px; position: absolute; top: 128px; left: 0; width: 100%; }
.piclist li.dis_btn div.pic0 { background-position: 0 -151px; }
.piclist li.dis_btn div.pic1 { background-position: -121px -151px; }
.piclist li.dis_btn div.pic2 { background-position: -242px -151px; }
.piclist li.dis_btn div.pic3 { background-position: -363px -151px; }
.piclist li.dis_btn div.pic4 { background-position: -484px -151px; }
.piclist li.btn-huih div.pic0 { background-position: 0 -578px; }
.piclist li.btn-huih div.pic1 { background-position: -121px -578px; }
.piclist li.btn-huih div.pic2 { background-position: -242px -578px; }
.piclist li.btn-huih div.pic3 { background-position: -363px -578px; }
.piclist li.btn-huih div.pic4 { background-position: -484px -578px; }
.piclist li.cur div { position: absolute; top: -14px; left: -13px; width: 156px; height: 151px; }
.piclist li.cur div.pic0 { background-position: 0 -272px; }
.piclist li.cur div.pic1 { background-position: -206px -272px; }
.piclist li.cur div.pic2 { background-position: -441px -272px; }
.piclist li.cur div.pic3 { background-position: 0 -423px; }
.piclist li.cur div.pic4 { background-position: -206px -423px; }
.clo_tip { color: #d64321; background: url(../img/tipbg.png) no-repeat; background-size: cover; padding: 10px 0; }
.clothbg .anbtnbox { margin-top: 34px; }

	/*dialog*/
.dialog { width: 100%; height: 100%; z-index: 100; position: fixed; top: 0; left: 0; display: none; }
.dia_bg { background: rgba(0, 0, 0, .8); width: 100%; height: 100%; position: relative; z-index: 1; }
.dia_com, .diahd li, .hd_center li, .jlhd a { background: url(../img/diacom.png) no-repeat; background-size: 750px auto; display: block; text-indent: -9999em; }
.dialog_cont { width: 100%; background: url(../img/diabg.jpg) no-repeat center top; background-size: 100% auto; position: fixed; bottom: 0; left: 0; color: #494543; z-index: 2; animation: fadeInUp 1s .2s ease both; }
.dia_close { width: 77px; height: 77px; left: 50%; top: -95px; margin-left: -38px; }
.dia_nr { padding: 52px 0; }
.dia_h2 { width: 674px; height: 29px; background-position: 0 -89px; margin: 0 auto; }
.scrollbox,.scrollbox_add {margin: 15px 0 0 26px; width: 704px; text-align: left; overflow-y: auto;max-height:1098px;}
.scrollbox::-webkit-scrollbar,.scrollbox_add::-webkit-scrollbar { width: 6px; background-color: #ddd8cb; border-radius: 3px; }
.scrollbox::-webkit-scrollbar-track,.scrollbox_add::-webkit-scrollbar-track { border-radius: 3px; }
.scrollbox::-webkit-scrollbar-thumb,.scrollbox_add::-webkit-scrollbar-thumb { background: #89663a; border-radius: 3px; }
.scrollcont { width: 698px; }
.nump { line-height: 36px; padding-left: 7px; }
.nump span { color: #89663a; display: inline-block; vertical-align: top; margin: 6px 4px 0 -3px; width: 24px; height: 24px; border: 1px solid #89663a; border-radius: 50%; line-height: 24px; text-align: center; }
.ppt1 { width: 662px; height: 340px; margin: 8px 0 0 7px; background: url(../img/ppt1.png) no-repeat; }
.ppt1 p { padding: 143px 0 0 216px; }
.ppt1 p span { padding-left: 126px; }
.ppt_tip { text-align: center; color: #ac401c; padding: 22px 0 26px; }
.ppt2 { width: 662px; height: 972px; margin: 15px 0 37px 7px; background: url(../img/ppt2.png) no-repeat; }
.ppt2 p:nth-child(1), .ppt2 p:nth-child(3) { top: 169px; width: 655px; text-align: center; left: 7px; }
.ppt2 p:nth-child(3) { top: 702px; }
.ppt2 p:nth-child(2) { left: 562px; top: 453px; }
.linkbox { margin-top: 17px; text-align: center; }
.linkbox a { display: inline-block; vertical-align: top; color: #ac401c; text-decoration: underline; }
@keyframes fadeInUp {
	0% { opacity: 0; transform: translateY(20px) }
	100% { opacity: 1; transform: translateY(0) }
}
.rewardbox { padding-top:14px; text-align: center; }
.reward1{ margin: 0 auto;width: 669px; height: 339px; background: url(../img/rewa1.png) no-repeat; background-size: 100%; position: relative;}
.reward1 .hdlist{margin-top:0;padding-top:94px;}
.reward1 .hdlist li{background:none;width:280px;}
.reward1 .hdlist li img{height:204px;}
.reward1 .hdlist li p{font-size:22px;color:#5c3a0e;margin-top:-62px;}
.reward2{ margin:52px auto 0; width: 669px; height: 616px; background: url(../img/rewa2.png) no-repeat; background-size: 100%; position: relative; }
.reward2 .hdlist{ margin-top:18px; }
.reward2 .hdlists { margin-top:56px;}
.reward2 .hdlisth li{width:183px;height:201px;background:url(../img/addlibgs.png) no-repeat;background-size:370px auto;margin:0 50px;position:relative;}
.reward2 .hdlists.hdlisth li{margin:0 8px;}
.reward2 .hdlisth li.firstli{background-position:right top;}
.yjicon{width:85px;height:70px;background:url(../img/ycicon.png) no-repeat;background-size:100% auto;text-indent:-9999em;top:5px;right:-21px;}
.reward2_p{line-height:32px;padding-top:42px;font-size:19px;}
.reward2_p strong{color:#a80000;font-size:22px;display:block;font-weight:bold;}
.reward2 .hdlist li div{display:flex;justify-content:center;align-items:center;}
.reward2 .hdlist li div img{height:auto;padding-top:0;display:inline;}
.hdlisth li p{padding-top:7px;}
.reward2 h2 {position: absolute; left:50%; width:290px;background:url(../img/addtit.png) no-repeat;height:60px;background-size:100% auto;top:-28px;margin-left:-145px;text-indent:-9999em;}
.reward2 h2.addtit{background-position:left bottom;}
.rulelist { line-height: 36px; }
.rulelist span { color: #ac401c; }
.rulelist li { padding-left: 50px; position: relative; width: 647px; }
.rulelist li em { font-style: normal; top: 3px; left: 9px; position: absolute; width: 30px; height: 30px; background: #eadbc8; border-radius: 50%; text-align: center; line-height: 30px; }
.qiehbox, .jltab { display: none; overflow: hidden; }
.qiehbox.dis, .jltab.dis { display: block; }
.hd_center { width: fit-content; margin: 0 auto; height: 70px; }
.hd_center li { margin: 0 1px; height: 70px; float: left; width: 229px; overflow: hidden; }
.hd_center li:nth-child(1) { background-position: 0 -318px; }
.hd_center li:nth-child(2) { background-position: -230px -318px; }
.hd_center li:nth-child(3) { background-position: -460px -318px; }
.hd_center li:nth-child(1).on { background-position: 0 -830px; }
.hd_center li:nth-child(2).on { background-position: -230px -830px; }
.hd_center li:nth-child(3).on { background-position: -460px -830px; }
.no_center .hd_center { height: 70px; width: 672px; margin: 0 auto; }
.no_center .hd_center li { width: 332px; height: 70px; float: left; margin: 0; }
.no_center .hd_center li:nth-child(2) { background-position: 0 -151px; margin-right: 4px; }
.no_center .hd_center li:nth-child(3) { background-position: -354px -151px; }
.no_center .hd_center li:nth-child(2).on { background-position: 0 -230px; }
.no_center .hd_center li:nth-child(3).on { background-position: -354px -230px; }
.jlhd { height: 29px; width: 533px; margin: 19px auto; }
.jlhd a { width: 105px; height: 29px; }
.jlhd a:nth-child(1) { float: left; background-position: 0 -443px; }
.jlhd a:nth-child(2) { width: 131px; float: left; margin-left: 96px; background-position: -139px -441px; }
.jlhd a:nth-child(3) { float: right; background-position: -292px -402px; }
.jlhd a:nth-child(1).on { background-position: 0 -402px; }
.jlhd a:nth-child(2).on { background-position: -137px -402px; }
.jlhd a:nth-child(3).on { background-position: -292px -443px; }
.cjm_tip { width: 609px; margin: 19px auto 0; }
.cjm_tip span { color: #ac401c; }
.rewardtable { width: 608px; margin: 0 auto; }
.rewardtable th, .rewardtable td { border: 1px solid #b09152; text-align: center; }
.rewardtable th { height: 54px; background: #eadbc8; width: 50%; border-bottom: none; }
.rewardtable tr:nth-child(2) td { border-top: none; }
.rewardtable td { padding: 12px 5px; }
.djbox { margin:0 auto 0; width: 669px; height: 363px; background: url(../img/djbox.png) no-repeat; background-size: 100%; }
.djlist { padding-top: 66px; width: 632px; margin: 0 auto; }
.djlist li { width: 158px; float: left; height: 280px; }
.djlist li:nth-child(1) { background: url(../img/dj0.png) no-repeat center top; background-size: 153px auto; }
.djlist li:nth-child(2) { background: url(../img/dj1.png) no-repeat center top; background-size: 153px auto; }
.djlist li:nth-child(3) { background: url(../img/dj2.png) no-repeat center top; background-size: 153px auto; }
.djlist li:nth-child(4) { background: url(../img/dj3.png) no-repeat center top; background-size: 153px auto; }
.djlist li p { padding-top: 137px; height: 60px; color: #fff; text-align: center; line-height: 60px; }
.zs_btn { margin: 15px auto 0; width: 120px; height: 42px; text-indent: 0; background-position: -126px 0; color: #895d25; font-weight: bold; text-align: center; line-height: 42px; text-shadow: 0 1px 1px #fbedc5; }
.mypicbox { margin: 38px auto 0; width: 669px; height: 444px; background: url(../img/picbox.png) no-repeat; background-size: 100%; }
.mypiclist { padding-top: 73px; width: 628px; margin: 0 auto; }
.mypiclist li { width: 157px; float: left; position: relative; }
.mypiclist li img { width: 150px; display: block; margin: 0 auto; }
.ssuobtn { width: 88px; height: 88px; background-position: -520px -407px; top: 88px; left: 50%; margin-left: -44px; }
.mypiclist li p { text-align: center; padding-top: 8px; }
.pop { width: 686px; height: 464px; background: url(../img/pop.png) no-repeat; background-size: 686px auto; position: relative; display: none; }
.gbbtn { width: 54px; height: 54px; background-position: -269px 0; display: block; text-indent: -9999em; top: 6px; right: 30px; }
.pop_cont { padding: 30px 34px 0; }
.pop_h2 { text-align: center; font-weight: bold; }
.pop_h2 span { color: #d6612d; font-size: 36px; align-self: center; -webkit-align-self: center; width: 100%; background-image: -webkit-linear-gradient(top, #d66230, #e59249); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.dllist { width: 556px; margin: 0 auto; text-align: center; }
.dllist li { width: 278px; float: left; padding-top: 25px; }
.wxicon, .glicon { margin: 13px auto 0; width: 183px; height: 184px; }
.wxicon { background-position: 0 -495px; }
.glicon { background-position: -218px -495px; }
.bdbtn { margin: 17px auto 0; width: 241px; height: 67px; background-position: -443px -535px; }
.p2_txt { padding-top: 18px; }
.pop1 { height: 310px; background: url(../img/pop1.png) no-repeat; background-size: 686px auto; }
.pop1 .pop_cont { padding-top: 64px; }
.pop1 .pop_h2 { height: 56px; }
.pophbg { background: url(../img/pophbg.png) no-repeat center top; background-size: auto 56px; }
.pop_inf { height: 122px; display: flex; display: -webkit-flex; }
.pop_inf p { width: 100%; align-self: center; -webkit-align-self: center; text-align: center; }
.pop_cont1 { padding-top: 48px; }
.hdlist { margin-top: 40px; height: 184px; }
.hdlist li { width: 278px; background: url(../img/libg.png) no-repeat center top; background-size: 183px auto; height: 184px; display: inline-block; vertical-align: top; }
.reward3 .hdlist li { background: url(../img/addlibg.png) no-repeat center top; background-size: 183px auto; height: 201px; }
.hdlist li div { height: 137px; position: relative; margin: 0 auto; position: relative; }
.hdlist li div img { display: block; height: 132px; margin: 0 auto; padding-top: 5px; }
.hdlist li.tc_picimg div img { padding-top: 0; height: 137px; }
.hdlist li p { color: #fff; height: 45px; line-height: 24px; display: flex; display: -webkit-flex; justify-content: center; -webkit-justify-content: center; align-items: center; -webkit-align-items: center; }
.reward3 .hdlist li p { height: 60px; }
.use_tip { padding-top: 40px; }
.use_tip span { color: #d76430; }
.pop2 { height: 408px; background: url(../img/pop2.png) no-repeat; background-size: 686px auto; }
.pop2 .pop_cont { padding-top: 46px; }
.pop2 .pop_inf { padding-top: 4px; }
.surebtn { margin: 33px auto 0; width: 292px; height: 80px; background-position: -430px -639px; text-indent: 0; line-height: 76px; color: #895d25; font-weight: bold; font-size: 32px; text-shadow: 0 1px 1px #fbedc5; }
.surebtn.mt6 { margin-top: 6px; }
.use_tip_top { padding-top: 14px; margin-bottom: -15px; }
.use_tip_top1 { text-align: left; width: 472px; margin: 20px auto -20px; }
.use_tip_top1 span { float: right; padding-right: 99px; }
.use_tip1 { padding-top: 33px; }
.pop_inf_full { height: 150px; }
.pop3 { height: 571px; background: url(../img/pop3.png) no-repeat; background-size: 686px auto; }
.pop5 { height: 668px; background: url(../img/pop4.png) no-repeat; background-size: 686px auto; }
.pop_tc { text-align: center; margin-top: 25px; }
.pop_tc a { display: inline-block; vertical-align: top; width: 241px; height: 67px; margin: 0 18px; text-indent: 0; font-size: 24px; font-weight: bold; line-height: 60px; }
.pop_tc a.btn1 { background-position: 0 -742px; color: #8a612b; text-shadow: 0 1px 1px #fbedc5; }
.pop_tc a.btn2 { background-position: -291px -742px; color: #fbddc3; text-shadow: 0 1px 1px #af3126; }
.sharebox { width: 246px; height: 351px; background: url(../img/sharebg.png) no-repeat; background-size: 100%; top: 0; right: 0; position: absolute; z-index: 2; padding-top: 229px; }
.sharebox p { height: 358px; width: 100px; color: #b2483f; writing-mode: vertical-lr; display: flex; display: -webkit-flex; align-items: center; -webkit-align-items: center; justify-content: center; -webkit-justify-content: center; }
.clostep1 { background: url(../img/clostep1.png) no-repeat; background-size: 100% auto; width: 694px; height: 214px; margin-bottom: 26px; padding-left: 2px; }
.clostep1 li { float: left; padding-top: 147px; width: 124px; text-align: center; }
.clostep1 li:last-child { padding-left: 60px; }
.pop1 .pop_cont1 { padding-top: 38px; }
.datu_pop { width: 662px; height: 934px; position: relative; display: none; }
.datu_pop .dia_close { top: -89px; }
.datu_pop img { width: 100%; display: block; }
.pop_inf_short { height: 96px; }
.use_tip2 span, .pop_inf span { color: #ac401c; }
.loadEffect { z-index: 2; display: none; width: 100px; height: 100px; position: absolute; top: 345px; left: 50%; margin-left: -50px; }
.loadEffect span { display: inline-block; width: 20px; height: 20px; border-radius: 50%; background: #fff; position: absolute; -webkit-animation: load 1.04s ease infinite; }
@-webkit-keyframes load {
	0% { -webkit-transform: scale(1.2); opacity: 1; }
	100% { -webkit-transform: scale(.3); opacity: 0.5; }
}
.loadEffect span:nth-child(1) { left: 0; top: 50%; margin-top: -10px; -webkit-animation-delay: 0.13s; }
.loadEffect span:nth-child(2) { left: 14px; top: 14px; -webkit-animation-delay: 0.26s; }
.loadEffect span:nth-child(3) { left: 50%; top: 0; margin-left: -10px; -webkit-animation-delay: 0.39s; }
.loadEffect span:nth-child(4) { top: 14px; right: 14px; -webkit-animation-delay: 0.52s; }
.loadEffect span:nth-child(5) { right: 0; top: 50%; margin-top: -10px; -webkit-animation-delay: 0.65s; }
.loadEffect span:nth-child(6) { right: 14px; bottom: 14px; -webkit-animation-delay: 0.78s; }
.loadEffect span:nth-child(7) { bottom: 0; left: 50%; margin-left: -10px; -webkit-animation-delay: 0.91s; }
.loadEffect span:nth-child(8) { bottom: 14px; left: 14px; -webkit-animation-delay: 1.04s; }
.suslist { padding: 16px 0 0 55px; text-align: left; height: 378px; }
.suslist li { float: left; width: 212px; }
.suslist li:nth-child(1) img { padding-top: 9px; }
.suslist li:nth-child(2) { padding: 77px 0 0 82px; width: 222px; }
.dhdaoju { margin: 10px 0 0 10px; width: 212px; height: 169px; background: url(../img/addbg.png) no-repeat; text-align: center; background-size: 100%; }
.dhdaoju div { padding-top: 2px; display: flex; display: -webkit-flex; height: 116px; }
.dhdaoju img { height: 114px; align-self: center; -webkit-align-self: center; display: block; margin: 0 auto; }
.dhdaoju p { color: #fff; line-height: 50px; }
.adddjbox { width: 168px; top: 80px; left: 49px; position: absolute; z-index: 4; text-align: center; opacity: 0; }
.adddjbox div { width: 74px; height: 74px; background: url(../img/smallbg.png) no-repeat; background-size: 100%; margin: 0 auto; display: flex; }
.adddjbox div img { max-height: 74px; margin: 0 auto; align-self: center; -webkit-align-self: center; margin: 0 auto; display: block; }
.adddjbox p { color: #725836; padding-top: 4px; }
.bxbtn { width: 219px; height: 224px; right: 0; position: absolute; z-index: 5; background: url(../img/flobg.png) no-repeat; text-indent: -9999em; background-position: -219px 0; top: 550px; }
.bxbtn_longmen {background: url(../img/giftBox_longmen.png) no-repeat;background-position: -219px 0; }
.kaix { background-position: 0 0; animation: blings 2s infinite ease-in-out; }
.pop6 { height: 1042px; background: url(../img/pop5.png) no-repeat; background-size: 100%; }
.pop6 .pop_cont { padding-top: 36px; }
.bx_tip { padding: 10px 38px 17px; text-align: left; line-height: 30px; text-indent: 2em; }
.bx_tip span { color: #da6e3b; }
.bxswiper { width: 450px; height: 590px; margin: 0 auto; }
.bxswiper img { width: 410px; height: 581px; border: 2px solid #8c7353; display: block; margin: 0 auto; }
.bianma { display: block; color: #494543; font-weight: bold; padding: 31px 0 14px; }
.bianmatip { width: 485px; height: 52px; background: #dfc18f; border: none; color: #da6e3b; font-weight: bold; font-size: 22px; text-align: center; line-height: 52px; margin-bottom: 28px; }
.wishtit { margin: 37px auto 0; width: 416px; height: 29px; background-position: 0 -1338px; }
.wish_tip { padding: 15px 0 25px; }
.whislist { width: 704px; margin: 0 auto; }
.whislist li { float: left; width: 176px; }
.whislist li img { margin: 0 auto; display: none; width: 167px; }
.whislist li img.lockimg, .whislist li.wjsli img.lockedimg { display: block; }
.whislist li.wjsli img.lockimg { display: none; }
.addbianma { color: #d76430; }
.use_tipadd { padding-top: 10px; }
.hdlists li { width: 196px; }
.addjllist{margin:47px auto 0;width:676px;height:438px;}
.addjllist li{float:left;width:338px;}
.addjllist li img{margin:0 auto;width:307px;border:2px solid #b39b66;display:block;}
.addtitcom{margin:25px auto;width:159px;height:31px;background:url(../img/addtitcom.png) no-repeat;text-indent:-9999em;background-size:159px auto;}
.addtitcom1{background-position:0 -52px;}
.addtitcom2{background-position:0 -102px;}
.dandanimg{margin:20px auto 0;display:block;}