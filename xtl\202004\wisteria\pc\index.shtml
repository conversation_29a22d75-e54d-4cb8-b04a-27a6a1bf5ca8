<!DOCTYPE HTML>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="keywords" content="天龙,新天龙,天龙八部,新天龙八部,天龙八部3,tl,tlbb,tl3,xtl,网络游戏,网游,武侠,浪漫武侠,武侠游戏,金庸,MMO,天龙八部OL,萧峰,段誉,虚竹,王语嫣,天龙八部钟汉良版,天龙八部黄日华版,8周年,八周年,周年庆,全民代言,纵情四海" />
<meta name="description" content="《新天龙八部》十三周年，万象更新！全新国韵华裳、音乐、场景为江湖注入新的灵魂，展现传统东方文化之美。多重周年福利上线！" />
<meta name="author" content="Design:CP; Web Layout:CP;" />
<title>藤花正晓晴-《新天龙八部》官方网站</title>
<link type="image/x-icon" href="http://xtl.changyou.com/favicon.ico" rel="icon"/>
<link type="image/x-icon" href="http://xtl.changyou.com/favicon.ico" rel="shortcut icon" /> 
<link type="text/css" rel="stylesheet" href="/all/css/reset.css" />
<link type="text/css" rel="stylesheet" href="css/style.css" />
</head>
<script>
(function() {
if(/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)){var a=document.referrer,b={"baidu.com":"seo_baidu","sogou.com":"seo_sogou","sm.cn":"seo_sm","so.com":"seo_360","bing.com":"seo_bing","google.com":"seo_google"},c;for(c in b){if(-1!=a.indexOf(c)){a=b[c];if(window.sessionStorage){sessionStorage.setItem("channel",a)}else{var d=d||0,b="";0!=d&&(b=new Date,b.setTime(b.getTime()+1000*d),b="; expires="+b.toGMTString());document.cookie="channel="+escape(a)+b+"; path=/"}break}}window.location.href="../m/index.shtml"};
})();
</script>
<body>
<h1 class="none">藤花正晓晴</h1>
<div class="xtl_logo_lk">
	<a href="http://tl.changyou.com/index.shtml" target="_blank" title="新天龙八部"></a>
</div>
<div class="xtl_logo_bg"></div>
<div class="page wrapper">
    <div class="part head show">
		
	</div>
	<div class="part part1 part-n">
		
	</div>
	<div class="part part2 part-n">
		
	</div>
	<div class="part part3 part-n">
		
	</div>
	<div class="part part4 part-n">
		
	</div>
</div>
<!--侧导航-->
<div class="float">
    <div class="float-con">
        <a class="nav0" href="javascript:;" date-page="2" title="春季新装">春季新装</a>
        <a class="nav1" href="javascript:;" date-page="3" title="元气新妆">元气新妆</a>
        <a class="nav2" href="javascript:;" date-page="4" title="精灵坐骑">精灵坐骑</a>
		<a class="nav3" href="javascript:;" date-page="5" title="幻饰武器">幻饰武器</a>
    </div>
	<a href="javascript:;" class="back-btn" title="返回首页">返回首页</a>
</div>
<a href="javascript:;" class="arrow com pa">滚动鼠标查看</a>
<script src="/all/js/jquery-1.8.3.min.js"></script>
<script src="js/jquery.mousewheel.js"></script>
<script src="js/index.js"></script>
<!--#include virtual="/all/nav/xtl_dark.html"-->
<!--#include virtual="/all/dma/dma_static.html"-->
</body>
</html>
