﻿function ImgPlayer(option) {
    var def = {
        el: null,
        source: [],
        autoplay: true,
        loop: true,
        filmCounts: 24,
        onPlay: function () {
        },
        onPause: function () {
        },
        onPlaying: function (time, index) {
            // console.log(time, index);
        },
        onComplete: function () {
        },
        onReady: function () {}
    }
    var o = Object.assign(def, option);
    if (!o.el) {
        throw '请设置容器元素dom'
    }
    var el = o.el,
        filmCounts = o.filmCounts,
        autoplay = o.autoplay,
        loop = o.loop,
        onPlay = o.onPlay,
        _onReady = o.onReady,
        onPause = o.onPause,
        onPlaying = o.onPlaying,
        onComplete = o.onComplete,
        source = o.source,
        interId = null,
        status = '',
        time = 0,
        canvas = document.createElement('canvas'),
        ctx = canvas.getContext('2d'),
        width = $(el).width(),
        height = $(el).height();
    console.log(width, height);
    canvas.width = width;
    canvas.height = height;
    var index = 0,
        countLoad = 0,
        total = source.length,
        loadStatus = false,
        imgs = [];


    loadSource();

    function loadSource() {
        source.forEach(function (url) {
            var img = document.createElement('img');
            img.src = url;
            img.onload = function () {
                countLoad++
                if (countLoad >= total) {
                    loadStatus = true
                    onReady()
                }
            }
            img.onerror = function () {
                countLoad++
                if (countLoad >= total) {
                    loadStatus = true
                    onReady()
                }
            }
            imgs.push(img)
        });
    }

    function onReady() {
        console.log('on ready')
        el.append(canvas);
        if (autoplay) play()
        _onReady()
    }

    function draw() {
        if (index >= total) {
            index = 0
            time = 0
            complete()
        } else {
            ctx.clearRect(0, 0, width, height);
            time = (index * (1000 / filmCounts)).toFixed(1);
            if (total > 0) {
                onPlaying(time, index)
                ctx.drawImage(imgs[index], 0, 0);
            }
            index++;
        }
    }

    function getCurrentTime() {
        return time
    }

    function getStatus() {
        return status
    }

    function complete() {
        console.log('do complete')
        status = 'finish'
        window.clearInterval(interId)
        onComplete()
        if (loop) {
            play()
        }
    }

    function play() {
        if (total <= 0) {
            throw 'please set imgs';
        }
        if (!loadStatus) {
            throw 'please wait imgs load finish';
        }
        if (status != 'playing') {
            console.log('do play')
            status = 'playing'
            interId = setInterval(function () {
                draw();
            }, 1000 / filmCounts)
        }
    }

    function pause() {
        console.log('do pause')
        status = 'pause'
        window.clearInterval(interId)
    }

    var r = {
        play: play,
        pause: pause,
        loop: loop,
        autoplay: autoplay,
        time: time,
        status: status
    }
    Object.defineProperty(r, 'autoplay', {
        set(v) {
            autoplay = v
            play()
            return autoplay
        },
        get() {
            return autoplay
        }
    })
    Object.defineProperty(r, 'loop', {
        set(v) {
            loop = v
            if (status === 'finish') play()
            return loop
        },
        get() {
            return loop
        }
    })
    Object.defineProperty(r, 'time', {
        get() {
            return time
        }
    })
    Object.defineProperty(r, 'status', {
        get() {
            return status
        }
    })
    return r
}
