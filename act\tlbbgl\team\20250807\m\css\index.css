@charset "utf-8";
html,body,div,p,ul,li,dl,dt,dd,em,i,span,a,img,input,h1,h2,h3,h4,h5{margin:0;padding:0;}
a,img,input{border:none;outline:none;}
a{text-decoration:none;}
img{display:block;}
ul,li{list-style:none;}
table{border-collapse:collapse;border-spacing:0;}
body{font:24px/1.5 -apple-system,"Helvetica Neue",Helvetica,Arial,sans-serif;-webkit-user-select: none;background-color:#fff;}
.none{display:none;}
input::-webkit-outer-spin-button,input::-webkit-inner-spin-button{-webkit-appearance: none !important;}
input[type="number"]{-moz-appearance:textfield;}
*{-webkit-appearance:none;-webkit-text-size-adjust:none;}
html{-webkit-tap-highlight-color:rgba(0,0,0,0);}
#cy_bot{padding:10px!important;position: relative;z-index: 2}
.cyLogo{margin-top:10px;}
.orientLayer {
    display: none;
    position: fixed;
    z-index: 1000;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, .9);
    color: #fff;
    font-size: 30px;
}

html,body{width:100%;touch-action: manipulation;max-width: 750px;margin: 0 auto;}
.pr{position: relative;}
.pa{position: absolute;}
.hide{font-size: 0; text-indent: -9999em; width: 0; height: 0;}
.t{font-size: 0; text-indent: -9999em;display: block}
img{width:100%;}
p{text-align: center;}
/***public***/
i,em{font-style: normal;}
.wrap{
    text-align: center;
    width: 100%;
    max-width:750px;
    height: 100%;
    background: url(../img/page02.jpg) no-repeat center;
    background-size: 100% auto;
    margin: 0 auto;
}
/* page2 */
.page2{width: 100%;height: 1650px;background: url(../img/page02-mass1.png) no-repeat center;background-size: 100% auto;}
.page2 .bt-box{margin-top:95px;}

/* page3 */
.page3{width: 100%;height: 1650px;background: url(../img/page02-mass1.png) no-repeat center;background-size: 100% auto;}

/* page4 */
.page4{width: 100%;height: 1650px;background: url(../img/page02-mass1.png) no-repeat center;background-size: 100% auto;}
.page4 .bt-box{margin-top:95px;}

/* page5 */
.page5{width: 100%;height: 1650px;background: url(../img/page02-mass1.png) no-repeat center;background-size: 100% auto;}

/* page6 */
.page6{width: 100%;height: 1650px;background: url(../img/page02-task.png) no-repeat center;background-size: 100% auto;}
.task-list{width: 630px;height: 724px;display: flex;flex-direction: column;justify-content: space-between;align-items: center;margin: 480px auto 0;overflow: hidden;}
.task-list li{width: 630px;height: 158px;background: url(../img/task-itemBg.png) no-repeat center;background-size: 100% auto;position: relative;}
/* .task-list li:nth-child(4){width: 630px;height: 160px;background: url(../img/task-itemBg.png) no-repeat center;background-size: 100% auto;position: relative;} */
.task-list li>img{width: 146px;height: 146;margin-left: 8px;overflow: hidden;margin-top: 6px;}
/* .task-list li:nth-child(4)>img {height: 168px;margin: 5px 0 5px 8px;} */
.task-list li .task-gift{width: 150px;height: 158px;position: absolute;top: 0;left: 160px;display: flex;flex-direction: column;justify-content: space-evenly;align-items: center;} /*background: url(../img/taskBg.png) no-repeat center;*/
/* .task-list li:nth-child(4) .task-gift{height: 178px;} */
.task-list li .task-gift:nth-child(2){left: 336px;}
.task-list li .task-gift>img{width:82px;height: 82px;overflow: hidden;}
.task-list li .task-gift p{font-size: 18px;color: #40382f;line-height:18px;font-weight: 500;}
/* .task-list li:nth-child(4) .task-gift p{line-height: 20px;height: 38px;display: flex;align-items: center;} */
.page6 .btn-taskGet{width: 146px;height:100%;background: url(../img/btn-taskGetUN.png) no-repeat center;background-size: 100% auto;position: absolute;right: 6px;top: 50%;transform: translateY(-50%);}
.page6 .btn-taskGet.get{width: 146px;height:100%;background: url(../img/btn-taskGet.png) no-repeat center;background-size: 100% auto;}
.page6 .btn-taskGet.gray{filter: grayscale(100%); width: 146px;height:100%;background: url(../img/btn-gray.png) no-repeat center;background-size: 100% auto;}
.page6 .btn-backHome{width: 160px;height:54px;background: url(../img/btn-backHome.png) no-repeat center;background-size: 100% auto;margin-bottom: 22px;}
.page6 .btn-member{width: 160px;height:54px;background: url(../img/btn-member.png) no-repeat center;background-size: 100% auto;margin-right: 25px;margin-bottom: 22px;}
.page6 .task-pop{width: 284px;height: 194px;background: url(../img/task-pop.png) no-repeat center;background-size: 100% auto;position: absolute;bottom: 178px;display: none;}
.page6 .task-pop>p{color: #ffeecf;font-size: 20px;text-align: center;align-content: center;margin-top: 46px;line-height: 32px;padding: 0 10px 0 20px;height: 90px;overflow-x: hidden;overflow-y: auto;}
/* pop */
.pop{ background:#816f57; width:578px; height:auto; color: #fff; font-size: 24px; text-align: center; margin: 0 auto 20px;  position: relative;display:none;}
.btn-close{width: 60px;height: 51px;position: absolute;top: 18px;right: 4px;z-index: 10;background: url(../img/btn-close.png) no-repeat center;background-size: 100% auto;}
/* .pop-home{display: block;} */

.pop-login{width: 660px;height:675px;background: none;background-size: 100% auto;}
.pop-login .login-box, .pop-login iframe {width: 100%; height: 100%;}
.pop-login .btn-close{top: -30px; right: -40px;}

.pop-common{width: 608px;height:433px;background: url(../img/pop-common.png) no-repeat center;background-size: 100% auto;}
.pop-common .btn-confirm{width:196px;height:65px;background: url(../img/btn-confirm3.png) no-repeat center;background-size: 100% auto;}
.pop-common .btn-bind{width:196px;height:65px;background: url(../img/bind.png) no-repeat center;background-size: 100% auto;margin: 0 auto;}
.pop-common .btn-create{width:239px;height:65px;background: url(../img/btn-create.png) no-repeat center;background-size: 100% auto;}
.pop-common .btn-phone{width:239px;height:65px;background: url(../img/btn-phone.png) no-repeat center;background-size: 100% auto;}
.pop-common .btn-close{top: -30px;right: -10px;}

.pop-userinfo1{width: 675px;height:674px;background: url(../img/pop-userinfo1.png) no-repeat center;background-size: 100% auto;}
.pop-userinfo1 .ipt-box{width:580px;height:70px;background: url(../img/ipt-name1.png) no-repeat center;background-size: 100% auto;margin:14px auto;}
.pop-userinfo1 .ipt-name{width:440px;}
.pop-userinfo1 .btn-selectAvatar{width:580px;height:70px;background: url(../img/ipt-avatar.png) no-repeat center;background-size: 100% auto;margin: 0 auto;}
.pop-userinfo1 .btn-confirm{width:340px;height:78px;background: url(../img/btn-confirm1.png) no-repeat center;background-size: 100% auto;margin: 0 auto;}
.pop-userinfo1 .avatar-list{width: 600px;height: 146px;display: flex;flex-direction: row;justify-content: space-between;align-items: center;overflow-x: scroll;margin:30px auto 0;}
.pop-userinfo1 .avatar-list li{width: 146px;height: 146px;background: url(../img/avatarBg.png) no-repeat center;background-size: 100% auto;position: relative;}
.pop-userinfo1 .avatar-list li.active::after{content: "";display: block;position: absolute;top: 0;right: 0;bottom: 0;left: 0;width: 146px;height: 146px;background: url(../img/avatarBg-ac.png) no-repeat center;background-size: 100% auto;}
.pop-userinfo1 .avatar-list li img{width: 120px;height: 120px;padding-top: 14px;margin-left: 15px;border-radius: 50%; overflow: hidden;}
.pop-userinfo1 .btn-close{top: -35px;right: -10px;}

.pop-userinfo2{width: 675px;height:464px;background: url(../img/pop-userinfo2.png) no-repeat center;background-size: 100% auto;}
.pop-userinfo2 .userinfo-box{height: 146px;margin-left: 54px;position: relative;}
.pop-userinfo2 .userinfo-box .avatar{width: 146px;height: 146px;background: url(../img/avatarBg.png) no-repeat center;background-size: 100% auto;}
.pop-userinfo2 .userinfo-box .avatar>img{width: 120px;height: 120px;padding-top: 14px;margin-left: 14px;border-radius: 50%; overflow: hidden;}
.pop-userinfo2 .ipt-box{width:384px;height:70px;background: url(../img/ipt-name2.png) no-repeat center;background-size: 100% auto;position: absolute;top: 38px;left: 180px;}
.pop-userinfo2 .ipt-name{width:240px;}
.pop-userinfo2 .btn-confirm{width:253px;height:78px;background: url(../img/btn-confirm2.png) no-repeat center;background-size: 100% auto;margin: 0 auto;}
.pop-userinfo2 .btn-cancel{width:253px;height:78px;background: url(../img/btn-cancel.png) no-repeat center;background-size: 100% auto;margin: 0 auto;}
.pop-userinfo2 .txt1{font-size: 28px;}
.pop-userinfo2 .btn-boxPop{bottom: 73px}/*bottom: 40px*/
.pop-userinfo2 .btn-close{top: -45px;right: -10px;}

.pop-shareWX{width:100%;height:100%;background:rgba(0,0,0,0.8) url(../img/pop-shareWX.png) right 10% top 188px/482px 412px  no-repeat ; position:fixed;top:0;left:0;z-index:999;display:none;}
.pop-shareWX .btn-close{top: 307px;right: 58px;width: 42px;height: 37px;}

.pop-share{width: 608px;height:393px;background: url(../img/pop-share.png) no-repeat center;background-size: 100% auto;}
.pop-share .txt1{width: 340px;}
.pop-share .ipt-copy{width: 479px;height:67px;background: url(../img/share-ipt2.png) no-repeat center;background-size: 100% auto;font-size: 22px;color: #fff;line-height: 40px;box-sizing: border-box;padding: 10px 62px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
.pop-share .ipt-copyBtn{width: 479px;height:67px;background: url(../img/share-ipt.png) no-repeat center;background-size: 100% auto;font-size: 30px;color: #fff;line-height: 40px;padding: 10px 0;margin-top: 20px;}
.pop-share .btn-close{top: -45px;right: -10px;}

.pop-apply{width: 633px;height:622px;background: url(../img/pop-apply.png) no-repeat center;background-size: 100% auto;padding-top: 220px;}
.pop-apply .btn-boxPop{width: 346px; height: 112px;left: 324px;bottom: 0;}
.pop-apply .btn-agree{width:160px;height:54px;background: url(../img/btn-agree.png) no-repeat center;background-size: 100% auto;margin: 0 auto;}
.pop-apply .btn-refuse{width:160px;height:78px;background: url(../img/btn-refuse.png) no-repeat center;background-size: 100% auto;margin: 0 auto;}
.pop-apply .btn-close{top: 40px;right: -10px;}


.pop-state{width: 627px;height:655px;background: url(../img/pop-state.png) no-repeat center;background-size: 100% auto;}
.pop-state .btn-unLogin{width:207px;height:54px;background: url(../img/btn-unLogin.png) no-repeat center;background-size: 100% auto;margin: 0 auto;margin-left: 10px;}
.pop-state .btn-unLogin.logined{width:207px;height:54px;background: url(../img/btn-logined.png) no-repeat center;background-size: 100% auto;margin: 0 auto;margin-left: 10px;}
.pop-state .user-level{width:207px;height:54px;background: url(../img/apply-nameBg.png) no-repeat center;background-size: 100% 100%;margin: 0 auto;text-align: center; font-size: 24px; line-height: 54px; color:#fbfbf1; text-shadow: 0 2px 2px #474444;}
.pop-state .btn-boxPop{width: 346px; height: 112px;left: 324px;bottom: 0;}
.pop-state .txt1{padding-top:110px;margin-bottom: 0;}
.pop-state .user-list{height: 455px;}
.pop-state .btn-close {top: -45px;right: -10px;}

.pop-rule{width: 627px;height:900px;background: url(../img/pop-rule.png) no-repeat center;background-size: 100% auto;}
.pop-rule .txt-box{height: 720px;overflow-y: scroll;margin-top: 33px;}
.pop-rule .rule-box{height: auto;overflow-y: hidden;}
.pop-rule .rule-box p{text-align: start;font-size: 18px;color: #000;line-height: 30px;}
.pop-rule .rule-box p i{text-align: center;font-size: 18px;color: #fff;line-height: 30px;text-align: center;width:30px;height:30px;background: url(../img/numBg.png) no-repeat center;background-size: 100% auto;position: absolute;}
.pop-rule .rule-box p span{padding-left: 32px;}
.pop-rule .btn-close{top: -50px;right: -10px;}

.pop-home{width: 608px;height:761px;background: url(../img/pop-home.png) no-repeat center;background-size: 100% auto;}
.pop-home .txt2{padding: 130px 70px 0;}
.pop-home .txt2,.pop-home ul li{font-size: 20px;color: #000;text-align: start;}
.pop-home ul {width: 508px;height:380px;margin: 150px auto 0;overflow-y: scroll;}
.pop-home ul li{display: flex;flex-direction: row;justify-content: start;align-items: center;margin: 10px 0; border-bottom: 1px dashed #cccccc;}
.pop-home h1{font-size: 18px;padding: 0;width: 160px;margin:0;flex-shrink: 0;}
.pop-home h1{line-height: 48px;}
.pop-home h2{line-height: 28px;margin-left: 10px; text-align: left;}
.pop-home .btn-close{top: -20px;right: -10px;}


@media screen and (min-width: 1200px) {
    .pop-rule {
        width: 510px;
        height: 887px;
    }
    .pop-rule .btn-close {
    width: 52px;
    top: 10px;
    }

    .pop-rule .txt-box {
        height: 80%;
    }

    .pop .txt-box {
        width:86%;
    }
    .pop-rule .txt-box p {
    line-height: 36px;
    }

   .pop-apply .btn-close {
    top: 128px;
   }
   .pop-state .btn-close {
    top: 80px;
   }
}