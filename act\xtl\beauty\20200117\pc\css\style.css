@charset "utf-8";
@import url(http://tl.changyou.com/logo/xtl_logo.css);

body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, textarea, p, blockquote, th, td {
	margin: 0;
	padding: 0;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

address, caption, cite, code, dfn, em, strong, th, var {
	font-weight: normal;
	font-style: normal;
}

ol, ul {
	list-style: none;
}

caption, th {
	text-align: left;
}

h1, h2, h3, h4, h5, h6 {
	font-weight: normal;
	font-size: 100%;
}

q:before, q:after {
	content: '';
}

fieldset, img, abbr, acronym {
	border: 0;
}

/*页面样式*/
.c:before, .c:after {
	content: "";
	display: table;
}

.c:after {
	clear: both;
}

.c {
	zoom: 1;
}

.pr {
	position: relative;
}

.pa {
	position: absolute;
}

.xtl_logo_bg, .xtl_logo_lk a {
	left: 50%;
	top: 17px;
	margin-left: -778px;
}

.xtl_logo_lk a {
	text-indent: -9999em;
}

select {
	-webkit-appearance: none;
	-webkit-text-size-adjust: none;
}

body {
	font: 12px/1.5 '\5FAE\8F6F\96C5\9ED1';
	padding-top: 55px;
	text-align: justify;
	min-width: 1200px;
}

a {
	text-decoration: none;
	overflow: visible;
}

a:hover {
	text-decoration: none;
}

.none {
	display: none;
}

.com, .btn, .pop_h3:before {
	background: url(../img/com.png) no-repeat;
	display: block;
	overflow: hidden;
	text-indent: -9999em;
}

.btn {
	transition: filter 0.2s linear;
}

.btn:hover {
	filter: brightness(1.1);
}

.bar {
	width: 1200px;
	margin: 0 auto;
	position: relative;
	z-index: 3;
	padding-bottom: 86px;
}

.page {
	text-align: center;
	position: relative;
	z-index: 1;
	overflow: hidden;
	zoom: 1;
	color: #95676e;
	font-size: 14px;
}

.bg {
	width: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: -1;
}

.bg1, .bg2, .bg3, .bg4 {
	height: 400px;
	background: url(../img/bg1.jpg) no-repeat center top;
}

.bg2 {
	background-image: url(../img/bg2.jpg);
}

.bg3 {
	background-image: url(../img/bg3.jpg);
}

.bg4 {
	background-image: url(../img/bg4.jpg);
	height: 2500px;
}

.bg5 {
	background-image: url(../img/bg5.jpg);
	height: 2085px;
}

.head {
	height: 909px;
}

.canyutxt {
	width: 32px;
	top: 576px;
	left: 50%;
	margin-left: 224px;
	font-size: 20px;
	color: #fff2ec;
	line-height: 24px;
}

.time {
	color: #935f5f;
	font-size: 16px;
	width: 20px;
	line-height: 16px;
	top: 558px;
	left: 50%;
	margin-left: 191px;
}

.daoyubox {
	width: 745px;
	height: 153px;
	background: url(../img/daoyubg.png) no-repeat;
	margin: 0 auto;
}

.daoyubox p {
	padding-top: 52px;
	line-height: 22px;
}

.login {
	color: #585348;
	margin-top: 3px;
	z-index: 2;
	position: relative;
}

.login a {
	color: #c05b38;
}

.csbtn {
	margin: -35px auto 0;
	z-index: 1;
	position: relative;
	width: 328px;
	height: 116px;
}

.steps {
	margin: 52px auto 0;
	width: 990px;
	height: 181px;
	position: relative;
	z-index: 2;
}

.steps ul {
	height: 181px;
}

.steps ul li {
	width: 330px;
	height: 181px;
	float: left;
	color: #de4457;
	line-height: 30px;
	position: relative;
}

.steps ul .stepbox {
	padding-top: 151px;
	height: 30px;
	width: 281px;
	margin: 0 auto;
}

.step1 .stepbox {
	background: url(../img/step1.png) no-repeat center top;
}

.step2 .stepbox {
	background: url(../img/step2.png) no-repeat center top;
}

.step3 .stepbox {
	background: url(../img/step3.png) no-repeat center top;
}

.fcbox {
	width: 530px;
	top: 229px;
	left: -113px;
	background: url(../img/fcrbg.jpg) repeat-y;
	display: none;
}

.fcbox:before, .fcbox:after {
	content: '';
	left: -124px;
	width: 778px;
	position: absolute;
	background: url(../img/fcbg.png) no-repeat;
}

.fcbox:before {
	top: -56px;
	height: 219px;
}

.fcbox:after {
	bottom: -46px;
	height: 46px;
	background-position: 0 -271px;
}

.steps ul.fclist {
	padding: 1px 0;
	height: auto;
}

.steps ul.fclist li {
	color: #8a7577;
	font-size: 14px;
	line-height: 24px;
	height: auto;
	width: 376px;
	padding-left: 127px;
	position: relative;
	text-align: left;
}

.steps ul.fclist li span {
	position: absolute;
	left: 0;
	top: 0;
	text-align: right;
	width: 127px;
	color: #f66574;
}

.jlbox {
	height: 817px;
}

.jlhd {
	top: 187px;
	z-index: 2;
	left: 862px;
	width: 336px;
}

.jlhd span {
	margin-top: 16px;
	display: block;
	text-indent: -9999em;
	width: 276px;
	height: 76px;
	cursor: pointer;
	background: url(../img/jlhd.png) no-repeat;
}

.jlhd span.jlhd2 {
	margin-left: 40px;
	background-position: 0 -76px;
}

.jlhd span.jlhd3 {
	margin-left: 60px;
	background-position: 0 -152px;
}

.jlhd span.jlhd4 {
	margin-left: 40px;
	background-position: 0 -228px;
}

.jlhd span.jlhd5 {
	background-position: 0 -304px;
}

.jlhd span.jlhd1.on {
	background-position: -276px 0;
}

.jlhd span.jlhd2.on {
	background-position: -276px -76px;
}

.jlhd span.jlhd3.on {
	background-position: -276px -152px;
}

.jlhd span.jlhd4.on {
	background-position: -276px -228px;
}

.jlhd span.jlhd5.on {
	background-position: -276px -304px;
}

.swiper-container {
	width: 100%;
	height: 100%;
}

.djbox {
	z-index: 2;
	width: 287px;
	height: 361px;
	background: url(../img/djboxbg.png) no-repeat;
	left: 6px;
	top: 259px;
	color: #8a7577;
	font-size: 14px;
	line-height: 28px;
	padding-top: 76px;
}

.tit {
	width: 411px;
	height: 80px;
	background-position: 0 -136px;
	margin: 0 auto;
}

.tip {
	color: #8a7577;
	font-size: 14px;
	line-height: 20px;
	padding-top: 20px;
	height: 60px;
}

.searchbox {
	height: 30px;
}

.searchbox a, .searchbox div, .searchbox label {
	display: inline-block;
	vertical-align: top;
	color: #f66574;
	font-size: 20px;
	line-height: 30px;
}

.searchbox .sebox {
	width: 194px;
	height: 30px;
	background-position: 0 -286px;
	text-indent: 0;
	margin-left: 6px;
}

.searchbox .sebox select {
	width: 100%;
	height: 100%;
	background: none;
	border: none;
	display: block;
	color: #8a7577;
	padding-left: 9px;
}

.searchbox label.pl {
	padding-left: 21px;
}

.serbox {
	margin-left: 22px;
}

.serbox input {
	float: left;
	width: 216px;
	height: 28px;
	background: #f1f1f3;
	border: 1px solid #faafaf;
	border-right: none;
	color: #8a7577;
	line-height: 28px;
	padding: 0 8px 0 9px;
}

.searchbox a.ssbtn, .searchbox a.pxbtn, .searchbox a.bhbtn {
	width: 70px;
	height: 30px;
	background-position: -236px -286px;
	color: #f0f2fa;
	font-size: 16px;
	text-indent: 0;
}

.searchbox a.pxbtn, .searchbox a.bhbtn {
	width: 110px;
	height: 30px;
	background-position: -318px -286px;
	color: #84532a;
}

.searchbox a.pxbtn {
	margin-left: 19px;
}

.searchbox a.bhbtn {
	margin-left: 11px;
}

.gostlist {
	margin: 10px 0 0 -12px;
	color: #f66574;
}

.gostlist li {
	margin: 19px 0 0 12px;
	float: left;
	display: inline;
	width: 230px;
	height: 331px;
	background: url(../img/libg.png) no-repeat;
	padding-top: 59px;
}

.gostbox {
	width: 190px;
    height: 190px;
    line-height: 190px;
	padding: 2px;
	border: 1px solid #fabfc6;
	margin: 0 auto;
	position: relative;
}

.gostbox img {
    cursor: pointer;
    max-width: 100%;
    vertical-align: middle;
}

.gostbox p {
	position: absolute;
	top: 2px;
	left: 2px;
	height: 16px;
	background: #de4d5c;
	color: #fff;
	font-size: 12px;
	line-height: 16px;
	padding: 0 6px;
}

.gostlist h3 {
	font-size: 24px;
	padding-top: 7px;
	height: 36px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.infbox {
	line-height: 20px;
	padding-bottom: 8px;
}

.zhicbox {
	width: 200px;
	margin: 0 auto;
	height: 30px;
	line-height: 30px;
}

.zhicbox p {
	float: left;
	background: #ffd2d8;
	padding: 0 12px;
	color: #de4457;
}

.zcbtn {
	float: right;
	width: 70px;
	height: 30px;
	background-position: 0 -329px;
	text-indent: 0;
	color: #fff;
}

.tpage {
	color: #8a6166;
	margin-top: 23px;
}

.tpage a {
	color: #8a6166;
}

.tpage a.prevbtn, .tpage a.nextbtn {
	text-decoration: underline;
}

.tpage a, .tpage span {
	margin: 0 5px;
}

.cont2 {
	padding-top: 53px;
}

.cont2 .tit {
	width: 695px;
	height: 48px;
	background-position: 0 -412px;
}

.cont2 .tip {
	padding: 15px 0 19px;
	height: auto;
}

.cont2 .gostlist {
	margin: -5px 0 0 -12px;
	color: #d38d55;
}

.cont2 .gostlist li {
	margin: 29px 0 0 12px;
	width: 230px;
	height: 331px;
	background: url(../img/libg2.jpg) no-repeat;
	padding-top: 19px;
}

.cont2 .gostbox {
	border-color: #edc8b2;
}

.cont2 .gostbox p {
	background: #d29a69;
	color: #483525;
}

.cont2 .zhicbox p {
	background: #f6d4b8;
	color: #bb7339;
}

.cont2 .zcbtn {
	color: #6f4d31;
	background-position: -88px -329px;
}

.cont2 .tpage, .cont2 .tpage a {
	color: #7c6858;
}

.cont3 {
	padding-top: 38px;
}

.cont3 .tit {
	width: 695px;
	height: 48px;
	background-position: 0 -503px;
}

.tabhd {
	margin-top: -18px;
	height: 116px;
	margin-left: 162px;
}

.tabhd li {
	width: 328px;
	height: 116px;
	float: left;
	text-indent: -9999em;
	background: url(../img/gzhd.png) no-repeat;
	margin-left: -28px;
	cursor: pointer;
}

.tabhd li.li2 {
	background-position: 0 -116px;
}

.tabhd li.li3 {
	background-position: 0 -232px;
}

.tabhd li.li1.on {
	background-position: -328px 0;
}

.tabhd li.li2.on {
	background-position: -328px -116px;
}

.tabhd li.li3.on {
	background-position: -328px -232px;
}

.gzbox {
	margin-top: 65px;
	color: #807571;
}

.gzbox a {
	color: #807571;
	text-decoration: underline;
}

.dis_tab {
	display: none;
	position: relative;
	background: url(../img/gzrbg.jpg) repeat-y;
}

.dis_tab:before, .dis_tab:after {
	width: 100%;
	height: 36px;
	content: '';
	background: url(../img/gzcon.png) no-repeat;
	left: 0;
	position: absolute;
}

.dis_tab:before {
	top: -36px;
}

.dis_tab:after {
	bottom: -36px;
	background-position: left bottom;
}

.dis_tab.dis {
	display: block;
}

.stit {
	width: 141px;
	height: 33px;
	background-position: -173px -329px;
	z-index: 2;
	top: -5px;
	left: 50%;
	margin-left: -71px;
}

.gzcont {
	padding: 0 45px 0;
	text-align: left;
	line-height: 24px;
	height: 498px;
	overflow-y: auto;
}

.gzcont::-webkit-scrollbar {
	width: 8px;
	border-radius: 4px;
}

.gzcont::-webkit-scrollbar-thumb {
	border-radius: 4px;
	background: #d38d55;
}

.gzcont h3 {
	padding-top: 9px;
	line-height: 36px;
	color: #f67168;
	font-size: 20px;
	font-weight: bold;
}

.gzcont h3.pt0 {
	padding-top: 0;
}

.shlist li {
	padding-left: 17px;
	width: 1093px;
	position: relative;
}

.shlist li span {
	position: absolute;
	top: 6px;
	left: 0;
	width: 12px;
	height: 12px;
	background-position: -468px -295px;
}

.stit1 {
	width: 140px;
	height: 34px;
	margin-left: -70px;
	background-position: -358px -331px;
}

.atenlist {
	padding-top: 14px;
}

.atenlist li {
	line-height: 34px;
	padding-left: 22px;
	width: 1088px;
	position: relative;
}

.atenlist li span {
	position: absolute;
	top: 0;
	left: 0;
}

.stit2 {
	width: 69px;
	height: 36px;
	margin-left: -35px;
	background-position: -541px -330px;
}

.wdlist li {
	border-bottom: 1px dashed #d9d2cd;
	padding-bottom: 12px;
}

.wdlist li p {
	padding: 12px 0 0 43px;
	width: 1067px;
	position: relative;
}

.wdlist li p.tishi, .wdlist li p.tishi a {
	color: #f56666;
}

.wdlist li span {
	position: absolute;
	left: 0;
	top: 11px;
	width: 25px;
	height: 25px;
	background: #f67168;
	border-radius: 4px;
	color: #fff;
	font-size: 16px;
	line-height: 25px;
	text-align: center;
}

.wdlist li span.da {
	background: #d38d55;
}

/*pop*/
.pop {
	width: 928px;
	position: relative;
	display: none;
}

.popauto {
	height: 85%;
}

.popauto:before, .popauto:after {
	position: absolute;
	width: 928px;
	height: 31px;
	left: 0;
	content: '';
	top: -31px;
	background: url(../img/poptop.png) no-repeat;
	z-index: 1;
}

.popauto:after {
	bottom: -31px;
	top: auto;
	height: 385px;
	background: url(../img/popbot.png) no-repeat;
}

.popauto {
	background: url(../img/popbg.png) repeat-y;
}

.pop_cont {
	padding: 25px 64px;
	color: #f66574;
	font-size: 14px;
	position: relative;
	z-index: 2;
}

.popauto .pop_cont {
	padding: 0 64px;
	height: 100%;
}

.scrollbox {
	margin-top: 12px;
	width: 774px;
	height: 97%;
	overflow-y: auto;
}

.top_box {
	padding-left: 56px;
}

.le_box {
	float: left;
	width: 196px;
	padding-right: 22px;
}

.le_box h3 {
	font-size: 24px;
	padding: 0 3px;
	margin-top: -8px;
}

.le_box p {
	padding: 2px 4px 6px;
	line-height: 24px;
}

.le_box img {
	padding: 2px;
	border: 1px solid #fac1c1;
}

.ribar {
	float: left;
	padding-left: 26px;
	border-left: 1px dashed #f66574;
	width: 232px;
}

.ribar p.useinf {
	font-size: 20px;
	line-height: 36px;
	margin-top: -7px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.ribar p.csxy {
	color: #8a7577;
	word-wrap: break-word;
	line-height: 20px;
	padding-top: 3px;
}

.zcta {
	width: 315px;
	height: 112px;
	background-position: -413px 0;
	margin: -38px 0 0 -64px;
}

.tpinf {
	color: #8a7577;
	line-height: 20px;
	padding-top: 4px;
}

.ewmbox {
	float: left;
	width: 174px;
	padding: 130px 0 0 44px;
}

.ewmbk {
	background: #ffdedb;
	border: 1px solid #f66574;
}

.ewmbk p {
	text-align: center;
	color: #d84658;
	line-height: 20px;
	padding: 5px 0;
}

.pop_h3 {
	margin-left: 55px;
	padding-left: 35px;
	font-size: 24px;
	margin-top: 14px;
	position: relative;
}

.pop_h3:before {
	width: 24px;
	height: 23px;
	background-position: -512px -288px;
	content: '';
	position: absolute;
	left: 0;
	top: 6px;
}

.mzlist {
	margin: 10px 0 0 54px;
	padding-bottom: 15px;
	position:relative;
	z-index:2;
}

.mzlist li {
	margin: 1px 0 0 1px;
	float: left;
	display: inline;
	padding: 1px;
	border: 1px solid #fac0c5;
	width: 110px;
	height: 110px;
    position: relative;
    line-height: 130px;
    text-align: center;
	cursor:pointer;
}
.mzlist li img {
    max-width: 100%;
    /* vertical-align: middle; */
}
.mzlist p {
	position: absolute;
	left: 1px;
	bottom: 1px;
	height: 19px;
	line-height: 19px;
	background: #f66574;
	color: #fff;
	padding: 0 9px 0 4px;
	border-top-right-radius: 9px;
	border-bottom-right-radius: 9px;
}

.vlist {
	margin: 9px 0 0 55px;
}

.vlist li {
	margin: 1px 0 0 1px;
	float: left;
	display: inline;
	padding: 2px;
	border: 1px solid #fac0c5;
	height: 180px;
	position: relative;
	cursor: pointer;
}

.vlist li span {
	background: url(../img/vzc.png) no-repeat;
	position: absolute;
	top: 2px;
	left: 2px;
	height: 180px;
	width: 310px;
	text-indent: -9999em;
}

.scrollbox::-webkit-scrollbar {
	width: 4px;
	background: #ffc6c1;
	border-radius: 2px;
}

.scrollbox::-webkit-scrollbar-thumb {
	background: #ff8d8d;
	border-radius: 2px;
}

.gbbtn {
	width: 50px;
	height: 50px;
	background-position: -537px -177px;
	top: 8px;
	right: 43px;
	position: absolute;
	z-index: 3;
}

.poptit {
	width: 329px;
	height: 65px;
	background-position: 0 -589px;
	top: -103px;
	left: 315px;
}

.scrollboxinf {
	margin-top: 88px;
	height: 86%;
}

.popsebox {
	height: 26px;
	padding-bottom: 12px;
	padding-left: 54px;
	font-size: 18px;
	line-height: 26px;
}

.popsebox label, .popsebox p, .popsebox input, .nunzs {
	float: left;
	display: inline;
}

.nunzs {
	color: #afa0a1;
	font-size: 14px;
}

.pop_tx {
	left: 0;
	width: 100%;
	text-align: center;
	color: #8a7577;
	top: -40px;
}

.popsebox p {
	width: 134px;
	height: 26px;
	background-position: -386px -600px;
	text-indent: 0;
}

.popsebox p select {
	width: 100%;
	height: 100%;
	border: none;
	background: none;
	color: #afa0a1;
	font-size: 14px;
	display: block;
	padding: 0 10px;
}

.ser_p {
	margin-left: 24px;
}

.popsebox p.role_p {
	width: 85%;
	background-position: -386px -641px;
}

.popsebox input {
	width: 112px;
	height: 24px;
	padding: 0 10px;
	background: #fff5ed;
	border: 1px solid #faafaf;
	color: #afa0a1;
	font-size: 14px;
	line-height: 24px;
}

.popsebox input.ml {
	margin-left: 24px;
}

.popsebox input.duan {
	width: 52px;
}

input::-webkit-input-placeholder {
	color: #afa0a1;
}

input::-moz-input-placeholder {
	color: #afa0a1;
}

input::-ms-input-placeholder {
	color: #afa0a1;
}

.plyear {
	padding: 0 1px 0 26px;
}

.plxingz {
	padding: 0 2px 0 12px;
}

.popsebox p.star_p {
	width: 94px;
	background-position: -530px -600px;
}

.popsebox input.long_w {
	width: 520px;
}

.livebox {
	padding-left: 55px;
	font-size: 18px;
	line-height: 20px;
}

.livebox input {
	margin: 0 9px 0 10px;
}

.livebox input.liv1 {
	margin-left: 33px;
}

.livebox input.liv2 {
	margin-left: 20px;
}

.mt17 {
	margin-top: 17px;
}

.xzbtn {
	width: 70px;
	height: 26px;
	background-position: 0 -667px;
	display: inline-block;
	vertical-align: top;
	text-indent: 0;
	color: #fff;
	font-size: 14px;
	margin: 6px 0 0 16px;
	line-height: 26px;
	text-align: center;
}

.pop-container {
	width: 600px;
	display: none;
}

.cropper-btn {
	text-align: center;
	padding: 30px 0;
}

.cropper-btn a {
	width: 128px;
	height: 32px;
	display: inline-block;
	text-indent: -9999px;
	margin: 0 20px;
}

.cropper-btn a.cb1 {
	background-position: -419px -739px;
}

.cropper-btn a.cb2 {
	background-position: -419px -781px;
}

.img-container {
	width: 600px;
	height: 400px;
}

.img-container img {
	min-width: 100%;
	min-height: 100%;
}

.pop-info-img {
	padding: 12px 0 0 52px;
}

.pop-info-img ul {
	width: 700px;
	height: 96px;
}

.pop-info-img li {
	width: 130px;
    height: 130px;
    line-height: 130px;
    text-align: center;
	float: left;
	margin-left: 4px;
	position: relative;
	padding: 2px;
	border: 1px solid #fac1c3;
}

.pop-info-img li input {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	opacity: 0;
	cursor: pointer;
}

.pop-info-img li a {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	line-height: 130px;
	text-align: center;
	color: #f66574;
	font-size: 30px;
}

.pop-info-img li img {
	max-height: 130px;
    max-width: 130px;
    vertical-align: middle;
	/* display: block; */
}

.mcbox {
	width: 130px;
	height: 130px;
	position: absolute;
	left: 2px;
	top: 2px;
	color: #fff5ef;
	text-align: center;
	line-height: 130px;
	background: url(../img/rbg.png) repeat;
	display: none;
}

.up_tip {
	color: #8a7577;
	font-size: 16px;
	padding-top: 16px;
}

.up_tip a {
	color: #f66574;
	text-decoration: underline;
	margin-left: 8px;
}

.vlink {
	padding: 9px 0 0 55px;
	height: 26px;
}

.vlink input {
	height: 24px;
	padding: 0 10px;
	background: transparent;
	border: 1px solid #faafaf;
	color: #afa0a1;
	font-size: 14px;
	line-height: 24px;
	width: 513px;
}

.vlink span {
	padding-left: 9px;
}

.popinfbox {
	padding-top: 4px;
}

.popinfbox div {
	padding-top: 12px;
	height: 26px;
}

.popinfbox div label {
	padding-left: 54px;
	float: left;
	width: 91px;
	color: #f66574;
	font-size: 18px;
}

.popinfbox div input {
	height: 24px;
	padding: 0 10px;
	background: transparent;
	border: 1px solid #faafaf;
	color: #afa0a1;
	font-size: 14px;
	line-height: 24px;
	width: 183px;
	float: left;
}

.yzmbtn {
	width: 90px;
	height: 26px;
	float: left;
	background-position: -83px -667px;
	margin-left: 11px;
	text-align: center;
	text-indent: 0;
	color: #fff;
	line-height: 26px;
}

.tjbtn, .jxbtn, .qrbtn {
	width: 328px;
	height: 112px;
	background-position: 0 -713px;
	margin: 0 0 0 236px;
}

.pop1 {
	height: auto;
}

.pop1 .gbbtn, .pop_com .gbbtn {
	right: 33px;
}

.pop1 .pop_cont {
	padding: 0;
}

.poptit1 {
	background-position: 0 -857px;
	left: 205px;
	top: -5px;
}

.zbrank {
	padding-top: 90px;
	width: 520px;
	margin: 0 auto;
}

.zbrank table {
	width: 100%;
}

.zbrank th {
	text-align: center;
	height: 32px;
	font-size: 24px;
}

.zbrank td {
	padding: 8px 0;
	border-bottom: 1px dashed #ead7b2;
	text-align: center;
	color: #8a7577;
	font-size: 20px;
}

.zbrank td.ft18 {
	font-size: 18px;
}

.zbrank td span {
	width: 40px;
	height: 40px;
	background: #f56375;
	display: inline-block;
	color: #fff;
	font-size: 24px;
	border-radius: 50%;
}

.bdimgbox {
	width: 65px;
	height: 65px;
	margin: 0 auto;
	position: relative;
}

.bdimgbox img {
	display: block;
	border-radius: 50%;
	width: 65px;
	height: 65px;
}

.zhez {
	width: 76px;
	height: 73px;
	background-position: -616px -736px;
	top: -4px;
	left: -6px;
}

.diyi, .dier, .disan {
	width: 27px;
	height: 27px;
	top: -12px;
	right: -12px;
}

.diyi {
	background-position: -440px -686px;
}

.dier {
	background-position: -473px -686px;
}

.disan {
	background-position: -509px -686px;
}

.pop_tpage {
	padding-top: 16px;
	height: 28px;
	text-align: center;
}

.pop_tpage a, .pop_tpage span {
	display: inline-block;
	cursor: default;
	vertical-align: top;
	width: 26px;
	height: 26px;
	background: #ffefed;
	border: 1px solid #f56375;
	color: #f56375;
	line-height: 26px;
	margin: 0 5px;
}

.pop_tpage span.on {
	background: #fff;
}

.pop_com {
	position: relative;
	width: 708px;
	background: url(../img/popr.png) repeat-y;
	display: none;
	/* margin-top: 100px; */
}

.pop_com .pop_cont {
	padding: 0 54px;
	position: relative;
	z-index: 2;
}

.pop_comh2 {
	color: #f66574;
	font-size: 36px;
	text-align: center;
}

.pop_com:before, .pop_com:after {
	left: 0;
	content: '';
	width: 100%;
	position: absolute;
	background: url(../img/popcom.png) no-repeat;
	z-index: 1;
}

.pop_com:before {
	top: -51px;
	height: 110px;
	z-index: 2;
}

.pop_com:after {
	height: 120px;
	background-position: 0 -269px;
	bottom: -40px;
}

.pop_com .gbbtn {
	top: -45px;
}

.comtxt {
	color: #8a7577;
	text-align: center;
	line-height: 24px;
}

.comtxt p {
	padding-top: 8px;
}

.pop_com .tjbtn, .jxbtn, .qrbtn {
	margin: -30px auto 0;
}

.whylist {
	text-align: left;
	padding: 14px 36px 0;
}

.whylist li {
	padding-left: 23px;
	position: relative;
	width: 505px;
}

.whylist li span {
	position: absolute;
	left: 0;
	top: 0;
}

.jxbtn, .qrbtn {
	background-position: -400px -824px;
}

.qrbtn {
	background-position: 0 -936px;
}

.whylist1 {
	padding-bottom: 14px;
}

.whylist em {
	color: #f66574;
}

.pop_comh2_lh {
	font-size: 30px;
	line-height: 40px;
	padding: 10px 0 31px;
}

.pop2 {
	width: 452px;
	height: 396px;
	background: url(../img/pop2.png) no-repeat;
}

.pop2 .pop_cont {
	padding: 25px 46px;
}

.pop2 .gbbtn {
	right: 25px;
}

.lpbox {
	padding-top: 52px;
}

.lpbox img {
	margin: 0 auto;
	display: block;
	border: 1px solid #f66574;
}

.lpbox p {
	color: #d84658;
	font-size: 18px;
	text-align: center;
	line-height: 28px;
	padding-top: 20px;
}

.stats {
	font-size: 18px;
	padding: 30px 0;
}

.stats li {
	padding: 0 24px 10px 57px;
	line-height: 26px;
}

.stats li em {
	color: #8a7577;
}

.stats li.zuf {
	padding-left: 147px;
	width: 429px;
	position: relative;
}

.stats li.zuf span {
	left: 57px;
	top: 0;
	position: absolute;
}

.sqbtn, .xgbtn {
	display: inline-block;
	vertical-align: top;
	width: 110px;
	height: 26px;
	text-indent: 0;
	color: #fff;
	background-position: -191px -667px;
	font-size: 14px;
	text-align: center;
	margin-left: 12px;
}

.lpbtn {
	width: 328px;
	height: 112px;
	background-position: -400px -936px;
	margin: -70px auto 0;
}

.xgbtn {
	width: 80px;
	background-position: -345px -677px;
}

.navbox {
	width: 84px;
	height: 505px;
	position: fixed;
	z-index: 2;
	right: 0px;
	top: 50%;
	margin-top: -252px;
	overflow: hidden;
}

.navbox, .navbox a:hover, .navbox a.on {
	background: url(../img/nav.png) no-repeat;
}

.navbox a {
	width: 84px;
	height: 90px;
	display: block;
	text-indent: -9999em;
	margin-bottom: 14px;
}

.navbox a.nav3 {
	margin-bottom: 13px;
}

.navbox a.nav1:hover, .navbox a.nav1.on {
	background-position: -84px 0;
}

.navbox a.nav2:hover, .navbox a.nav2.on {
	background-position: -84px -104px;
}

.navbox a.nav3:hover, .navbox a.nav3.on {
	background-position: -84px -208px;
}

.navbox a.nav4:hover, .navbox a.nav4.on {
	background-position: -84px -311px;
}

.navbox a.nav5:hover, .navbox a.nav5.on {
	background-position: -84px -415px;
}

.navbox a.nav2yc {
	margin-bottom: 0;
	height: 0;
	overflow: hidden;
}

.pop_video {
	width: 600px;
	position: relative;
	display: none;
}

.pop_video .gbbtn {
	top: 0;
	right: -50px;
}

.pop_video video {
	width: 100%;
}

.popauto .gbbtn {
	top: -50px;
}

.floswrt {
	width: 98px;
	height: 142px;
	background: url(../img/flower.png) no-repeat;
	top: -56px;
	left: 18px;
	z-index: 3;
}

.yun {
	width: 240px;
	height: 31px;
	background: url(../img/yun.png) no-repeat;
	bottom: -47px;
	right: 18px;
	z-index: 3;
}

.pop1 .pop_tx {
	top: 61px;
}

.pop-info-img img.shiliimg {
	top: -138px;
	left: 50%;
	width: 110px;
	height: auto;
	margin-left: -55px;
	display: none;
}

.up_tip a:hover img.shiliimg {
	display: block;
}

.stepsimg {
	padding-top: 5px;
}

.delebtn {
	width: 22px;
	height: 22px;
	background: url(../img/delebtn.png) no-repeat;
	position: absolute;
	text-indent: -9999em;
	top: -6px;
	right: -4px;
	display: none;
}

.djbox {
	height: 437px;
	box-sizing: border-box;
}

.sd1 .djbox {
	background: url(../img/djboxbg1.png) no-repeat;
	padding-top: 150px;
}

.sd2 .djbox {
	background: url(../img/djboxbg2.png) no-repeat;
	padding-top: 130px;
}

.sd3 .djbox {
	background: url(../img/djboxbg3.png) no-repeat;
	padding-top: 96px;
}

.sd4 .djbox {
	background: url(../img/djboxbg4.png) no-repeat;
	padding-top: 90px;
}

.sd5 .djbox {
	background: url(../img/djboxbg5.png) no-repeat;
	padding-top: 116px;
}

.pop-info-img .popimg1 img.shiliimg {
	top: -198px;
}

.pop_jc{width: 893px; height: 517px; background: url(../img/jc_pop.png) no-repeat; background-size: 893px 517px; }
.mzlist li img.addfada{position:absolute;width:220px;height:220px;max-width:220px;top:-226px;left:50%;margin-left:-110px;padding:1px;border:1px solid #fac0c5;display:none;}