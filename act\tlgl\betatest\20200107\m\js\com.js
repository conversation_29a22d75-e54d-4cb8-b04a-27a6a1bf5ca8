
var pageName = location.pathname.replace(/^.*\//, '').split('.')[0],
    isClick = true,
    commPath = '/changyou/tlgl/backtogether/';
// 微信分享
function wxShare(inviteCode, nickName, headImgUrl, order, shareName, fun) {
    ja.wx.init(function () {
        obj = {
            nickName: nickName,
            headImgUrl: headImgUrl,
            inviteCode: inviteCode,
            order: order
        }
        var desc = '', link = '';
        if (shareName == 'index') { // 直接分享
            var details={
                channel:'',
                type:''
            }
            link = location.origin + "/tlgl/betatest/20200107/m/index.shtml";
            // link = location.origin + "/tlgl/betatest/20200107/m/index.shtml"+ ja.utils.setUrlParams(details); //如果加了渠道详情
        } else {  //点击了邀请分享 或者在beshared直接分享
            link = location.origin + "/tlgl/betatest/20200107/m/beshared.shtml" + ja.utils.setUrlParams(obj);
        }
        ja.wx.share({
            title: '初心不负，江湖同归', // 标题
            desc: '《天龙八部·归来》初心首测即将开启，请助我获取测试资格！', // 介绍
            link: link, // 访问路径
            imgUrl: location.origin + "/act/tlgl/betatest/20200107/m/img/share/shareIcon.jpg", // 图标路径
            success: function () {
                if(shareName!='index'){
                    wxShareNumber(inviteCode);
                }
                fun && fun();
            }
        });
        wx.ready(function () {
            wx.hideMenuItems({
                menuList: [
                    "menuItem:share:qq",
                    "menuItem:share:QZone",
                    "menuItem:share:facebook",
                    "menuItem:openWithQQBrowser",
                    "menuItem:openWithSafari"
                ]
            })
        });
    });
}

function wxShareNumber(inviteCode) {
    ja.get(commPath + 'addsharetimes?invite_code=' + inviteCode + '', function (res) {
        if (res.code == 10000) {
        } else {
            console.log(res.message);
        }
    })
}
// 按钮统一判断 分享页面
function $shareClick(fun) {
    var code = Number(ja.glob.code);
    console.log(code)
    if (code == 0) {
        popTip('系统繁忙，请稍后重试！');
    } else if (code == 1106) {
        popTip('不在活动时间内'); //活动未开始
    } else if (code == 1103) {
        popTip('不在活动时间内'); //活动已结束
    } else if (code == 1102) {
        popTip('活动无效');
    } else {
        fun && fun();
    }
}
//规则说明
$(".rulebtn").on("click", function () {
    var sendObj = ja.utils.getUrlParams();
    window.location.replace("rule.shtml" + ja.utils.setUrlParams(sendObj));
});
// 分享公共提示
function popTip(msg) {
    popShow('popCom');
    $('#popCom .popComTip').html(msg)
}

//弹窗
var popIsShow = false;
var popDom = null;
function popShow(id) {
    popHide();
    var p = $('#' + id);
    popDom = p;
    if (p) {
        p.show().css({
            position: 'fixed',
            top: '50%',
            left: '50%',
            marginTop: -popDom.height() / 2 + 'px',
            marginLeft: -popDom.width() / 2 + 'px',
            zIndex: 998
        });
        p.attr('for', 'pop');
        popIsShow = true;
        if ($('[for="' + id + '"]').length >= 1) return;
        $('body').append('<div name="overlay" for=' + id + ' style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:997;background:rgba(0,0,0,0.8);"></div>');
    }
}
function popHide() {
    $('[for="pop"]').hide().attr('style', '');
    $('[name="overlay"]').remove();

}
window.addEventListener("resize", function () {
    if (!popIsShow)
        return;
    setTimeout(function () {
        popDom.css({
            marginTop: -popDom.height() / 2 + 'px',
            marginLeft: -popDom.width() / 2 + 'px',
            zIndex: 998
        });
    }, 400)
});


$('.wrapper').addClass('show');
$(function(){
    var cp_top=$(".logo").offset().top;
    $("html,body").animate({"scrollTop": cp_top}, 1000);
});

