a { text-decoration: none; blr: expression(this.onFocus=this.blur()); outline: none; }
html {-webkit-text-size-adjust: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); }
a, button, input { -webkit-tap-highlight-color: rgba(255, 0, 0, 0); }
input[type="text"], input[type="password"], select, input[type="search"] { -webkit-appearance: none; appearance: none; outline: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); border-radius: 0; }
img { display: block; height: auto; }
i, em { font-style: normal; }
.pr { position: relative; }
.pa { position: absolute; }
.hh { text-indent: -999rem; overflow: hidden; }
body, html { width: 100%; height: auto; }
body { color: #69403f; background: #fffaed; font-size: 20px; overflow-x: hidden; position: relative; }
.wrap { width: 100%; height: auto; overflow-x: hidden; position: relative; }
.flex, .clearfix {display: flex;display: -webkit-flex;flex-flow: wrap;-webkit-flex-flow: wrap;}
.pop { color: #3c7c81;display:none; }

/*head*/
.mid { width: 100%; margin: 0 auto; position: relative;}
.icon,.tit { background: url("../img/icon.png") no-repeat; display: block;background-size:930px auto;}
.content-bg { background: url("../img/bg.jpg") top center no-repeat; background-size: 750px auto; width: 100%; height: 5838px; margin-top: -1px; position: relative;z-index:2;}
.rule-btn { background: url("../img/rule-ico.png") no-repeat; background-size: 68px auto; width: 68px; height: 369px; position: absolute; right: 30px; top: 0; z-index: 20; }

/**content**/
.cont2{position:relative;padding-top:25px;}
.tit{width:617px;height:90px;background-position:0 -452px;text-indent:-9999em;margin:0 auto;}
/**cont1**/
.c-time { font-size: 24px; line-height: 28px; padding:13px 5px 0; color: #23313a; text-align: center; }
.red { color: #d2260a;}
.tips { width: 710px; margin: 11px auto 0; line-height: 28px; text-align: center; color: #23313a;font-size:20px;}
.tips span{color:#f2d76f;}
.com { width: 219px; height: 62px; line-height: 62px; margin: 0 auto; font-size: 24px; color: #683425; text-align: center;text-indent: -999em; }
.fl-list {width:750px; margin: 0 auto;height:1053px;background:url(../img/lbbg.png) no-repeat center top;background-size:100% auto;position:relative;padding-top:33px;}
.fl-list li {width:277px;float:left;height:496px;}
.fl-list li.li1{padding:0 34px 0 80px;}
.fl-list li.li3{height:510px;width:585px;padding-left:84px;}
.lbname{color:#eeeff3;text-align:center;font-size:18px;padding:58px 0 2px;}
.lbnamemid{padding:36px 0 5px;}
.lqbtn{width:257px;height:90px;background-position:0 0;margin:53px auto 0;text-indent:-9999em;}
.lqbtn.gray{background-position:0 -98px;pointer-events: none;}
.lqbtn.ylq{background-position:-336px -215px;}
.lblist{width:100%;text-align:center;height:99px;overflow:hidden;}
.lblist dd{display:inline-block;vertical-align:top;width:114px;}
.li3 .lblist dd{width:126px;}
.lblist dd img{width:54px;height:54px;display:block;margin:0 auto;padding-top:12px;}
.lblist dd p{color: #eeeff3;font-size: 20px;line-height: 20px; margin-top: 3px;}
.lblist3{top:62px;}
.lblist3 dd p{padding-top:0;padding-bottom:4px;}
.team-a-box { justify-content: center; }
.team-a-box a { margin: 0 17px 0; }
.join-btn { width: 182px; height: 52px; background-position: -423px -314px; }
.set-btn { width: 182px; height: 52px; background-position: -233px -314px; }
.my-apply { width: 182px; height: 52px;background-position: 0 -388px;text-indent:-9999em;}
.my-apply_inf{background-position:-205px -388px;}
.yllq-btn { width: 219px; height: 62px; background-position: -233px -235px; }
.team-apply-box { padding-top:44px; width: 100%; height:1408px;margin: 0 auto;background:url(../img/apply.png) no-repeat center top;}
.search-form { width: 100%; height: 32px;margin: 20px auto 0; align-items: center; color: #23313a;font-size:20px;}
.search-btn { width:310px; position: relative; }
.search-form span { margin-right:6px;padding-left:294px;}
.search-form input { font-size: 24px;	background:#888a8e; border: 1px #6b7385 solid; color: #23313a; width: 272px; height: 30px; display: block; outline: none; border-radius:4px;line-height:30px;}
.search-form input::-webkit-input-placeholder { color: #23313a; }
.sea-ico { width: 36px; height: 32px; background: url("../img/search.png"); position: absolute; right:0px; top:0; }
.huil-wj, .huoy-wj { display: flex; display: -webkit-flex; align-items: center; }
.huil-wj i, .huoy-wj i, .team-peo i { width: 30px; height: 46px; background: url("../img/icon.png"); background-position: -727px -315px; display: inline-block; vertical-align: middle; }
.huil-wj { color: #3c7c81; font-size: 22px; }
.huil-wj i { background-position: -657px -315px; ; margin-right: 10px; }
.huoy-wj { margin: 0 46px 0 30px; }
.huoy-wj { color: #d55515; font-size: 22px; }
.huoy-wj i { background-position: -688px -235px; margin-right: 10px; }
.apply-list {justify-content: center;margin-top:9px;}
.apply-list li {background: url("../img/team-bg.png") no-repeat;width: 308px;margin: 0 10px 21px;position: relative;color: #eeeff3;font-size:18px;background-size:100% 100%;height:367px;}
.apply-list .rank {height: 28px; line-height: 28px; text-align: left; width: 136px; position: absolute; left: 21px; top:25px;}
.apply-list .apply-btn { width: 130px; height: 39px;background-position: -311px 0;position: absolute; top: 17px; right:16px;text-indent:-99999em;}
.team-con { padding: 63px 20px 0;line-height:27px;}
.hg-tag { width: 61px; height: 32px; background-position: -509px -173px; text-align: center; color: #eeeff3; position: absolute; top:62px; right: 22px; font-size: 0px; display: none; text-indent: -9999999px; }
.team-con .t-name span { width: 146px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block; vertical-align: top; }
.team-peo { overflow: hidden; margin-top: 6px;}
.team-peo i {margin:0 5px;}
.team-peo i:last-child {margin-right: 0;}
.team-peo i.huig-ico { background-position: -620px -315px }
.team-peo i.huoy-ico { background-position: -674px -315px; }
.change-btn { width: 257px; height: 90px; background-position: 0 -209px;margin:5px auto 0;}
.more-btn { font-size: 20px; color: #875932; text-decoration: underline; padding-bottom: 3px; margin-top: 16px; text-align: center; display: block; }
.sharetip { width: 100%; height: 100%; background: rgba(0, 0, 0, 0.7) url("../img/sharetips.png") no-repeat 420px 10px; background-size: 314px auto; position: fixed; top: 0; left: 0; z-index: 999; display: none; }
.lqlb-btn.ylq, .team-a-box .yllq-btn.ylq { background-position: -4px -315px; text-indent: -999em; }
.cont3 .c-time{padding:5px 0 0 0;}
.addtit{margin:96px 0 0 93px;width:617px;height:90px;background-position:0 -642px;text-indent:-9999em;}
.jltip{text-align:center;padding:0 70px;color:#23313a;font-size:20px;line-height:24px;height:90px;}
.jlbox{width:750px;height:706px;background:url(../img/jlbg.png) no-repeat;text-align:center;background-size:100% auto;}
.shaozi{width:706px;height:706px;margin:0 auto;background:url(../img/shaoz.png) no-repeat;background-size:100% 100%;}
.jlbg0{background:url(../img/jlbg0.png) no-repeat;background-size:100% auto;}
.jlbg1{background:url(../img/jlbg1.png) no-repeat;background-size:100% auto;}
.jlbg2{background:url(../img/jlbg2.png) no-repeat;background-size:100% auto;}
.jlbg3{background:url(../img/jlbg3.png) no-repeat;background-size:100% auto;}
.jlbg4{background:url(../img/jlbg4.png) no-repeat;background-size:100% auto;}
.jlbg5{background:url(../img/jlbg5.png) no-repeat;background-size:100% auto;}
.jlbg6{background:url(../img/jlbg6.png) no-repeat;background-size:100% auto;}
.jlbg7{background:url(../img/jlbg7.png) no-repeat;background-size:100% auto;}
.jlbg8{background:url(../img/jlbg8.png) no-repeat;background-size:100% auto;}
.jlbg9{background:url(../img/jlbg9.png) no-repeat;background-size:100% auto;}
.jlbg10{background:url(../img/jlbg10.png) no-repeat;background-size:100% auto;}
.jlbg11{background:url(../img/jlbg11.png) no-repeat;background-size:100% auto;}
.cishu{color:#23313a;left:0;width:100%;top:713px;font-size:30px;font-weight:bold;}
.cjbtn{top:770px;left:50%;width:318px;height:111px;margin-left:-159px;background-position:-481px 0;text-indent:-9999em;}
.cjbtn.gray{background-position:-612px -125px;}
.cjtishi{text-align:center;padding:179px 80px 0;color:#23313a;font-size:20px;line-height:24px;height:82px;}
.addlblist{margin:0 auto;width:750px;text-align:center;height:555px;background:url(../img/botnh.png) no-repeat;background-size:100% 100%;}
.gztit{width:351px;height:90px;background-position:0 -792px;position:static;margin:90px 0 0 219px;}
.rule{color:#23313a;font-size:20px;line-height:24px;padding:18px 39px 0;}
.rule li{position:relative;padding:0 0 16px 34px;}
.rule p{padding:14px 0 13px;font-weight:bold;}
.rule em{position:absolute;left:-4px;font-size:18px;font-weight:bold;top:-4px;color:#d4efff;width:26px;height:26px;text-align:center;line-height:26px;text-indent:0;background-position:-904px 0;}
.rule  span{color:#851e1b;}