@charset "utf-8";

body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
form,
fieldset,
input,
textarea,
p,
blockquote,
th,
td {
	margin: 0;
	padding: 0;
}

table {
	border-collapse: collapse;
	border-spacing: 0;
}

address,
caption,
cite,
code,
dfn,
em,
strong,
th,
var {
	font-weight: normal;
	font-style: normal;
}

ol,
ul {
	list-style: none;
}

caption,
th {
	text-align: left;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-weight: normal;
	font-size: 100%;
}

q:before,
q:after {
	content: '';
}

fieldset,
img,
abbr,
acronym {
	border: 0;
}

/*页面样式*/
.c:before,
.c:after {
	content: "";
	display: table;
}

.c:after {
	clear: both;
}

.c {
	zoom: 1;
}

.none {
	display: none;
}

.pr {
	position: relative;
}

.pa {
	position: absolute;
}

.fl {
	float: left;
}

.fr {
	float: right;
}
.show{
	display: block !important;
}
body {
	font: 12px/1.5 '\5FAE\8F6F\96C5\9ED1';
	text-align: justify;
	min-width: 1500px;
	position: relative;
}

a {
	text-decoration: none;
	overflow: visible;
	transition: filter .2s;
}

a:hover {
	text-decoration: none;
	filter: brightness(1.1);
}

body {
	background-color: #421214;
}

html,
body,
.wrap {
	width: 100%;
	overflow-x: hidden;
	min-width: 1500px;
	margin: 0 auto;
}

.wrap {
	position: relative;
	background: url(../img/wbg.jpg) no-repeat center top;
	max-width: 1920px;
	margin: 0 auto;
}

.bar {
	width: 1500px;
	margin: 0 auto;
	position: relative;
	z-index: 1;
}

.head {
	height: 937px;
	background: url(../img/head.jpg) no-repeat center top;
	position: relative;
}

.vbg {
	position: absolute;
	top: -55px;
	width: 1920px;
	height: 1080px;
	left: 50%;
	margin-left: -960px;
	z-index: 1;
}

.vbg video {
	width: 100%;
	height: 100%;
}

.logo {
	width: 186px;
	height: 86px;
	background: url(../img/logo.png) no-repeat;
	display: block;
	text-indent: -9999em;
	position: absolute;
	top: 26px;
	left: 4px;
	z-index: 2;
}
.logo2 {
	pointer-events: none;
	width: 138px;
	height: 96px;
	background: url(../img/logo2.png) no-repeat;
	display: block;
	text-indent: -9999em;
	position: absolute;
	top: 18px;
	left: 214px;
	z-index: 2;
}

.arr {
	position: absolute;
	z-index: 3;
	width: 261px;
	height: 66px;
	left: 50%;
	margin-left: -130px;
	text-align: center;
	top: 835px;
	text-indent: -9999em;
	background: url(../img/arr.png) no-repeat;
	animation: flow 2s linear infinite both;
	-webkit-animation: flow 2s linear infinite both;
}

@keyframes flow {
	0% {
		transform: translateY(0px);
	}

	50% {
		transform: translateY(10px);
	}

	100% {
		transform: translateY(0px);
	}
}

@-webkit-keyframes flow {
	0% {
		-webkit-transform: translateY(0px);
	}

	50% {
		-webkit-transform: translateY(10px);
	}

	100% {
		-webkit-transform: translateY(-0px);
	}
}

.cont {
	position: relative;
	z-index: 2;
	width: 100%;
	height: 2682px;
	background: url(../img/cont.jpg) no-repeat center top;
}
.navbox {
	height: 108px;
	position: relative;
	width: 100%;
}

.navbox div {
	position: absolute;
	width: 100%;
	left: 0;
	top: -8px;
	height: 166px;
	background: url(../img/navbg.png) no-repeat center top;
}

.navbox a {
	background: url(../img/navcom.png) no-repeat;
	text-indent: -9999em;
}

.navbox .nav0 {
	position: absolute;
	top:-10px;
	z-index: 2;
	left: 50%;
	margin-left: -134px;
	width: 268px;
	height: 166px;
	background-position: -500px -270px;
}


.navlist {
	z-index: 1;
	position: absolute;
	top: 1px;
	left: 50%;
	margin-left: -750px;
	width: 1500px;
	height: 166px;
}

.navlist li {
	cursor: pointer;
	float: left;
	display: inline;
	display: flex;
	align-items: center;
	justify-content: center;
}

.navlist li.li0 , .navlist li.li3 {
	margin-top: -7px;
}
.navlist li.li1 {
	margin-left: -2px;
}
.navlist li.li2 {
	margin-left: 264px;
}

.navlist li a {
	width: 100%;
	height: 100%;
	text-indent: -9999em;
	display: block;
	z-index: 1;
	position: relative;
}

.navlist li.li0 a {
	width: 383px;
	height: 116px;
	background-position: -500px 0;
}

.navlist li.li1 a {
	width: 238px;
	height: 102px;
	background-position: -500px -140px;
}

.navlist li.li2 a {
	width: 238px;
	height: 102px;
	background-position: -500px -464px;
}

.navlist li.li3 a {
	width: 375px;
	height: 116px;
	background-position: -500px -590px;
}
.pg1 .nav_box .nav0{
	background-position: 0 -270px;
}
.pg2 .navlist li.li0 a {
	background-position: 0 0;
}

.pg3 .navlist li.li1 a {
	background-position: 0 -140px;
}

.pg4 .navlist li.li2 a {
	background-position: 0 -464px;
}

.pg5 .navlist li.li3 a {
	background-position: 0 -590px;
}

.tit {
	margin: 0 auto;
	width: 928px;
	height: 129px;
	background: url(../img/tit.png) no-repeat;
	text-indent: -9999em;
}
.part1{
	height: auto;
	padding-bottom: 75px;
}

.course_nav_box{
	display: flex;
	justify-content: space-between;
	width: 930px;
	margin: 48px auto 0;
}
.course_nav_box a{
	display: block;
	width: 282px;
	height: 64px;
	background: url(../img/tabcom.png) no-repeat;
	text-indent: -9999em;
}
.course_nav_box .nav0 {
	background-position: -300px 0;
}
.course_nav_box .nav1 {
	background-position: -300px -100px;
}
.course_nav_box .nav2 {
	background-position: -300px -200px;
}
.course_nav_box .nav0.on {
	background-position: 0 0;
}
.course_nav_box .nav1.on {
	background-position: 0 -100px;
}
.course_nav_box .nav2.on {
	background-position: 0 -200px;
}
.course_item{
	display: none;
}
.part1 .time{
	width: 543px;
	height: 46px;
	background: url(../img/time_bg.png) no-repeat;
	color: #ffefcc;
	font-size: 24px;
	line-height: 46px;
	text-align: center;
	margin: 48px auto 0;
}
.subgroup_box{
	display: flex;
	justify-content: space-between;
	width: 1200px;
	margin: 28px auto;
}
.subgroup{
	width: 279px;
	height: 375px;
	background: url(../img/group_bg.png) no-repeat;
	padding-top: 26px;
	box-sizing: border-box;
}
.group_name{
	font-size: 34px;
	color: #ffefcc;
	text-align: center;
	line-height: 1;
}
.team_list{
	width: 226px;
	margin: 10px auto 0;
}

.team_list li{
	margin-bottom: 13px;
}
.team_list li a{
	display: block;
	position: relative;
	width: 226px;
	height: 50px;
	font-size: 20PX;
	line-height: 50px;
	text-align: center;
}
.team_list li:last-child{
	margin-bottom: 0;
}
.team_list .team_cx{
	color: #7a0903;
	background: url(../img/team_bg1.png) no-repeat;
}
.team_list .team_obsolete{
	color: #ddb689;
	background: url(../img/team_bg2.png) no-repeat;
}

.team_list .team_cx::after{
	content:"";
	position: absolute;
	top: -10px;
	right: -10px;
	width: 60px;
	height: 27px;
	background: url(../img/info_01.png) no-repeat;
}
.team_list .team_obsolete::after{
	content:"";
	position: absolute;
	top: -10px;
	right: -10px;
	width: 60px;
	height: 27px;
	background: url(../img/info_02.png) no-repeat;
}
.tips_txt{
	font-size: 20px;
	text-align: center;
	color: #cba074;
}
.tips_txt2{
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	bottom: 46px;
	font-size: 20px;
	color: #cba074;
}
.sc_box{
	position: relative;
	width: 1219px;
	height: 1184px;
	background: url(../img/sc_bg.png) no-repeat;
	margin: 12px auto 0;
	padding: 40px 34px 0;
	box-sizing: border-box;
}
.win_sc , .lose_sc{
	position: relative;

}
.sc_tips{
	position: absolute;
	left: 0;
	display: flex;
	align-items: center;
}
.sc_time{
	font-size: 19px;
	color: #cba074;
	line-height: 1.4;
}
.sc_tips .tips_line{
	width: 2px;
	height: 44px;
	background: url(../img/tips_line.png) no-repeat;
	margin: 0 6px;
}
.sc_ti{
	font-size: 26px;
	color: #cba074;
	line-height: 1;
}
.sc_ti span{
	font-size: 16px;
	color: #e92829;
	vertical-align: middle;
}
.team_one{
	position: absolute;
	width: 214px;
	z-index: 2;
}

.team_one a{
	display: block;
	position: relative;
	width: 214px;
	height: 47px;
	font-size: 20PX;
	line-height: 50px;
	text-align: center;
	
}
.team_one .team_win{
	color: #7a0903;
	background: url(../img/team_bg1.png) no-repeat;
	background-size: 100% auto;
}
.team_one .team_lose{
	margin-top: 12px;
	color: #ddb689;
	background: url(../img/team_bg2.png) no-repeat;
	background-size: 100% auto;
}
.team_one .team_win::after{
	content:"";
	position: absolute;
	top: -8px;
	left: -8px;
	width: 57px;
	height: 25px;
	background: url(../img/info_03.png) no-repeat;
}
.team_one .team_lose::after{
	content:"";
	position: absolute;
	top: -8px;
	right: -8px;
	width: 92px;
	height: 26px;
	background: url(../img/info_04.png) no-repeat;
}
.lose_sc .team_lose::after{
	content:"";
	position: absolute;
	top: -8px;
	right: -8px;
	width: 60px;
	height: 27px;
	background: url(../img/info_02.png) no-repeat;
}
.sc_line{
	position: absolute;
	top: 156px;
	left: 244px;
	width: 55px;
	height: 920px;
	background: url(../img/sc_line.png) no-repeat;
}
.sc_line2{
	position: absolute;
	top: 220px;
	left: 456px;
	width: 108px;
	height: 791px;
	background: url(../img/sc_line2.png) no-repeat;
}
.sc_line_top{
	position: absolute;
	top: 352px;
	left: 730px;
	width: 72px;
	height: 152px;
	background: url(../img/sc_line_top.png) no-repeat;
}
.sc_line_bottom{
	position: absolute;
	top: 637px;
	left: 730px;
	width: 72px;
	height: 245px;
	background: url(../img/sc_line_bottom.png) no-repeat;
}
.sc_line3{
	position: absolute;
	top: 585px;
	left: 838px;
	width: 126px;
	height: 66px;
	background: url(../img/sc_line3.png) no-repeat;
}
.champion{
	position: absolute;
	top: 450px;
	right: 0;
	width: 289px;
	height: 315px;
	background: url(../img/champion.png) no-repeat;
}
.winsc1{
	left: 0;
	top: 0;
}
.winsc2{
	left: 263px;
	top: 62px;
}
.winsc3{
	left: 527px;
	top: 201px;
}
.winsc4{
	left: 659px;
	top: 460px;
}
.winsc1 .team1{
	top: 60px;
}
.winsc1 .team2{
	top: 193px;
}
.winsc1 .team3{
	top: 323px;
}
.winsc1 .team4{
	top: 455px;
}
.winsc1 .team5{
	top: 585px;
}
.winsc1 .team6{
	top: 720px;
}
.winsc1 .team7{
	top: 850px;
}
.winsc1 .team8{
	top: 983px;
}
.winsc2 .team9{
	top: 64px;
}
.winsc2 .team10{
	top: 327px;
}
.winsc2 .team11{
	top: 590px;
}
.winsc2 .team12{
	top: 853px;
}
.winsc3 .team13{
	top: 57px;
}
.winsc3 .team14{
	top: 582px;
}
.winsc4 .team15{
	top: 59px;
}
.losesc1 i{
	display: inline-block;
	vertical-align: middle;
	font-style: normal;
	width: 93px;
	height: 16px;
	background: url(../img/first.png) no-repeat;
	margin-left: 4px;
}
.losesc2 i{
	display: inline-block;
	vertical-align: middle;
	font-style: normal;
	width: 91px;
	height: 16px;
	background: url(../img/second.png) no-repeat;
	margin-left: 4px;
}
.losesc1{
	top: 0;
	left: 0;
}
.losesc2{
	left: 0;
	top: 556px;
}
.losesc3{
	left: 310px;
	top: 201px;
}
.losesc4{
	left: 621px;
	top: 330px;
}
.losesc1 .team1{
	top: 60px;
}
.losesc1 .team2{
	top: 183px;
}
.losesc1 .team3{
	top: 307px;
}
.losesc1 .team4{
	top: 430px;
}
.losesc2 .team5{
	top: 60px;
}
.losesc2 .team6{
	top: 183px;
}
.losesc2 .team7{
	top: 307px;
}
.losesc2 .team8{
	top: 430px;
}
.losesc3 .team9{
	top: 57px;
}
.losesc3 .team10{
	top: 320px;
}
.losesc3 .team11{
	top: 582px;
}
.losesc4 .team12{
	top: 60px;
}
.losesc4 .team13{
	top: 323px;
}
.lose_sc_line1{
	position: absolute;
	left: 231px;
	top: 153px;
	width: 115px;
	height: 928px;
	background: url(../img/lose_sc_line1.png) no-repeat;
}
.lose_sc_line2{
	position: absolute;
	left: 525px;
	top: 352px;
	width: 133px;
	height: 528px;
	background: url(../img/lose_sc_line2.png) no-repeat;
}
.lose_sc_line3{
	position: absolute;
	left: 867px;
	top: 484px;
	width: 89px;
	height: 266px;
	background: url(../img/lose_sc_line3.png) no-repeat;
}
.jijun{
	position: absolute;
	top: 450px;
	right: 0;
	width: 289px;
	height: 315px;
	background: url(../img/jijun.png) no-repeat;
}


.allbox {
	margin-top: -54px;
	height: 462px;
	position: relative;
}

.allbox a {
	width: 459px;
	height: 462px;
	background: url(../img/allbg.png) no-repeat center top;
	margin: 0 auto;
	display: block;
	text-indent: -9999em;
}

.allbox a.cham {
	background: url(../img/allbgcham.png) no-repeat;
	text-indent: 0;
	text-align: center;
	padding-top: 265px;
	height: 462px;
	cursor: default;
}

.allbox a.cham:hover {
	filter: brightness(1);
}

.allbox a.cham span {
	color: #cbc29d;
	font-size: 18px;
	font-weight: bold;
	display: inline-block;
	vertical-align: top;
	line-height: 38px;
	background-image: -webkit-linear-gradient(top, #774a1b, #a44f03);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}

.stepsbox {
	position: relative;
	top: -38px;
	left: -14px;
	width: 1228px;
	height: 246px;
	background: url(../img/linebg.png) no-repeat;
}

.stepsbox a {
	width: 182px;
	height: 202px;
	float: left;
	text-indent: -9999em;
	margin-left: 27px;
	display: inline;
	cursor: default;
	transition: none;
}

.stepsbox a:hover {
	filter: brightness(1);
}

.allbox a.normallink,
.stepsbox a.stepslink {
	transition: all .5s;
	cursor: pointer;
}

.allbox a.normallink:hover,
.stepsbox a.stepslink:hover {
	transform: scale(.95);
	filter: brightness(1.1);
}

.stepsbox a.step0 {
	background: url(../img/step0.png) no-repeat;
	margin-left: 0;
}

.stepsbox a.step1 {
	background: url(../img/step1.png) no-repeat;
}

.stepsbox a.step2 {
	background: url(../img/step2.png?v0.2) no-repeat;
	cursor: pointer;
}

.stepsbox a.step3 {
	background: url(../img/step3.png?v03) no-repeat;
}

.stepsbox a.step4 {
	background: url(../img/step4.png?v01) no-repeat;
}

.stepsbox a.step5 {
	background: url(../img/step5.png?v20240815) no-repeat;
}
.tit.t0 {
	background-position: 0 0;
	margin-top: 40px;
}
.tit.t1 {
	margin-top: 28px;
	background-position: 0 -200px;
}

.tit.t2 {
	margin-top: 54px;
	background-position: -20px -161px;
}

.newsbox {
	position: relative;
	left: -7px;
	margin: 23px -12px 0 -30px;
	width: 1294px;
	height: 495px;
	background: url(../img/newbox.png) no-repeat;
	margin: 0 auto;
}

.newsbox div {
	padding: 75px 72px 46px 72px;
}

.newsbox ul {
	width: 1017px;
	height: 340px;
	overflow-y: auto;
	margin: 0 auto;
	padding: 0 40px;
}
/* 修改垂直滚动条 */
.newsbox ul::-webkit-scrollbar {
  width: 10px; /* 修改宽度 */
}

/* 修改滚动条轨道背景色 */
.newsbox ul::-webkit-scrollbar-track {
  background-color: #5d0e12;
	border-radius: 10px;
}

/* 修改滚动条滑块颜色 */
.newsbox ul::-webkit-scrollbar-thumb {
	background-image: linear-gradient(to top,#c9a058, #f5c787 );
  border-radius: 10px;
}
.newsbox ul li {
	height:68px;
	border-bottom: 1px dashed #f6cc9b;
	font-size: 20px;
	line-height: 68px;
}

.newsbox a {
	color: #ffebc0;
	display: block;
}

.newsbox span {
	padding-left: 14px;
	width: 800px;
	float: left;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.newsbox em {
	padding-left: 39px;
	float: left;
}

.newsbox span,
.newsbox em {
	color: #ffebc0;
	/* background-image: -webkit-linear-gradient(top, #fffced, #751b01); */
	/* -webkit-background-clip: text; */
	/* -webkit-text-fill-color: transparent; */
	font-weight: bold;
}

.swibox {
	width: 100%;
	margin-top: 53px;
	height: 600px;
	position: relative;
}

.swiper-button-next,
.swiper-button-prev {
	margin-top: -80px;
	width: 58px;
	height: 57px;
}

.swiper-button-prev {
	left: -70px;
	background: url(../img/prevbtn.png) no-repeat;
}

.swiper-button-next {
	right: -70px;
	background: url(../img/nextbtn.png) no-repeat;
}

.vlist {
	margin: 0 -24px;
	width: 1248px;
}

.vlist li {
	margin: 0 24px;
	float: left;
	display: inline;
	width: 368px;
}

.vlist li a {
	width: 100%;
	height: 100%;
	display: block;
}

.vlist div {
	width: 100%;
	height: 214px;
	background: url(../img/vbg.jpg) no-repeat;
	position: relative;
	overflow: hidden;
}

.vlist div::after,
.vlist div::before {
	content: '';
	position: absolute;
	width: 56px;
	height: 56px;
	background: url(../img/vbtn.png) no-repeat;
	position: absolute;
	top: 50%;
	left: 50%;
	margin-top: -28px;
	margin-left: -25px;
}

.vlist div::before {
	background: url(../img/mc.png?v=2) no-repeat;
	width: 368px;
	height: 214px;
	left: 0;
	top: 0;
	margin: 0;
}

.vlist div::after,
.vlist div::before {
	transition: all .2s;
}

.vlist li a:hover div::after {
	opacity: 0;
	transform: scale(2);
}

.vlist li a:hover div::before {
	opacity: 0;
}

.vlist span {
	color: #b1a994;
	font-size: 20px;
	display: block;
	text-align: center;
	padding-bottom: 16px;
	height: 56px;
	display: flex;
	justify-content: center;
	align-items: center;
	background-image: -webkit-linear-gradient(top, #c37624, #c05702);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	font-weight: bold;
}

.snavbox {
	margin-top: 18px;
}

.s_nav a {
	background: url(../img/flocom.png) no-repeat;
	text-indent: -9999em;
	overflow: hidden;
	display: block;
}

.navbtn1 {
	width: 91px;
	height: 88px;
	background-position: -49px -412px !important;
	margin: 0 auto;
}

.snavbox a{
	width: 215px;
	height: 70px;
	margin-bottom: 15px;
}
.snavbox a.snav0 {
	background-position: 13px -1px;
}

.snavbox a.snav1 {
	background-position: 13px -81px;
}

.snavbox a.snav2 {
	background-position: 13px -161px;
}

.snavbox a.snav3 {
	background-position: 13px -241px;
}

.snavbox a.snav4 {
	background-position: 13px -321px;
}

.pg1 .snavbox a.snav0 {
	background-position: -210px -1px;
}

.pg2 .snavbox a.snav1 {
	background-position: -210px -81px;
}

.pg3 .snavbox a.snav2 {
	background-position: -210px -161px;
}

.pg4 .snavbox a.snav3 {
	background-position: -210px -241px;
}

.pg5 .snavbox a.snav4 {
	background-position: -210px -321px;
}
/* 赛事奖励 */
.pg2 .cont {
	height: 3849px;
	background: url(../img/cont2.jpg) no-repeat center top;
}

.guanjunbox {
	margin: 50px 0 0 180px;
	width: 760px;
	height: 304px;
	background: url(../img/guanjunbox.png) no-repeat;
}

.qiansanbox {
	margin: 90px auto 0;
	width: 1382px;
	height: 1247px;
	background: url(../img/qiansanbox.png?v0.1) no-repeat;
	position: relative;
}

.dianjunbox {
	margin: 0 auto;
	width: 1289px;
	height: 541px;
	background: url(../img/dianjunbox.png) no-repeat;
	position: relative;
}
.rewardbox{
	margin: 0 auto;
	width: 1289px;
	height: 541px;
	position: relative;
	background: url(../img/rewardbox.png) no-repeat;
}
.rewardbox1,
.rewardbox2,
.rewardbox3 {
	margin: 0 auto;
	width: 1289px;
	height: 395px;
	position: relative;
}

.pjzrlink,
.dfzjlink {
	width: 64px;
	height: 64px;
	position: absolute;
	z-index: 2;
}
.pjzrlink:hover,
.dfzjlink:hover{
	filter: brightness(1);
}
.lihelink1 {
	top: 688px;
	left: 296px;
}

.lihelink2 {
	top: 687px;
	left: 744px;
}

.lihelink3 {
	top: 686px;
	right: 131px;
}

.newreward{
	position: absolute;
	bottom: 124px;
	left: 201px;
	height: 88px;
	width: 172px;
	display: flex;
	align-items: center;
	justify-content: center;
}
.newreward1{
	bottom: 127px;
	left: 635px;
}
.newreward2{
	left: 1075px;
}
.newreward3{
	left: 910px;
	bottom: 103px;
}
.newreward4{
	left: 819px;
	bottom: 103px;
}
.pjzrlink img,
.dfzjlink img {
	display: none;
	position: absolute;
	left: 50%;
	margin-left: -368px;
	bottom: 70px;
}

.pjzrlink:hover img,
.dfzjlink:hover img {
	display: block;
}

.dianjunbox .lihelink1 {
	top: 196px;
	left: 906px;
}

.ffimg,
.longimg,
.qilinimg {
	position: absolute;
	top: 682px;
	left: 328px;
	z-index: 2;
}

.longimg {
	top: 519px;
	left: 764px;
}

.qilinimg {
	left: 1201px;
}

.rewardbox1 .lihelink1,
.rewardbox2 .lihelink1 {
	top: 197px;
	left: 940px;
}

.rewardbox .lihelink1 {
	top: 195px;
	left: 615px;
}

.rewardbox1 {
	background: url(../img/rewardbox1.png?v2024) no-repeat;
}

.rewardbox2 {
	background: url(../img/rewardbox2.png?v2024) no-repeat;
}

.rewardbox3 {
	background: url(../img/rewardbox3.png) no-repeat;
}
/* 直播页 */
.pg5 .cont {
	height: 1608px;
	background: url(../img/cont5.jpg) no-repeat center top;
}

.pg5 .t4{
	background-position:  0 -600px;
	margin-top: 40px;
}
.livehd {
	/* padding-top: 44px; */
	text-align: center;
	font-size: 0;
	margin: 50px auto;
	overflow: hidden;
	width: 1200px;
	display: flex;
	justify-content: space-between;
}

.livehd a {
	width: 282px;
	height: 64px;
	/* margin: 31px 89px 0; */
	float: left;
	display: inline-block;
	vertical-align: top;
	font-weight: bold;
	background: url(../img/livehd.png) no-repeat;
}

.livehd a.cur {
	background-position: left bottom;
}

.livehd a span{
	color: #913a00;
	font-size: 30px;
	line-height: 58px;
	background-image: -webkit-linear-gradient(top, #7a3205, #431b05);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}
.livehd a.cur span {
	background-image: -webkit-linear-gradient(top, #d8600c, #972c01);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}
.livebox {
	margin: 64px auto 0;
	width: 1294px;
	height: 732px;
	background: url(../img/livebox.png) no-repeat;
	padding: 53px 70px 46px 70px;
	box-sizing: border-box;
}
.livebox a {
	display: block;
    width: 100%;
    height: 100%;
    background: url(../img/livebtn.png) rgba(0, 0, 0, .4) no-repeat center;
    background-size: 224px auto;
    text-indent: -9999em;
}

.livetip {
	color: #c3b69a;
	font-size: 18px;
	margin: 27px 24px 0 24px;
	line-height: 36px;
	margin-left: 150px;
}

.livetip span {
	display: block;
	color: #ffde98;
}
/* 规则页 */
.pg3 .cont {
	height: auto;
	padding-bottom: 300px;
	background: url(../img/cont3.jpg?v02) no-repeat center top;
}

.tit.t3 {
	background-position:  0 -400px;
}

.rulebox {
	width: 1441px;
	padding: 80px 120px 0;
	color: #e3cfb6;
	font-size: 16px;
	line-height: 24px;
	position: relative;
	box-sizing: border-box;
}
.rulebox::after{
	content: ' ';
	background: url(../img/ruleBG.png?v02) no-repeat center top;
	width: 1441px;
	height: 5998px;
	position: absolute;
	top: 0;
	left: 50%;
	margin-left: -708px;
	z-index: -1;
}
.stit {
	margin-left: 1px;
	margin-bottom: 10px;
	height: 48px;
	background: url(../img/stit.png) no-repeat;
	text-indent: -9999em;
}

.rulebox p {
	line-height: 22px;
	margin-left: 4px;
	margin-bottom: 4px;
	padding-left: 4px;
	background: url(../img/pbg.png) no-repeat left 2px;
}

.rulebox p.nobg {
	background: none;
}

.rulebox li {
	margin-left: 8px;
}

.rulebox li span,
.rulebox p span,
.rulebox dd span {
	color: #fff29d;
}
.rulebox li span.blod,
.rulebox p span.blod,
.rulebox dd span.blod {
	font-weight: bold;
}
.rulebox li::before,
.rulebox dd::before {
	content: '';
	display: inline-block;
	vertical-align: middle;
	margin: 0 5px 0 12px;
	width: 11px;
	height: 11px;
	background: url(../img/icon.png) no-repeat;
}

.stit1,
.stit2,
.stit3,
.stit4 {
	margin-top: 10px;
}
.stit0{
	width: 176px;
	background-position: 0 0;
}
.stit1{
	width: 219px;
	background-position: 0 -50px;
}
.stit2 {
	width: 278px;
	background-position: 0 -100px;
}
.stit3 {
	width: 592px;
	background-position: 0 -150px;
}
.stit4 {
	width: 176px;
	background-position: 0 -200px;
}


.rulebox p.pg1 {
	background: url(../img/pbg1.png) no-repeat left 2px;
}

.rulebox p.pg2 {
	background: url(../img/pbg2.png) no-repeat left 2px;
}

.rulebox p.pg3 {
	background: url(../img/pbg3.png) no-repeat left 2px;
}

.rulebox p.pg4 {
	background: url(../img/pbg4.png) no-repeat left 2px;
}

.rulebox p.pg5 {
	background: url(../img/pbg5.png) no-repeat left 2px;
}

.rulebox p.pg6 {
	background: url(../img/pbg6.png) no-repeat left 2px;
}

.rulebox p.pg7 {
	background: url(../img/pbg7.png) no-repeat left 2px;
}

.rulebox p.pg8 {
	background: url(../img/pbg8.png) no-repeat left 2px;
}

.rulebox p.pg9 {
	background: url(../img/pbg9.png) no-repeat left 2px;
}

.rulebox p.pg10 {
	background: url(../img/pbg10.png) no-repeat left 2px;
}

.rulebox p.pg11 {
	background: url(../img/pbg11.png) no-repeat left 2px;
}
.rulebox p.pg12 {
	font-weight: bold;
	background: url(../img/pbg12.png) no-repeat left 2px;
}
.rulebox .nobg p {
	background: none;
}

.rulebox dd {
	margin-left: 8px;
}

.rulebox dd::before {
	margin: 0 7px 0 32px;
	width: 5px;
	height: 5px;
	background: url(../img/icon1.png) no-repeat;
}

.tablebox {
	margin: 6px auto 10px;
	width: 1182px;
	position: relative;
}

.tablebox::before {
	content: '';
	width: 1193px;
	height: 5px;
	background: url(../img/topbg.png) no-repeat;
	position: absolute;
	z-index: 2;
	top: 0;
	left: 50%;
	margin-left: -596px;
}

.tablebox table {
	width: 100%;
}

.tablebox th,
.tablebox td {
	
	border: 1px solid #8e6149;
	text-align: center;
	width: 50%;
	font-size: 16px;
}

.tablebox td {
	height: 26px;
	border: 1px solid #d74147;
}

.tablebox th {
	height: 32px;
	color: #60090c;
	background: #d3aa7c;
}

.stit.stit3 {
	width: 592px;
	background-position: 0 -150px;
}

.linedl dd {
	width: 153px;
	display: inline-block;
	vertical-align: top;
}

.linedl dd::before {
	margin-left: 0;
}

.linedl dd.dd1::before {
	margin-left: 32px;
}

.mt {
	margin-top: 14px;
}
.rulebox .ml1 p {
	text-indent: 16px;
}
.rulebox .ml2 p {
	text-indent: 42px;
}

.rulebox .ml p {
	padding-left: 8px;
	text-indent: 75px;
}

.tablebox1 td {
	width: 20%;
}

.tablebox1 th {
	background: #d3aa7c;
}

.szlink {
	position: absolute;
	width: 150px;
	height: 170px;
	z-index: 2;
	top: 342px;
	left: 978px;
}

.szlink.gjsz {
	top: 338px;
	left: 87px;
}
.szlink.gjsz img{
	bottom: -50px;
}
.szlink.yjsz {
	top: 194px;
	left: 530px;
}

.dianjunbox .szlink,
.rewardbox .szlink{
	top: 149px;
	left: 99px;
}
.rewardbox1 .szlink,
.rewardbox2 .szlink {
	top: 149px;
	left: 99px;
}

.rewardbox3 .szlink {
	top: 153px;
	left: 272px;
}

.szlink img {
	display: none;
	position: absolute;
	left: 50%;
	margin-left: -272px;
	bottom: -100px;
}

.szlink:hover img {
	display: block;
}

.openbtn {
	width: 56px;
	height: 218px;
	background: url(../img/zk.png) no-repeat;
	position: absolute;
	z-index: 5;
	top: 50%;
	margin-top: -109px;
	cursor: pointer;
	right: 214px;
	background-position: 0 0;
}

.openbtn.sq {
	background-position: -60px 0;
	right: 0;
	left: auto;
}
.flobox{
	z-index: 5;
	position: fixed;
	width: 226px;
	height: 667px;
	background: url(../img/flo.png) no-repeat left top;
	top: 50%;
	margin-top: -333px;
	display: none;
	right: 0px;
}
.s_nav {
	position: relative;
	padding: 43px 5px 0;
	z-index: 6;
	box-sizing: border-box;
}
.flobox.none {
	background: none;
	width: 56px;
}

.flobox.none .s_nav {
	display: none;
}

.addbg {
	width: 1200px;
	height: 643px;
	background: url(../img/addbg.png) no-repeat;
	margin: 35px auto 87px;
	padding-top: 210px;
	box-sizing: border-box;
}

.teamscrollbox {
	overflow-y: auto;
	height: 377px;
	margin-right: 38px;
}

.teamscrollbox a {
	cursor: pointer;
	margin: 0 16px 17px 47px;
	float: left;
	display: inline;
	width: 223px;
	height: 47px;
	background: url(../img/colbg.png) no-repeat;
	line-height: 47px;
	text-align: center;
}

.teamscrollbox a span {
	font-size: 20px;
	color: #7a3b03;
	/* background-image: -webkit-linear-gradient(top, #fbf8e7, #cfc7a3); */
	/* -webkit-background-clip: text; */
	/* -webkit-text-fill-color: transparent; */
}

.teamscrollbox::-webkit-scrollbar {
	width: 4px;
	background: #d7ab7d;
}

.teamscrollbox::-webkit-scrollbar-thumb {
	background: #eb8f1b;
}

.infbox {
	width: 731px;
	height: 619px;
	background: url(../img/infbg.png) no-repeat;
	color: #172746;
	z-index: 1;
	display: none;
}

.infbox .popclosebtn {
	right: -20px;
}

.teambox {
	padding: 28px 66px 0;
    text-align: center;
    font-size: 22px;
    line-height: 40px;
	color: #612a05;
}

.infbox ul {
	margin: 6px 67px 0;
}

.infbox li {
	height: 79px;
	text-align: center;
}

.touxian {
	float: left;
	margin-top: 6px;
	width: 37px;
	height: 71px;
	text-indent: 0;
	color: #632800;
	line-height: 28px;
	font-size: 20px;
	text-align: center;
	background: url(../img/touxian.png) no-repeat;
	padding: 8px 5px 0;
	box-sizing: border-box;
}

.detailbox {
	float: left;
	line-height: 24px;
	color: #ffefcc;
	font-size: 20px;
	padding-top: 12px;
}

.detailbox div {
	padding-left: 8px;
	width: 445px;
	text-align: left;
	float: left;
	box-sizing: border-box;
}

.detailbox div em {
	float: left;
	width: 336px;
}

.detailbox div i {
	float: left;
	font-style: normal;
	color: #feda7a;
	width: 92px;
	text-align: center;
}

.detailbox span {
	width: 90px;
	text-align: center;
	float: left;
}

/* pop-bili */
#video_pop {
	background: #000;
	width: 840px;
	height: 455px;
	border: 3px solid #f4e8b6;
}

#video_pop .video_bili {
	width: 840px;
	height: 455px;
	background: #000;
	margin: 0 auto;
}

#video_pop iframe {
	width: 100%;
	height: 100%;
}

#video_pop .popclosebtn {
	right: -50px;
	top: -15px;
}
