
/* 动画 */
@keyframes ani-fade-in{
    0%{opacity: 0;}
    100%{opacity: 1;}
}
@keyframes ani-fade-up-in{
    0%{opacity: 0; transform: translateY(20px);}
    100%{opacity: 1; transform: translateY(0);}
}
@keyframes ani-circle{
    0%{opacity: 1; transform: rotate(0deg);}
    100%{opacity: 1; transform: rotate(360deg);}
}
@keyframes ani-fade-scale-in{
    0%{transform: scale(0.8); opacity: 0;}
    100%{transform: scale(1); opacity: 1;}
}
@keyframes ani-fade-left-in{
    0%{left:40px; opacity: 0;}
    50%{opacity: 0.2;}
    100%{left:0;  opacity: 1;}
}

@keyframes ani-card-cover-translate{
    0%{transform: rotateY(-180deg) scale(1.02) skewY(0deg); }
    10%{opacity: 0.5; }
    20%{transform: rotateY(-180deg) scale(1.1) skewY(0deg); opacity: 0.8;}
    30%{transform: rotateY(-90deg) scale(1.19) skewY(-12deg);}
    70%{transform: rotateY(0deg) scale(1.1, 1.4) skewY(0deg); opacity: 1;}
    100%{transform: rotateY(0deg) scale(1) skewY(0deg); opacity: 1;}
}
@keyframes ani-card-back-translate{
    0%{transform: rotateY(0deg) scale(1.02) skewY(0deg); }
    10%{opacity: 0.5; }
    20%{transform: rotateY(0deg) scale(1.1) skewY(0deg); opacity: 0.8;}
    30%{transform: rotateY(-90deg) scale(1.19) skewY(-12deg);}
    70%{transform: rotateY(-180deg) scale(1.1, 1.4) skewY(0deg); opacity: 1;}
    100%{transform: rotateY(-180deg) scale(1) skewY(0deg); opacity: 1;}
}
@keyframes ani-yellow-card-cover-translate{
    0%{transform: rotateY(-180deg) scale(1.02) skewY(0deg) translate(-0px, -0px); }
    10%{opacity: 0.5; }
    20%{transform: rotateY(-180deg) scale(1.1) skewY(0deg) translate(-0px, -0px);  opacity: 0.8;}
    30%{transform: rotateY(-90deg) scale(1.29) skewY(-12deg);}
    70%{transform: rotateY(0deg) scale(1.2, 1.6) skewY(0deg) translate(10px, 10px); opacity: 1;}
    100%{transform: rotateY(0deg) scale(1) skewY(0deg) translate(-0px, -0px); opacity: 1;}
}
@keyframes ani-yellow-card-back-translate{
    0%{transform: rotateY(0deg) scale(1.02) skewY(0deg) translate(-0px, -0px); }
    10%{opacity: 0.5; }
    20%{transform: rotateY(0deg) scale(1.1) skewY(0deg) translate(-0px, -0px); opacity: 0.8;}
    30%{transform: rotateY(-90deg) scale(1.29) skewY(-12deg);}
    70%{transform: rotateY(-180deg) scale(1.2, 1.6) skewY(0deg) translate(10px, 10px); opacity: 1;}
    100%{transform: rotateY(-180deg) scale(1) skewY(0deg) translate(-0px, -0px); opacity: 1;}
}
@keyframes ani-jump{
    0%{transform: scale(1, .98);}
    20%{transform: scale(.92, 1.05);}
    40%{transform: scale(1, 1);}
    50%{transform: scale(1, 1); bottom: -10px;}
    80%, 90%{transform: scale(.94, 1.05);}
}
@keyframes ani-scale-big-fade-in{
    from{opacity: 0; transform: scale(0);}
    to{opacity: 1; transform: scale(1);}
}
