
html,body,div,p,ul,li,dl,dt,dd,em,i,span,a,img,input,h1,h2,h3,h4,h5{margin:0;padding:0;}
a,img,input{border:none;outline:none;}
a{text-decoration:none;}
img{display:block;}
ul,li{list-style:none;}
table{border-collapse:collapse;border-spacing:0;}
body{font:24px/1.5 -apple-system,"Helvetica Neue",Helvetica,Arial,sans-serif;-webkit-user-select: none;background-color:#fff;}
.none{display:none;}
input::-webkit-outer-spin-button,input::-webkit-inner-spin-button{-webkit-appearance: none !important;}
input[type="number"]{-moz-appearance:textfield;}
*{-webkit-appearance:none;-webkit-text-size-adjust:none;}
html{-webkit-tap-highlight-color:rgba(0,0,0,0);}
#cy_bot{padding:10px!important;position: relative;z-index: 2}
.cyLogo{margin-top:10px;}
.orientLayer {
    display: none;
    position: fixed;
    z-index: 1000;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, .9);
    color: #fff;
    font-size: 30px;
}

html,body{width:100%;height: 100%;touch-action: manipulation;}
.pr{position: relative;}
.pa{position: absolute;}
.hide{font-size: 0; text-indent: -9999em; width: 0; height: 0;}
.t{font-size: 0; text-indent: -9999em;display: block}
img{width:100%;}
p{text-align: center;}
/***public***/
i,em{font-style: normal;}

.page{position: relative; width: 750px; margin-left: auto; margin-right: auto; min-height: 100%;}
.page_mid{position: absolute; width: 100%; height: 1172px; top: 50%; margin-top: -586px; left: 0; overflow: hidden;}


/* pop */
.pop h1,h2,p{text-align: center;color: #4c4537;}
.pop h1,.txt-box{margin: 0 auto;font-size: 42px;}
.pop h1{padding-top: 50px;font-weight: bold;}
.pop .txt-box{width: 500px;height: 240px;position: absolute;top: 50%;left: 50%;transform: translate(-50%,-50%); color: #4c4537;}
.pop-common .txt-box{transform: translate(-50%,-55%);font-size:32px;display: flex;flex-direction: column; align-items: center; justify-content: center;}
.pop .txt1{width: 580px; font-size: 24px;color: #4c4537;font-weight: bold;line-height: 44px;padding-top:70px;margin: 0 auto 14px;}
.pop .btn-boxPop{display: flex;justify-content: center;align-items: center;position: absolute;bottom: 50px;left: 50%;transform: translateX(-50%);}
.pop .btn-boxPop>a:nth-child(1){margin-right: 20px;}

/*重置lay.js的toast弹窗样式*/
body .lay-msg{
    font-size: 24px;
    padding: 10px 22px;
}

body .lay-load{
    font-size: 24px;
}
body .lay-load .lay-load-icon{
    width: 60px;
    height: 60px;
    background-size: 100% auto;
    background-position: center center;
}