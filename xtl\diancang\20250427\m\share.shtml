<!DOCTYPE HTML>
<html>
<head>
    <meta charset="utf-8">
    <meta name="keywords" content="天龙,新天龙,天龙八部,新天龙八部,天龙八部3,tl,tlbb,tl3,xtl,网络游戏,网游,武侠,浪漫武侠,武侠游戏,金庸,<PERSON><PERSON>,天龙八部OL,萧峰,段誉,虚竹,王语嫣,天龙八部钟汉良版,天龙八部黄日华版,8周年,八周年,周年庆,全民代言,纵情四海" />
    <meta name="description" content="《新天龙八部》第十三门派桃花岛11月1日上线！高爆强控，单兵之王，箫中剑+离人偶，双武器作战方式颠覆江湖！许嵩全新桃花岛主题曲发布！" />
    <title>江湖同归-《天龙八部·归来》官方网站</title>
    <meta name="author" content="Design:CP; Web Layout:CP;" />
    <meta name="applicable-device" content="mobile">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no">
    <meta content="yes" name="apple-mobile-web-app-capable" />
    <meta content="black" name="apple-mobile-web-app-status-bar-style" />
    <meta content="telephone=no" name="format-detection" />
    <link type="image/x-icon" href="//xtl.changyou.com/favicon.ico" rel="icon"/>
    <link type="image/x-icon" href="//xtl.changyou.com/favicon.ico" rel="shortcut icon"/>
    <link type="text/css" rel="stylesheet" href="/act/all/css/reset.css" />
    <link type="text/css" rel="stylesheet" href="/act/xtl/betatest/20200107/m/css/share.css" />
    <script>
      //移动端版本兼容
      var phoneWidth = parseInt(window.screen.width);
      var phoneScale = phoneWidth/750;
    
      var ua = navigator.userAgent;
      if (/Android (\d+\.\d+)/.test(ua)){
        var version = parseFloat(RegExp.$1);
        // andriod 2.3
        if(version>2.3){
          document.write('<meta name="viewport" content="width=750, minimum-scale = '+phoneScale+', maximum-scale = '+phoneScale+', target-densitydpi=device-dpi">');
          // andriod 2.3以上
        }else{
          document.write('<meta name="viewport" content="width=750, target-densitydpi=device-dpi">');
        }
        // 其他系统
      } else {
        document.write('<meta name="viewport" content="width=750, user-scalable=no, target-densitydpi=device-dpi">');
      }
    </script>
    <script type="text/javascript">
      var deviceWidth = document.documentElement.clientWidth;
      if (deviceWidth > 750) {
        deviceWidth = 750
      };
      document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
    </script>
</head>
<body>
<!--逍遥sharebg0 峨眉sharebg1 明教sharebg2 武当sharebg3 天山sharebg4 丐帮sharebg5-->
<div class="wrapper sharebg0">
	<a href="//m.tl.changyou.com/" class="logo midpa com">天龙八部归来</a>
	<strong class="sharetip midpa">不删档测试激活码，游戏周边限量放送<br>快邀请好友同归江湖吧</strong>
	<a href="javascript:;" class="btn invebtn midpa">邀请好友同归</a>
	<a href="javascript:popShow('pop_tgui');" class="btn recoredbtn midpa">好友同归记录>></a>
	<a href="rule.shtml?id=0" class="btn rulebtn midpa">规则说明</a>
</div>
<!--弹窗-->
<!--还有同归记录-->
<div class="pop" id="pop_tgui">
	<p class="hznum">共有XX个好友响应了号召</p>
	<p class="hudhd">
		<span>微信好友</span>响应时间
	</p>
	<div class="scrollbox">
		<table>
			<tr>
				<td width="152">
					<div class="firbox">
						<img src="/act/xtl/betatest/20200107/m/img/share/friicon.jpg" width="84" height="84" alt="头像">
					</div>
				</td>
				<td class="midtd">微信名微信名</td>
				<td>x月x日</td>
			</tr>
			<tr>
				<td width="152">
					<div class="firbox">
						<img src="/act/xtl/betatest/20200107/m/img/share/friicon.jpg" width="84" height="84" alt="头像">
					</div>
				</td>
				<td class="midtd">微信名微信名</td>
				<td>x月x日</td>
			</tr>
			<tr>
				<td width="152">
					<div class="firbox">
						<img src="/act/xtl/betatest/20200107/m/img/share/friicon.jpg" width="84" height="84" alt="头像">
					</div>
				</td>
				<td class="midtd">微信名微信名</td>
				<td>x月x日</td>
			</tr>
			<tr>
				<td width="152">
					<div class="firbox">
						<img src="/act/xtl/betatest/20200107/m/img/share/friicon.jpg" width="84" height="84" alt="头像">
					</div>
				</td>
				<td class="midtd">我有一个很长很长的名字</td>
				<td>x月x日</td>
			</tr>
			<tr>
				<td width="152">
					<div class="firbox">
						<img src="/act/xtl/betatest/20200107/m/img/share/friicon.jpg" width="84" height="84" alt="头像">
					</div>
				</td>
				<td class="midtd">微信名微信名</td>
				<td>x月x日</td>
			</tr>
			<tr>
				<td width="152">
					<div class="firbox">
						<img src="/act/xtl/betatest/20200107/m/img/share/friicon.jpg" width="84" height="84" alt="头像">
					</div>
				</td>
				<td class="midtd">微信名微信名</td>
				<td>x月x日</td>
			</tr>
			<tr>
				<td width="152">
					<div class="firbox">
						<img src="/act/xtl/betatest/20200107/m/img/share/friicon.jpg" width="84" height="84" alt="头像">
					</div>
				</td>
				<td class="midtd">微信名微信名</td>
				<td>x月x日</td>
			</tr>
		</table>
	</div>
	<a href="javascript:popHide();" class="btn close">x</a>
</div>
<!--请在微信中进行好友邀请-->
<div class="pop pop3" id="pop_use">
	<div class="pop_cont pop_cont1">
		<h2 class="tctit tctit6 com">请在微信中进行好友邀请</h2>
		<a href="javascript:popHide();" class="btn btn-know">知道了</a>
	</div>
	<a href="javascript:popHide();" class="btn close">x</a>
</div>
<!--分享指示图片-->
<div class="fxovlay">
	<img src="/act/xtl/betatest/20200107/m/img/share/sharetip.png" width="350" height="207" alt="分享图层">
</div>
<script type="text/javascript" src="/act/all/js/jquery-1.8.3.min.js"></script>
<script src="/act/xtl/betatest/20200107/m/js/commshare.js"></script>
<!--#include virtual="/act/all/nav/cy_public_js_dark.html"-->
<!--#include virtual="/act/all/dma/dma_activity.html"-->
</body>
</html>
