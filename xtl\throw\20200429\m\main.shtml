<!DOCTYPE HTML>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="keywords" content="天龙,新天龙,天龙八部,新天龙八部,天龙八部3,tl,tlbb,tl3,xtl,网络游戏,网游,武侠,浪漫武侠,武侠游戏,金庸,MMO,天龙八部OL,萧峰,段誉,虚竹,王语嫣,天龙八部钟汉良版,天龙八部黄日华版,8周年,八周年,周年庆,全民代言,纵情四海" />
    <meta name="description" content="《新天龙八部》十三周年，万象更新！全新国韵华裳、音乐、场景为江湖注入新的灵魂，展现传统东方文化之美。多重周年福利上线！" />
    <meta name="author" content="Design:CP; Web Layout:CP;" />
    <meta name="viewport" content="width=750, minimum-scale=1', maximum-scale=1,target-densitydpi=device-dpi">
    <title>只为兄弟战今生-《新天龙八部》官方网站</title>
    <link type="image/x-icon" href="http://xtl.changyou.com/favicon.ico" rel="icon" />
    <link type="image/x-icon" href="http://xtl.changyou.com/favicon.ico" rel="shortcut icon" />
    <link rel="stylesheet" href="/act/all/css/reset.css" />
    <link rel="stylesheet" href="/act/all/cdn/Swiper/4.5.0/css/swiper.min.css">
    <link rel="stylesheet" href="/act/xtl/throw/20200429/m/css/main.css">
    <script>
        function getQueryString(name) {
            var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return r[2]; return null;
        }
        var uId = getQueryString('UID');
    </script>
    <!--抖音统计-初始化-->
    <script>
        (function (r, d, s, l) {
            var meteor = r.meteor = r.meteor || []; meteor.methods = ["track", "off", "on"]; meteor.factory = function (method) {
                return function () {
                    var args = Array.prototype.slice.call(arguments); args.unshift(method); meteor.push(args); return meteor
                }
            }; for (var i = 0; i < meteor.methods.length; i++) {
                var key = meteor.methods[i]; meteor[key] = meteor.factory(key)
            } meteor.load = function () {
                var js, fjs = d.getElementsByTagName(s)[0]; js = d.createElement(s);
                js.src = "https://analytics.snssdk.com/meteor.js/v1/" + l + "/sdk"; fjs.parentNode.insertBefore(js, fjs)
            }; meteor.load(); if (meteor.invoked) { return }
            meteor.invoked = true; meteor.track("pageview")
        })(window, document, "script", uId);
    </script>
    <!--快手统计-初始化-->
    <script type="text/javascript">(function (root) {    var ksscript = document.createElement('script');    ksscript.setAttribute('charset', 'utf-8');    ksscript.src = 'https://static.yximgs.com/udata/pkg/ks-ad-trace-sdk/ks-trace.2.0.1.beta.js';    var s = document.getElementsByTagName('script')[0];    s.parentNode.insertBefore(ksscript, s);})(window);</script>
    <!-- 适配 -->
    <script>
        var phoneWidth = parseInt(window.screen.width);
        var phoneScale = phoneWidth / 750;
        var ua = navigator.userAgent;
        if (/Android (\d+\.\d+)/.test(ua)) {
            var version = parseFloat(RegExp.$1);
            // andriod 2.3
            if (version > 2.3) {
                document.write('<meta name="viewport" content="width=750, minimum-scale = ' + phoneScale + ', maximum-scale = ' + phoneScale + ', target-densitydpi=device-dpi">');
                // andriod 2.3
            } else {
                document.write('<meta name="viewport" content="width=750, target-densitydpi=device-dpi">');
            }
        } else {
            document.write('<meta name="viewport" content="width=750, user-scalable=no, target-densitydpi=device-dpi">');
        }
        var deviceWidth = document.documentElement.clientWidth;
        if (deviceWidth > 750) {
            deviceWidth = 750
        }
        document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
    </script>
</head>
<body>
    <div id="boxThrow">
        <div class="wrap">
            <div class="bg">
                <div class="top"></div>
                <div class="main">
                    <div class="head_box">
                        <div class="head">
                            <div class="title sp bounceInDown">
                                <div class="min_title sp">上新服，免费抢重楼肩</div>
                                <p class="text bounceIn animated logo_text">5月22日19:00&nbsp;&nbsp;&nbsp;周年庆专区：山河万象&江山风华</p>
                            </div>
                        </div>
                        <!-- 切换 -->
                        <div class="tabs">
                            <a href="javascript:;" class="sp btn_gift gift" title="拆取礼包" @click="gift().show()">拆取礼包</a>
                            <a href="javascript:;" class="sp login btn_login" title="登陆账号" @click="login">登陆账号</a>
                            <a href="javascript:;" class="sp lottery_ btn_go_lottery" title="抽取大奖" @click="ja.utils.backTop(1600)">抽取大奖</a>
                        </div>
                        <!-- 登录 -->
                        <div class="login_">
                            <strong class="login_before" v-if="!isLogin">您好，请先登录
                                <a href="javascript:;" class="btn_login" @click="$click">【登录】</a></strong>
                            <strong class="login_after" v-if="isLogin">欢迎您！<span class="nickname" v-text="userInfo.openid"></span>
                                <a href="javascript:;" @click=myCenter>【个人中心】</a><a href="javascript:;" @click="ja.logout">【退出】</a></strong>
                        </div>
                    </div>
                    <!--抽奖-->
                    <div class="lottery_box">
                        <h2 class="sp t_1" id="lo"></h2>
                        <!-- 抽奖规则 -->
                        <p class="act_dec">
                            <b><em>1、</em> 拆取礼包并成功登录游戏账号；（请一定要预留手机号）</b><br>
                            <b><em>2、</em> 成功到大理龚彩云处领取【新年祝福礼包】后返回到该页面进行抽奖。</b><br>
                            <b><em>3、</em> 所有在大理龚彩云处领取礼包的用户获得【新服专属】和【全服通用】各一次抽奖机会。</b><br>
                            <b><em>4、</em> 全服通用的道具将发放到所有服务器，包括新服，而新服专属的道具只能在新服领取。</b><br>
                            <b><em>5、</em>获得重楼肩的用户，当您进入新服5个工作日内客服会联系您进行发放。</b>
                        </p>
                        <h5 id="bg1">您还有 <em v-text="lotteryNum"></em> 次抽奖机会</h5>
                        <!-- 抽奖转盘切换 -->
                        <div class="toggle_box">
                            <a href="javascript:;" class="sp new" :class="{'active':status===1}" title="新服专属" @click="lottery().tab(1)"></a>
                            <a href="javascript:;" class="sp old" :class="{'active':status===2}" title="全服通用" @click="lottery().tab(2)"></a>
                        </div>
                        <!-- 抽奖 -->
                        <div class="l_main_box">
                            <!-- 抽奖转盘-->
                            <div class="lotery1 lo">
                                <div class="lott lottery" id="lottcon">
                                    <!-- <ul class="lott_box">
                                        <li v-for="(row, index) in lotteryList" :class="'l_' + (index + 1)">
                                            <div v-for="item in row" :class="item.style" :name="item.type">
                                                <img :src="imgPath + item.url" :alt="item.name">
                                                <p v-html="item.display || item.name"></p>
                                            </div>
                                        </li>
                                    </ul> -->
                                    <a class="btn-lott sw_bg" href="javascript:;" @click="lottery().start()" title="立即抽奖"></a>
                                    <div class="lott-cover sw_bg"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 轮播图 -->
                    <div class="wiper_box">
                        <h2 class="sp"></h2>
						<div class="pl_box">
							<div class="srollbox">
								<img src="/act/xtl/throw/20200429/m/img/plimg.png">
							</div>
						</div>
                        <!-- <div class="my_siper">
                            <div class="swiper-container">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide" v-for="item in imgArr">
                                        <img :src="imgPath + item" alt="">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 如果需要分页器 -->
                       <!-- <div class="swiper-pagination"></div>
                        <!-- 如果需要导航按钮 -->
                       <!-- <div class="swiper-button-prev sp"></div>
                        <div class="swiper-button-next sp"></div> -->
                    </div>
                </div>
            </div>
        </div>
        <!-- 弹窗 -->
        <div class="pop_box">
            <!-- 抽中实物奖励 -->
            <div class="pop1 pop">
                <h3 class="addr_title sp"><span>恭喜您</span></h3>
                <p class="addr_dec">获得大大大大奖品一个，请填写您的手机号和联系方式，<br>以便我们及时给您发放奖品！</p>
                <div class="msg clearfix">
                    <div class="dj_box fl">
                        <!-- <img :src="giftSelect ? (imgPath + giftSelect.url) : defaultGift" class="img_gift" width="115" height="120" alt="道具">
                        <p class="gift_select_name">{{ giftSelect && giftSelect.name }}</p> -->
						<img src="/act/xtl/throw/20200429/m/img/pop_gift.png">
						<p class="gift_select_name">111111</p>
                    </div>
                    <ul class="user fl clearfix">
                        <li>
                            <label for="Name">姓名：</label>
                            <input id="Name" type="text" v-model="formAddr.rname" maxlength="12">
                        </li>
                        <li>
                            <label for="Phone">电话：</label>
                            <input id="Phone" type="text" v-model="formAddr.phone" maxlength="11">
                        </li>
                        <li>
                            <label for="Address">地址：</label>
                            <input id="Address" v-model="formAddr.address" type="text" maxlength="20">
                        </li>
                    </ul>
                </div>
                <a href="javascript:;" class="sbumit_btn sp" title="提交" @click="addr().submit()"><span>提交</span></a>
                <a href="javascript:;" class="sp off close" title="关闭">关闭</a>
            </div>
            <!-- 抽中虚拟奖励-->
            <div class="pop2 pop">
                <h3 class="addr_title sp"><span>恭喜您</span></h3>
                <div class="dj_box">
                    <img :src="giftSelect ? (imgPath + giftSelect.url) : defaultGift" class="img_gift" width="115" height="120" alt="道具">
					<!-- <img src="/act/xtl/throw/20200429/m/img/pop_gift.png"> -->
                </div>
                <p>获得 <span class="gift_select_name">{{ giftSelect && giftSelect.name }}</span><br/><span class="seccess_dec">请到大理龚彩云处进行领取哦！</span></p>
                <a href="javascript:;" class="sp off close" title="关闭">关闭</a>
            </div>
            <!-- 请先领取礼包-->
            <div class="pop3 pop">
                <p>亲爱的少侠，请不要心急哦，请到大理龚彩云 <br> 处领取礼包后再回来抽奖哦！</p>
                <a href="javascript:;" class="sp off close" title="关闭">关闭</a>
            </div>
            <!-- 预留手机 -->
            <div class="pop4 pop">
				<h3 class="addr_title sp"><span>恭喜您</span></h3>
                <p class="dec">您已获得新服礼包一份，请登录游戏在大理龚彩云（171，123）处领取，领取后即可回到此页面获得一次抽奖机会。预留您的手机号，届时我们将短信提醒您抽奖地址！</p>
                <p class="input_wrap"><input type="text" class="input_phone" placeholder="输入手机号"></p>
                <p class="input_wrap"><input type="text" class="input_phone_code" placeholder="输入验证码">
                    <a href="javascript:;" class="btn_phone_code" @click="appoint().code()">发送</a>
                </p>
                <a href="javascript:;" class="sbumit_btn sp" title="预留" @click="appoint().submit()">预留</a>
                <a href="javascript:;" class="sp off close" title="关闭">关闭</a>
            </div>
            <!-- 拆礼包 -->
            <div class="pop6 pop">
                <a href="javascript:;" class="gift_box" title="礼包" @click="gift().open()"></a>
                <a href="javascript:;" class="sp off close" title="关闭" @click="gift().close()">关闭</a>
            </div>
            <!--没有抽中-->
            <div class="pop7 pop">
                <p>很遗憾！您没有抽中礼包</p>
                <a href="javascript:;" class="sp off close" title="关闭">关闭</a>
            </div>
            <!-- 获奖记录 -->
            <div class="pop8 pop">
				<h3 class="addr_title sp"><span>抽奖记录</span></h3>
                <div class="gift_list">
                    <table>
                        <tbody>
                        <tr>
                            <td style="width:220px">获奖时间</td>
                            <td>所获物品</td>
                        </tr>
                        <tr v-show="!newInfo.prizeName && !allInfo.prizeName">
                            <td colspan="2">暂无记录</td>
                        </tr>
                        <tr v-show="newInfo.prizeName">
                            <td>{{ newInfo.createTime | formatDateTime }}</td>
                            <td>{{ newInfo.prizeName }}</td>
                        </tr>
                        <tr v-show="allInfo.prizeName">
                            <td>{{ allInfo.createTime | formatDateTime }}</td>
                            <td>{{ allInfo.prizeName }}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <a href="javascript:;" v-show="allInfo.prizeType==='R' && !allInfo.hasAddress" title="填写收货地址" class="sp write btn_addr_show" @click="pop(true, 1)">填写收货地址</a>
				<!-- <a class="sp write btn_addr_show active" href="javascript:;">填写收货地址</a> -->
                <a href="javascript:;" class="sp off close" title="关闭">关闭</a>
            </div>
        </div>
    </div>
<script src="/act/all/cdn/jquery/3.4.1/jquery.min.js"></script>
<script src="/act/all/cdn/Swiper/4.5.0/js/swiper.min.js"></script>
<script src="/act/all/cdn/join-activity/2.5/join-activity.min.js"></script>
<script src="/act/all/cdn/popout/1.0/popout.min.js"></script>
<script src="/act/all/cdn/lottery/1.1/lottery.min.js"></script>
<script src="/act/all/cdn/vue/2.6.10/vue.min.js"></script>
<script src="index.js"></script>
<!--#include virtual="/all/nav/cy_public_js_dark.html"-->
<!--#include virtual="/all/dma/dma_activity.html"-->
</body>
</html>