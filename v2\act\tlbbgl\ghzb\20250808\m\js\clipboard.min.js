(function(b, a) { var c = "undefined" !== typeof module && module.exports; "function" === typeof define ? define(a) : c ? module.exports = a() : this[b] = a() })("clipBordCopy", function() {
    return function(b) {
        var a = document.createElement("textarea");
        a.style.fontSize = "12pt";
        a.style.border = "0";
        a.style.padding = "0";
        a.style.margin = "0";
        a.style.position = "absolute";
        a.style.left = "-9999px";
        a.style.top = (window.pageYOffset || document.documentElement.scrollTop) + "px";
        a.setAttribute("readonly", "");
        a.value = b;
        document.body.appendChild(a);
        a.focus();
        a.select();
        a.setSelectionRange && a.setSelectionRange(0, a.value.length);
        b = void 0;
        try { b = document.execCommand("copy") } catch (c) { b = !1 } a && document.body.removeChild(a);
        return b
    }
});