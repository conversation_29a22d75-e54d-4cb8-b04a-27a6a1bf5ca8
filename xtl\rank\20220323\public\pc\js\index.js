var BASE_URL = "/changyou/meeting/",
    hasInitRole = false,
    activity = 'meeting',
    app = 'xtl',
    version = '20210317',
    cityName = '郑州';
$(function () {
    var platform = 'changyou';
    ja.simpleConfig(app, activity, version, platform, function () {
        init();
    },
    //登录
    function (cn) {
        if (!(validate(cn))) {
            loginFlag = false;
            $(".nav").find("span:eq(0)").show();
            $(".nav").find("span:eq(1)").hide();
            $(".nav").find("span:eq(1) em").html("");
        } else {
            loginFlag = true;
            $(".nav").find("span:eq(1)").show();
            $(".nav").find("span:eq(0)").hide();
            $(".nav").find("span:eq(1) em").html(cn.data.openid);
        }
    });
    //省市区调用
    // new PCAS("ProvinceId","CityId");
    // $('select.select').select();
    //报名
    $('.baoming_btn').click(function () {
        // if(!loginFlag){
        // 	isLogin(1);
        // 	return;
        // }
        $("#popCityName").text(cityName);
        var time = new Date().getTime();
        if (activity == 'zzmeeting') {
            if (time < parseInt(localStorage.getItem("zzStartTime"))) {
                popup($("#pop5"));
                return;
            } else if (time > parseInt(localStorage.getItem("zzEndTime"))) {
                popup($("#pop7"));
                return;
            }
        } else if (activity == 'shmeeting') {
            if (time < parseInt(localStorage.getItem("shStartTime"))) {
                popup($("#pop5"));
                return;
            } else if (time > parseInt(localStorage.getItem("shEndTime"))) {
                popup($("#pop7"));
                return;
            }
        } else if (activity == 'xameeting') {
            if (time < parseInt(localStorage.getItem("xaStartTime"))) {
                popup($("#pop5"));
                return;
            } else if (time > parseInt(localStorage.getItem("xaEndTime"))) {
                popup($("#pop7"));
                return;
            }
        }else {
            popWin("活动未开始或者已结束");
            return;
        }
        ja.ajax({
            type: "get",
            url: ja.getDomain() + BASE_URL + "status/signup",
            async: false,
            data: {},
            headers: {
                APP: app,
                ACTIVITY: activity,
                VERSIONCODE: version
            },
            dataType: "json",
            success: function (data) {
                console.log(data);
                // return false;
                if (data.code != 10000) {
                    popWin(data.message);
                    return;
                }
                if (data.data.status == 0) {
                    // if (!hasInitRole)
                    //     getRoleList();
                    // 展开报名信息输入弹窗
                    popup($("#pop6"));
                } else if (data.data.status == 1) {
                    popWin("你已报名，请勿重复报名哦~");
                }
            },
            error: function () {
                popWin("服务器繁忙，请稍候重试！");
            }
        });
    });
    
    // 打开报名信息输入弹窗
    $(".pop-baoming_btn").on("click", function(){
        hideMask($("#pop6"));
        popup($("#pop3"));
    });

    //弹窗里面确定按钮关闭
    $('.btn2').click(function () {
        if (isExist($('#Overlay'))) $('#Overlay').remove();
        if (isExist($('#Overlay1'))) $('#Overlay1').remove();
        $('.cyou_dialog2').hide();
    });

    //退出
    $(".quit").click(function () {
        loginOut();
    });

    // $('#info_yanzheng').click(function () {
    // sendCode(trim($('#info_phone').val()));
    // });
    //提交报名信息
    $('.subbtn').click(function () {
        subInfo();
    });

});
function init() {
    ja.ajax({
        url: ja.getDomain() + BASE_URL + "init",
        type: "get",
        data: {},
        dataType: "json",
        success: function (data) {
            console.log(data);
            if (data.code == 10000) {
                localStorage.setItem("zzStartTime", data.data.zzStartTime);
                localStorage.setItem("zzEndTime", data.data.zzEndTime);
                localStorage.setItem("shStartTime", data.data.shStartTime);
                localStorage.setItem("shEndTime", data.data.shEndTime);
                localStorage.setItem("xaStartTime", data.data.xaStartTime);
                localStorage.setItem("xaEndTime", data.data.xaEndTime);
                localStorage.setItem("zzStartTime", data.data.zzStartTime);
                timeInit();
            }else {
                // 
                popWin(data.message);
                return;
            }
        },
        error: function () { }
    });

}
function initZZ(e) {
    activity = "zzmeeting";
    cityName = "郑州";
}
function initSH(e) {
    activity = "shmeeting";
    cityName = "二号站";
}
function initXA(e) {
    activity = "xameeting";
    cityName = "三号站";
}
function login() {
    ja.slogin(1);
}
function logout() {
    ja.logout();
}
function subInfo() {
    var tmp;
    var obj = {
        signUpCity: cityName,
        nickName: trim($('#info_game').val()),//游戏
        name: trim($('#info_name').val()),
        city: trim($('#info_city').val()),
        qq: trim($('#info_qq').val()),
        phone: trim($('#info_phone').val()),
        code: trim($('#info_code').val()),
        comment: trim($('#info_comment').val()),
    };

    if (obj.nickName == '0') {
        subError('请选择游戏');
        return;
    }
    tmp = comCheck(obj.name, 10, "姓名");
    if (!tmp.code) {
        $(".err span").html(tmp.mes);
        return;
    }
    // obj.city=obj.province+obj.cityStr+obj.area;
    if (!obj.city || obj.city.length < 2) {
        subError("请输入具体城市辖区");
        return;
    }else if(obj.city.length > 25) {
        subError("输入城市过长");
        return;
    }
    if (!boolean_qq(obj.qq)) {
        return;
    }
    if (!boolean_phone(obj.phone)) {
        return;
    }
    if (!obj.code) {
        subError("请输入验证码");
        return;
    }
    if (obj.code.length != 6 || !checkRate(obj.code)) {
        subError("请输入正确的验证码");
        return;
    }
    /*if(obj.comment=="十年了，您与《新天龙八部》有怎样难忘的回忆或故事？"){
        obj.comment="";
    }*/
    if (obj.comment.length <2) {
        subError("请输入您最想和策划讨论的问题和建议哦");
        return;
    }else if (obj.comment.length > 150) {
        subError("输入的问题较长");
        return;
    }

    obj.deviceType = "M";
    $(".err span").html("");
    $("#subbtn").removeClass("subbtn");
    //$("#pop3").hide();
    ja.ajax({
        url: ja.getDomain() + BASE_URL + "saveInfo",
        type: "post",
        data: obj,
        headers: {
            APP: app,
            ACTIVITY: activity,
            VERSIONCODE: version
        },
        dataType: "json",
        success: function (data) {
            if (data.code == 10000) {
                hideMask($("#pop3"));
                popup($("#pop1"));
            } else if (data.code == 3302) {
                popWin("您已经报名过见面会了，请勿重复报名。");
                return;
            } else if (data.code == 1024) {
                $("#errorMsg").text("您填写的手机号已报过名，请勿重复使用。");
                //popWin("您填写的手机号已报过名，请勿重复使用。");
                return;
            } else if (data.code == 1202) {
                $("#errorMsg").text("请您填写完整信息，方便工作人员与您联系哦~");
                //popWin("参数不能为空");
                return;
            } else if (data.code == 1200) {
                $("#errorMsg").text(data.message);
                //popWin(data.message);
                return;
            } else if (data.code == 1313) {
                $("#errorMsg").text("信息保存失败");
                //popWin("信息保存失败");
                return;
            } else if (data.code == 3002) {
                $("#errorMsg").text(data.message);
                //popWin(data.message);
                return;
            } else if (data.code == 1108) {
                $("#errorMsg").text("活动未开始或者已结束");
                //popWin($('#info_bmcity').find("option:selected").text() + "当前时间段不支持报名");
                return;
            } else {
                $("#errorMsg").text("系统繁忙，请刷新后重试！");
                //popWin("系统繁忙，请刷新后重试！");
            }
            $("#subbtn").addClass("subbtn");
        },
        error: function () {
        }
    });
}

function checkRate(nubmer) {
    //判断正整数
    var re = /^\d+$/;
    if (re.test(nubmer)) {
        return true;
    } else {
        return false;
    }
}
function getRoleList() {
    ja.ajax({
        url: ja.getDomain() + BASE_URL + "getRoleList",
        type: "get",
        data: {},
        dataType: "json",
        success: function (data) {
            if (data.code != 10000) {
                popWin("加载角色列表错误，请登录后重试！");
                return;
            }
            if (!data.data || data.data.length == 0) {
                popWin("角色列表为空，请至游戏中创建角色！");
                return;
            }
            var ops = '<option value="0">选择</option>';
            $.each(data.data, function (index, obj) {
                // ops += '<option value="' + obj.role_name + '">' + obj.role_name + '</option>'
                var str = obj.server_name;
                var server_name = str.split("_")[1];
                console.log(str);
                console.log(str.split("_"));
                ops += '<option value="' + obj.role_name + '-' + server_name + '">' + obj.role_name + '-' + server_name + '</option>'
            });

            $('#info_nickName').empty();
            $('#info_nickName').append(ops);
            hasInitRole = true;
        },
        error: function () {
        }
    });
}

function boolean_qq(qq_num) {
    var u_qq = new RegExp("^[1-9][0-9]{4,9}$");
    console.log(qq_num);
    if (qq_num == "") {
        $(".err span").html("请输入QQ号码");
        return false;
    } else if (qq_num.length > 11) {//!u_qq.test(qq_num) ||
        $(".err span").html("请输入正确的QQ号码");
        return false;
    } else {
        return true;
    }
}

function boolean_phone(phone_num) {
    var u_phone = new RegExp("^1[3|4|5|6|7|8|9][0-9]{9}$");
    if (phone_num == "") {
        $(".err span").html("请输入手机号");
        return false;
    } else if (!u_phone.test(phone_num)) {
        $(".err span").html("请输入正确的手机号");
        return false;
    } else {
        return true;
    }
}

//验证码倒计时
function clickButton(obj) {
    var phone_num = trim($('#info_phone').val());
    if (!boolean_phone(phone_num))
        return;
    var obj = $(obj);
    obj.attr("disabled", "disabled");
    /*按钮倒计时*/
    var time = 60;
    var set = setInterval(function () {
        obj.val(--time + "(s)");
    }, 1000);
    /*等待时间*/
    setTimeout(function () {
        obj.attr("disabled", false).val("验证码");
        /*倒计时*/
        clearInterval(set);
    }, 60000);
    ja.ajax({
        url: ja.getDomain() + "/changyou/core/zhpt/sendcode",
        type: "post",
        data: { "phone": phone_num },
        dataType: "json",
        success: function (data) {
            if (data.code != 10000) {
                popWin("发送验证码失败，请稍候重试！");

            }
        },
        error: function () {
        }
    });
}

//登陆状态绑定
function afterLocation(cn) {

    if ("" == cn || null == cn || "null" == cn) {
        $(".nav").find("span:eq(0)").show();
        $(".nav").find("span:eq(1)").hide();
        $(".nav").find("span:eq(1) em").html("");
        return;
    }
    $(document).afterLogin(cn);
}
function afterLogin(cn) {
    $(document).afterLogin(cn);

}
//删除左右两端的空格 输入：字符串 输出：去空格的字符串
function trim(str) {
    return str.replace(/(^\s*)|(\s*$)/g, "");
}

// 判断是否非空 输入：要检查的内容 输出：true 非空 false 空
function isNull(info) {
    if (info == null || trim(info) == "" || info == undefined || info == "undefined") {
        return true;
    }
    return false;
}
//验证参数
function validate(value) {
    if (value == null || value == 'null' || value == '' || value == undefined) {
        return false;
    } else {
        return true;
    }
}

function popWin(content) {
    if (content == undefined) {
        return;
    }
    $("#pop2 div h3").html(content);
    popup($("#pop2"));
}
function subError(content) {
    $(".err span").html(content);
}

function comCheck(data, len, str) {
    var mes = {
        mes: "",
        code: true
    };
    if (data.length == 0) {
        mes.mes = "请输入" + str + "！";
        mes.code = false;
        return mes;
    } else if (data.length > len) {
        mes.mes = "输入" + str + "过长";
        mes.code = false;
        return mes;
    } else if (checkRate(data)) {
        mes.mes = "请输入汉字";
        mes.code = false;
        return mes;
    }
    return mes;
}

function timestampToTime(timestamp) {
    var date = new Date(timestamp);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
    Y = date.getFullYear() + '-';
    M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
    D = date.getDate() + ' ';
    h = date.getHours() + ':';
    m = date.getMinutes() + ':';
    s = date.getSeconds();
    return Y + M + D + h + m + s;//时分秒可以根据自己的需求加上
}

function timestampToDate(timestamp) {
    var date = new Date(timestamp);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
    Y = date.getFullYear() + '-';
    M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
    D = date.getDate();
    h = date.getHours() + ':';
    m = date.getMinutes() + ':';
    s = date.getSeconds();
    return Y + M + D;//时分秒可以根据自己的需求加上
}

function timeInit() {
    var time = new Date().getTime();
    if (time >= localStorage.getItem("zzStartTime") && time <= localStorage.getItem("zzEndTime")) {
        initZZ(localStorage.getItem("zzStartTime"));
        cityStart [0] = 1;//开放当前城市巡礼
        showXl();//开放巡礼模块
        return;
    }else if (time >= localStorage.getItem("shStartTime") && time <= localStorage.getItem("shEndTime")) {
        initSH(localStorage.getItem("shStartTime"));
        cityStart [1] = 1;//开放当前城市巡礼
        $(".times-plank li").eq(0).text('已结束');
        showXl();//开放巡礼模块
        return;
    }else if (time >= localStorage.getItem("xaStartTime") && time <= localStorage.getItem("xaEndTime")) {
        initXA(localStorage.getItem("xaStartTime"));
        cityStart [2] = 1;//开放当前城市巡礼
        $(".times-plank li").eq(0).text('已结束');
        $(".times-plank li").eq(1).text('已结束');
        showXl();//开放巡礼模块
    }
}

// 开放巡礼模块
function showXl(){
    // $(".part.p-4").addClass('on');
}

//视频播放 
$('.play_btn').parent('li').click(function () {
    var dataSrc = $('.play_btn').attr("data-src")
    playVideo(dataSrc)
})

function playVideo(vid) {
    popup($('#video_player'));
    $('#video_placeholder').commVideoPlayer({
        v_id: vid,
        width: 800,
        height: 450,
        autoPlay: true
    });
}

$(".close").on("click", function () {
    $('#video_placeholder').replaceWith('<span id="video_placeholder">视频正在下载中...</span>')
    $("#video_player").css("display", "none")
})
