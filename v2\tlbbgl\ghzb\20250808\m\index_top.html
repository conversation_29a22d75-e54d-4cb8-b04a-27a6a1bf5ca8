<!DOCTYPE HTML>
<html>
<head>
    <meta charset="utf-8">
    <title>《天龙八部·归来》公会争霸</title>
    <meta name="keywords" content="天龙八部归来，天龙八部端游，新天龙八部，经典天龙八部，怀旧天龙八部，福利，元宝，" />
    <meta name="description" content="公会争霸，谁能问鼎？冲榜，瓜分百万元宝！" />
    <meta name="referrer" content="no-referrer-when-downgrade">
    <meta content="width=750,minimum-scale=0,maximum-scale=5,user-scalable=no" name="viewport" />
    <meta content="yes" name="apple-mobile-web-app-capable" />
    <meta content="black" name="apple-mobile-web-app-status-bar-style" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="author" content="" />
    <meta name="robots" content="index, follow" />
    <meta name="googlebot" content="index, follow" />
    <link type="image/x-icon" href="//tlbbgl.changyou.com/favicon.ico" rel="icon" />
    <link type="image/x-icon" href="//tlbbgl.changyou.com/favicon.ico" rel="shortcut icon" />
    <link href="/act/all/css/reset.css" rel="stylesheet" type="text/css" />
    <link href="/v2/act/tlbbgl/ghzb/20250407/m/css/index.css?1.6" rel="stylesheet" type="text/css" />
    <script>
        //移动端版本兼容
        var phoneWidth = parseInt(window.screen.width);
        var phoneScale = phoneWidth/750;

        var ua = navigator.userAgent;
        if (/Android (\d+\.\d+)/.test(ua)){
            var version = parseFloat(RegExp.$1);
            // andriod 2.3
            if(version>2.3){
                document.write('<meta name="viewport" content="width=750, minimum-scale = '+phoneScale+', maximum-scale = '+phoneScale+', target-densitydpi=device-dpi">');
                // andriod 2.3以上
            }else{
                document.write('<meta name="viewport" content="width=750, target-densitydpi=device-dpi">');
            }
            // 其他系统
        } else {
            document.write('<meta name="viewport" content="width=750, user-scalable=no, target-densitydpi=device-dpi">');
        }
        window.onload = function () {
            var popRule = document.getElementById("popRule");
            if (popRule && document.documentElement.clientHeight < 1076) {
                console.log("window.screen.height:" + document.documentElement.clientHeight)
                popRule.style.transform = "scale(" + (document.documentElement.clientHeight / 1076) + ")";
            }
        };
    </script>
    <script type="text/javascript">
        var deviceWidth = document.documentElement.clientWidth;
        if (deviceWidth > 750) {
            deviceWidth = 750
        };
        document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
    </script>
</head>
<body>
    <div class="container">
        <!--  首页  -->
        <div class="page page0 on">
            <div class="wrap">
                <a href="https://tlbbgl.changyou.com/" class="logo bg t">天龙 归来</a>
                <div class="t slogan">公会争霸
                    拉兄弟冲榜·赢百万元宝</div>
                <a href="javascript:;" class="btn btn-login">登录</a>
                <div class="flex justify-center">
                    <a href="javascript:;" class="btn btn-rule">活动规则</a>
<!--                    <a href="javascript:;" class="btn btn-center">个人中心</a>-->
                </div>
            </div>
        </div>
        <!--  创建公会  -->
        <div class="page page1">
            <div class="wrap">
                <a href="javascript:app.ewm();" class="ewmtn"></a>
                <a href="javascript:;" class="btn btn-logout">退出</a>
                <a href="javascript:;" class="btn btn-create">创建公会</a>
                <a href="javascript:;" class="btn btn-join">加入其他公会</a>
                <div class="flex justify-center">
                    <a href="javascript:;" class="btn btn-rule">活动规则</a>
                    <!--                    <a href="javascript:;" class="btn btn-center">个人中心</a>-->
                </div>
            </div>
        </div>
        <!--  公会首页  -->
        <div class="page page2">
            <div class="wrap">
                <a href="javascript:app.ewm();" class="ewmtn"></a>
                <a href="javascript:;" class="btn btn-logout">退出</a>
                <div class="flex justify-center">
                    <a href="javascript:;" class="btn btn-rule btn-rule2">活动规则</a>
                    <a href="javascript:;" class="btn btn-center btn-center2">个人中心</a>
                </div>

                <div class="guild-code">
                    <p class="J_nickname guild-nickname">-</p>
                    <p>您是公会【<span class="red J_ganghuiName">-</span>】的<span class="isLeader"></span>，已有<span class="red J_memberNum">-</span>人加入本公会</p>
                    <div class="guild-line J_qqAccount">
                        <p>公会QQ群</p>
                        <input type="text" readonly value="没有QQ群号">
                        <a href="javascript:;" class="btn btn-copy">复制</a>
                        <a href="javascript:;" class="btn btn-modify">修改</a>
                    </div>
<!--                    <div class="guild-line J_wxAccount">-->
<!--                        <p>会长微信号</p>-->
<!--                        <input type="text" readonly value="没有微信号">-->
<!--                        <a href="javascript:;" class="btn btn-copy">复制</a>-->
<!--                        <a href="javascript:;" class="btn btn-modify">修改</a>-->
<!--                    </div>-->
                    <div class="guild-line J_ghId">
                        <p>公会码</p>
                        <input type="text" readonly>
                        <a href="javascript:;" class="btn btn-copy">复制</a>
                    </div>
                    <a href="javascript:;" class="btn btn-detail">查看成员详情</a>
                </div>
                <p class="tip">您可以通过分享公会码或者通过下方链接集结公会成员。</p>
                <a href="javascript:;" class="btn btn-guild">集结公会成员</a>
                <div class="t tit">公会争霸</div>
                <div class="guild-num">
                    本公会有效成员数量达到<span class="red J_personTotal">-</span>人，有效排名<span class="red J_personRank">-</span>，消费排名<span class="red J_buyRank">-</span><br />
                    每榜单满足条件前20名公会可瓜分百万元宝，数据延迟1小时左右更新。
                </div>
                <div class="rank">
                    <div class="rank-tab">
                        <a href="javascript:;" class="btn on">有效数量排名</a>
                        <a href="javascript:;" class="btn">消费排名</a>
                    </div>
                    <div class="rank-con rank-con0">
                        <table>
                            <tr>
                                <th>排名</th>
                                <th>公会名</th>
                                <th>有效数</th>
                            </tr>
                        </table>
                        <div class="rank-table">
                            <table id="rankData1">
                                <tr>
                                    <td>1</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>4</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>5</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>6</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>7</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>8</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>9</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>10</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="rank-con rank-con1" style="display: none;">
                        <table>
                            <tr>
                                <th>排名</th>
                                <th>公会名</th>
                            </tr>
                        </table>
                        <div class="rank-table">
                            <table id="rankData2">
                                <tr>
                                    <td>1</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>4</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>5</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>6</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>7</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>8</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>9</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                                <tr>
                                    <td>10</td>
                                    <td>公会名公会名</td>
                                    <td>21463</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <!--  公会列表   -->
        <div class="page page3">
            <div class="wrap">
                <a href="javascript:app.ewm();" class="ewmtn"></a>
                <a href="javascript:;" class="btn btn-back">返回</a>
                <div class="guild-num guild-num2">
                    已有<span class="red J_memberNum">-</span>人加入本公会，有效成员数量达到<span class="red J_personTotal">-</span>人。<br/>
                    本公会有效成员数量排名<span class="red J_personRank">-</span>，消费排名<span class="red J_buyRank">-</span>
                </div>
                <div class="rank rank2">
                    <div class="rank-tab rank-tab2">
                        <a href="javascript:;" class="btn on">有效数量排名</a>
                        <a href="javascript:;" class="btn">消费排名</a>
                    </div>
                    <div class="rank-con rank-con0">
                        <table>
                            <tr>
                                <th>序号</th>
                                <th>昵称</th>
                                <th>是否有效</th>
                            </tr>
                        </table>
                        <div class="rank-table">
                            <div class="user-list">
                                <table id="userList1">
<!--                                    <tr>-->
<!--                                        <td>1</td>-->
<!--                                        <td>公会名公会名</td>-->
<!--                                        <td>21463</td>-->
<!--                                    </tr>-->
                                </table>
                            </div>
                        </div>
                        <div class="paging">
                            <a href="javascript:;" class="btn btn-prev">上一页</a>
                            <p><span id="pageNo1">1</span>/<span id="pageTotal1">1</span></p>
                            <a href="javascript:;" class="btn btn-next">下一页</a>
                            <a href="javascript:;" class="btn btn-up">返回顶部</a>
                            <div>跳转<input type="number">页</div>
                        </div>
                    </div>
                    <div class="rank-con rank-con1" style="display: none;">
                        <table>
                            <tr>
                                <th>排名</th>
                                <th>昵称</th>
                            </tr>
                        </table>
                        <div class="rank-table">
                            <div class="user-list">
                                <table id="userList2">
<!--                                    <tr>-->
<!--                                        <td>1</td>-->
<!--                                        <td>公会名公会名</td>-->
<!--                                        <td>21463</td>-->
<!--                                    </tr>-->

                                </table>
                            </div>
                        </div>
                        <div class="paging">
                            <a href="javascript:;" class="btn btn-prev">上一页</a>
                            <p><span id="pageNo2">1</span>/<span id="pageTotal2">1</span></p>
                            <a href="javascript:;" class="btn btn-next">下一页</a>
                            <a href="javascript:;" class="btn btn-up">返回顶部</a>
                            <div>跳转<input type="number">页</div>
                        </div>
                    </div>
                    <p class="rank-tip">达到68级且在福利大厅解锁了高级成长密令的玩家为公会有效成员。</p>
                </div>
            </div>
        </div>
        <!--  被邀请加入公会  -->
        <div class="page page4">
            <div class="wrap">
                <a href="javascript:app.ewm();" class="ewmtn"></a>
                <a href="javascript:;" class="btn btn-logout">退出</a>
                <div class="flex justify-center">
                    <a href="javascript:;" class="btn btn-rule btn-rule2">活动规则</a>
<!--                    <a href="javascript:;" class="btn btn-center btn-center2">个人中心</a>-->
                </div>
                <div class="invite-text">
                    【<span class="red" id="J_inviterGh">-</span>】公会邀请您加入，一起瓜分百万元宝，您是否加入此公会？
                </div>
                <a href="javascript:;" class="btn btn-joins">加入此公会</a>
                <a href="javascript:;" class="btn btn-create">创建公会</a>
                <a href="javascript:;" class="btn btn-join">加入其他公会</a>
            </div>
        </div>

        <!-- ↓↓↓弹窗↓↓↓ -->
        <!--新增二维码弹窗-->
        <div class="pop pop-ewm" id="ewmPop">
            <img src="/v2/act/tlbbgl/ghzb/20250808/m/img/adp.png" alt="">
            <a href="javascript:;" class="btn-close"></a>

            <!-- <img src="/v2/act/tlbbgl/ghzb/20250808/m/img/qrcode1.png" alt="" class="qrcode_img qrcode1">
            <img src="/v2/act/tlbbgl/ghzb/20250808/m/img/qrcode2.png" alt="" class="qrcode_img qrcode2"> -->
        </div>
        <!-- 通用弹窗 -->
        <div class="pop pop-alert" id="popCom">
            <a href="javascript:;" class="btn btn-close">×</a>
            <h3>温馨提示</h3>
            <div class="pop-com">
                <span class="pop-msg-txt">通用弹窗通用弹窗通用弹窗通用弹窗通用弹窗通用弹窗通用</span>
            </div>
            <div class="flex justify-center">
                <a href="javascript:;" class="btn btn-confirm">确认</a>
            </div>
        </div>
        <!-- 通用弹窗 -->
        <div class="pop" id="popCom2">
            <a href="javascript:;" class="btn btn-close">×</a>
            <div class="pop-com">
                通用弹窗通用弹窗通用弹窗通用弹窗通用弹窗通用弹窗通用
            </div>
            <div class="flex justify-center">
              <a href="javascript:;" class="btn btn-confirm">确认</a>
              <a href="javascript:;" class="btn btn-cancel">取消</a>
            </div>
        </div>
        <!-- 登录 -->
        <div class="pop pop-login" id="popLogin">
            <a href="javascript:;" class="btn btn-close closeAll">×</a>
            <div class="login-box">

            </div>
        </div>
        <!-- 活动规则 -->
        <div class="pop pop2" id="popRule">
            <a href="javascript:;" class="btn btn-close closeAll">×</a>
            <p class="rule">
                <strong class="tit-color">活动时间：</strong>
                <br />即日起至2025年8月17日
<!-- <br /> -->
                <br /><strong class="tit-color">活动内容：</strong>
                <br />在活动期间内，玩家可以在活动页面参加《天龙八部·归来》公会争霸活动，会长可以通过页面集结链接或者公会码集结公会成员。一个账号只能进入一个公会，不可退出。
                <br />玩家加入公会，可获得绑定称号：盟契山河（30天），所有属性+5。
                <br />7月25日至8月17日24:00期间，达到68级且解锁了高级成长密令的玩家为公会有效成员（7月25日公测后进入游戏，通过右上方福利大厅“成长密令”活动解锁高级密令）。拥有至少3名有效成员的公会为有效公会。
                <br />分别根据公会中有效成员数量以及有效成员的消费元宝总额进行排名，会长和有效成员可瓜分百万元宝奖励。页面数据更新有延迟，延迟约1小时左右，获奖信息以官方公告为准。
<!-- <br /> -->
                <br /><strong class="tit-color">活动奖励：</strong>
                <br />《天龙八部·归来》7月25日公测开服后至2025年8月17日，角色达到40级以上的公会成员可前往大理（169，120）NPC龚彩云领取公会成员奖励。
                <br />有效数量排行和消费排行元宝奖励将在活动结束后15个工作日内发放至对应会长和成员账号中，发放后可前往龚彩云处领取。
<!-- <br /> -->
                <br /><strong>1、公会成员奖励</strong>
                <br />每名加入公会的账号，均可获得绑定称号：盟契山河（30天），所有属性+5。
<!-- <br /> -->
                <br /><strong>2、有效数量排行</strong>
                <br />7月25日至8月17日24:00期间，达到68级且解锁了高级成长密令的玩家为公会有效成员，根据公会有效成员数量排名发放元宝奖励。页面数据更新有延迟，延迟约1小时左右，获奖信息以官方公告为准。
                <br />会长可获得排名奖励中50%的元宝，其他有效成员可平分剩余50%元宝。
                <br />有效成员数量排名前20名，且满足最少有效成员数量，公会可获得总奖励元宝如下：
                <br />第1名公会，且有效成员数量最少80名，公会可获得60万元宝；
                <br />第2名公会，且有效成员数量最少60名，公会可获得40万元宝；
                <br />第3名公会，且有效成员数量最少40名，公会可获得30万元宝；
                <br />第4~5名公会，且有效成员数量最少30名，每公会可获得20万元宝；
                <br />第6~10名公会，且有效成员数量最少20名，每公会可获得15万元宝；
                <br />第11~20名公会，且有效成员数量最少15名，每公会可获得10万元宝；
                <br />如有效成员数量相同，则按创建公会时间排序。
                <br />如果排名对应公会的有效成员数量不满足最少有效成员数，则奖励元宝数量向下调整为满足最少成员数量档次元宝。
                <br />例如，如果排名第2名公会有效成员数量为31人，则奖励元宝数量为第4名对应20万元宝。
<!-- <br /> -->
                <br /><strong>3、消费排名</strong>
                <br />7月25日至8月17日24:00期间，根据公会有效成员消费元宝总额进行排名发放元宝奖励。页面数据更新有延迟，延迟约1小时左右，获奖信息以官方公告为准。
                <br />拥有至少3名有效成员的公会为有效公会，有效公会才能获得消费排名对应奖励。
                <br />会长可获得排名奖励中50%的元宝，其他有效成员可平分剩余50%元宝。
                <br />有效成员消费元宝总额排名前20名，且满足有效成员最少消费元宝数，公会可获得总奖励元宝如下：
                <br />第1名公会，且有效成员消费总额最少600万元宝，公会可获得60万元宝；
                <br />第2名公会，且有效成员消费总额最少400万元宝，公会可获得40万元宝；
                <br />第3名公会，且有效成员消费总额最少300万元宝，公会可获得30万元宝；
                <br />第4~5名公会，且有效成员消费总额最少200万元宝，每公会可获得20万元宝；
                <br />第6~10名公会，且有效成员消费总额最少150万元宝，每公会可获得15万元宝；
                <br />第11~20名公会，且有效成员消费总额最少100万元宝，每公会可获得10万元宝；
                <br />如有消费元宝数量相同，则按创建公会时间排序。
                <br />如果排名对应公会有效成员消费元宝总额不满足最少消费元宝数，则奖励元宝数量向下调整为满足最少消费元宝数档次元宝。
                <br />例如，如果排名第2公会有效成员消费总额为200万元宝，则奖励元宝数量为第4名对应的20万元宝。
<!-- <br /> -->
                <br /><strong>其他：</strong>
                <br />畅游公司有权在法律允许的范围内对本活动规则进行解释和调整。
            </p>
        </div>
        <!-- 创建公会 -->
        <div class="pop pop4" id="popCreate">
            <a href="javascript:;" class="btn btn-close closeAll">×</a>
            <ul class="ipt-list">
                <li>
                    <div class="ipt">
                        <i class="icon1"></i><p><span class="red">*</span>公会名称：</p><input type="text" maxlength="8" placeholder="创建的公会名称" id="name">
                    </div>
                </li>
                <li>
                    <div class="ipt">
                        <i class="icon5"></i><p><span class="red">*</span>成员昵称前缀：</p><input type="text" maxlength="4" placeholder="可用此前缀命名昵称" id="namePre" class="namePre">
                    </div>
                </li>
                <li>
                    <div class="ipt">
                        <i class="icon6"></i><p><span class="red">*</span>我的昵称：</p><input type="text" maxlength="8" placeholder="可用成员昵称前缀+其他命名" id="nickname">
                    </div>
                </li>
<!--                <li>-->
<!--                    <div class="ipt">-->
<!--                        <i class="icon7"></i><p>微信号：</p><input type="text" placeholder="选填，展示给成员方便联系" maxlength="20" minlength="6" id="wxAccount">-->
<!--                    </div>-->
<!--                </li>-->
                <li>
                    <div class="ipt">
                        <i class="icon8"></i><p>QQ群号：</p><input type="number" placeholder="选填，展示给成员方便联系" id="qqAccount">
                    </div>
                </li>
                <li>
                    <div class="ipt">
                        <i class="icon3"></i><p><span class="red">*</span>手机号：</p><input type="phone" id="phone">
                    </div>
                </li>
                <li>
                    <div class="ipt ipt2">
                        <i class="icon4"></i><p><span class="red">*</span>短信验证码：</p><input type="text" maxlength="6" id="vCode">
                    </div>
                    <a href="javascript:;" class="btn-getcode">获取验证码</a>
                </li>
            </ul>
            <div class="flex justify-center">
                <a href="javascript:;" class="btn btn-confirm">确认</a>
                <a href="javascript:;" class="btn btn-cancel">取消</a>
            </div>
        </div>
        <!-- 加入公会 -->
        <div class="pop pop3" id="popJoin">
            <a href="javascript:;" class="btn btn-close closeAll">×</a>
            <ul class="ipt-list ipt-list2">
                <li id="ghId">
                    <div class="ipt">
                        <i class="icon1"></i><p><span class="red">*</span>公会码：</p><input type="text">
                    </div>
                </li>
                <li>
                    <div class="ipt">
                        <i class="icon6"></i><p><span class="red">*</span>我的昵称：</p><input type="text" id="nickname2" maxlength="8">
                    </div>
                </li>
                <li class="radio-li">
<!--                    <div class="radio checked"></div>-->
                    <p style="font-size: 16px;color: #f2ebb5;"><span id="J_ghName">-</span>公会邀请您加入，您可以使用此公会成员昵称前缀<span class="J_namePre">-</span>
                        <!-- TODO:+其他命名自己的昵称 -->
                    </p></li>
            </ul>

            <div class="flex justify-center">
                <a href="javascript:;" class="btn btn-confirm">确认</a>
                <a href="javascript:;" class="btn btn-cancel">取消</a>
            </div>
        </div>
        <!-- 修改QQ -->
        <div class="pop pop1" id="modifyQQ">
            <div class="t tit1">修改公会QQ群</div>
            <a href="javascript:;" class="btn btn-close closeAll">×</a>

            <ul class="ipt-list" style="margin: 46px 0">
                <li>
                    <div class="ipt">
                        <i class="icon8"></i><p>QQ群号：</p><input type="number" placeholder="展示给成员方便联系" id="qqAccount2">
                    </div>
                </li>
            </ul>

            <div class="flex justify-center">
                <a href="javascript:;" class="btn btn-confirm">确认</a>
                <a href="javascript:;" class="btn btn-cancel">取消</a>
            </div>
        </div>
        <!-- 修改微信 -->
        <div class="pop pop1" id="modifyWX">
            <a href="javascript:;" class="btn btn-close closeAll">×</a>
            <div class="t tit2">修改微信号</div>
            <ul class="ipt-list" style="margin: 46px 0">
                <li>
                    <div class="ipt">
                        <i class="icon7"></i><p>微信号：</p><input type="text" placeholder="展示给成员方便联系" maxlength="20" minlength="6" id="wxAccount2">
                    </div>
                </li>
            </ul>

            <div class="flex justify-center">
                <a href="javascript:;" class="btn btn-confirm">确认</a>
                <a href="javascript:;" class="btn btn-cancel">取消</a>
            </div>
        </div>
        <!-- 分享引导 -->
        <div class="pop-share" onclick="$(this).hide();"></div>
        <!-- 复制链接 -->
        <div class="pop pop5" id="popCopy">
            <a href="javascript:;" class="btn btn-close closeAll">×</a>
            <div class="copy-tip">【<span class="J_ganghuiName"></span>】公会邀请你加入，一起瓜分百万元宝-《天龙八部·归来》</div>
            <div class="copy-text">
                <input type="text" readonly>
                <i></i></div>
            <a href="javascript:;" class="btn-copy2">复制链接</a>
        </div>
        <!-- 个人中心 -->
        <div class="pop pop6" id="popCenter">
            <a href="javascript:;" class="btn btn-close closeAll">×</a>
            <div class="pop-tit t">提示</div>
            <p class="record-text"><span class="J_cn">---</span>，您好<br><span class="record-txt"></span></p>
            <h3>获奖记录</h3>
            <ul class="gift-list-head">
                <li>
                    <div class="col1"></div>
                    <div class="col2"></div>
                </li>
            </ul>
            <ul id="giftRecord" class="gift-list">
                <!--				<li>-->
                <!--					<div class="col1">奖品类型</div>-->
                <!--					<div class="col2">奖品内容奖品内容奖品内容奖品内容奖品</div>-->
                <!--				</li>-->
            </ul>
        </div>
    </div>
    <script src="/act/all/cdn/jquery/3.4.1/jquery.min.js"></script>
    <script src="/v2/act/tlbbgl/ghzb/20250808/m/js/clipboard.min.js"></script>
    <script src="/v2/act/tlbbgl/ghzb/20250808/m/js/jquery.lay.js?1.7"></script>
    <script src="/v2/act/tlbbgl/ghzb/20250808/m/js/tools.js"></script>
    <script src="/v2/act/tlbbgl/ghzb/20250808/m/js/cdc_js_sdk_1.1.1.js"></script>
    <script src="/v2/act/tlbbgl/ghzb/20250808/m/js/tracker.js" data-project="gonghui"></script>
    <script>document.write('<script class="external" src="/v2/act/tlbbgl/ghzb/20250808/m/js/common.js?r='+ (new Date().getTime()) +'"><\/script>')</script>
    <script class="external" src="/act/all/cdn/vConsole/3.3.0/vconsole.min.js"></script>
    <script>
      // new VConsole();
    </script>
    <script>
      var loaderInit = function(config) {
        var a = {
          path: '/v2/act/tlbbgl/ghzb/20250808/m/img/',
          source: [],
          delay: 100,
          mode: 2,
          handle: function(count, len) {},
          complete: function() {}
        };
        for (var k in config)
          a[k] = config[k];
        var count = 0,
            len = a.source.length,
            array = [],
            intv,
            time = 0;
        for (var i = 0; i < len; i++) {
          array.push(new Image());
          array[i].loaded = false;
          array[i].onload = function() {
            this.loaded = true;
          };
          array[i].onerror = function() {
            this.loaded = true;
          };
          array[i].src = a.path + a.source[i];
        }
        intv = setInterval(function() {
          time += 20;
          for (var i = 0; i < array.length; i++) {
            if (array[i].loaded && time >= a.delay) {
              count++;
              array.splice(i, 1);
              a.handle(count, len);
              if (a.mode !== 1) {
                time = 0;
                break;
              }
            }
          }
          if (count === len) {
            clearInterval(intv);
            a.complete();
          }
        }, 20);
      };
      var imagePath = "/v2/act/tlbbgl/ghzb/20250808/m/img/";
      loaderInit({
        source: ['pop1.png','pop2.png','pop3.png','pop4.png','pop5.png','pop6.png','pop-share.png',
        ],
        handle: function(count, max) {
          // loadClick = false;
          // var per = Math.floor(count / max * 100) + '%';
          // $('.loader-prob i').width(per);
          // $('.loader-txt span').text(per);
        },
        complete: function() {
          // console.log('complete');
        }
      });
    </script>