// 查询微信是否绑定
function isWxBind() {
    console.log('查询微信是否绑定');
    if (ja.glob.isWechat) {
        ja.user.wxIsBind(function (res) {
            var isBind = false;
            $('#wxLogin .text').text('');
            if (res.code === 10000 && res.data) {
                $('#wxLogin .text').html(res.data + '<br>已绑定');
                $('#wxLogin a .btn').hide();
                isBind = true;
            } else {
                isBind = false;
            }
            $('#wxLogin').click(function () {
                if (isBind) {
                    ja.user.wxAutoLogin(function (res) {
                        if (res.code === 10000) {
                            ja.user.getUserInfo(function (res) {
                                if (res.code === 10000) {
                                    loginAfter();
                                    popHide()
                                }
                            })
                        } else {
                            alert(res.message);
                        }
                    })
                } else {
                    location.href = location.origin + '/xtl/wxBind/********/mobile/index.html'
                }
            })
        })
    }
}

$('.login_account').click(ja.login);
