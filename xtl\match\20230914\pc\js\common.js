// $(function () {
/**
 * 通用
 */
var common = {
	base_url: '/xtl/sectrank/',
	isLongmen: location.href.indexOf('longmen') !== -1,
	isPersonal: location.href.indexOf('single') !== -1,
	initParam: { // 初始化参数
		app: 'xtl',
		activity: 'sectrank',
		version: '20230908',
		platform: 'wgchangyou'
	},
	errorMsg: { // 通用的错误码提示文案
		1102: '活动无效',
		1106: '活动未开始',
		1103: '活动已结束'
	},
	levelLow: false,
	/** 默认头像 */
	defaultAvatar: '//i0.cy.com/tlpt/default.png',
	/**	 门派对应关系 */
	sectsList: {
		"6": "天龙",
		"1": "明教",
		"2": "丐帮",
		"3": "武当",
		"4": "峨眉",
		"7": "天山",
		"0": "少林",
		"8": "逍遥",
		"5": "星宿",
		"10": "慕容",
		"11": "唐门",
		"12": "鬼谷",
		"13": "桃花岛",
		"14": "绝情谷"
	},
	/** 是否绑定角色 */
	isBindRole: false,
	init: function () {
		/* 初始化  config配置项详见 表 3.1 */
		ja.config({
			app: this.initParam.app, // 必填，应用标识
			activity: this.initParam.activity, // 必填，活动标识
			version: this.initParam.version + (common.isLongmen ? '01' : ''), // 必填，活动版本标识
			platform: this.initParam.platform, // 必填，登陆平台标识
			isWxLogin: true,
			isWxStatus: true,
			isAutoWxLogin: false
		});

		// logDebug依赖阿里云错误日志上报js
		$.ajaxSetup({
			async: false,
			error: function (jqXHR) {
				if (this.url.indexOf(location.hostname) != -1) {
					alert('系统异常，请稍后再试！');
					logDebug.error({
						activity: common.initParam.activity,
						message: [
							'title：' + '全局ajax请求Error处理',
							'Request URL：' + this.url,
							'Request Method：' + this.type,
							'header：' + JSON.stringify(this.headers),
							'Status Code：' + jqXHR.status + jqXHR.statusText,
							'data：' + this.data
						].join('\n')
					})
				}
			}
		})
		window.onerror = function (message, source, lineno, colno, error) {
			logDebug.error({
				activity: common.initParam.activity,
				message: [
					'title：' + 'window.onerror',
					'errorMessage：' + message + '----in：' + source + '：' + lineno + '：' + colno
				].join('\n')
			})
		}

		/*  初始化完成后执行函数 返回全局对象 glob,详见 表 3.2 */
		ja.ready(function (glob) {
			personal.ready(glob.isLogin);

			glob.isLogin ? common.loginAfter() : common.loginBefore();
			glob.isLogin ? personal.loginAfter() : personal.loginBefore();
		});

		/* 龙门标志跟提示 */
		if (common.isLongmen) {
			ja.longmen({
				top: '50%',
				left: '0',
			})

			alert('此活动为龙门测试版本，奖品均发放到龙门服')
		};

		// 登录按钮
		$('#login').on('click', function () {
			common.commHandle()
		})

		// 二选一登录 畅游平台登录
		$('#cyLogin').on('click', function () {
			hideMask()
			ja.login({ w2c: true })
		})

		// 二选一登录 wegame平台登录
		$('#wgLogin').on('click', function () {
			ja.login()
		})

		// // 二选一登录 微信登录
		// $('#wxLogin').click(function () {
		//   if (ja.wxBindName) {
		//     ja.wxAutoLogin();
		//   } else {
		//     location.href = ja.urlWxBind;
		//   }
		// })

		// 注销
		$('#logout').on('click', function () {
			ja.logout()
		})

		// 个人赛页面事件初始化
		common.isPersonal && personal.init();

		//#region 绑定角色
		// 角色选择框
		$('#SelectRole').on('change', function () {
			$('.pop5-sect-name').empty();

			if (this.value) {
				var val = JSON.parse(this.value);
				common.getRolesLeague(val.roleId, val.groupNo, $(this));
			}
		})

		// 绑定按钮
		$('#BtnBindRole').on('click', function () {
			common.bindRole($('#SelectRole').val());
		})
		//#endregion
	},
	/** 登录后 */
	loginAfter: function () {
		$('.login_box').hide()
		$('.user_name').text(ja.userInfo.nickname || ja.userInfo.openid) // wgchangyou二选一
		$('.logout_box').show()

		common.userInit();
	},
	/** 登录前 */
	loginBefore: function () {
		if (ja.isWechat) {
			// 点击过授权的就直接拉起登录弹窗
			if (sessionStorage.getItem(common.initParam.activity + common.initParam.version + 'wLogin')) {
				ja.isWLogin && popup('#popLogin')
				sessionStorage.removeItem(common.initParam.activity + common.initParam.version + 'wLogin')
			}
			// if (ja.wxBindName) {
			//   $('#account').text(ja.wxBindName)
			// }
		}
	},
	/**
	* 通用错误码处理
	* @param {*} data 返回的对象
	*/
	commonErrPop: function (data) {
		var flag = true;
		switch (parseInt(data.code)) {
			case 1202: case 1209: case 1207: case 1007: //1202请求参数缺失或者为空  1209操作频繁
				common.tips('系统繁忙，请稍后重试！');
				break;
			case 1019: case 1012:
				ja.isLogin = false; // 重置下ja的登录状态标识以触发登录操作
				$('#login').click();
				break;
			case 1102:
				ja.code = 1102;
				common.tips(common.errorMsg[1102]);
				break;
			case 1106:
				ja.code = 1106;
				common.tips(common.errorMsg[1106]);
				break;
			case 1103:
				ja.code = 1103;
				common.tips(common.errorMsg[1103]);
				break;
			case 3103:
				common.tips('账号已封停！');
				break;
			case 2131:
				common.tips('由于账号中的角色<50级，无法参与活动，请先升级再参与活动');
				common.levelLow = true;
				break;
			case 0:
				common.tips('系统异常，请刷新后重试~');
				break;
			default:
				flag = false;
				break;
		}
		return flag;
	},
	/**
	* 通用的前置处理方法
	*/
	commHandle: function () {
		var flag = true;
		if (ja.code == 1102) {
			common.tips(common.errorMsg[1102]);
		} else if (ja.code == 1106) {
			common.tips(common.errorMsg[1106]);
		} else if (ja.code == 1103) {
			common.tips(common.errorMsg[1103]);
		} else if (!ja.isLogin) {
			// 微信绑定登录，需要先判断是否拉取过授权
			if (ja.isWechat) {
				if (!ja.isWLogin) { // 未授权
					ja.login('wechat'); // 拉起授权
					sessionStorage.setItem(common.initParam.activity + common.initParam.version + 'wLogin', '1');
				} else {
					popup('#popLogin') // 弹出二选一登录弹窗
				}
			} else {
				popup('#popLogin') // 弹出二选一登录弹窗
			}
		} else if (common.levelLow) {
			common.tips('由于账号中的角色<30级，无法参与活动，请先升级再参与活动');
		} else if (!common.isBindRole) {
			common.getRolesList()
		} else {
			flag = false;
		}
		return flag;
	},
	/**
	 * 通用提示弹窗
	 * @param {*} msg 内容
	 * @param {*} popPrev 是否弹出上一个弹窗
	 */
	tips: function (msg, popPrev) {
		$('#tips .pop-text').html(msg);
		popup('#tips', popPrev)
	},
	/**
	* 单向绑定 方便数据驱动视图的更新
	* @description 监听属性修改 set修改 get读取
	* @param {String} attr 属性名
	* @param {object} set 设置数据时触发的操作
	* @param {object} get 获取数据时触发的操作
	* @example
	* new example.dataObserver('param', function (val) {
	*	$('element').text(val);
	* })
	*/
	dataObserver: function (attr, set, get) {
		var _value = common[attr];
		Object.defineProperty(common, attr, {
			get: function () {
				get && get(_value);
				return _value;
			},
			set: function (val) {
				set && set(val);
				_value = val;
			}
		})
		common[attr] = _value;
	},

	/**
	 * 分页初始化
	 * @param {*} eleBox 需要被分页的容器
	 * @param {*} size  每页的条数
	 * @example new InitPagination('.box', 3)
	 */
	initPagination: function (eleBox, size) {
		var box = $(eleBox),
			children = box.children(),
			total = children.length,
			pageBox = box.next(),
			pageNum = pageBox.find('.page-num'),
			maxNum = !Math.ceil(total / size) ? 1 : Math.ceil(total / size);

		pageNum.text('1/' + maxNum);
		children.hide();
		children.slice(0, size).show();

		pageBox.off().on('click', '.prev, .next', function (e) {
			var nowNum = parseInt(pageNum.text().split('/')[0]);

			if ($(this).hasClass('prev')) {
				nowNum--;
				if (nowNum < 1) {
					nowNum = 1
					return;
				}
			} else {
				nowNum++;
				if (nowNum > maxNum) {
					nowNum = maxNum
					return;
				}
			}

			children.hide();
			children.slice(size * (nowNum - 1), nowNum * size).show();
			pageNum.text(nowNum + '/' + maxNum);
		})
	},
	/**
	* 用户信息初始化
	*/
	userInit: function () {
		$.ajax({
			type: 'POST',
			url: common.base_url + 'init',
			async: false,
			success: function (res) {
				// 前置过滤器 处理通用错误码
				if (common.commonErrPop(res)) return;

				if (res.code == 10000) {

					common.isBindRole = res.data.isBind && res.data.roleOcc;
					!common.isBindRole && common.getRolesList();

				} else {
					switch (parseInt(res.code)) {
						// case 5111:
						// 	common.tips('创建队伍失败，请刷新页面后重试');
						// 	break;
						default:
							common.tips(res.message);
							break;
					}
				}
			}
		})
	},
	/**
	 * 获取角色列表
	 */
	getRolesList: function () {
		$.ajax({
			type: 'GET',
			url: common.base_url + 'getRoles',
			async: false,
			success: function (res) {
				// 前置过滤器 处理通用错误码
				if (common.commonErrPop(res)) return;

				if (res.code == 10000) {

					if (res.data) {
						var ops = `<option value="">请选择角色</option>`;
						$.each(res.data, function (i, v) {
							ops += `<option value='${JSON.stringify(v)}'>${v.roleName + '_' + v.serverName}</option>`
						})
						$('#SelectRole').html(ops);
						popup('#pop5');
					} else {
						common.tips('账号下没有符合要求的角色~')
					}

				} else {
					switch (parseInt(res.code)) {
						// case 5111:
						// 	common.tips('创建队伍失败，请刷新页面后重试');
						// 	break;
						default:
							common.tips(res.message);
							break;
					}
				}
			}
		})
	},
	/**
	 * 获取角色的帮派信息
	 */
	getRolesLeague: function (roleId, groupNo, $ele) {
		$.ajax({
			type: 'GET',
			url: common.base_url + 'getSect',
			async: false,
			data: {
				"roleId": roleId,
				"groupNo": groupNo
			},
			success: function (res) {
				// 前置过滤器 处理通用错误码
				if (common.commonErrPop(res)) return;

				if (res.code == 10000) {
					if (res.data) {
						var newVal = JSON.stringify($.extend(JSON.parse($ele.find(':selected').val()), res.data))
						$ele.find(':selected').val(newVal);
						$('.pop5-sect-name').text(res.data.sectName);
					} else {
						$('.pop5-sect-name').text('尚未加入帮会');
					}

				} else {
					switch (parseInt(res.code)) {
						// case 5111:
						// 	common.tips('创建队伍失败，请刷新页面后重试');
						// 	break;
						default:
							common.tips(res.message);
							break;
					}
				}
			}
		})
	},
	/**
	 * 绑定角色
	 * @param {*} obj
	 */
	bindRole: function (obj) {
		$.ajax({
			type: 'POST',
			url: common.base_url + 'bindRole',
			async: false,
			data: obj,
			contentType: 'application/json;charset=utf-8',
			success: function (res) {
				// 前置过滤器 处理通用错误码
				if (common.commonErrPop(res)) return;

				if (res.code == 10000) {
					// 更新用户信息
					common.userInit();
					common.tips('绑定成功!');
				} else {
					switch (parseInt(res.code)) {
						case 5021:
							common.tips('您已绑定过角色了~');
							common.userInit();
							break;
						default:
							common.tips(res.message);
							break;
					}
				}
			}
		})
	},
}


common.init()
// })
