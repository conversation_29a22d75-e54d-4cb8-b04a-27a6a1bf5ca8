$.fn.QrCode = function (txt) {
    return this.each(function () {
        const el = this;
        $(el).empty()
        new QRCode(el, {
            text: txt,
            correctLevel: 3,
            width: 300,
            height: 300
        });
    })
};


$('.J_qr_box').QrCode(location.href)


~(function () {
    function App() {
        const that = this
        let mock = false
        let mockUserInfo = false
        let mockToken = false
        let mockTokens = [
            'a9c7ePovYDughB1VMgJgzXcd2bt0lfAckyDxDv4KQcaFXkUHsbHIPVrb2JtxdwqQCtrCD3vgb_Fj6_J1KzHbWBUFhQ6DL7Yp-7JduIUxHzBlaGPdYpdMVmaHrdV-3So_e8cyaM7KW2xUDZLLmlqU1w34C_sF5V-Wb_OF79hAJ9ezIkDLVp2gICOkEObA2Q',
            '10b790xDzGC3de7q6JFCwZ38fuMbchjdRF6untQPqcd21LwQYORoxZfybjUZDyW0wBnzOk2gIoG_BX-JVqR1sax-IZ0bmWyjkd0q2GMJHlKBLYeoDQhuApbSmLWdORjq039xNerujfqtDePsE7Tf2BXZZH99cIPLL2gsYwUR9Xw_vye5xpL5er6gUPMkZA'
        ]
        let mockVerifyToken = mockTokens[0]

        this.data = {
            scenceIndex: 0,
            start_time: '2025年7月25日', // 新服开启时间
            userId: null, // 登录信息，可用于判断是否为空
            cn: ""
        }

        this.ajax = function (l, d, opt) {
            let loading
            return new Promise((resolve, reject) => {
                const env = 3  // 1-内网IP接口  2-内部服务器接口  3-畅游测试接口  4-畅游正式接口
                const domainMap = {
                    1: 'http://localhost:8883',
                    2: 'http://hbcloud.top:8388',
                    3: 'https://api-tlgl-zhjd.changyou.com',
                    4: 'https://api-tlbbglact.changyou.com'
                }
                const domain = domainMap[env]
                const activeName = 'zhjd'
                const path = `/changyou/${activeName}/api/`
                const url = domain + path + l

                // console.log('api', l);
                // console.log('api url', url);
                if (opt && opt.loading) loading = lay.load()
                const data = d || {}
                const ajaxOptions = {
                    url: opt?.sp ? l : url,
                    type: opt?.method ? opt.method : 'POST',
                    data: data,
                    dataType: 'json',
                    headers: {
                        // 'Content-Type': 'application/json;charset=UTF-8'
                    },
                    success: function (res) {
                        if (opt && opt.loading) lay.close(loading)
                        resolve(res)
                    },
                    error: function (err) {
                        if (opt && opt.loading) lay.close(loading)
                        console.log('post err res', err);
                        lay.msg('接口请求繁忙，请稍后重试')
                        reject(err)
                    }
                }
                let verifyToken = $.tools.getStorage('token_zhjd')
                if (l !== 'changyouLogin' && verifyToken) {
                    if (mockToken) verifyToken = mockVerifyToken
                    ajaxOptions.headers = {
                        // 'Content-Type': 'application/json;charset=UTF-8',
                        'Authorization': verifyToken
                    }
                }
                $.ajax(ajaxOptions)
            })
        }

        this.loginPop = function () {
            if ($('.page1 .btn-login').hasClass('timeover')) return that.alert('当前不在活动时间内')
            const src = 'https://auth.changyou.com/interfaceLogin?project=tlglactivity&s=' + encodeURIComponent(window.location.href);
            // console.log('登录组件链接：', src)
            $.tools.setStorage('URL_BEFORE_LOGIN', window.location.href)
            $('#popLogin .login-box').html(`<iframe src="${src}" frameborder="0" width="100%" height="100%" scrolling="no"></iframe>`);
            lay.open('popLogin')
        }

        this.login = function () {
            const ticket = $.tools.getStorage('ticket')
            if (!ticket) return Promise.resolve({code: -1, msg: '没有ticket'})
            // 登录
            const urlBeforeLogin = $.tools.getStorage('URL_BEFORE_LOGIN')
            if (!urlBeforeLogin) return Promise.resolve({code: -1, msg: '没有传登录前的url'})
            const s_url = encodeURIComponent(urlBeforeLogin)
            const sid = $.tools.getParam('sid')
            const sign = $.tools.getParam('sign')
            const data = {ticket, s_url}
            if (sid) data.sid = sid
            if (sign) data.sign = sign
            const CYSCID = $.tools.getParam('type')
            if (CYSCID) data.CYSCID = CYSCID
            return new Promise(resolve => {
                that.ajax('changyouLogin', data)
                    .then(res => {
                        $.tools.removeStorage('ticket')
                        if (mock) res = {
                            "code": 0,
                            "msg": "success",
                            "data": {
                                "userid": "078661e77fcc5ec4e13af24b2135d333",
                                "verifyToken": "773cH5N6ftVo2vklF2xX-ih1QuQAxaSz3DwWSyZyKDlDtj3dgyef6znaNtjEu2TKJ24ZXmvLX31LIvAeS-XYc3iVAiDpR-Xgk2oKV8dhlsw30cRaKys3NT17p0AMRm3mVMjc7gXQpZ72Eoju9-x1F8ZVq8okOqJA96akEB73Swfw5g4F2w-1K3T6JXwVziDqBg",
                                "expire": 1648494989,
                                "role_name": "摩登兄弟最帅",
                            }
                        }

                        if (res.code === -20003) {
                            lay.close(that.loading_index)
                            that.alert('您的账号已停权', that.logout)
                            return;
                        }

                        if (res.code === -90001 || res.code === -90002) {
                            lay.close(that.loading_index)
                            that.alert('当前不在活动时间内')
                            $('.page1 .btn_start').addClass('timeover')
                            return;
                        }

                        if (res.code === 0) {
                            // 拿到了 verifyToken 信息，存到 localStorage
                            $.tools.setStorage('token_zhjd', res.data.token)
                        } else {
                            logoutAuto()
                        }

                        resolve(res)
                    })
                    .catch(res => {
                        if (mock) {
                            res = {
                                "code": 0,
                                "msg": "success",
                                "data": {
                                    "userid": "078661e77fcc5ec4e13af24b2135d333",
                                    "verifyToken": "773cH5N6ftVo2vklF2xX-ih1QuQAxaSz3DwWSyZyKDlDtj3dgyef6znaNtjEu2TKJ24ZXmvLX31LIvAeS-XYc3iVAiDpR-Xgk2oKV8dhlsw30cRaKys3NT17p0AMRm3mVMjc7gXQpZ72Eoju9-x1F8ZVq8okOqJA96akEB73Swfw5g4F2w-1K3T6JXwVziDqBg",
                                    "expire": 1648494989,
                                    "role_name": "摩登兄弟最帅",
                                }
                            }

                            resolve(res)
                        }
                    })
            })
        }

        function logoutAuto() {
            // that.alert("登录失效,请重新登录！", that.logout)
            // that.logout()
            $.tools.removeStorage('token_zhjd')
            location.reload()
        }

        this.checkLogin = function () {
            try {
                let verifyToken = $.tools.getStorage('token_zhjd');
                if (!verifyToken) return Promise.resolve({code: -2, msg: '没有verifyToken'})

                function mockData() {
                    return {
                        "code": 0,
                        "msg": "success",
                        "data": {
                            "userId": "8941c3807876ef1ff0d4b06735eec6a5",
                            "expire": 1743132501,
                            "cn": "qs***<EMAIL>",
                            "nickname": "我是第一",//昵称
                            "headNo": 3,//头像
                            "level": 0,
                            "isLoginGame": 1,
                            "isLeader": 0,//1创建小队，队长
                            "memberNum": 0,//队伍数
                            "taskCompleteNum": 0,
                            "leaderUin": "896a628ea29e2043a9ced1624ff6f418",//是否加入小队
                            "menpaiNo": 0,//门派id
                            "token": "a9c7ePovYDughB1VMgJgzXcd2bt0lfAckyDxDv4KQcaFXkUHsbHIPVrb2JtxdwqQCtrCD3vgb_Fj6_J1KzHbWBUFhQ6DL7Yp-7JduIUxHzBlaGPdYpdMVmaHrdV-3So_e8cyaM7KW2xUDZLLmlqU1w34C_sF5V-Wb_OF79hAJ9ezIkDLVp2gICOkEObA2Q",
                            "menpaiCount": [//门派数据，如果门派无人选择加入，则不会出现在数组中
                                {
                                    "count": 18,
                                    "menpaiNo": 1//门派id
                                },
                                {
                                    "count": 20,
                                    "menpaiNo": 2
                                },
                                {
                                    "count": 42,
                                    "menpaiNo": 3
                                },
                                {
                                    "count": 9,
                                    "menpaiNo": 4
                                }
                            ],
                            "teamData": [//队伍列表
                                {
                                    "userId": "078661e77fcc5ec4e13af24b2135d333",
                                    "nickname": null,
                                    "headNo": 6,
                                    "isLeader": 0
                                },
                                {
                                    "userId": "8941c3807876ef1ff0d4b06735eec6a5",
                                    "nickname": "我是第一",
                                    "headNo": 3,
                                    "isLeader": 0
                                },
                                {
                                    "userId": "896a628ea29e2043a9ced1624ff6f418",
                                    "nickname": null,
                                    "headNo": 1,
                                    "isLeader": 1//是否队长：1是
                                }
                            ]
                        }
                    }
                }

                function handler(res) {

                    if (res.code === -20003) {
                        lay.close(that.loading_index)
                        that.alert('您的账号已停权', that.logout)
                        return;
                    }

                    if (res.code === -90001 || res.code === -90002) {
                        that.data.activity_status = res.code
                        lay.close(that.loading_index)
                        that.alert('当前不在活动时间内')
                        $('.page1 .btn_start').addClass('timeover')
                        return;
                    }

                    if (res.code === 0) {
                        // 登录成功
                        const user_info = res.data
                        $.tools.setStorage('token', res.data.token)
                        that.data.user_info = user_info
                    } else {
                        logoutAuto()
                    }
                }

                return new Promise(resolve => {
                    that.ajax('index').then(res => {
                        if (mockUserInfo) res = mockData()
                        handler(res)
                        resolve(res)
                    }).catch(res => {
                        console.log('checkLogin catch---:', res);
                        if (mockUserInfo) res = mockData()
                        console.log('checkLogin catch mock res：', res);
                        if (res.responseJSON) res = res.responseJSON
                        handler(res)
                        resolve(res)
                    })
                })
            } catch (error) {
                return Promise.resolve({code: -1, msg: '登录超时', error})
            }
        }

        this.init = function () {
            console.log('init');


            setWxShare()

            document.addEventListener('touchstart', function (event) {
                if (event.touches.length > 1) {
                    event.preventDefault();
                }
            }, {passive: false});

            document.addEventListener('touchmove', function (event) {
                if (event.touches.length > 1) {
                    event.preventDefault();
                }
            }, {passive: false});

            tracker.pv_uv()

            $('.start_time').text(this.data.start_time)

            $('.btn-logout').hide()
            const ticket = $.tools.getParam('ticket')
            if (ticket) {
                $.tools.setStorage('ticket', ticket)
                const href = $.tools.removeParam(location.href, 'ticket')
                return window.location.href = $.tools.removeParam(href, 'state')
            }

            //默认打开页面
            open('page1');
            this.bind()

            setTimeout(() => {
                that.loading_index = lay.load()

                this.checkLogin().then(res => {
                    if (res.code === -20003) {
                        lay.close(that.loading_index)
                        that.alert('您的账号已停权', that.logout)
                        return;
                    }
                    if (res.code === 500) {
                        lay.close(that.loading_index)
                        that.alert('当前不在活动时间内')
                        $('.page1 .btn-login').addClass('timeover')
                        return;
                    }
                    if (res.code !== 0) {
                        lay.close(that.loading_index)
                        this.login().then(res => {
                            if (res.code !== 0) {
                                lay.close(that.loading_index)
                            } else {
                                that.refreshUserInfo()
                            }
                        }).catch(e => {
                            console.log(e);
                        }).finally(() => {
                            lay.close(that.loading_index)
                        })
                    }
                    if (res.code === 0) {
                        handlerLoginSuccess()
                    }

                }).catch(e => {
                    lay.close(that.loading_index)
                })
            }, 300)
        }

        this.refreshUserInfo = function () {
            this.checkLogin().then(res => {
                if (res.code === 0) {
                    handlerLoginSuccess()
                }
            })
        }

        // 渲染页面
        function renderPage() {
        }

        function handlerLoginSuccess() {
            lay.close(that.loading_index)
            $('.login-info').show()
            $('.btn-logout').show()
            // 判断是否已入队，判断是否为队长
            const {cn} = that.data.user_info
            $('.J_username').text(cn)
            $('#unlogin').hide()
            $('#login').show()
            $('.btn_agree').addClass('checked')

            open('page2')

            $('.J_qr_box').QrCode(getShareLink())

            setWxShare()
            renderPage()
        }

        function initSwiper() {
            if (that.swiper) return
            that.swiper = new Swiper('.swiper-container', {
                threshold: 150 * ($(window).height() / 1334),
                clickable: true,
                direction: 'vertical',
                noSwiping: true,
                noSwipingClass: 'no-swiping',
                // effect: 'fade'，
                on: {
                    slideChange: function () {
                        if (this.activeIndex === 3) {
                            this.allowSlideNext = false
                        } else {
                            this.allowSlideNext = true
                        }
                    },
                    tap: function () {
                        if (this.activeIndex < 3) {
                            this.slideNext()
                        }
                    }
                }
            })
        }

        // 跳转到指定模块
        function open(page) {
            $('.page').hide();
            $('#' + page).show();
            if (page === 'page2') {
                initSwiper()
            }
        }

        this.open = open

        function copyMyLink() {
            lay.closeAll()
            if (clipBordCopy('你的天龙账号值钱吗？点击测试，还有账号专属称号-《天龙八部·归来》！' + getShareLink())) {
                lay.msg('复制成功')
            } else {
                lay.msg("复制失败");
            }
        }

        this.jianding = function () {
            const {userId, sign} = that.data.user_info
            const load = lay.load(1, {content: '鉴定中...'})
            that.ajax('identify', {}).then(res => {
                lay.close(load)
                if ([-1, -90003].includes(res.code)) return logoutAuto()
                if (res.code !== 0) return that.alert(res.msg)
                if (res.code === 0) {
                    that.swiper.allowSlideNext = true
                    that.swiper.slideTo(4)
                    renderResult(res.data)
                }
            })
        }

        function renderResult(data) {
            const identifyPrizeMap = {
                1: ['称号：潜龙在渊（90天），血上限+2000', '金蚕丝*6'],
                2: ['称号：青云独步（90天），血上限+2200', '金蚕丝*8'],
                3: ['称号：中流砥柱（90天），血上限+2400', '金蚕丝*10'],
                4: ['称号：江湖名士（90天），血上限+2600', '金蚕丝*12'],
                5: ['称号：天龙传奇（90天），血上限+3000', '金蚕丝*15'],
            }
            $('.regedit_time').text("【" + data.regDate + "】");
            $('.regedit_days').text("【" + data.regDays + "天】");
            $('.account_step').text("【" + data.regYears + "】");
            $('.step_name').text("【" + data.identifyTitle + "】");
            $('.regedit_seasons').text("【" + data.regSeasons + "】");
            $('.beyond_percent').text("【" + data.percentage + "】");
            $('.step_prize_name').text("【" + identifyPrizeMap[data.identifyLeavl][0] + "】");
            $('.regedit_gift').text("【" + identifyPrizeMap[data.identifyLeavl][1] + "】");
            $('.result').removeClass('result1 result2 result3 result4 result5');
            $('.result').addClass('result' + data.identifyLeavl);
        }

        function isLogin() {
            return that.data.user_info
        }

        // 获得分享链接
        function getShareLink() {
            let link = location.href
            if (that.data.user_info?.userId) {
                const {userId, sign} = that.data.user_info;
                link = $.tools.replaceParam(link, 'sid', userId)
                link = $.tools.replaceParam(link, 'sign', sign)
            }
            return link
        }

        // 获得微信分享参数
        function getWxShareData() {
            // ja方式
            return {
                isWxDebug: false,
                title: '你的天龙账号值多少钱？',
                desc: '击测试，领专属称号和金蚕丝！十几年天龙老玩家证明自己的时候到了！',
                link: getShareLink(),
                imgUrl: 'https://' + location.host + '/act/tlbbgl/zhjd/20250508/m/img/share.png',
                success: function () {
                    // alert('分享成功')
                }
            }
        }

        // 设置微信分享参数
        function setWxShare() {
            if ($.tools.browser.versions.weixin) {
                $.wechat.share(getWxShareData())
            }
        }

        //集结小队战友
        this.share = function () {
            tracker.click_share()
            // 预约集结，点击预约集结按钮，如果在微信内，则引导玩家进行分享，微信内分享后显示小卡片；如果在非微信内，则弹出分享链接提示
            if ($.tools.browser.versions.weixin) {
                setWxShare()
                lay.open('shareWX')
            } else {
                $('.ipt-copy').val(getShareLink())
                lay.open('share');
            }
        }

        function createPoster() {
            tracker.click_share_pic()
            var $poster = $('#poster')
            var width = $poster.outerWidth();
            var height = $poster.outerHeight();
            const load = lay.load()
            html2canvas($poster.get(0), {
                useCORS: true,
                allowTaint: false,
                backgroundColor: null,
                width: width,
                height: height,
                y: $poster.offset().top,
                scrollY: 0,
                scrollX: 0,
            }).then(function (canvas) {
                const url = canvas.toDataURL("image/png");
                $('#poster_img').attr('src', url)
                lay.close(load)
                lay.open('popPoster')

            }).catch((e) => {
                console.log('海报生成失败')
            })
        }

        this.logout = function () {
            $.tools.removeStorage('token_zhjd')
            const b = encodeURIComponent(location.href)
            const url = `https://auth.changyou.com/logout?b=${b} `
            location.href = url
        }


        function isVivoBrowser() {
            var userAgent = navigator.userAgent;
            return userAgent.indexOf('VivoBrowser') > -1
        }

        function isiOSNativeSafari() {
            var userAgent = navigator.userAgent;
            // 检查是否为iOS设备
            var isiOS = /iPhone|iPad|iPod|Mac OS X/.test(userAgent);
            // 排除特定的应用或浏览器
            var excludedBrowsers = [
                /MicroMessenger/i, // 微信
                /QQ/i,             // QQ或QQ浏览器
                /MQQBrowser/i,     // QQ浏览器
                /UCBrowser/i,      // UC浏览器
                /CriOS/i,          // Chrome for iOS
                /FxiOS/i,          // Firefox for iOS
                /Quark/i,          // 夸克浏览器
                /Wework/i,         // 企业微信
                /Lark/i            // 飞书
            ];
            // 检查是否在排除列表中的任何一个应用或浏览器中
            var isInExcludedBrowser = excludedBrowsers.some(function (regex) {
                return regex.test(userAgent);
            });
            // 如果是iOS设备，并且不在排除列表中的任何一个应用或浏览器中，则返回true
            return (isiOS && !isInExcludedBrowser);
        }

        this.bind = function () {
            // 系统类型判断
            ~(function () {
                $('body')[isVivoBrowser() ? 'addClass' : 'removeClass']('browser-vivo')
                $('body')[isiOSNativeSafari() ? 'addClass' : 'removeClass']('browser-safari')
            }());

            $('.btn_rule, .btn_rule_link').click(function (e) {
                e.stopPropagation()
                lay.open('rule')
            })

            $('.btn_agree').click(function () {
                $(this).toggleClass('checked')
            })

            $('.btn_share_pic').click($.tools.throttle(createPoster, 1000, true))

            $('.btn_start').click(function () {
                if ($('.page1 .btn_start').hasClass('timeover')) return that.alert('当前不在活动时间内')

                if (!$('.btn_agree').hasClass('checked')) return lay.msg('请您认真阅读并勾选同意活动规则后再参与活动')

                // 如果未登录，则先登录
                if (!isLogin()) return that.loginPop()

                open('page2')
            })

            $('.btn_next').click(function () {
                that.data.scenceIndex++
                const {scenceIndex} = that.data
                if (scenceIndex <= 3) {
                    $('.scence_box').eq(scenceIndex).addClass('show').siblings().removeClass('show')
                }
            })

            // 通用弹窗里的点击关闭按钮
            $('#common .btn-confirm').click(function () {
                lay.close('common')
            })

            // 退出登录
            $('.btn-logout').click(function () {
                that.logout()
                // location.reload()
            })

            //复制我的邀请链接
            $('.pop-share input').click(function () {
                copyMyLink()
            })

            //分享（微信内关闭
            $('.pop-shareWX').on('click', function () {
                lay.closeAll()
            });

            $('.btn-rule').click(function () {
                lay.open('rule')
            })

            // 返回首页
            $('.btn-close').click(function () {
                const popId = $(this).parents('.pop').attr('id')
                lay.close(popId)
            })
        }

        this.alert = function (msg, cbConfirm, cbCancel) {
            lay.open('common')
            $('#common .txt-box').html(msg)
            if (cbCancel && cbConfirm) {
                $('#common .btn-confirm').one('click', cbConfirm)
                $('#common .btn-close').one('click', cbCancel)
            } else if (cbConfirm) {
                $('#common .btn-confirm, #common .btn-close').one('click', cbConfirm)
            }
        }
        this.open = open

        const versions = $.tools.browser.versions
        if (versions.android || versions.ios) {
            $(window).on('load orientationchange', handlerScreen);
        } else {
            $(window).on('load resize', handlerScreen)
        }

        function handlerScreen() {
            setTimeout(() => {
                const contbox = $('.contbox')
                const ww = $(window).width()
                const wh = $(window).height()
                const condition = ww / wh >= 375 / 667
                if (condition) {
                    const {versions} = $.tools.browser
                    console.log('versions.ios || versions.android', versions.ios || versions.android)
                    const contHeight = 1650
                    if (versions.ios || versions.android) {
                        contbox.css({'height': contHeight})
                    }
                    let scale0 = (wh / contHeight) > 1 ? 1 : wh / contHeight
                    let scale = 1
                    if (!((versions.ios || versions.android) && ww / wh < 1)) {
                        scale = scale0 > 1 ? 1 : scale0
                    }
                    const scale1 = wh / 1334
                    const scaleCss = {
                        'transform': ' translateY(-50%) scale(' + scale + ')',
                    }
                    if ((versions.ios || versions.android)) {
                        if (wh <= 1334) {
                            scaleCss.height = contHeight
                            const contH = ww / wh > 1 ? contHeight : (wh > 1100 ? 1100 : wh)
                            console.log('contH', contH);
                            $('.start_box_page').height(contH)
                        } else {
                            scaleCss.height = 1334
                            $('.start_box_page').height(1334)
                        }
                    } else {
                        scaleCss.height = contHeight
                        $('.start_box_page').height(contHeight)
                    }
                    const scaleCss2 = {
                        'transform': 'scale(' + scale1 + ')'
                    }
                    contbox.css(scaleCss);
                    $('.scence_box, .page2 .result, .pop').css(scaleCss2)

                    // if(isiOSNativeSafari() && (ww / wh > 375/500)) {
                    //     $('.start_box').css({'margin-bottom': '90px'})
                    // }
                } else {
                    const scale = 1
                    const scaleCss = {
                        'transform': ' translateY(-50%) scale(' + scale + ')'
                    }
                    const scaleCss2 = {
                        'transform': 'scale(' + scale + ')'
                    }
                    contbox.css(scaleCss);
                    $('.scence_box, .page2 .result, .pop').css(scaleCss2)
                    $('.start_box_page').height(1334)
                }
            }, 550)
        }
    }

    window.app = new App()
    app.init()
}());