// config 对象在web-cofnig.js文件中

var GG = {
		timeCountMax: 60,
		apis: {
				roleList: 'roleList',
				lotNum: 'lotNum',
				personCenter: 'personCenter',
				saveBindRole: 'saveBindRole',
				save: 'save',
				vote: 'vote',
				lot: 'lot',
				share: 'share',
				list: 'list',  // 参赛列表，
				cumulateVotes: 'cumulateVotes', // 总的投喂次数
		},

		ajax: function (o, cb) {
				var d = {
						method: 'GET',
						dataType: 'json',
						data: {},
						sp: false
				};
				var t = $.extend(true, d, o);
				ja.ajax({
						url: (t.sp ? t.url : (config.BASE_URL + t.url)),
						type: t.method,
						dataType: t.dataType,
						data: t.data,
						success: function (ret) {
								if (cb) {
										cb(ret);
								}
						},
						error: function (e) {
								var ret = {code: -5000, msg: '请求出错', message: '请求出错'};
								if (cb) {
										cb(ret);
								}
						}
				})
		},

		configLoaded: false,
		loginFlag: false,
		bindRoleFlag: false,

		data: {
				roleInfo: {flag: false},
				lotNum: {cntJoinByIP: 0, currLotNum: 0},
				personCenter: {prizes: []},
				totalVotes: 0
		},
		init: function () {
				var that = this;
				that.bindStatus = false;
				that.version = config.version; //默认版本号
				if (location.href.indexOf('/longmen/') != -1) {
						that.version = config.versionLongmen; //龙门版本号
						$('.longmen').show();
						alert('亲爱的玩家您好，本次活动为测试，奖励仅在测试服可用，感谢您的支持！')
				}

				// 是否是手机QQ内置浏览器
				if (/QQ\/\d/i.test(window.navigator.userAgent)) {
						setShareInfo({
								title: '萌动江湖萌宠大赛', // 分享标题
								summary: '快来助力可爱猫咪加盟天龙御猫园！', // 分享内容
								pic: 'http://i0.cy.com/tlpt/youth/20210629/share.jpg', // 分享图片
								url: location.href.split('#')[0], // 分享链接
						});
				}

				if (window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i) == 'micromessenger') {
						ja.simpleConfig(config.app, config.activity, that.version, 'wechat', function (cn) {
								ja.wx.config(function () {
										ja.wx.share({
												title: "萌动江湖萌宠大赛",
												url: location.href.split('#')[0],
												imgUrl: "http://i0.cy.com/tlpt/youth/20210629/share.jpg",
												desc: "快来助力可爱猫咪加盟天龙御猫园！",
												success: function () {

												}
										});
								})

								ja.simpleConfig(config.app, config.activity, that.version, config.platform, function (cn) {

										that.cbReady();
								}, function (ret) {
										that.cbLogin(ret);
								}, 0)
						});
				} else {
						ja.simpleConfig(config.app, config.activity, that.version, config.platform, function (cn) {

								that.cbReady();
						}, function (ret) {
								that.cbLogin(ret);
						}, 0)
				}

				$('#foo').val(location.href);
		},

		cbReady: function () {
				// 判断登录前执行
				var that = this;
				that.hdLogin();

				// 直接拉取数据
				that.pageNum = 1  //设置当前页码
				that.list(that.pageNum);

				// 总的投喂次数
				that.ajax({url: that.apis.cumulateVotes}, function (ret) {
						if (ret.code === 10000) that.data.totalVotes = ret.data;
						that.hdLogin();
				})
				that.bindBeforLogin();
		},
		list: function (page) {
				var that = this;
				var sn = $('.searchbox input').val()
				var sort = $('.sort-by .btn.cur').index() === -1 ? 0 : $('.sort-by .btn.cur').index()

				if (that.pages != undefined) {
						if (that.pageNum > that.pages) {
								that.pageNum = that.pages
						}
						page = page < that.pages ? page : that.pages;
				}

				var data = {type: sort, pageNum: page, pageSize: ja.client.isMobile ? config.pagesizeM : config.pagesizePC}
				if (sn != '' && !isNaN(sn)) {
						data.sn = parseInt(sn);
				}
				that.ajax({url: 'list', data: data}, function (ret) {
						var pages = 1;

						function mockData(page) {
								var list = []
								var pagesize = page < pages ? config.pagesizePC : Math.ceil(Math.random() * config.pagesizePC)
								for (var i = 0; i < pagesize; i++) {
										var listItem = {
												"server": "纵横双线_一梦十年",
												"images": [
														"/act/xtl/show/20210629/pc/img/fx.jpg"
												],
												"rolename": "飞雪之韵",
												"sn": i + 1 + (page - 1) * config.pagesizePC,
												"declaration": "哈哈哈这是内容",
												"vote": Math.ceil(Math.random() * 100),
												"target": "哈哈哈"
										}
										list.push(listItem)
								}
								var ret = {
										"code": 10000,
										"data": {
												"pages": pages,
												"data": list,
												"pageNum": page
										}
								}
								return ret
						}

						var list = []
						if (ret.code === 10000) {
								list = ret.data.data;
								that.pages = ret.data.pages;
								pages = that.pages;
						}
						$('.hostlist').removeClass('loading')
						if (page === 1 && list.length == 0) {
								$('.hostlist').addClass('no-data');
						} else {
								$('.hostlist').removeClass('no-data');
						}
						that.pageRender(page, sort, list, pages)
				});
		},
		pageRender: function (page, sort, list, pages) {
				var $ul = $('.hostlist')
				$('.ranbox').hide();
				if (pages > 0) {
						$('.ranbox').show();
				}
				page = page < pages ? page : pages;
				page = page < 1 ? 1 : page;
				$('.page-info').text(page + '/' + pages);
				$('.pagenum').val(page)

				var maxCount = Math.min(list.length, config.pagesizePC);
				var lis = '';
				for (var i = 0; i < maxCount; i++) {
						var it = list[i];

						var patName = it.target;
						var patDes = it.declaration;
						var votesNum = it.vote;
						// votesNum = votesNum > 10e4 ? '10w<em style="vertical-align: super">+</em>' : votesNum
						lis += '<li>' +
								'    <h3>主人：' + it.rolename + '</h3>' +
								'    <div class="picbox">' +
								'     <img src="' + it.images[0] + '" />' +
								'    </div>' +
								'    <div class="scrollbox">' +
								'     <div class="namebox">' +
								'      <p><span>编号：' + it.sn + '</span><span>姓名：' + (patName) + '</span></p>' +
								'     </div>' +
								'     <p class="gushi">萌宠故事：' + (patDes) + '</p>' +
								'    </div>' +
								'    <div class="zanbox">' +
								'     <p>口粮数:<span>' + votesNum + '</span></p>' +
								'     <a href="javascript:;" class="btn zanbtn" title="点赞投喂" data-sn="' + it.sn + '">点赞投喂</a>' +
								'    </div>' +
								'</li>'
				}
				$ul.html(lis)
		},

		cbLogin: function (loginInfo) {
				// 获取到登录信息后执行
				var that = this;

				if (!that.bindStatus) {
						that.loginInfo = loginInfo;
						that.loginFlag = that.tools.validate(loginInfo);
				}
				that.hdLogin();

				// 总的投喂次数
				that.ajax({url: that.apis.cumulateVotes}, function (ret) {
						if (ret.code === 10000) that.data.totalVotes = ret.data;
						that.hdLogin();
				})

				// return;
				// 登陆后再拉取数据
				// 获取角色信息
				that.roleList(function () {
						if (!that.data.roleInfo.flag) {
								$('.btn-bindrole').show();
						} else {
								$('.btn-bindrole').hide();
						}

						// 获取用户的抽奖信息和报名参与的次数
						that.lotNum(function () {
								if (!that.bindStatus) that.bindAfterLogin();

								// 获取个人中心的数据，得到的数据会更新到 that.data.personCenter中
								that.personCenter(function () {
										that.hdLogin();
								});
						})
				});

		},

		bindBeforLogin: function () {
				var that = this;
				$('#login').click(function () {
						ja.slogin(1);
				})
				$('#logout').click(function () {
						ja.logout();
				})
				$('#bindPop .tjbtn').on('click', function () {
						that.saveBindRole();
				})

				//送TA参选
				$('.sendbtn').off().on('click', function () {
						if (!that.loginFlag) {
								ja.slogin(1);
								return;
						}
				});

				// 抽奖
				$('.cjbtn').on('click', function () {
						if (!that.loginFlag) {
								ja.slogin(1);
								return;
						}
						that.lot();
				});

				//个人中心
				$('#centerBtn,.centerbtn').on('click', function () {
						if (!that.loginFlag) {
								ja.slogin(1);
								return;
						}
						popup($('#personPop'));
				});

				// 实时监测特殊表情删除
				// 微软拼音bug，没根据键盘做实时检测，需要改为，中文输入开始和结束监听，输入结束后在响应
				var reg = /[^\a-\z\A-\Z0-9\u4E00-\u9FA5\.\,\?\？\、\<\>\。\，\-\——\=\;\@\！\!\+\ \t\$]/g;
				var typing = false;
				$('#nickName')
						.on('compositionstart', function () {
								typing = true;//中文输入法开始
						})
						.on('compositionend', function () {
								typing = false;//中文输入法结束
								$(this).keyup();
						})
						.on('keyup', function () {
								var val = $(this).val();
								if (typing == false) {
										$(this).val(val.replace(reg, ''));
										if (val.length > 5) {
												val = val.substring(0, 5);
												$(this).val(val);
										}
								}
						})
						.on('blur', function () {
								var val = $(this).val();
								typing = false;
								$(this).keyup();
								$(this).val(val.replace(reg, ''));
								console.log(val);
								if (val.length > 5) {
										val = val.substring(0, 5);
										$(this).val(val);
								}
						})

				$('#content')
						.on('compositionstart', function () {
								typing = true;//中文输入法开始
						})
						.on('compositionend', function () {
								typing = false;//中文输入法结束
								$(this).keyup();
						})
						.on("keyup", function () {
								var val = $(this).val();
								if (typing == false) {
										$(this).val(val.replace(reg, ''));
										if (val.length > 150) {
												val = val.substring(0, 150);
												$(this).val(val);
										}
								}
						})
						.on("blur", function () {
								var val = $(this).val();
								typing = false;
								$(this).keyup();
								$(this).val(val.replace(reg, ''));
						})
				//监听手机号输入
				$("#phone").on('input propertychange', function () {
						var regPhone = /\D/ig
						var val = $(this).val();
						$(this).val(val.replace(regPhone, ''));
						if (val.length > 11) {
								val = val.substring(0, 11);
								$(this).val(val);
						}
				});

				that.timeCount = that.timeCountMax;
				// 发送验证码
				$('.yzbtn').click(function () {
						var $this = $(this);
						var phone = $('#phone').val();
						if (phone == '') {
								alert('手机号不能为空！')
								return;
						}
						if (!(/^1[3456789]\d{9}$/.test(phone))) {
								alert("手机号码有误，请重填");
								return;
						}

						if (that.timer) {
								alert('请过会儿再点~');
								return;
						}
						that.timer = setInterval(function () {
								if (that.timeCount === 0) {
										$this.text('重新发送')
										clearInterval(that.timer);
										that.timer = null
										that.timeCount = that.timeCountMax
								} else {
										that.timeCount--
										$this.text(that.timeCount + 's')
								}
						}, 1000);
						$('#code').val('');
						that.sendPhone();

				});


				//图片裁剪
				var imageCropper = $('#imageCropper');
				var options = {
						aspectRatio: 293 / 194, //图片裁剪比例
						crop: function (e) {
						}
				};
				imageCropper.cropper(options);
				$('.acbtn input').live('change', function (event) {
						var i = $('.acbtn input').index($(this));
						var eve = event || event;
						var file = event.currentTarget.files[0];
						var format = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
						var str = 'jpg,jpeg,png';
						if (str.indexOf(format) === -1) {
								if (ja.isMobile) {
										alert('请选择jpeg，jpg，png格式的图片');
								} else {
										that.alert('请选择jpeg，jpg，png格式的图片');
								}
								return;
						}
						var fileSizeBuffer = Math.ceil(file.size);
						if (fileSizeBuffer - config.uploadMaxSize > 0) {
								if (ja.isMobile) {
										alert('请选择小于' + (config.uploadMaxSize / 1024 / 1024) + 'M的图片')
								} else {
										that.alert('请选择小于' + (config.uploadMaxSize / 1024 / 1024) + 'M的图片')
								}
								$('.acbtn input').val('');
								return;
						}
						var reader = new FileReader();
						reader.onload = function () {
								popup($('#cropperPop'));
								$('.acbtn input').val('');
								imageCropper.cropper('destroy').attr('src', reader.result).attr('len', i).cropper(options);
						};
						if (file) {
								reader.readAsDataURL(file);
						}
				});
				$('.btn-upload-cirfirm').on('click', function () {
						var imageSrc = imageCropper.cropper('getCroppedCanvas').toDataURL('image/jpeg');
						$('.upimgbox img').attr('src', imageSrc);
						// 去掉base64编码中的前缀 data:image/png;base64,
						// 计算base64格式字符算对应的文件大小
						var fileSize = that.tools.getImgSize(imageSrc);
						if (fileSize > config.uploadMaxSize) {
								if (ja.isMobile) {
										alert('图片过大请重新上传')
								} else {
										that.alert('图片过大请重新上传')
								}

								$('#cropperPop').hide();
								popup($('#enterPop'));
								return false;
						}
						$('#cropperPop').hide();
						popup($('#enterPop'));
				})
				$('.btn-upload-cancel').on('click', function () {
						$('#cropperPop').hide();
						popup($('#enterPop'));
				});

				$('#enterPop .ckbtn').on('click', function () {
						popup('#gzPop');
				})
				// 提交参选
				$('#enterPop .tjbtn').on('click', function () {

						// 萌宠昵称不能为空
						var nickName = $.trim($("#nickName").val())
						if (nickName == '') {

								if (ja.isMobile) {
										alert('萌宠昵称不能为空！')
								} else {
										that.alert('萌宠昵称不能为空！')
								}

								return;
						}

						// 萌宠故事不能为空
						var content = $.trim($("#content").val())
						if (content == '') {
								if (ja.isMobile) {
										alert('萌宠故事不能为空！')
								} else {
										that.alert('萌宠故事不能为空！')
								}

								return;
						}

						var img = $('.upimgbox img').attr('src');
						if (img === '') {
								if (ja.isMobile) {
										alert('您还没有上传图片，请先上传图片！')
								} else {
										that.alert('您还没有上传图片，请先上传图片！')
								}

								return;
						}

						// 校验手机号
						var phone = $.trim($("#phone").val());  //手机号码
						if (phone == '') {
								if (ja.isMobile) {
										alert('手机号不能为空！')
								} else {
										that.alert('手机号不能为空！')
								}

								return;
						}
						if (!(/^1[3456789]\d{9}$/.test(phone))) {
								if (ja.isMobile) {
										alert("手机号码有误，请重填");
								} else {
										that.alert("手机号码有误，请重填");
								}

								return;
						}

						// 校验验证码
						var code = $.trim($("#code").val());  //手机号码
						if (code === '') {
								if (ja.isMobile) {
										alert("验证码不能为空！");
								} else {
										that.alert("验证码不能为空！");
								}

								return;
						}

						if (code.length != 6) {
								if (ja.isMobile) {
										alert("验证码格式错误！");
								} else {
										that.alert("验证码格式错误！");
								}
								return;
						}
						that.save();
				});


				// 点赞
				$('.hostlist').on('click', '.zanbtn', function () {
						var $this = $(this)
						var $li = $this.parents('li')
						var $vote = $li.find('.zanbox span')
						var sn = $this.data('sn');
						that.vote(sn, $vote);
				})

				// 上一页
				$('.tpage').on('click', '.btn-prev-page', function () {
						if (that.pageNum > 1) {
								that.pageNum--;
						}
						that.list(that.pageNum)
				})
				// 下一页
				$('.tpage').on('click', '.btn-next-page', function () {
						if (that.pageNum < that.pages) {
								that.pageNum++;
						}
						that.list(that.pageNum)
				})
				// 排序
				$('.ranbox').on('click', '.sort-by .btn', function () {
						$(this).addClass('cur').siblings().removeClass('cur');
						var sort = $('.sort-by .btn.cur').index() === -1 ? 0 : $('.sort-by .btn.cur').index()  // 展示类型；0：按时间展示；1：按票数展示
						that.list(that.pageNum)
				})
				// 按编号搜索
				$('.bhbtn').click(function () {
						var sn = $('.searchbox input').val();
						// 检测sn是否正确
						if (!(sn == '' || (sn != '' && !isNaN(sn) && sn.indexOf('.') == -1 && sn > 0))) {
								that.alert('萌宠编号格式不正确！')
								return;
						}
						that.pageNum = 1;
						that.list(that.pageNum)
				})
				// 按页码翻页
				$('.tzbtn').click(function () {
						var pagenum = $('.pagenum').val();
						if (!(pagenum != '' && !isNaN(pagenum) && pagenum.indexOf('.') == -1 && pagenum > 0)) {
								that.alert('页码格式不正确！')
								return;
						}
						that.pageNum = pagenum
						that.list(that.pageNum)
				})


				$('#csbtntop').on('click', function () {
						popup($('#csPop1'));
				});
				$('#csbtnbop').on('click', function () {
						popup($('#csPop2'));
				});
				var clipboard = new Clipboard('.copybtn');
				clipboard.on('success', function (e) {
						that.alert('复制成功！');
				});
				clipboard.on('error', function (e) {
						that.alert('复制失败，请手动复制！');
				});
				//分享活动
				$('.fxbtn,.sharebtn').on('click', function () {
						popup($('#fxPop'));
				});
				return that;
		},
		bindAfterLogin: function () {
				var that = this;

				$('.btn-bindrole').off().on('click', function () {
						popup($('#bindPop'));
				})
				//送TA参选
				$('.sendbtn').off().on('click', function () {
						if (!that.data.roleInfo.flag) {
								popup($('#bindPop'));
								return;
						}
						if (that.data.personCenter.status === 0) {
								that.alert('您的投稿正在审核中，请耐心等待！')
								return;
						}
						if (that.data.personCenter.status === -1) {
								that.alert('您未通过审核，无法再次参加')
								return;
						}
						if (that.data.personCenter.status === 1) {
								that.alert('每个账号仅能报名参选一次！')
								return;
						}
						popup($('#enterPop'));
				});


				// 复制分享链接，发起分享请求
				$('.copybtn').off().on('click', function () {
						that.share();
				});


				that.bindStatus = true;
				return this;
		},
		hdLogin: function () {
				var that = this;
				if (that.loginFlag) {
						$('#userName').text(that.loginInfo.data.openid);
						$('.login_box').hide();
						$('.logout_box').show();
				} else {
						$('.login_box').show();
						$('.logout_box').hide();
				}


				// 更新总的投票次数
				$('.ytouwei span').text(that.tools.toThousands(that.data.totalVotes));
				$('.jindu li:nth-child(n+2)').removeClass('ydc');

				var areaNums = [1e4, 5e4, 10e4, 25e4, 50e4]  // 10000；50000；100000；250000；500000
				if (location.href.indexOf('/longmen/') != -1) {
						areaNums = [5e2, 10e2, 15e2, 50e2, 100e2]
				}
				if (that.data.totalVotes >= areaNums[0]) {
						$('.jindu li').eq(1).addClass('ydc')
				}
				;
				if (that.data.totalVotes >= areaNums[1]) {
						$('.jindu li').eq(2).addClass('ydc')
				}
				;
				if (that.data.totalVotes >= areaNums[2]) {
						$('.jindu li').eq(3).addClass('ydc')
				}
				;
				if (that.data.totalVotes >= areaNums[3]) {
						$('.jindu li').eq(4).addClass('ydc')
				}
				;
				if (that.data.totalVotes >= areaNums[4]) {
						$('.jindu li').eq(5).addClass('ydc')
				}
				;
				areaNums.forEach(function (it, index) {
						$('.jindu li').eq(index + 1).find('span').text(areaNums[index])
				});

				// 个人中心
				$('.person_currLotNum, .cishu span').text(that.data.lotNum.currLotNum) // 当前剩余抽奖次数
				$('.person_currCanVote').text(that.data.personCenter.currCanVote ? that.data.personCenter.currCanVote : 0) // 当前投喂次数
				$('.person_state').text(that.getPersonState()) // 参选状态
				$('.person_gifts').html(that.getPersonPrizes()) // 已获奖励

				// 获得投喂次数任务状态
				$('#csPop1 .tasklist li:nth-child(1) em').text(that.getTodayVoteStatus(1));
				$('#csPop1 .tasklist li:nth-child(2) em').text(that.getTodayVoteStatus(2));

				// 获取抽奖次数任务状态
				$('#csPop2 .tasklist li:nth-child(1) em').text(that.getLotStatus(1));
				$('#csPop2 .tasklist li:nth-child(2) em').text(that.getLotStatus(2));
		},

		getTodayVoteStatus: function (type) {
				var that = this
				var ret = '今日0/1'
				if (type === 1) {
						// 每日登陆完成任务则可增加一次投喂次数
						if (that.data.personCenter.totalCanVote >= 1) ret = '今日1/1'
				}
				if (type === 2) {
						// 成功分享活动页面
						if (that.data.personCenter.totalCanVote >= 2) ret = '今日1/1'
				}
				return ret
		},
		getLotStatus: function (type) {
				var that = this;
				var ret = '未完成'
				if (type === 1) {
						// 成功发布参选信息并通过审核
						if (that.data.personCenter && that.data.personCenter.status === 1) ret = '已完成'
				}
				if (type === 2) {
						// 获得投喂数达到50
						if (that.data.personCenter && that.data.personCenter.currCanVote >= 50) ret = '已完成'
				}
				return ret
		},
		getPersonState: function () {
				var that = this
				var ret = '尚未参赛'
				switch (that.data.personCenter.status) {
						case 0: {
								ret = '正在审核';
								break;
						}
						case -1: {
								ret = '审核失败';
								break;
						}
						case 1: {
								ret = '参赛成功';
								break;
						}
				}
				return ret;
		},
		getPersonPrizes: function () {
				var that = this
				var ret = ''
				var prizesList = that.data.personCenter.prizes
				prizesList.forEach(function (it, idx) {
						ret += it.toUpperCase(it) == 'N' ? '' : (config.gifts[it] + '、');
				})
				if (ret != '') ret = ret.substring(0, ret.length - 1)
				if (ret === '') {
						ret = '暂无奖励'
				}
				return ret
		},
		roleList: function (cb) {
				var that = this;
				var elSelect = $('#roleList')
				elSelect.empty();
				that.ajax({url: that.apis.roleList}, function (ret) {
						that.data.roleInfo.flag = ret.code === 5021;
						if (ret.code === 10000) {
								var list = ret.data
								if (!list) {
										var html = '';
										list.forEach(function (it, idx) {
												html += '<option value="' + it.group_no + '-' + it.role_guid + '">' + it.server_name + ' - ' + it.role_name + '</option>'
										})
										elSelect.append(html)
								}
						}
						cb();
				})
		},
		lotNum: function (cb) {
				var that = this;
				that.ajax({url: that.apis.lotNum}, function (ret) {
						if (ret.code === 10000) {
								that.data.lotNum = ret.data;
						}
						cb();
				})
		},
		personCenter: function (cb) {
				var that = this;
				that.ajax({url: that.apis.personCenter}, function (ret) {
						if (ret.code === 10000) {
								that.data.personCenter = ret.data
						}
						if (cb) cb()
				})
		},
		saveBindRole: function () {
				var that = this;
				var curSelect = $('#roleList').find('option:selected').val();
				if (curSelect === '') {
						that.alert('请先选择区服角色!')
						return;
				}
				var selectArr = curSelect.split('-');
				var groupNo = selectArr[0]
				var roleGuid = selectArr[1]
				that.ajax({
						url: that.apis.saveBindRole,
						method: 'POST',
						data: {groupNo: groupNo, roleGuid: roleGuid}
				}, function (ret) {
						if (ret.code === 5023) {
								// 绑定成功
								that.alert('角色绑定成功！')
								that.data.roleInfo.flag = true;
								that.cbLogin();
						} else {
								that.alert(ret.message)
						}
				})

		},
		sendPhone: function () {
				var that = this
				var url = ja.options.host + "/phone/core/zhpt/sendcode"
				var phone = $.trim($('#phone').val());
				that.ajax({url: url, sp: true, method: 'POST', data: {phone: phone}}, function (ret) {
						if (ret.code === 10000) {
								that.alert('验证码已发送，请注意查收！');
								popup($('#enterPop'))
								return;
						}
						that.alert(ret.message)
				})
		},
		save: function () {
				var that = this
				var data = {
						target: encodeURIComponent(Base64.encode($('#nickName').val())),
						declaration: encodeURIComponent(Base64.encode($('#content').val())),
						cimg1: $('.upimgbox img').attr('src'),
						phone: $('#phone').val(),
						code: $('#code').val()
				}
				that.ajax({url: that.apis.save, method: 'POST', data: data}, function (ret) {
						if (ret.code === 10000) {
								that.alert('恭喜您投稿成功！')
								that.cbLogin();
								return;
						}
						if (ret.code === 3002) {
								alert('短信验证码错误，请重试')
						} else {
								alert(ret.message)
						}
				})
		},
		lot: function () {
				// 获取抽奖数据
				var that = this;
				if (that.lotObj.status === 'loading') {
						return;
				}
				that.lotObj.status = 'loading';

				// 移动端抽奖
				if (ja.client.isMobile) {
						that.ajax({url: that.apis.lot, method: 'POST'}, function (ret) {
								if (ret.code === 10000) {
										that.lotObj.gId = ret.data;
										var gIndex = config.giftsList.indexOf(that.lotObj.gId);
										if (gIndex == -1) {
												that.alert('谢谢参与！');
												delete that.lotObj['gId']
												delete that.lotObj['status']
												return;
										}
										// 开始抽奖
										lotteryStart(gIndex, {
												complete: function (e) {
														var gId = that.lotObj.gId
														var giftName = config.gifts[gId]
														$('#dialogGetPrize .g-name').text(giftName)
														popup($('#dialogGetPrize'));
														delete that.lotObj['gId']
														delete that.lotObj['status']
												}
										});
										that.cbLogin();
								} else {
										delete that.lotObj['gId']
										delete that.lotObj['status']
										if (ret.code === 2007) {
												popup($('#noLotNum'))
										} else {
												that.alert(ret.message)
										}
								}
						})
				}
				// PC端抽奖
				else {
						that.ajax({url: that.apis.lot, method: 'POST'}, function (ret) {
								if (ret.code === 10000) {
										that.lotObj.gId = ret.data;
										var gIndex = config.giftsList.indexOf(that.lotObj.gId);
										if (gIndex == -1) {
												that.alert('谢谢参与！');
												delete that.lotObj['gId']
												delete that.lotObj['status']
												return;
										}
										that.lotObj.startRoll(gIndex);
										that.cbLogin();
								} else {
										delete that.lotObj['gId']
										delete that.lotObj['status']
										if (ret.code === 2007) {
												popup($('#noLotNum'))
										} else {
												that.alert(ret.message)
										}
								}
						})
				}
		},
		// 创建抽奖实例
		lotObj: new LottOBJ({
				obj: $('#lottcon'),
				cover: $('#lottcon .lott-cover'),
				count: 6,
				position: '74_25,247_25,420_25,578_25,766_25,939_25',
				complete: function () {
						var that = GG;
						//抽奖完成
						var gId = that.lotObj.gId
						var giftName = config.gifts[gId]
						$('#dialogGetPrize .g-name').text(giftName)
						popup($('#dialogGetPrize'));
						delete that.lotObj['gId']
						delete that.lotObj['status']
				}
		}),
		vote: function (sn, $vote) {
				var that = this;
				that.ajax({url: that.apis.vote, method: 'POST', data: {sn: sn}}, function (ret) {
						if (ret.code === 10000) {
								that.alert('点赞投喂成功！')
								// 给当前点赞的数据的点赞数+1
								var curVote = parseInt($vote.text())
								$vote.text(++curVote)
								that.cbLogin();
								return;
						}
						if (ret.code === 3202) {
								if (that.data.personCenter.currCanVote < 1) {
										that.alert('投喂次数不够！')
								} else {
										that.alert(ret.message)
								}
								return;
						}

						if (ret.code === 2111) {
								popup($('#noVote'));
								return;
						}
						if (ret.code === 1000) {
								that.alert('您要投喂的萌宠不存在！')
								return;
						}
						if (ret.code != 10000) {
								that.alert(ret.message)
						}
				});
		},
		share: function () {
				var that = this;
				that.ajax({url: that.apis.share, method: 'POST'}, function (ret) {
						if (ret.code === 10000) {
								// 分享成功
								that.cbLogin();
						}
						if (ret.code != 10000) {
								console.log(ret.message)
						}
				})
		},

		alert: function (msg) {
				$('#popAlert .txtbox').html(msg)
				popup($('#popAlert'));
		},

		tools: {
				//验证参数是否为空 null '' undefined
				validate: function (value) {
						if (value == null || value == 'null' || value == '' || value == undefined) {
								return false;
						} else {
								return true;
						}
				},
				getImgSize: function (base64Img) {
						var tag = 'base64,';
						var baseStr = base64Img.substring(base64Img.indexOf(tag) + tag.length);
						// 去掉base64编码中的“=”号　　
						var eqTagIndex = baseStr.indexOf('=');
						baseStr = eqTagIndex !== -1 ? baseStr.substring(0, eqTagIndex) : baseStr;
						// 计算文件流大小
						var strLen = baseStr.length;
						var fileSize = strLen - (strLen / 8) * 2
						return fileSize
				},
				toThousands: function (num) {
						num = (num || 0).toString(), result = '';
						while (num.length > 3) {
								result = ',' + num.slice(-3) + result;
								num = num.slice(0, num.length - 3);
						}
						if (num || num != 0) {
								result = num + result;
						}
						if (num == 0) {
								result = num;
						}
						return result;
				}
		}
}

$(function () {
		GG.init();
});

//抽奖组件类
function LottOBJ(o) {
		var conf = {
				obj: '',
				cover: '',
				count: 0,
				position: '',
				complete: function () {
				}
		}
		conf = $.extend(conf, o);
		conf.cover.hide();
		var pos = [],
				timer = null,
				spd = 100,
				curr = 0,
				round = 0;
		for (var i = 0; i < conf.position.split(',').length; i++) {
				var s = conf.position.split(',')[i];
				pos.push({
						left: parseInt(s.split('_')[0]),
						top: parseInt(s.split('_')[1])
				});
		}
		if (pos.length != conf.count) return;
		this.startRoll = function (n) {
				if (typeof (n) != 'number' && n > pos.length) n = 0;
				spd = 100;
				round = Math.ceil(Math.random() * 4) + 1;
				conf.cover.show().css({
						left: pos[curr].left,
						top: pos[curr].top
				});
				timer = setTimeout(function () {
						roll(n);
				}, spd);
		};

		function roll(e) {
				clearTimeout(timer);
				if (curr == pos.length - 1) {
						if (round > 0) {
								round--;
								curr = 0;
						}
				} else
						curr++;
				conf.cover.css({
						left: pos[curr].left,
						top: pos[curr].top
				});
				if (curr === e && round === 0) {
						setTimeout(conf.complete, 500);
						return;
				}
				if (round === 0) spd += 80;
				timer = setTimeout(function () {
						roll(e);
				}, spd);
		}
};


//移动端抽奖
//e:中奖序号
//complete:抽奖完成回调
function lotteryStart(e, config) {
		var opt = $.extend({
						loop: 6, //圈数 默认6
						duration: 3000, //时间 默认6秒
						ease: 5, //启动速度系数 默认5
						complete: function () {
								//to do
						}
				}, config),
				obj = $('[data-lottery]'),
				count = parseInt(obj.attr('data-lottery')),
				rotate = 0,
				target = opt.loop * 360 + 360 - 360 / count * e,
				fps = 1000 / 60,
				past = 0,
				state = function (s) {
						if (s) obj.attr('data-state', s);
						return obj.attr('data-state');
				},
				run = function () {
						if (past > opt.duration) {
								opt.complete(e);
								state('结束');
						} else {
								rotate = Math.sin(past / opt.duration * Math.PI / 2) * target;
								past += fps;
								obj.css({
										'transform': 'rotate(' + rotate + 'deg)',
										'-webkit-transform': 'rotate(' + rotate + 'deg)'
								});
								setTimeout(run, fps);
						}
				};
		if (state() == '运行' || e >= count) return;
		state('运行');
		run();
};
