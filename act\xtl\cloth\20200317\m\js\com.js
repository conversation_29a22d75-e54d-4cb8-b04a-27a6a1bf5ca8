// 切换图纸 必须在没有图纸在绘制时才可以切换
$('.draw').on('click', function () {
	var $this = $(this)
	if ($this.children('span').css('display') == 'block') { // 未解锁图纸
		return;
	} else if (underwayDraw != 0) { // 有图纸正在绘制
		th.tips('请先完成正在绘制的图纸哦~');
		return
	}
	$('#step_box').removeClass().addClass('stepbox cloth' + $this.index() + '_4');//图纸框显示
})

//必须完成绘制才可以解锁下一个图片
$('.suobtn').on('click', function () {
	if ($click()) return;
	if (!th.methodForPhase_2()) return;
	if (th.clickFlag) return;
	th.clickFlag = true

	if (underwayDraw == 0 && unlockedDraws < 4) {
		isDrawFlag = false;
	};

	if (!isDrawFlag) {
		$('#sure_cloth').attr('flag', $(this).parent().index() + 1)
		popShow('pop_sure');
		th.clickFlag = false;
		isDrawFlag = true;
	} else {
		th.tips('制作完成才可以解锁下一个哦!')
		th.clickFlag = false;
	}
});
$('#sure_cloth').on('click', function () {
	if ($click()) return;
	if (!th.methodForPhase_2()) return;
	if (th.clickFlag) return;

	th.clickFlag = true
	th.getDrawings(this)

});
//绘制图纸
$('.cailist li').on('click', function () {
	if ($click()) return;
	if (!th.methodForPhase_2()) return;
	if (th.clickFlag) return;
	th.clickFlag = true;
	if (underwayStep == 0 && unlockedDraws < 4) {
		th.tips('点击铜锁，即可点亮图纸，开始绘制!')
		th.clickFlag = false;
		return;
	} else if (unlockedDraws >= 4) {
		th.tips('您已经完成全部图纸的绘制了!')
		th.clickFlag = false;
		return;
	} else {
		if ($(this).hasClass('dis_btn')) {
			th.tips('您所需要消耗的道具不足，<br>去看看好友是否有多余可以使用吧！')
			th.clickFlag = false;
			return;
		} else {
			if ($(this).hasClass('cur')) {
				if (underwayStep == 1) {//勾线步骤
					th.toStep(underwayDraw, underwayStep, function () {
						$('.adddjbox').removeClass('fadeoup').addClass('fadeDown');//获得道具气泡隐藏
						$('#step_box').css({ 'display': 'none' });
						$('#step_box').fadeIn(2000);
						$('#step_box').removeClass().addClass('stepbox cloth' + (underwayDraw - 1) + '_' + underwayStep);//图纸框显示
						setTimeout(function () {
							th.ReduceProp()
							th.getAwards() // 更新礼包数量
							$('.adddjbox').removeClass('fadeDown').addClass('fadeup');//获得道具气泡显示
							setTimeout(function () { $('.adddjbox').removeClass('fadeoup').addClass('fadeDown');}, 3000)
							$('.cailist li').removeClass('cur').eq(underwayStep).addClass('cur');//道具栏状态改变
							underwayStep++;
							th.clickFlag = false;
						}, 2000);
						return;
					})
				} else if (underwayStep == 2) {//描绘步骤
					th.toStep(underwayDraw, underwayStep, function () {
						$('.adddjbox').removeClass('fadeoup').addClass('fadeDown');//获得道具气泡隐藏
						$('#step_box').css({ 'display': 'none' });
						$('#step_box').fadeIn(2000);
						$('#step_box').removeClass().addClass('stepbox cloth' + (underwayDraw - 1) + '_' + underwayStep);//图纸框显示
						setTimeout(function () {
							th.ReduceProp()
							th.getAwards() // 更新礼包数量
							$('.adddjbox').removeClass('fadeDown').addClass('fadeup');//获得道具气泡显示
							setTimeout(function () { $('.adddjbox').removeClass('fadeoup').addClass('fadeDown'); }, 3000)
							$('.cailist li').removeClass('cur').eq(underwayStep).addClass('cur');//道具栏状态改变
							underwayStep++;
							th.clickFlag = false;
						}, 2000);
						return;
					})
				} else {//水袖金粉步骤
					th.toStep(underwayDraw, underwayStep, function () {
						th.ReduceProp()
						th.getAwards() // 更新礼包数量
						$('.loadEffect').fadeIn();//加载动画显示
						$('.adddjbox').removeClass('fadeoup').addClass('fadeDown');//获得道具气泡隐藏
						loading(underwayDraw - 1);//加载当前水袖素材
						return;
					})
				};
			} else {
				if ($(this).index() < underwayStep) {
					th.tips('少侠您已完成此步绘制，快去进行下一步吧！');
					th.clickFlag = false;
					return;
				} else {
					th.tips('少侠还未完成上一步的绘制，先完成上一步吧！');
					th.clickFlag = false;
					return;
				}
			};
		};
	};
});
//资源加载
var fnamearr = [
	['cloth0_0.jpg', 'cloth0_1.jpg', 'cloth0_2.jpg', 'cloth0_3.jpg', 'cloth0_4.jpg', 'cloth0_5.jpg', 'cloth0_6.jpg', 'cloth0_7.jpg', 'cloth0_8.jpg', 'cloth0_9.jpg', 'cloth0_10.jpg', 'cloth0_11.jpg', 'cloth0_12.jpg', 'cloth0_13.jpg', 'cloth0_14.jpg', 'cloth0_15.jpg', 'cloth0_16.jpg', 'cloth0_17.jpg', 'cloth0_18.jpg', 'cloth0_19.jpg', 'cloth0_20.jpg', 'cloth0_21.jpg', 'cloth0_22.jpg', 'cloth0_23.jpg', 'cloth0_24.jpg', 'cloth0_25.jpg', 'cloth0_26.jpg', 'cloth0_27.jpg', 'cloth0_28.jpg', 'cloth0_29.jpg', 'cloth0_30.jpg', 'cloth0_31.jpg', 'cloth0_32.jpg', 'cloth0_33.jpg', 'cloth0_34.jpg', 'cloth0_35.jpg'],
	['cloth1_0.jpg', 'cloth1_1.jpg', 'cloth1_2.jpg', 'cloth1_3.jpg', 'cloth1_4.jpg', 'cloth1_5.jpg', 'cloth1_6.jpg', 'cloth1_7.jpg', 'cloth1_8.jpg', 'cloth1_9.jpg', 'cloth1_10.jpg', 'cloth1_11.jpg', 'cloth1_12.jpg', 'cloth1_13.jpg', 'cloth1_14.jpg', 'cloth1_15.jpg', 'cloth1_16.jpg', 'cloth1_17.jpg', 'cloth1_18.jpg', 'cloth1_19.jpg', 'cloth1_20.jpg', 'cloth1_21.jpg', 'cloth1_22.jpg', 'cloth1_23.jpg', 'cloth1_24.jpg', 'cloth1_25.jpg', 'cloth1_26.jpg', 'cloth1_27.jpg', 'cloth1_28.jpg', 'cloth1_29.jpg', 'cloth1_30.jpg', 'cloth1_31.jpg', 'cloth1_32.jpg', 'cloth1_33.jpg', 'cloth1_34.jpg', 'cloth1_35.jpg'],
	['cloth2_0.jpg', 'cloth2_1.jpg', 'cloth2_2.jpg', 'cloth2_3.jpg', 'cloth2_4.jpg', 'cloth2_5.jpg', 'cloth2_6.jpg', 'cloth2_7.jpg', 'cloth2_8.jpg', 'cloth2_9.jpg', 'cloth2_10.jpg', 'cloth2_11.jpg', 'cloth2_12.jpg', 'cloth2_13.jpg', 'cloth2_14.jpg', 'cloth2_15.jpg', 'cloth2_16.jpg', 'cloth2_17.jpg', 'cloth2_18.jpg', 'cloth2_19.jpg', 'cloth2_20.jpg', 'cloth2_21.jpg', 'cloth2_22.jpg', 'cloth2_23.jpg', 'cloth2_24.jpg', 'cloth2_25.jpg', 'cloth2_26.jpg', 'cloth2_27.jpg', 'cloth2_28.jpg', 'cloth2_29.jpg', 'cloth2_30.jpg', 'cloth2_31.jpg', 'cloth2_32.jpg', 'cloth2_33.jpg', 'cloth2_34.jpg', 'cloth2_35.jpg'],
	['cloth3_0.jpg', 'cloth3_1.jpg', 'cloth3_2.jpg', 'cloth3_3.jpg', 'cloth3_4.jpg', 'cloth3_5.jpg', 'cloth3_6.jpg', 'cloth3_7.jpg', 'cloth3_8.jpg', 'cloth3_9.jpg', 'cloth3_10.jpg', 'cloth3_11.jpg', 'cloth3_12.jpg', 'cloth3_13.jpg', 'cloth3_14.jpg', 'cloth3_15.jpg', 'cloth3_16.jpg', 'cloth3_17.jpg', 'cloth3_18.jpg', 'cloth3_19.jpg', 'cloth3_20.jpg', 'cloth3_21.jpg', 'cloth3_22.jpg', 'cloth3_23.jpg', 'cloth3_24.jpg', 'cloth3_25.jpg', 'cloth3_26.jpg', 'cloth3_27.jpg', 'cloth3_28.jpg', 'cloth3_29.jpg', 'cloth3_30.jpg', 'cloth3_31.jpg', 'cloth3_32.jpg', 'cloth3_33.jpg', 'cloth3_34.jpg', 'cloth3_35.jpg'],
	['scloth0_1.png', 'scloth0_2.png', 'scloth1_1.png', 'scloth1_2.png', 'scloth2_1.png', 'scloth2_2.png', 'scloth3_1.png', 'scloth3_2.png']];
function loading(com_var) {
	var ffold = '/act/xtl/cloth/20200317/m/img/cloth/',
		fname = fnamearr[com_var];
	for (var n in fname) {
		fname[n] = ffold + fname[n];
	}
	var loader = new mo.Loader(fname, {
		loadType: 1,
		minTime: 0,
		onLoading: function (count, total) {
			// console.log(parseInt(count / total * 100));
		},
		onComplete: function (time) {
			if (com_var < 4) {
				$('#step_box').removeClass().addClass('stepbox cloth' + (underwayDraw - 1) + '_' + underwayStep);//图纸框显示
				$('.loadEffect').fadeOut();//加载动画隐藏
				$('#tc_pic img').attr('src', '/act/xtl/cloth/20200317/m/img/tcpic_' + (underwayDraw - 1) + '.jpg');
				$('#tc_pic').find('p').html(drawNames[underwayDraw - 1]);//完成绘制弹窗图片 以及 文案修改
				setTimeout(function () {
					popShow('pop_compele');
					$('.cailist li').removeClass('cur');//道具栏状态改变
					underwayStep = 0;
					isDrawFlag = false;
					underwayDraw = 0;
					th.clickFlag = false;
				}, 4000);
			}
		}
	});
};
loading(4);
