.pop{color: #926f45;text-align: center;font-size:18px;position:relative;/* display: none; */}
.poptit{text-align:center;margin-top:16px;line-height:42px;height: 42px;}
.poptit::after,.poptit::before{content:'';display:inline-block;vertical-align: top;background: url(../img/poptit.png) no-repeat;height: 42px;width: 69px;}
.poptit::before{margin-right: 19px;}
.poptit::after{margin-left: 19px;background-position: top right;}
.poptit span{color:#5c4a33;font-size:32px;font-weight:bold;background-image:-webkit-linear-gradient(top,#5c4a33,#ac9c76);-webkit-background-clip:text;-webkit-text-fill-color:transparent;}
.rolename{color: #7c6c4e;text-align: center;margin-top: 3px;}
.rolename p{display: inline-block;vertical-align: top;height: 25px;line-height: 25px;background: #eeece5;padding: 0 34px;}
.centerbox{margin: 17px auto 0;width: 757px;height: 457px;background: url(../img/centerbox.png) no-repeat;overflow: hidden;}
.centerbox .divbox{width: 348px;float: left;}
.centerbox .divbox.fr{float: right;}
.toptable{width: 325px;margin: 0 auto;padding-top: 34px;height: 72px;}
.toptable table{width: 100%;}
.toptable td{color: #475e5c;line-height: 20px;height: 27px;font-size: 16px;}
.toptable td span{color: #766a47;}
.toptable td.txc{text-align: center;}
.sendbtn{width: 147px;height: 45px;background-position: 0 -81px;}
.botbg{width: 330px;height: 281px;background: url(../img/botbg.png) no-repeat center top;margin: 14px auto 0;}
.botbg ul{width: 316px;margin: 0 auto;}
.botbg li{float: left;width: 158px;padding: 115px 0 13px;}
.getbtn,.ckbtn{width: 132px;height: 42px;margin: 0 auto;background-position: 0 -136px;}
.ckbtn{background-position: 0 -242px;}
.getbtn.ylq{background-position: -145px -131px;}
.sendfirebtn{width: 147px;height: 45px;background-position: 0 -182px;}
.divbox.fr .botbg{margin-top: 89px;background: url(../img/botbgfr.png) no-repeat center top;}
.poptip{text-align: center;margin-top: 11px;}
.pop6{width: 721px;height: 425px;background: url(../img/pop4.png) no-repeat;box-sizing: border-box;}
.pop6 .popcont{padding: 41px 110px 0;}
.upload li,.pop-close{background:url(../img/com.png) no-repeat;display:block;text-indent:-9999em;background-size: 1200px auto;}
.pop1{ width: 1206px; height: 712px; text-align: left; background: url(../img/pop1.png); background-size: 100% auto; box-sizing: border-box;}
.pop1 .popcont{padding: 50px 180px 0;}
.pop-close{ width: 55px; height: 56px; background-position: bottom right; position: absolute; top: 58px; right: 58px;}
.pop6 .pop-close{top: 27px; right: 1px;}
.tit6{ width: 544px; height: 55px; background-position: 0 -815px; margin: 30px auto 0;}
.pop-name{ font-size: 32px; color: #5c4a33; margin: 5px auto 0;background: url(../img/pop-name.png) repeat-x;display:inline-block;vertical-align: top;height: 43px;padding: 0 38px;position: relative;}
.pop-name p{width: 100%;height:100%;background-image:-webkit-linear-gradient(top,#5c4a33,#ac9c76);-webkit-background-clip:text;-webkit-text-fill-color:transparent;}
.pop-name::after,.pop-name::before{width: 29px;height: 100%;background: url(../img/nameicon.png) no-repeat;content: '';position: absolute;top:0;}
.pop-name::before{left: -29px;}
.pop-name::after{right: -29px;background-position: top right;}
.pop-sub,.pop-p{ font-size: 20px; line-height: 30px; color: #926f45;text-align:left;padding:21px 40px 0;}
.pop-p{padding-top:0;}
.upload{ display: flex; margin: 20px 0 30px 40px;}
.upload li{ width: 145px; height: 145px; background-position: -788px 0; margin-right: 24px;}
.story,.pop-tjly{ width: 100%; height: 74px; font-size: 18px;color: #8e7d5a;border: none;margin: 0 auto;padding:10px 0;font-family:'\5FAE\8F6F\96C5\9ED1';display:block;line-height: 20px;background: none;}
.pop-tjly{ height: 39px;margin-top: 56px;}
.popsimple .pop-tjly{ height: 129px;background: url(../img/storybg.png) no-repeat;width: 575px;margin: 44px auto 0;padding: 56px 16px 10px;}
.zjs-con{text-align: center; margin: 0 auto; overflow-y: auto; overflow-x: hidden;height: 580px;}
.zjs-con::-webkit-scrollbar{
    width: 5px;
    height: 7px;
}
/*定义滚动条轨道 内阴影+圆角*/
.zjs-con::-webkit-scrollbar-track {
    background:#cebda9;
}
/*定义滑块 内阴影+圆角*/
.zjs-con::-webkit-scrollbar-thumb{
    background: #bb9951;
}
.yysw{ width: 602px; height: 68px; line-height: 68px;text-align: center; font-size: 24px; color: #926f45; margin: 41px auto 0;background:#e5e1dd;border:1px solid #fff;}
.tjsfbox{padding: 18px 0 0 27px;height: 26px;}
.popall .tjsfbox{padding-top: 15px;}
.btn-tjfs{ font-size: 20px; color: #926f45; margin-right: 27px; cursor: pointer;line-height: 26px;float: left;}
.btn-tjfs span{ display: inline-block;width: 20px; height: 20px;margin-right: 5px;vertical-align: top;border: 1px solid #926f45;border-radius: 50%;margin-top: 3px;position: relative;}
.btn-tjfs.act span::after{content: '';position: absolute;width: 12px;height: 12px;background: #926f45;border-radius: 50%;left: 50%;top:50%;margin-left: -6px;margin-top: -6px;}
.btn-tjzjs{ width: 211px; height: 65px; background-position: 0 0; margin: 51px auto 0;}
.popall .btn-tjzjs{margin-top: 19px;}
.tips{ font-size: 22px; color: #926f45; display: flex; align-items: center; justify-content: center; margin-top: 6px;}
.tips span{ width: 18px; height: 18px; background-position: -1182px -902px; margin-right: 6px;}
.pop2{ width: 1183px; height: 774px; background: url(../img/pop2.png?v=1.0)  no-repeat; background-size: 100% auto; position: relative; box-sizing: border-box; padding: 90px;}
.pop2 .pop-close{ position: absolute; right: -18px; top: 2px; width: 93px; height: 178px; background-position: -1107px -683px;}
.pop-con{ width: 974px; height: 610px; margin: 0 auto; overflow-x: hidden; overflow-y: auto; text-align: center; font-size: 18px; color: #565149;  box-sizing: border-box; padding: 0 14px;}
.pop-con::-webkit-scrollbar{
    width: 7px;
    height: 7px;
    background-color: #a49984;
	border-radius: 10px;
}
/*定义滚动条轨道 内阴影+圆角*/
.pop-con::-webkit-scrollbar-track {
    border-radius: 10px;
    background-color: #a49984;
}
/*定义滑块 内阴影+圆角*/
.pop-con::-webkit-scrollbar-thumb{
    border-radius: 10px;
    background-color: #50402c;
}
.props-name{ font-size: 16px; color: #151515; line-height: 1;}
.sub-tit{ width: 374px; height: 35px; background-position: 0 -965px; line-height: 35px; text-align: center; text-indent: 0; margin: 20px auto 10px; font-size: 20px; color: #fff8ea;}
.pmch,.pmch2{ width: 723px; height: 135px; background: url(../img/pmch-bg.png)  no-repeat; background-size: 100% auto; margin: 20px 0 10px;}
.pmch:first-child{ margin-top: 0;}
.pmch2{ background-image: url(../img/pmch2-bg.png);}
.num{ width: 160px; height: 100%; display: flex; align-items: center; justify-content: center; margin-left: 6px;}
.num1{ width: 36px; height: 58px; background-position: -831px 0;}
.num2{ width: 45px; height: 58px; background-position: -889px 0;}
.num3{ width: 44px; height: 59px; background-position: -956px 0;}
.num4{ width: 75px; height: 26px; background-position: -925px -79px;}
.num5{ width: 80px; height: 22px; background-position: -825px -79px;}
.pop3{ width: 539px;background: url(../img/pop3.png); background-size: 100% 100%; position: relative; box-sizing: border-box;height: 267px;}
.pop3 .popcont{padding: 26px 80px 0;}
.pop3 .pop-close{ position: absolute;top:17px;right: -8px;}
.pop-tit{font-size: 36px;text-align: center;color: #5c4a33;background-image: -webkit-linear-gradient(top,#5c4a33,#ac9c76);-webkit-background-clip: text;-webkit-text-fill-color: transparent;font-weight: bold;margin-top: 82px;}
.ft30{ font-size: 30px;}
.mt40{ margin-top: 40px;}
.pop-tip{ font-size: 18px;}
.pop-txt{ font-size: 28px; margin: 36px auto 29px;line-height: 36px;}
.btn-confirm,.btn-back{ width: 211px; height: 65px; background-position: -198px -1180px; margin: 0 20px 50px;}
.btn-back{ background-position: -516px -902px;}
.zj-bg{ width: 550px; height: 113px; background: url(../img/zj-bg.png) no-repeat; background-size: 100% auto; margin: 32px auto 36px; font-size: 19px; color: #ffedba; text-align: left;}
.btn-bhzj{ width: 197px; height: 50px; background-position: -774px -826px; margin-left: 60px;}
.zj-bg p{ margin-bottom: 10px;}
.pop-text{font-size: 20px; display: flex; align-items: center; justify-content: center; text-align: center;height: 120px;}
.pop-textcom{height: 230px;}
.pop6 .pop-text{margin-top: 42px;height: 43px;color: #486268;font-size: 24px;font-weight: bold;}
.pop6 .pop-text p{padding-right: 6px;}
.pop6 .pop-text input{width: 264px;height: 43px;background: url(../img/input.png) no-repeat;border:none;font-size: 20px;color: #926f45;text-align: center;}
.btn-copy{margin-left: 4px;width: 147px;height: 45px;background-position: 0 -334px;margin-top: -2px;}
.poptip6{font-size: 16px;color: #926f45;line-height: 20px;margin-top: 14px;}
.popinf6{color: #2e524f;font-size: 16px;line-height: 18px;margin-top: 58px;}
.ewm{ width: 155px; height: 155px; position: absolute;top:114px;left: 136px;}
.ewm img{ width: 100%; height: 100%;}
.general-rank-list thead { white-space: nowrap }
.img-list li { position: relative; }
.img-list li input { position: absolute; z-index: 3; width: 100%; height: 100%; opacity: 0; cursor: pointer; left: 0; top: 0; }
.pop-clipimg { width: 600px; }
.pop-clipimg .img-container { height: 400px; }
.pop-clipimg .pop-close { width: 68px; height: 62px;position: absolute; top: 17px;right: -55px;background-position: -1132px -1228px;}
.cropper-btn {display: -webkit-box;display: -ms-flexbox;display: flex;flex-shrink: 0;-webkit-box-pack: center;-ms-flex-pack: center;justify-content: center;padding: 20px 0;}
.img-list img { width: 100%; height: 100%; position: absolute;left: 0; top: 0; }
#pop8 .box {background-color: #fff; }
#SelectRole { color: #000; text-align: left; padding-left: 20px; font-size: 17px;}
.delebtn {display: none; width: 20px; height: 20px;position: absolute; top: -10px;right: -10px;z-index: 10;color: rgb(221, 103, 103);font-size: 20px;text-indent: 0; border: 3px solid rgb(221, 103, 103); border-radius: 50%;text-align: center; line-height: 20px;font-weight: bold;}
.delebtn.show { display: block;}
.poplogin{width: 673px;height: 397px;background: url(../img/poplogin.png) no-repeat center top;}
.poplogin .popcont{padding: 38px 100px 0;}
.poplogin .pop-close,.popbind .pop-close{right: 0px;top:38px;}
.popbind .pop-close{right: 6px;}
.poplogin .poptit{margin-top: 47px;}
.logintype{margin: 34px auto 0;width: 436px;}
.logintype li{width: 218px;float: left;text-align: center;}
.logintype span{width: 116px;height: 116px;display:block;margin: 0 auto;background: url(../img/login.png) no-repeat;}
.logintype span.wglogin{background-position: top right;}
.logintype p{color: #926f45;font-size: 22px;margin-top: 9px;}
.popbind{width: 757px;height: 477px;background: url(../img/popbind.png) no-repeat;}
.popbind .popcont{padding: 43px 110px 0;}
.popbind .poptit{margin-top: 32px;}
.select-role{ font-size: 22px; color: #486268; margin-top: 8px;}
.select-role select{ width: 281px; height: 39px;text-indent: 0; text-align: center; font-size: 20px; color: #926f45;-webkit-appearance:none;-moz-appearance:none;-o-appearance:none;appearance:none;padding-right: 40px;background: url(../img/select-role.png) no-repeat;border:none;}
.btn-bd{ width: 147px; height: 46px; background-position: 0 -399px; margin: 14px auto 0;}
.pop-name2{ font-size: 22px; color: #486268; margin-top: 13px;text-align: left;padding-left: 30px;}
.pop-name2 span{color: #926f45;}
.pop-tip1{color: #926f45;font-size: 16px;line-height: 20px;margin-top: 13px;}
.pop-tip2{line-height: 18px;font-size: 16px; color: #2e524f; margin: 17px auto 0;}
.popenter,.popselect{width: 766px;height: 876px;background: url(../img/popenter.png) no-repeat;}
.popenter .pop-close,.popselect .pop-close{right: 8px;}
.popenter .popcont,.popselect .popcont{padding: 108px 110px 0;}
.popenter .poptit,.popselect .poptit{margin-top: 28px;}
.popenter .avtarbox{width: 149px;height: 149px;margin-top: 12px;}
.popenter .zwbtn{width: 100%;height: 100%;background: url(../img/zw.jpg) no-repeat;display: block;}
.popenter .avtarbox::after{pointer-events: none;top:-8px;left: -7px;width: 161px;height: 165px;background: url(../img/zwzz.png) no-repeat;}
.xuyanbox{margin: 25px auto 0;width: 495px;height: 358px;background: url(../img/xuyanbox.png) no-repeat;padding: 13px 18px 0;}
.ljbmbtn{width: 211px;height: 65px;background-position: -566px -807px;margin: 20px auto 0;}
.xytitbox{line-height: 44px;text-align: left;height: 44px;}
.xytitbox.mt{margin-top: 15px;}
.xytitbox h3{font-size: 24px;color: #5c4a33;background-image: -webkit-linear-gradient(top,#5c4a33,#ac9c76);-webkit-background-clip: text;-webkit-text-fill-color: transparent;font-weight: bold;float: left;}
.xytitbox span{float: left;font-size: 14px;color: #2e524f;line-height: 20px;padding-top: 15px;}
.inputbox{height: 45px;padding-left: 11px;background: #eae3d2;line-height: 44px;}
.inputbox input{width: 340px;height: 100%;background: none;border: none;color: #5c4a33;float: left;padding: 0;font-size: 18px;}
.inputbox input::placeholder{color: #b4a586;}
.inputbox label{float: left;}
.textareabox{margin-top: 11px;height: 43px;padding:2px 0 0 11px;background: #eae3d2;text-align: left;line-height: 20px;}
.textareabox label{float: left;}
.textareabox label span{color:#b4a586;}
.textareabox textarea{font-size: 16px;line-height: 20px;resize: none;border: none;background: none;width: 380px;padding: 0;color: #5c4a33;font-family:'\5FAE\8F6F\96C5\9ED1';}
.tjbtn{float: right;margin: -3px -4px 0 0;width: 147px;height: 46px;background-position: -630px -884px;}
.signlist{color: #926f45;font-size: 26px;width: 458px;overflow: hidden;margin: 30px auto 0;}
.signlist li{width: 185px;height: 48px;line-height: 48px;float: left;padding-left: 44px;position: relative;text-align: left;cursor: pointer;}
.signlist li::before{width: 31px;height: 48px;content: '';position: absolute;left: 0;top:0;background: url(../img/gou.png) no-repeat;}
.signlist li.on::before{background-position: top right;}
.surebtn{margin: 28px auto 0;width: 211px;height: 65px;background-position: -566px -947px;}
.signtip{font-size: 18px;margin-top: 8px;}
.popselect .xuyanbox{height: 272px;background: url(../img/xuyanbox1.png) no-repeat;}
.popselect .textareabox{padding-top: 15px;height: 55px;margin-top: 0;}
.popselect .textareabox textarea{width: 473px;}
.popselect .xytitbox.mt{margin-top: 2px;}
.popselect .inputboxlong,.popdetail .inputboxlong{height: 84px;text-align: left;}
.popselect .inputboxlong input{width: 473px;line-height: 20px;height: 46px;}
.popselect .inputboxlong a{color: #b3442e;border-bottom: 1px solid #b3442e;display: inline-block;line-height: 24px;vertical-align: top;}
.popbtnbox{padding-top: 28px;font-size: 0;}
.popbtnbox a{display: inline-block;vertical-align: top;width: 211px;height: 65px;margin: 0 20px;}
.xgbtn{background-position: 0 -1032px;}
.ljtjbtn{background-position: -329px -1032px;}
.txtip{color: #926f45;font-size: 16px;line-height: 20px;padding: 43px 30px 0;text-align: left;}
.popupload{width: 1055px;height: 496px;background: url(../img/popupload.png) no-repeat;}
.popupload .poptit{display: none;}
.popupload .popcont{padding: 59px 149px 0;}
.uplebar{width: 361px;padding-top: 107px;float: left;}
.uplebar .upload{margin-bottom: 0;width: 145px;height: 145px;margin: 0 auto;}
.uplebar .upload li{width: 145px;height: 145px;margin: 0;background: url(../img/up.png) no-repeat;}
.uptxt{font-size: 16px;color: #926f45;line-height: 20px;padding: 25px 18px 0;text-align: left;}
.uptxt p{padding-bottom: 10px;}
.upribar{width: 324px;padding: 68px 0 0 22px;float: left;}
.upribar .avtarbox,.popdetail .avtarbox{width: 107px;height: 107px;}
.upribar .avtarbox::after,.popdetail .avtarbox::after{width: 119px;height: 119px;background: url(../img/upribarzz.png) no-repeat;top:-6px;left: -7px;}
.qrtjbtn{margin: 46px auto 0;width: 147px;height: 46px;background-position: 0 -1117px;}
.upribar .uptxt{padding-top: 12px;}
.popdetail{width: 734px;height: 913px;background: url(../img/popdetail.png) no-repeat;}
.popdetail .popcont{padding: 97px 86px 0;}
.popdetail .pop-close{right: 0;}
.popdetail .rankpai{width: 320px;line-height: 39px;height: 39px;font-size: 24px;font-weight: bold;color: #5c4a33;background-image: -webkit-linear-gradient(top,#5c4a33,#ac9c76);-webkit-background-clip: text;-webkit-text-fill-color: transparent;}
.popdetail .examined{padding-top: 14px;position: relative;}
.popdetail .topbar{height: 128px;}
.popdetail .avtarbox{float: left;margin-left: 42px;}
.popdetail .avtarbox::after{background: url(../img/popdetailzz.png) no-repeat;}
.popdetail .avtarinf{padding-left: 20px;float: left;color: #475e5c;font-size: 18px;margin-top: 28px;line-height: 25px;width: 193px;text-align: left;}
.popdetail .avtarinf span{color: #807657;}
.popdetail .hotbox{width: 128px;float: left;margin-top: -15px;}
.popdetail .hot{margin-top: -26px;width: 87px;height: 87px;background-position: 0 -54px;}
.popdetail .hot.on{background-position: 0 -159px;}
.popdetail .hotnum{margin-top: 11px;width: 100%;height: 25px;border-radius: 12px;background: #eae3d3;line-height: 25px;font-size: 20px;color: #475e5c;}
.popdetail .hotnum span{color: #807657;}
.popdetail .sharebox{height: 30px;line-height: 30px;width: 522px;color: #926f45;font-size: 16px;position: absolute;top:103px;left: 0;z-index: 3;margin-top: 0;padding-right: 0;}
.popdetail .wxbtn{margin-right: 7px;width: 28px;height: 30px;background-position: -712px -1061px;}
.popdetail .qqbtn{width: 30px;height: 30px;background-position: -747px -1061px;}
.popdetail .zhanshibox{width: 495px;height: 261px;background: url(../img/zhanshibox.png) no-repeat;margin: 0 auto;padding: 8px 18px 0;}
.zhanshibox .textareabox{margin-top: 0;height: 44px;padding: 13px 11px;font-family:'\5FAE\8F6F\96C5\9ED1';}
.zhanshibox .xytitbox.mt{margin-top: 2px;}
.zhanshibox .inputboxlong{padding-left: 0;}
.zhanshibox .inputboxlong p{line-height: 20px;padding: 13px 11px 0;height: 40px;}
.zhanshibox .inputboxlong a{color: #b3442e;font-size: 18px;border-bottom:1px solid #b3442e;display:inline-block;vertical-align: top;line-height: 24px;margin-left:10px;}
.popdetail .signbox{margin:17px auto 0;width: 531px;height: 241px;background: url(../img/signbox1.png) no-repeat;}
.popdetail .signbox .showtxt{padding: 9px 0 0 178px;}
.popdetail .signbox .btn-ztgd{width: 133px;height: 37px;background-position: -328px -1126px;}
.popdetail .signbox .btn-ztgd.open{background-position: -520px -1126px;}
.popdetail .signbox .roll li:nth-child(2n){margin-left: 184px;}
.popdetail .signbox .roll li:nth-child(3n){margin-left: 244px;}
.popdetail .signbox .roll li:nth-child(4n){margin-left: 354px;}
.popdetail .signbox .roll li:nth-child(5n){margin-left: 44px;}
.popdetail .signbox .roll li:nth-child(6n){margin-left: 204px;}
.popdetail .signbox .roll li:nth-child(7n){margin-left: 74px;}
.popdetail .signbox .roll li:nth-child(8n){margin-left: 304px;}
.popdetail .signbox .roll li:nth-child(9n){margin-left: 124px;}
.popdetail .tiebtn{width: 147px;height: 46px;background-position: 0 -1177px;margin: 5px 0 0 375px;}
.popwx{width: 721px;height: 353px;background: url(../img/popwx.png) no-repeat;}
.popqq{width: 725px;height: 418px;background: url(../img/popqq.png) no-repeat;}
.popqq .ewm{top:96px;left: 294px;}
.popcn,#pop11,.sharepop{display: none;}
.surebtnbox a{margin: 0 14px;width: 151px;height: 46px;background: url(../img/surebtnbox.png) no-repeat;}
.surebtnbox a.btn-confirm{background-position: left top;}
.surebtnbox a.btn-back{background-position: right top;}
.popsimple{width: 816px;height: 925px;background: url(../img/popsimple.png) no-repeat;}
.popsimple .popcont{padding: 59px 75px 0;}
.popsimple .pop-close{top:44px;right: -7px;}
.popsimple .poptit,.popall .poptit{line-height: 78px;height: 78px;margin-top: 35px;}
.popsimple .poptit::after,.popsimple .poptit::before,.popall .poptit::after,.popall .poptit::before{margin-top: 18px;}
.popsimple .poptit::before,.popall .poptit::before{margin-right: 60px;}
.popsimple .poptit::after,.popall .poptit::after{margin-left: 60px;}
.popsimple .poptit span,.popall .poptit span{font-size: 56px;}
.popall{width: 1093px;height: 834px;background: url(../img/popall.png) no-repeat;}
.popall .popcont{padding: 59px 104px 0 87px;}
.popall .pop-sub,.popall .pop-p{padding-right: 20px;}
.upname{margin-top: 21px;width: 162px;height: 39px;background-position: 0 -78px;}
.popall .pop-p span,.popall .pop-p em{display: block;width: 575px;text-indent: 7.95em;}
.popall .pop-p span.nobreak{display:inline;text-indent: 0;}
.bangtxicon{top:-2px;left: 622px;width: 165px;padding-top: 8px;}
.bangtxicon .avtarbox{width: 152px;height: 152px;}
.bangtxicon img{border: 1px solid #ddd3b6;border-radius: 50%;}
.bangtxicon .avtarbox::after{width: 165px;height: 198px;top:-8px;left:-6px;background: url(../img/bangtxiconzz.png) no-repeat;}
.bangbox p{width: 575px;}
.addbgbox{margin: 22px auto 0;width: 784px;height: 236px;background: url(../img/addbgbox.png) no-repeat;padding: 49px 34px 0;}
