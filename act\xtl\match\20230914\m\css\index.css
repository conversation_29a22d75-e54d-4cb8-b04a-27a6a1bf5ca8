@charset "utf-8";
* { -webkit-tap-highlight-color: rgba(0, 0, 0, 0) }
body{background-color:#112023;}
.none{display:none;}
.bar{width:100%;margin:0 auto;}
.fl{float:left;display:inline;}
input::-webkit-outer-spin-button,input::-webkit-inner-spin-button{-webkit-appearance: none !important;}
input[type="number"]{-moz-appearance:textfield;}
select option { color: #7A3333 }
.wrap{color:#e0ddbe;font-size:20px;}
.pr{position: relative;}
.pa{position: absolute;}
.head{height:804px;background:url(../img/head.jpg) no-repeat;position:relative;}
.logo{width: 162px;height: 75px;position: absolute;z-index: 2;background: url(../img/logo.png) no-repeat;top:29px;left: 26px;text-indent:-9999em;overflow: hidden;}
.com,.btn,.like,.bh-pm{background:url(../img/com.png) no-repeat; display: block; background-size: 1200px auto; text-indent:-9999em;}
.btn{background:url(../img/btn.png) no-repeat;}
.tit{width: 459px;height: 127px;background: url(../img/tit.png) no-repeat;margin: 0 auto;text-align: center;}
.flex{ display: flex}
.flex1{ flex: 1;}
.flex-jc{ display: flex; justify-content: center;}
.flex-je{ display: flex; justify-content: space-evenly;}
.flex-ac{ display: flex; align-items: center;}
.flex-jc-ac{ display: flex; justify-content: center; align-items: center;}
.flex-fdc-ac-jc{ display: flex;flex-direction: column; align-items: flex-end; justify-content: center;}
.btn,.bh-ckxq,.btn-first,.btn-last,.pop-close{transition:all .2s;}
.btn:hover,.bh-ckxq:hover,.btn-ztgd:hover,.btn-first:hover,.btn-last:hover,.pop-close:hover{filter:brightness(1.1);}
.time{ width: 265px; height: 31px; line-height: 31px; background-position: -565px -1243px; text-indent: 0; text-align: center; font-size: 22px; color: #8c2f19; position: absolute; top: 360px; left: 310px;}
.btn-home{ width: 204px; height: 49px; background-position: -774px -508px; position: absolute; top:70px;right:36px;}
.page1 .btn-home{ position: relative; margin: 15px auto 0; top: 0; left: 0;}
.login-detail{ position: absolute; right: 30px; top: 30px; font-size: 18px; color: #fff0da;}
.login-detail a{ color: #fff0da;}
#cy_bot{padding:10px!important;}
.cyLogo{margin-top:10px;}
/**/
.nav{ position: fixed; z-index: 99; top: 30%; right: 0; width: 312px; height: 597px; background: url(../img/nav-bg.png) no-repeat; background-size: 100% auto; box-sizing: border-box; padding: 24px 0 0;}
.nav-item{ font-size: 30px; color: #c87471; text-align: center; display: block;  line-height: 94px;}
.nav-item.act{ color: #f0e094;}
.nav-top{ width: 184px; height: 84px; background-position: 0 -1206px;  margin: 36px auto 0;}
.cont1{overflow: hidden; margin-top: -1px;}
.page1 .cont1{ height: 1750px; background: url(../img/cont1-page1.jpg) center top;padding-top: 43px;}
.bg{height: 6796px;background: url(../img/bg.jpg) no-repeat center top;}
.bg .tit{background-position: 0 -767px;}
.bg .tit.t1{margin: 153px auto 0;width: 527px;height: 112px;background: url(../img/addtit.png) no-repeat;}
.tit1{ width: 576px; height: 95px; margin: 46px auto 10px;}
.tit-tip{ width: 598px; margin: 0 auto; text-align: center; font-size: 18px; color: #171514;}
.rank-bar{ position: relative; width: 750px; height: 667px; background: url(../img/rank-bg.png) no-repeat; background-size: 100% auto; position: relative; box-sizing: border-box; padding-top: 50px; padding-left: 72px;}
.rank-tit{ width: 487px; height: 31px; line-height: 31px; font-size: 18px; color: #5a4f42; text-align: center;}
.rank-table{ width: 487px; text-align: center; display: flex; flex-direction: column; text-align: center; background: url(../img/thead.png) no-repeat center top;}
.rank-table table{ width: 100%;}
.bh-rankbar .rank-table{ width: 678px; margin: 0 auto; background-size: 100% auto;}
.rank-th{  height: 34px; line-height: 34px; box-sizing: border-box; font-size: 16px; color: #fffdba; text-align: center;}
.bh-rankbar .rank-th{ height: 46px; line-height: 46px;}
th,td{ text-align: center;}
.rank-table tbody tr{ height: 27px; line-height: 27px; background: #ebe3d7; font-size: 16px; color: #151515;}
.bh-rankbar tbody tr{ height: 36px; line-height: 36px;}
.rank-table tbody tr:nth-child(odd){ background: #c5bcab;}
.rank-table .bh-tbody tr{ background: #eaeaea;}
.rank-table .bh-tbody tr:nth-child(odd){ background: #b7b7b7;}
.rank-tip{ width: 487px;text-align: center; font-size: 22px; color: #a6bdb8; display: flex; align-items: center; justify-content: center; margin: 23px auto 0;line-height: 26px;}
.pagination{ width: 100%; margin-top: 30px; display: flex; justify-content: center; align-items: center; font-size: 22px; color: #e0ddbe;height: 86px;}
.pagination a{ margin: 0 7px;}
.pagination p{padding: 0 16px;}
.btn-first,.btn-last,.btn-prev,.btn-next{ width: 87px; height: 30px; line-height: 30px; text-align: center; background-position: -258px 0; font-size: 22px; color: #e0ddbe; background: #163e3d;}
.btn-first:hover,.btn-last:hover,.btn-prev:hover,.btn-next:hover{width: 85px; height: 28px;line-height: 28px;border: 1px solid #fffbbe;background-image: -webkit-linear-gradient(top,#163e3d,#667b5e);}
.role{ width: 213px; height: 620px; position: absolute; right: -16px; top: 36px;}
.btn-jlxq{ width: 162px; height: 35px; position: absolute; right: 42px; bottom: 64px;}
/**/
.bangtxt{text-align: center;color: #dcb565;font-size: 24px;line-height: 34px;font-weight: bold;}
.bangtxt p{background-image:-webkit-linear-gradient(top,#deb96a,#fbf7bf);-webkit-background-clip:text;-webkit-text-fill-color:transparent;}
.track-tab{ width: 689px; height: 49px; background-position: 0 -215px; margin: 0 auto; display: flex;}
.track-tab1{ background-position: 0 -320px;}
.track-tab a{ display: block; flex: 1; height: 100%;}
.track-time{ font-size: 22px; color: #413d37; display: flex; align-items: center; justify-content: center; margin-top: 18px;}
.tit2{ width: 602px; height: 107px; background-position: 0 -138px; margin: 10px auto 0;}
.bg .login{width: 100%;margin-top: 21px;}
.login-box{ text-align: center; font-size: 22px; color: #a6bdb8;height: 60px;line-height: 30px;}
.login-box a{ color: #a6bdb8; border-bottom: 1px solid #a6bdb8;}
.login-bar{ display: flex; flex-direction: column; align-items: center;background: url(../img/login-bg.png) no-repeat; background-size: 100% auto; margin: -39px auto 0; overflow: hidden;position: relative;padding: 41px 0 0 330px;justify-content: flex-start;height: 215px;}
.bhicon{background: #fff;border-radius: 50%;width: 227px;height: 227px;top:14px;left: 35px;display: none;}
.btn-change{ font-size: 24px; color: #bead6c; margin-top: 10px;}
.btn-change a{color: #bead6c;border-bottom: 1px solid #f7f5cb;}
.login-con{width: 100%; box-sizing: border-box; padding: 10px 0;  display: flex; align-items: center;font-size: 22px; color: #fff1cb;}
.login-con p{
    line-height: 35px;
		font-size: 24px;
}
.login-con div p,.btn-change{background-image:-webkit-linear-gradient(top,#ccbe83,#f7f5cb);-webkit-background-clip:text;-webkit-text-fill-color:transparent;}
.btn-join{ width: 243px; height: 62px;background-position: 0 -425px;transition: none;margin: -23px auto 0;}
/*.btn-join1{ }*/
.btn-join2{ background-position: -258px -425px;}
.btn-join3{ background-position: -516px -425px;}
.btn-join4{ background-position: 0 -508px;}
.btn-join5{ background-position: -258px -508px;}
.btn-join6{ background-position: -516px -508px;pointer-events: none;}
.bhnr{ width: 690px; height: 992px; background: url(../img/bhnr-bg.png) no-repeat; background-size: 100% auto; margin: 61px auto 0;}
.page1 .bhnr{ width: 695px; height: 1666px;margin: 0 auto;background: none;}
.bh-rank{ width: 100%; height: 60px; line-height: 60px; background-position: 1000px 1000px; text-indent: 0; font-size: 32px; color: #feedab; text-align: center; box-sizing: border-box;display: flex; align-items: center; justify-content: center;}
.bh-rank p{font-size: 32px; color: #feedab;background-image:-webkit-linear-gradient(top,#feedab,#fff7d8);-webkit-background-clip:text;-webkit-text-fill-color:transparent;font-weight: bold;}
.page1 .bh-rank{padding-top: 128px;height: 182px;line-height: 54px;width: 219px;}
.bh-rank span{color: #6c470d;font-weight: bold;width: 65px;height: 62px;background: url(../img/rankbg1.png) no-repeat;line-height: 56px;margin-top: -6px;}
.page1 .bh-rank span{width: auto;height: auto;background: none;margin-top: 0;line-height: 54px;height: 54px;color: #feedab;background-image:-webkit-linear-gradient(top,#feedab,#fff7d8);-webkit-background-clip:text;-webkit-text-fill-color:transparent;font-weight: bold;}
.bh-box{width: 688px; height: 449px; position: relative;}
.bh-box::after{content: '';position: absolute;z-index: 2;pointer-events: none;width: 688px; height: 385px; background: url(../img/kuang.png) no-repeat;z-index: 0;top:0;}
.bh-swiper{ width: 100%; height: 385px; overflow: hidden;
    -webkit-mask: no-repeat center / contain;
    mask: no-repeat center / contain;
    -webkit-mask-image: url(../img/lb.png);
    mask-image: url(../img/lb.png);
}
.bh-swiper-prev,.bh-swiper-next{  cursor: pointer; width: 36px; height: 36px; background-position: -312px -591px; position: absolute; right: 18px; bottom: 30px;display: none;}
.bh-swiper-next{ background-position: -394px -591px; bottom: 76px}
.swiper-pagination{ width: 100%; height: 28px;position: absolute; left: 0;top: 396px; display: flex; justify-content: center; align-items: center;}
.swiper-pagination-bullet{ width: 19px; height: 28px; background: url(../img/swidot.png) no-repeat; opacity: 1; margin: 0 11px; position: relative;}
.swiper-pagination-bullet-active{width: 29px;height: 28px; box-sizing: border-box;background-position: top right;}
.btn-ckxq{ width: 219px; height: 56px; background-position: -258px -65px;position: absolute;top:371px;right: 27px;}
.bhmc,.fwqmc{ font-size: 26px; color: #bead6c;position: absolute;left: 14px;line-height: 37px;top:363px;}
.fwqmc{top:400px;}
.bhmc p,.fwqmc{background-image:-webkit-linear-gradient(top,#cbbd81,#f7f4ca);-webkit-background-clip:text;-webkit-text-fill-color:transparent;}
.tit-dec1{ width: 97px; height: 7px; background-position: -516px -591px; margin-right: 10px;display: none;}
.tit-dec2{ width: 97px; height: 7px; background-position: -653px -590px; margin-left: 10px;display: none;}
.tjly{margin: 41px auto 0; box-sizing: border-box;width: 667px;}
.tjly-tit{height: 40px;padding-right: 4px;}
.btn-ztgd{ width: 147px; height: 40px;background-position: -796px -168px;float: right;}
.btn-ztgd.open{background-position: -796px -235px;}
.roll{height: 250px; overflow: hidden;margin: 21px 6px 0;}
.roll li{height: 50px;line-height: 50px;}
.roll p{ padding: 0 15px; background:#182e2f; border-radius: 20px;font-size: 21px; color: #dbc79b;display:inline-block;vertical-align: middle;height: 40px;line-height: 40px;}
a.like{ padding-left: 20px; flex: 1; height: 15px; line-height: 15px; background-position: -1183px -215px; font-size: 16px; color: #5b2c2c; text-indent: 0;}
.sy-bg{ width: 549px; height: 80px; background-position: 1000px 1000px; text-indent: 0; margin: 0 auto; display: flex; align-items: center; position: relative; box-sizing: border-box; padding-left: 126px; font-size: 22px; color: #eceacf;border-radius: 40px;background: rgba(15,46,49,.6);}
.sy-bg p{width: 248px;}
.sy-bg p em{color: #fffba8;}
.page1-sy{margin: 29px 0 0 75px;width: 548px;height: 51px;border:1px solid #cfb374;background: #0d2122;border-radius: 0;padding-left: 50px;}
.page1-sy p{width: 294px;}
.btn-dz{ display: block; width: 134px; height: 134px; background-position: 0 -188px; position: absolute; top: -26px; left: -27px;}
.btn-dz.mr{background-position: -234px -188px;}
.detailbg .btn-dz{top:-40px;left:-84px;}
.btn-qq,.btn-wx{ width: 66px; height: 66px; background-position: -582px -222px; margin-left: 15px;}
.btn-wx{ background-position: -501px -222px;}
.btn-sjsyz{ width: 204px; height: 49px; background-position: -774px -320px;}
/**/
.bh-btns{ display: flex; align-items: center; justify-content: center; margin-top: 26px;flex-wrap: wrap;}
.addrankbox{width: 100%;display: flex; align-items: center; justify-content: center;height: 42px;padding-bottom: 28px;}
.btn-zxyx,.btn-pmyx{ width: 197px; height: 42px; line-height: 42px; background-position: -412px 0; text-indent: 0; text-align: center; font-size: 26px; color: #fff7d7; margin: 0 22px;}
.btn-yx{ background-position: -532px -72px; color: #f9ef92;}
.bh-input{ width: 337px; height: 47px; line-height: 47px; background-position: -774px -392px; text-indent: 0; border: none; box-sizing: border-box; margin: 0 9px; font-size: 24px; color: #fff7d7;
    display: flex; align-items: center; padding-left: 20px;
}
.bh-input p{ font-size: 24px; color: #fff7d7;}
.bh-input select{ flex: 1; height: 47px; font-size: 24px; color: #fff7d7;
    border: none; background: transparent; -webkit-appearance:none;
    -moz-appearance:none;
    -o-appearance:none;
    appearance:none;}

.bh-list{ display: flex; flex-wrap: wrap; box-sizing: border-box; padding-top:20px;justify-content: center;}
.bh-item{ width: 337px; height: 472px; background: url(../img/bhpic-bg.png) no-repeat; background-size: 100% auto; box-sizing: border-box;overflow: hidden; margin: 10px 8px 11px;}
.bh-pm{ width: 100%; height: 51px; line-height: 51px; background-position: 1000px 1000px; font-size: 30px; color: #feedab; text-indent: 0; text-align: center;font-weight: bold;}
.bhpic{ position: relative; width: 318px; height: 207px;margin: 11px auto 0; box-sizing: border-box;border:1px solid #9a8654;}
.bhpic img{ width: 100%; height: 100%;
    -webkit-mask: no-repeat center / contain;
    mask: no-repeat center / contain;
    -webkit-mask-image: url(../img/bhpic.png);
    mask-image: url(../img/bhpic.png);
    /*background: red;*/
}
.bh-ckxq{ position: absolute; left: 50%; margin-left: -90px; bottom: -20px;width: 180px; height: 40px;background: url(../img/bh-ckxq.png) no-repeat;text-indent:-999em;}
.bh-ckxq .dec{ width: 4px; height: 4px;display: none;}
.bhname{ text-align: center; font-size: 22px; color: #eceacf; margin: 36px 0 0 0!important;line-height: 26px;max-width: 100%!important;}
.fwname{ display: flex; align-items: center; justify-content: center; font-size: 22px; color: #eceacf;line-height: 26px;}
.fwname span{max-width: 100%!important;}
.fwname .dec{ background: #ffd5b6;display: none;}
.bh-dzbg{ width: 283px; height: 60px; text-indent: 0; background-position: 1000px 1000px; display: flex;align-items: center; position: relative; margin: 25px 0 0 45px; box-sizing: border-box; padding-left: 62px; font-size: 22px; color: #eceacf;background: rgba(15,46,49,.6);border-radius: 30px;}
.btn.bh-dz,.btn.btn-qq2,.btn.btn-wx2{background: url(../img/com.png) no-repeat;}
.bh-dz{ width: 123px; height: 123px; background-position: -161px -872px!important; position: absolute; top: -32px; left: -56px;}
.bh-dz.mr{background-position: 0 -892px!important;width: 103px; height: 103px;top: -22px; left: -46px;}
.bh-dzbg p em{font-size: 22px!important;color: #fffba8;display:block;letter-spacing: 0;}
.bh-dzbg p{width: 102px;}
.btn-qq2,.btn-wx2{ width: 48px; height: 48px; background-position: -60px -622px!important;}
.btn-wx2{ background-position: 0 -622px!important;margin-left: 12px;}
.hr{ width: 686px; height: 12px; background-position: 0 -988px; margin: 38px auto;}
.libao{ width: 687px; height: 724px; background: url(../img/libao-bg.png) no-repeat; background-size: 100% auto; margin: 41px auto 0;}
.libao3{ width: 687px; height: 1025px; background-image: url(../img/libao3-bg.png);margin: 29px auto 0;}
.libao2{ width: 337px; height: 382px; background-image: url(../img/libao2-bg.png);}
.libao-tit{ line-height: 50px; text-align: center; color: #f7ecd8; font-size: 26px;display: none;}
.libao2 .libao-tit{ font-size: 18px; line-height: 38px;}
.libao-tip{ display: flex; align-items: center; justify-content: center; font-size: 29px; color: #ffe9a1;line-height: 36px;padding-top: 48px;}
.libao2 .libao-tip{ font-size: 18px;}
.libao-tip.none{display: none;}
.lbaddbgbox{width: 650px;height: 162px;margin: 7px auto 0;border:1px solid #3a473c;background: #14292a;position: relative;}
.lbaddbgbox.gygl::after{content: '';position: absolute;top:-1px;left: -1px;z-index: 2;width: 41px;height: 151px;background: url(../img/gyglzs.png) no-repeat;}
.libao-tip .dec{ background: #fff6e8; margin: 0 14px;}
.libaopic{ width: 160px; height: 134px; flex-shrink: 0; text-indent: 0; background-position: top right; margin: 15px 19px 0 0;}
.libaomc{ width: 428px; font-size: 22px; color: #eceacf; text-align: left;line-height: 26px;height: 78px;}
.btn-ljlq,.btn-ljlqed{ width: 238px; height: 57px; background-position: -481px -616px;transition: none;}
.btn-ljlqed{ background-position: -481px -686px;pointer-events: none;}
.btn-ljlqno{ background-position: -477px -760px;pointer-events: none;}
.libao-rule{ display: flex; font-size: 16px; color: #ffe4bd; margin: 10px 24px;}
.libao-rule .dec{ background: #ffe4bd; margin: 8px;}
.libao-pic{ width: 316px; height: 185px; box-sizing: border-box; padding: 4px 0 8px 0; background: url(../img/libaokuang2.png) no-repeat; background-size: 100% auto; margin: 0 auto;}
.libao-pic2{ margin: 50px auto 20px;}
.libao-pic img{ background: red; width: 100%; height: 100%;
    -webkit-mask: no-repeat center / contain;
    mask: no-repeat center / contain;
    -webkit-mask-image: url(../img/libaopic2.png);
    mask-image: url(../img/libaopic2.png);}
.btn-qwjd{ width: 168px; height: 43px; background-position: -516px -697px; margin: 12px auto 0;}
.btn-ljck{ width: 168px; height: 43px; background-position: -516px -759px; margin: 12px auto 0;}
.bag-bg{ width: 686px; height: 199px; background: url(../img/bag-bg.png) no-repeat; background-size: 100% auto; margin: 20px auto 0; overflow: hidden; box-sizing: border-box; padding-top: 20px;}
.bag-bg .libao-rule{ font-size: 18px; color: #fff0da;}
.collibao1{margin: 33px auto 0;width: 687px;height: 584px;background: url(../img/collibao1.png) no-repeat center top;padding-top: 64px;}
.collibao2{margin: 37px auto 0;width: 687px;height: 102px;background: url(../img/collibao2.png) no-repeat center top;padding-top: 64px;}
.collibao3{margin: 25px auto 0;width: 687px;height: 102px;background: url(../img/collibao3.png) no-repeat center top;padding-top: 64px;}
.explain2{ height: 336px; background-image: url(../img/explain2-bg.png); margin: 0 auto;}
.explain-tit{ font-size: 26px; color: #f5dfa2; text-align: center; margin: 16px 0 10px; display: flex; align-items: center; justify-content: center;display: none;}
.explain-tit .dec{ background: #f5dfa2;}
.explain p{ font-size: 18px; color: #fff0da; margin: 6px 0;}
.for-example{ display: flex; align-items: flex-start; color: #f5dfa2; margin: 14px 0;}
.for-example p{ display: flex; align-items: center;  color: #f5dfa2; margin: 0;}
.for-example .dec{ background: #f5dfa2;}
.libao-sub{ font-size: 28px; text-align: center; color: #ffefc3; margin: 10px 0;}
.libao2 .libao-sub{ font-size: 20px;}
.page1-exexplain{ width: 642px; height: 336px;margin: 185px auto 76px;}
.page1-exexplain p{ margin: 10px 0;}
.btn-qwcx{ display: block; width: 204px; height: 49px;background: url(../img/btn-qwcx.png) no-repeat;}
.explain .mt30{ margin-top: 30px;}
.colol-ff7e7e{ color: #ff7e7e;}
.underline{ border-bottom: 1px solid #f5dfa2;}
/**/
.tit3{ width: 602px; height: 107px; background-position: 0 -286px; margin: 10px auto 40px;}
.wdbh{ width: 678px; height: 535px; background: url(../img/wdbh.png) no-repeat; display: flex; justify-content: space-evenly; background-size: 100% auto; margin: 30px auto 0; text-align: center;}
.wdbh-tit{ font-size: 18px; color: #fff8ea; line-height: 35px;}
.wd1{ width: 308px; height: 358px; box-sizing: border-box; padding-top: 26px; font-size: 22px; color: #292929; line-height: 68px;}
.wdbgmc{ font-size: 26px; color: #0d0d0d; display: flex; align-items: center; justify-content: center; margin-top: 4px; white-space: nowrap;}
.wdbgmc .dec{ background: #0d0d0d;}
.wd2{ width: 308px; height: 336px; font-size: 22px; color: #343434; box-sizing: border-box; padding-top: 26px;}
.wd2-tip{ font-size: 18px; color: #6a6a6a; margin: 4px 0 10px;}
.wd2-tip .dec{ background: #6a6a6a;}
.wd-libao{ width: 160px; height: 159px; background-position: -774px -591px; margin: 20px auto 10px;}
.libao-p{ width: 280px; font-size: 18px; margin: 0 auto; text-align: left;}
.wd3{ width: 100%; height: 138px;}
.btn-qq3,.btn-wx3{ width: 76px; height: 77px; background-position: -940px -591px; margin: 10px 20px;}
.btn-wx3{ background-position: -1064px -591px;}
.table-tit{ display: flex; justify-content: space-between; font-size: 18px; line-height: 30px; color: #494949; margin-top: 30px;}

.tit4{ width: 662px; height: 119px; background-position: 0 -434px; margin: 40px auto 30px;}

/**/
.tit5{ width: 576px; height: 153px; background-position: 0 -601px; margin: 150px auto 0;}
.rule-bar{ width: 738px; height: 263px; background: url(../img/rule-bar.png) no-repeat; background-size: 100%; margin: 30px auto 10px; display: flex; justify-content: space-between; text-align: center; font-size: 20px; color: #47362a; box-sizing: border-box; padding: 140px 70px 0;}
.btn-ljtj{ width: 138px; height: 35px; background-position: -516px -621px; margin-top: 14px;}
.rule{ width: 700px; margin: 0 auto; font-size: 20px; color: #47362a;}
.rule p{ margin: 10px 0; display: flex;}
.dot{ display: block; flex-shrink: 0; width: 6px; height: 6px; border-radius: 50%; background: #47362a; margin: 10px;}
.tit7{ width: 427px; height: 36px; background-position: 0 -894px; margin: 40px auto 20px;}

/* .first-say-list p, .reason-list p { width: 580px;overflow: hidden;white-space: nowrap; text-overflow: ellipsis; } */
.ellipsis { overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }
.rect-story { word-wrap: break-word; text-indent: 2em; }
.role-info { text-align: center;color:#a6bdb8;display: none;}
.role-info a { color: #a6bdb8; }
.sect-name { display: inline-block; max-width: 200px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; vertical-align: bottom; text-align: left;}
.first-sect-name { max-width: 240px; display: inline-block; vertical-align: bottom;}
/*index s*/
.bg1{background: url(../img/bg1.jpg) no-repeat;padding-bottom: 150px;}
.tit p{color: #f2eaba;font-size: 20px;padding-top: 32px;}
.tip{color: #a6bdb8;font-size: 22px;text-align: center;margin-top: 11px;height: 78px;}
.bg .tip{height: 39px;}
.bgcont1{height: 1833px;}
.batul{display:flex;justify-content: center;}
.batul li{margin: 0 25px;text-indent:-999em;overflow: hidden;width: 297px;height: 71px;background: url(../img/batul.png) no-repeat;}
.batul li:first-child{background-position: bottom right;}
.batul li:last-child{background-position: top right;}
.batul li:first-child.on{background-position: top left;}
.batul li:last-child.on{background-position: bottom left;}
.bg1 .batdistab{display: none;}
.bg1 .batdistab.dis{display:block;}
.clobox{width: 100%;padding-top: 230px;background: url(../img/gerenbg.png) no-repeat;}
.clobox.fr{background: url(../img/teambg.png) no-repeat;}
.txbox{width: 118px;height: 118px;position: relative;margin: 0 auto;}
.txbox img{width: 118px;height: 118px;border-radius: 50%;}
.txbox::after{content: '';width: 162px;height: 199px;z-index: 2;position: absolute;background: url(../img/zz.png) no-repeat;top:-70px;left:-33px;}
.clobox.fr .txbox::after{background: url(../img/frzz.png) no-repeat;}
.showinf{color: #785922;text-align: center;font-size: 20px;line-height: 24px;margin: 22px auto 0;width: 322px;}
.showinf p{height: 22px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;}
.clobox.fr .showinf{color: #784522;}
.showad{color: #785922;font-size: 20px;line-height: 24px;margin: 13px auto 0;height: 72px;width: 322px;display: -webkit-box;white-space: normal;text-overflow: ellipsis;-webkit-line-clamp: 3;-webkit-box-orient: vertical;word-break: break-all;overflow: hidden;}
.clobox.fr .showad{color: #ffdbb8;}
.showicon{width: 322px;color: #785922;font-size: 20px;line-height: 24px;margin: 19px auto 0;height: 96px;display: -webkit-box;white-space: normal;text-overflow: ellipsis;-webkit-line-clamp: 4;-webkit-box-orient: vertical;word-break: break-all;overflow: hidden;}
.clobox.fr .showicon{color:#784522;}
.shownum{margin: 23px auto 0;width: 225px;height: 36px;color: #774116;font-size: 20px;line-height: 36px;background: #fffec0;border:1px solid #eccf6c;text-align: center;border-radius: 15px;}
.clobox.fr .shownum{color:#774116;}
.tablebox{margin: 113px auto 0;width: 690px;background: url(../img/bgbg.png) no-repeat;background-size: cover;}
.tablebox table{width: 100%;}
.tablebox th,.tablebox td{border:1px solid #8e957c;text-align: center;height: 48px;color: #e0ddbe;font-size: 22px;}
.tablebox th{background-image:-webkit-linear-gradient(top,#0d2525,#174a4a);}
.tablebox td{height: 37px;font-size: 20px;}
.tpage{text-align: center;height: 32px;margin-top: 35px;line-height:1;font-size: 22px;}
.tpage a{color: #e0ddbe;font-size: 16px;background: #163e3d;padding: 8px 6px;display:inline-block;vertical-align: top;}
.tpage a,.tpage span{margin: 0 2px;}
.tpage a.cur,.tpage span{border: 1px solid #fffbbe;padding: 4px 5px;background: #163e3d;background-image:-webkit-linear-gradient(top,#163e3d,#667b5e);display:inline-block;vertical-align: top;}
.showtip{text-align: center;line-height: 26px;margin-top: 21px;font-size: 20px;}
.tqbox{width: 691px;height: 576px;background: url(../img/tqbox.png) no-repeat;margin: 0 auto;}
.jdingbtn{width: 277px;height: 71px;top:431px;right: 200px;}
.jlhd{margin: 324px auto 0;text-align: center;font-size: 0;}
.jlhd li{display: inline-block;vertical-align: top;margin: 0 25px;background: url(../img/jlhd.png) no-repeat;width: 297px;height: 71px;cursor: pointer;}
.jlhd li.li0{background-position: 0 0;}
.jlhd li.li1{background-position: top right;}
.jlhd li.li0.on{background-position: bottom left;}
.jlhd li.li1.on{background-position: bottom right;}
.distab{padding-top: 63px;display: none;}
.distab.dis{display: block;}
.tit.t1,.tit.t2{width: 432px;height:108px;text-indent: -9999em;background-position:0 -166px;}
.tit.t2{background-position:0 -294px;}
/*index e*
/*single s*/
.bg2{height: 4220px;background: url(../img/bg2.jpg) no-repeat;}
.bg2 .tit{background-position: 0 -473px;}
.bg2 .tip{height: 61px;}
.loginbk{width: 100%;margin: 0 auto;text-align: center;font-size: 22px;line-height: 46px;}
.ablesend{color: #e0ddbe;font-size: 28px;float:none;height: 46px;display:inline-block;background: #0a1518;padding: 0 25px;line-height: 46px;border-radius: 23px;margin-bottom: 6px;}
.loginbk span{color: #e4e088;}
.loginbk a{color: #e0ddbe;border-bottom: 2px solid #91917d;}
.bminfbox{width: 689px;height: 781px;margin: 9px auto 0;background: url(../img/bminfbox.png) no-repeat;padding-top: 8px;}
.bmbtn{width: 352px;height: 340px;z-index: 2;background-position: -425px 0;left: 50%;top:190px;margin-left: -176px;}
.bmbtn.repeatbtn{background-position: 0 -672px;}
.canlebox{height: 248px;background: url(../img/canlebox.png) no-repeat;position: relative;}
.canlebox p{color: #dccda7;font-size: 27px;line-height: 34px;padding: 79px 0 0 65px;}
.zsbtn{width: 211px;height: 65px;background-position: -543px -1221px;top:21px;left: 511px;}
.paiul{text-align: center;font-size: 0;}
.paiul li{display: inline-block;vertical-align: top;margin: 13px 3px 0 4px;background: url(../img/paiul.png) no-repeat;width: 172px;height: 51px;cursor: pointer;padding-top: 8px;line-height: 44px;}
.paiul li.on{background-position: left bottom;}
.paiul li span{color: #b4e7de;font-size: 28px;font-weight: bold;background-image:-webkit-linear-gradient(top,#b6e0df,#fbfefd);-webkit-background-clip:text;-webkit-text-fill-color:transparent;}
.paiul li.on span{background-image:-webkit-linear-gradient(top,#674222,#cb8747);-webkit-background-clip:text;-webkit-text-fill-color:transparent;font-size: 31px;}
.starbox{height: 780px;text-align: center;padding-top: 20px;}
.meipaibox{width: 692px;height: 693px;padding-top: 87px;margin: 0 auto;display: none;}
.qiansbox{height: 287px;width: 660px;position: relative;background: url(../img/qiansbox.png) no-repeat;margin: 0 auto;}
.fribox{width: 211px;top:0;left: 226px;padding-top: 18px;}
.avtarbox{width: 77px;height: 77px;position: relative;margin: 0 auto;}
.avtarbox::after{content:''; width:98px;height:105px;background:url(../img/friboxzz.png) no-repeat;z-index: 2;position: absolute;top:-19px;left: -12px;}
.avtarbox img{width: 100%;height: 100%;display: block;border-radius: 50%;border:1px solid #dccfa9;}
.avtarinf{color: #e0ddbe;font-size: 24px;line-height: 30px;margin: 8px auto 0;width: 100%;}
.avtarinf p{overflow: hidden;text-overflow:ellipsis;white-space: nowrap;}
.hot{width: 57px;height: 57px;margin: 2px auto 0;background: url(../img/hot.png) no-repeat;float: right;margin-right: 8px;}
.hot.on{background-position: top right;}
.hotnum{width: 127px;height: 27px;background: #183a3a;border-radius: 13px;font-size: 24px;color: #e0ddbe;line-height: 27px;float: left;margin: 28px 0 0 15px;}
.hotnum span{color: #fff698;}
.secbox,.thibox{top:16px;width: 211px;padding-top: 28px;}
.thibox{left: 449px;}
.secbox .avtarbox,.thibox .avtarbox{width: 65px;height: 65px;}
.secbox .avtarinf,.thibox .avtarinf{margin-top: 17px;}
.secbox .avtarbox::after,.thibox .avtarbox::after{width:83px;height:88px;background:url(../img/secboxzz.png) no-repeat;top:-15px;left: -10px;}
.thibox .avtarbox::after{height: 87px;background:url(../img/thiboxzz.png) no-repeat;}
.ranklist{margin: 27px auto 0;width: 686px;height: 303px;background: url(../img/ranklist.png) no-repeat;}
.ranklist li{width: 100%;height: 101px;}
.ranklist .avtarbox{float: left;width: 68px;height: 68px;margin-left: 27px;display:inline;margin-top: 19px;}
.ranklist .avtarbox img{border-radius: 0;}
.ranklist .avtarbox::after{width: 70px;height: 70px;background: url(../img/fangzz.png) no-repeat;top:-1px;left: -1px;}
.ranklist .avtarbox span{color: #ffefb1;font-size: 18px;text-shadow: 2px 0 2px #224b48,0 2px 2px #224b48,0 -2px 2px #224b48,-2px 0 2px #224b48;top:0;left: 4px;position: absolute;z-index: 3;line-height: 20px;}
.ranklist .avtarinf{float: left;margin: 20px 0 0 13px;width: 403px;line-height: 34px;display:inline;text-align: left;font-size: 24px;}
.ranklist .hotbox{width: 125px;float: left;padding-top: 4px;}
.ranklist .hotbox .hot{float: none;margin: 4px auto 0;display:block;}
.ranklist .hotnum{margin: 4px auto 0;float: none;width: 125px;background: #183a3a;}
.starbox .tpage{margin-top: 16px;}
.meipaibox.tl{background: url(../img/tl.png) no-repeat center top;display:block;}
.meipaibox.mj{background: url(../img/mj.png) no-repeat center top;}
.meipaibox.gb{background: url(../img/gb.png) no-repeat center top;}
.meipaibox.wd{background: url(../img/wd.png) no-repeat center top;}
.meipaibox.em{background: url(../img/em.png) no-repeat center top;}
.meipaibox.ts{background: url(../img/ts.png) no-repeat center top;}
.meipaibox.sl{background: url(../img/sl.png) no-repeat center top;}
.meipaibox.xy{background: url(../img/xy.png) no-repeat center top;}
.meipaibox.xx{background: url(../img/xx.png) no-repeat center top;}
.meipaibox.mr{background: url(../img/mr.png) no-repeat center top;}
.meipaibox.tm{background: url(../img/tm.png) no-repeat center top;}
.meipaibox.gg{background: url(../img/gg.png) no-repeat center top;}
.meipaibox.thd{background: url(../img/thd.png) no-repeat center top;}
.meipaibox.jqg{background: url(../img/jqg.png) no-repeat center top;}
.pkinfbox{width: 100%;height: 100%;background: url(../img/pkinfbox.png) no-repeat center top;position: relative;z-index: 3;color: #fff6dc;font-size: 24px;}
.pkinfbox .rankpai{padding: 58px 0 0 96px;line-height: 30px;}
.pkinfbox .avtarbox{margin: 25px auto 0;width: 170px;height: 170px;}
.pkinfbox .avtarbox::after{width: 184px;height: 186px;background: url(../img/pkinfboxzz.png) no-repeat;top:-8px;left:-7px;}
.pkinfbox .avtarinf{color: #fff499;font-size: 25px;line-height: 31px;margin-top: 19px;width: 486px;text-align: center;}
.pkinfbox .avtarinf span{color: #fffce2;}
.pkinfbox .shenheing{margin: 13px auto 0;width: 486px;height: 304px;background: url(../img/shenheing.png) no-repeat;}
.pkinfbox .toptxt{padding: 8px 0 0 14px;font-size: 24px;line-height: 32px;height: 137px;}
.pkinfbox .bottxt{padding-left:14px;font-size: 24px;line-height: 32px;}
.pkinfbox .nosign{margin: 26px auto 0;width: 122px;height: 33px;border-radius: 16px;display:block;background: #dfd285;color: #987f3c;font-size: 24px;text-align: center;line-height: 33px;}
.pkinfbox .examined{background: url(../img/examined.png) no-repeat center top;height: 616px;margin: 6px auto 0;width: 523px;}
.pkinfbox .topbar{padding-top: 0;height: 126px;}
.pkinfbox .topbar .avtarbox{float: left;display:inline;margin: 12px 0 0 23px;width: 102px;height: 102px;}
.pkinfbox .topbar .avtarbox::after{width: 110px;height: 113px;background: url(../img/topbarzz.png) no-repeat;top:-5px;left:-4px;}
.pkinfbox .topbar .avtarinf{float: left;display:inline;margin: 35px 0 0 12px;line-height: 31px;width: 229px;font-size: 25px;text-align: left;}
.pkinfbox .topbar .hotbox{width: 132px;float: left;display:inline;padding-top: 1px;}
.pkinfbox .topbar .hot{width: 92px;height: 92px;background-position: 0 -69px;margin: 0 auto;float: none;display:block;}
.pkinfbox .topbar .hot.on{background-position: 0 -183px;}
.pkinfbox .topbar .hotnum{margin: 2px auto;width: 132px;background: #6e5726;text-align: center;color: #fff6dc;float: none;}
.sharebox{margin-top: 7px;height: 51px;text-align: right;padding-right: 20px;color: #fff6dc;line-height: 51px;}
.sharebox a,.sharebox p{display:inline-block;vertical-align: top;}
.wxbtn{width: 47px;height: 51px;margin-right: 8px;background-position: -666px -1049px;}
.qqbtn{width: 51px;height: 51px;background-position: -726px -1049px;}
.pkinfbox .examined .shenheing{background: none;margin-top: 9px;height: 221px;}
.pkinfbox .examined .toptxt{padding: 9px 14px 0;height: 96px;font-size: 26px;}
.pkinfbox .examined .bottxt{padding: 0 14px;height: 64px;font-size: 26px;}
.cklink{display:inline-block;color: #fff499;border-bottom: 1px solid #fff499;margin-left: 20px;font-size: 24px;margin-top: 5px;}
.signbox{margin: 12px auto 0;width: 486px;}
.signbox .showtxt{line-height: 32px;text-align: left;padding:8px 0 0 14px;font-size: 26px;}
.signbox .btn-ztgd{display: inline-block;vertical-align: top;width: 143px;height: 30px;background-position: -634px -696px;float: none;text-indent: 0;color: #fff;line-height: 30px;margin-top: 1px;}
.signbox .btn-ztgd.open{background-position: -634px -738px;}
.signbox .roll{margin-top: 15px;height: 115px;}
.signbox .roll li{color:#8e7d5a;font-size: 18px;border: none;height: 43px;line-height: 33px;padding: 0 10px;margin-left: 6px;background: none;text-align: left;}
.signbox .roll li span{display:inline-block;color:#8e7d5a;font-size: 24px;height: 33px;line-height: 33px;padding: 0 10px;background: #e9e3d2;border-radius: 16px;}
.signbox .roll li:nth-child(2n){margin-left: 84px;}
.signbox .roll li:nth-child(3n){margin-left: 144px;}
.signbox .roll li:nth-child(4n){margin-left: 54px;}
.signbox .roll li:nth-child(5n){margin-left: 264px;}
.signbox .roll li:nth-child(6n){margin-left: 154px;}
.signbox .roll li:nth-child(7n){margin-left: 74px;}
.signbox .roll li:nth-child(8n){margin-left: 104px;}
.signbox .roll li:nth-child(9n){margin-left: 224px;}
.bg2 .tit.t1{background-position: 0 -620spx;margin-top:108px;width:459px;height:127px;}
.bg2 .jlhd{margin: 21px auto 0;width: 693px;height: 85px;}
.bg2 .jlhd li{width: 50%;height: 100%;margin: 0;background: url(../img/jlhd2.png) no-repeat;}
.bg2 .jlhd li.li0{background-position: left bottom;}
.bg2 .jlhd li.li1{background-position: right top;}
.bg2 .jlhd li.li0.on{background-position: left top;}
.bg2 .jlhd li.li1.on{background-position: right bottom;}
.bg2 .distab{padding-top: 0;}
.bg2 .jlnrbox{width: 693px;height: 1135px;background: url(../img/distab.jpg) no-repeat;margin: 0 auto;}
.zsgiftbox{width: 656px;text-align: center;margin:39px auto 0;position: relative;height: 310px;}
.zsgiftbox h3{color: #fffe9a;font-size: 30px;background-image:-webkit-linear-gradient(top,#f4f494,#ffedb3);-webkit-background-clip:text;-webkit-text-fill-color:transparent;position: absolute;left: 0;width: 100%;line-height: 36px;top:12px;}
.zsgiftbox p{color: #e0ddbe;font-size: 24px;position: absolute;left: 0;width: 100%;line-height: 30px;top:52px;}
.zsgiftbox img{width: 656px;margin: 0 auto;display:block;}
.rizsbox{width: 100%;}
.rizsbox .rizsboxname{color: #e0ddbe;font-size: 28px;padding: 24px 0 0 17px;line-height: 40px;}
.rizsbox .sxlist{width: 661px;margin-top:0;}
.rizsbox .sxlist li{line-height: 36px;}
.rizsbox .belong{color: #e0ddbe;font-size: 22px;margin-top: 8px;padding: 24px 0 0;background: url(../img/sxtip.png) no-repeat center top;line-height: 30px;text-align: center;width: 100%;}
.rizsbox .cxbox{margin: 28px auto 0;padding-left: 310px;height: 41px;background: url(../img/cxbg.png) no-repeat left center;width: 165px;}
.ljbtn{width: 297px;height: 71px;background-position: 0 -454px;margin: 26px auto 0;}
.ljbtn.ylq{background-position: -376px -454px;}
.endtime{color: #e0ddbe;font-size: 26px;text-align: center;padding-top: 18px;line-height: 36px;}
.endtime span{color: #fffe9a;}
.sxjl{margin: 20px auto 0;width: 653px;display:block;}
.sxjlribox{width: 100%;color: #e0ddbe;}
.sxtip{font-size: 24px;line-height: 30px;padding-top: 25px;text-align: center;background: url(../img/sxtip.png) no-repeat center top;margin-top: 13px;}
.sxlist{margin: 22px 0 0 16px;font-size: 24px;line-height: 28px;width: 385px;}
.sxlist li{text-indent: 28px;position: relative;}
.sxlist li span{position: absolute;top:-2px;left:-7px;width: 35px;height: 36px;text-indent: 8px;text-align: center;line-height: 36px;background: url(../img/dot.png) no-repeat;}
.cdktable{margin: 15px auto 0;width: 646px;}
.cdktable.mt{margin-top: 4px;width: 671px;}
.cdktable table{width: 100%;}
.cdktable th{height: 41px;border:1px solid #e0ddbe;font-size: 24px;}
.cdktable td{border:1px solid #e0ddbe;font-size: 24px;text-align: center;height: 36px;}
.cdktable.mt td,.cdktable.mt th{font-size: 20px;}
.cdkbox{width: 288px;background: url(../img/cdkbox.png) no-repeat center top;top:111px;right:8px;padding-top: 47px;}
.cdkbox div{width: 226px;height: 226px;margin: 0 auto;}
.cxbtn{margin: 52px auto 0;width: 165px;height: 41px;background-position: 0 -571px;}
.rizsbox .cxbox .cxbtn{margin: 0;}
.navbot{width: 100%;height: 108px;background: url(../img/nav.png) no-repeat;position: fixed;z-index: 3;left: 0;bottom: 0;display: flex;}
.navbot a{width: 25%;height: 100%;text-indent:-9999em;overflow: hidden;}
.navbot.navbot1{background-position: 0 -128px;}
.navbot.navbot2{background-position: 0 -256px;}
img[src=""],img:not([src]){
    opacity:0;
}
#imageCropper {
    width: 100%;
    height: 100%;
}
/*single e*/
/*sharebg*/
.sharebg{width: 100%;height: 1456px;background: url(../img/sbg.jpg) no-repeat center center;padding-top: 194px;}
.stopbox{height: 56px;padding: 0 30px;display:flex;justify-content: space-between;}
.fhbtn{margin-top: 21px;width: 139px;height: 35px;background: url(../img/fhbtn.png) no-repeat;text-indent:-9999em;overflow: hidden;}
.loginbox{width: 524px;font-size: 22px;line-height: 28px;display:flex;justify-content: flex-end;align-items: flex-end;}
.loginbox,.loginbox a{color:#ffd991;}
.sharebg .bminfbox{margin-top: 264px;width: 100%;background: none;}
.sharebg .pkinfbox{background: none;height: 880px;}
.sharebg .pkinfbox .rankpai{margin: 22px 0 0 52px;width: 320px;height: 39px;line-height: 39px;padding: 0;color: #fff4c6;text-align: center;font-size: 25px;font-weight: bold;background-image:-webkit-linear-gradient(top,#fff4c7,#fffffe);-webkit-background-clip:text;-webkit-text-fill-color:transparent;}
.sharebg .pkinfbox .examined{background: none;width: 100%;height: 181px;margin: 0 auto;position: relative;}
.sharebg .pkinfbox .topbar{height: 100%;}
.sharebg .pkinfbox .topbar .avtarbox{width: 113px;height: 113px;margin: 15px 0 0 51px;}
.sharebg .pkinfbox .topbar .avtarbox img{border:1px solid #a08957;}
.sharebg .pkinfbox .topbar .avtarbox::after{width: 123px;height: 125px;background: url(../img/shrezz.png) no-repeat;top:-6px;left:-5px;}
.sharebg .pkinfbox .topbar .avtarinf{margin: 44px 0 0 18px;line-height: 28px;font-size: 24px;color: #fff5cc;width: 206px;}
.sharebg .pkinfbox .avtarinf span{color: #fff5cc;}
.sharebg .pkinfbox .topbar .hotbox{margin-left: 87px;width: 176px;margin-top: -62px;}
.sharebg .pkinfbox .topbar .hot{width: 133px;height: 133px;background-position: 0 -669px;}
.sharebg .pkinfbox .topbar .hot.on{background-position: 0 -519px;}
.sharebg .pkinfbox .topbar .hotnum{margin: 0 auto;height: 35px;width: 100%;background: #e6debd;border-radius: 17px;color: #475e5c;font-size: 24px;line-height: 35px;}
.sharebg .pkinfbox .topbar .hotnum span{color:#ac3916;}
.sharebg .sharebox{margin: 0;height: 54px;position: absolute;top:114px;right: 34px;padding-right: 0;z-index: 2;color: #f3e6c8;font-size: 22px;line-height: 54px;}
.sharebg .wxbtn{margin-right: 15px;width: 51px;height: 54px;background-position: 0 -1254px;}
.sharebg .qqbtn{width: 54px;height: 54px;background-position: -71px -1254px;}
.sharebg .pkinfbox .examined .shenheing{margin: 62px auto 0;width: 100%;height: 288px;}
.sharebg .pkinfbox .examined .toptxt,.sharebg .pkinfbox .examined .bottxt{color: #dbc79b;font-size: 24px;width: 600px;margin: 0 auto;height: 71px;line-height: 30px;padding: 11px 0 0 0;}
.sharebg .pkinfbox .examined .bottxt{margin-top: 58px;height: 65px;}
.sharebg .cklink{color: #df994d;font-size: 22px;margin: 0 0 0 68px;border-bottom: 1px solid #df994d;}
.sharebg .signbox{margin: 0 auto;width: 644px;}
.sharebg .signbox .showtxt{padding: 0 0 0 210px;}
.sharebg .signbox .btn-ztgd{margin-top: 0;width: 133px;height: 37px; background-position: -328px -1126px;}
.sharebg .signbox .btn-ztgd.open{background-position: -520px -1126px;}
.sharebg .signbox .roll{height: 142px;}
.sharebg .signbox .roll li{margin-bottom: 15px;margin-left: 37px;}
.sharebg .signbox .roll li span{background: #224543;font-size: 24px;color: #dbc79b;height: 36px;line-height: 36px;padding: 0 18px;}
.sharebg .signbox .roll li:nth-child(2n){margin-left: 84px;}
.sharebg .signbox .roll li:nth-child(3n){margin-left: 194px;}
.sharebg .signbox .roll li:nth-child(4n){margin-left: 104px;}
.sharebg .signbox .roll li:nth-child(5n){margin-left: 120px;}
.sharebg .signbox .roll li:nth-child(6n){margin-left: 420px;}
.sharebg .signbox .roll li:nth-child(7n){margin-left: 114px;}
.sharebg .signbox .roll li:nth-child(8n){margin-left: 324px;}
.sharebg .signbox .roll li:nth-child(9n){margin-left: 224px;}
.sharebg .tiebtn{margin-left: 465px;width: 174px;height: 51px;background-position: -196px -1275px;}
.libao .sxlist,.explain .sxlist{color: #e0ddbe;font-size: 20px;margin: 78px 0 28px 35px;width: 630px;line-height: 30px;}
.explain .sxlist{margin-top: 0;}
.libao .sxlist li em{color: #ffc87a;}
.libao .sxlist.hlong{height: 165px;}
.explain .cdktable{margin-top: 10px;}
.explain .cdktable td{font-size: 20px;height: 38px;}
.explain p.addbg{margin-top: 14px;padding-left: 396px;background: url(../img/zjcf.png) no-repeat left top;}
.detailbg .head{background: url(../img/head1.jpg) no-repeat;}
.detailbg .bg{height: 1793px;}
.detailbg .bhmc{font-size: 44px;line-height: 62px;top:9px;}
.detailbg .bhmc,.detailbg .fwqmc{left:0;text-align: center;width: 100%;}
.detailbg .fwqmc{color: #c9c29e;font-size: 26px;line-height: 38px;top:71px;}
.detailbg .bh-box{width: 642px;height: 352px;margin: 20px auto 0;border:2px solid #ffedc4;}
.detailbg .bh-swiper{height: 352px;}
.detailbg .bh-box::after{display: none;}
.detailbg .swiper-pagination{top:306px;width: 100%;height: 46px;background: url(../img/yybg.png) no-repeat;}
.detailbg .swiper-pagination{justify-content: flex-end;}
.detailbg .swiper-pagination-bullet{background: url(../img/swibg1.png) no-repeat;}
.detailbg .swiper-pagination-bullet.swiper-pagination-bullet-active{background-position: top right;}
.detailbg .roll{height: 330px;margin-top: 30px;}
