//名单滚动
var listRollInit = function(o, config) {
	var ul = o.find('ul'),
		playbtn=o.prev().find('.btn-ztgd'),
		a = $.extend({
			col: 1, //每一行列数 默认1
			speed: 1, //速度 默认1
			delay: 0 //每一行停留时间 默认0
		}, config),
		t = 0,
		f = 1000 / 60,
		g = function() {
			var li = ul.find('li'),
				ph = parseInt(li.height()) || 0,
				ln = Math.ceil(li.length / a.col),
				pn = a.col - li.length % a.col;
			tm && clearTimeout(tm);
			if (ln * ph < o.height()) {
				tm = setTimeout(g, 100);
				return;
			}
			if (pn != a.col)
				for (var i = 0; i < pn; i++)
					ul.append(li.eq(i).clone());
			if (!ul.find('[for="append"]').length) {
				ul.append(ul.find('li').clone().attr('for', 'append'));
				ln *= 2;
			}
			t -= a.speed;
			if (-t % ph < a.speed) {
				if (-t >= ph * ln / 2) t = 0;
				tm = setTimeout(g, a.delay < f ? f : a.delay);
			} else {
				tm = setTimeout(g, f);
			}
			ul.css({
				marginTop: t
			});
		},
		tm = setTimeout(g, f);
	playbtn.click(function() {
		if (!playbtn.hasClass('open')) {
			playbtn.addClass('open');
			clearTimeout(tm);
		} else {
			playbtn.removeClass('open');
			tm = setTimeout(g, f);
		}
	})
	// ul.on('mouseenter', function () {
	//     clearTimeout(tm);
	// });
	// ul.on('mouseleave', function () {
	//     tm = setTimeout(g, f);
	// });
};

$('.roll').each(function (i, el) {
	listRollInit($($(el)), {
		col: 1 //一行1列
	});
});


//总赛道活动奖励，活动规则切换
$('.jlhd li').on('click', function() {
	$(this).addClass('on').siblings().removeClass('on');
	$('.distab').removeClass('dis').eq($(this).index()).addClass('dis');
});
//选中标签
$('.signlist li').on('click', function() {
	$(this).addClass('on').siblings().removeClass('on');
});
//打开可展示才华弹窗
$('.zsbtn').on('click', function() {
	popup($('#popCn'));
});
//复制
var clipboard = new ClipboardJS('.btn-copy ', {
	text: function() {
		return personal.share(personal.shareID, true);
	}
});
clipboard.on('success', function(e) {
	alert('复制成功');
});
clipboard.on('error', function(e) {
	alert('复制失败，请手动复制');
});
//生成图片
function generateQRCode(url) {
	createShareQrcode = new QRCode('shareqrcode', {
		text: url,
		width: 146,
		height: 146,
		colorDark: '#000000',
		colorLight: '#ffffff',
		correctLevel: QRCode.CorrectLevel.M
	});
};

function drawinit(url) {
	generateQRCode(personal.share(personal.shareID, true));
	setTimeout(function() {
		draw();
	}, 100);
};

function utf16to8(str) {
	var out, i, len, c;
	out = "";
	len = str.length;
	for (i = 0; i < len; i++) {
		c = str.charCodeAt(i);
		if ((c >= 0x0001) && (c <= 0x007F)) {
			out += str.charAt(i);
		} else if (c > 0x07FF) {
			out += String.fromCharCode(0xE0 | ((c >> 12) & 0x0F));
			out += String.fromCharCode(0x80 | ((c >> 6) & 0x3F));
			out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
		} else {
			out += String.fromCharCode(0xC0 | ((c >> 6) & 0x1F));
			out += String.fromCharCode(0x80 | ((c >> 0) & 0x3F));
		}
	}
	return out;
};

function draw() {
	var shareDom = document.getElementById("save-bg");
	html2canvas(shareDom, {
		useCORS: true,
		allowTaint: false,
		backgroundColor: null,
		y: $("#save-bg").offset().top, //鐟欙絽鍠呭⿰姘З缁岃櫣娅ч梻顕€顣�
		scrollY: 0,
		scrollX: 0,
	}).then(function(canvas) {
		var last_img = canvas.toDataURL("image/png");
		document.getElementById('saveImg').src = last_img;
		setTimeout(function() {
			$('#saveImg,.popshowbox,.sharepop .backbtn').show();
			$('.sharepicbox p').hide();
			if ($(window).height() < 1150) {
				$('.sharepicbox').css('transform', 'scale(' + $(window).height() / 1150 + ')');
				$('.save-bg').hide();
			};
		}, 100)
	})
};
//生成图片
// popup($('.sharepop'));
// drawinit('ww');
if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
	$('.wxbtn,.qqbtn').on('click', function() {
		popup($('.sharepop'));
		drawinit('ww');
	});
}
//生成图片分享关闭
$('.backbtn').on('click', function() {
	$('#saveImg,.popshowbox,.sharepop .backbtn').hide();
	$('.sharepicbox p').show();
	$('#shareqrcode').empty();
	document.getElementById('saveImg').src = '';
});
