function wxLogin() {
    const appid = 'wx708c437a90b8759d';
    const redirect_uri = encodeURIComponent(location.href);
    const scope = 'snsapi_userinfo'; // snsapi_base(静默)或snsapi_userinfo(需用户同意)
    const state = '';

    window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${redirect_uri}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`;
}

if ($.tools.browser.versions.weixin && !$.tools.getParam('code')) {
    wxLogin()
} else {
	$('#wxCode').val($.tools.getParam('code'))
}

$('.tbnum li').on('click', function (e) {
    if (example.commHandle()) return;

    if ($(this).parents('.jjjclist').find('.jcbtn').hasClass('gray') ||
        $(this).parents('.jjjclist').find('.jcbtn').hasClass('committed')) return;
    $(this).addClass('cur').siblings().removeClass('cur');
});

$('select').on('mousedown focus', function (e) {
    if (example.commHandle()) return false;
    // if (example.betTipsHandle($(this))) return false;
});
var selected = new Map();
$('#selectUl select').on('change', function (e) {
    var val = $(this).val(),
        index = $(this).index('#selectUl select');

    if (!val) {
        selected.delete(index);
    } else if (Array.from(selected.values()).includes(val)) {
        $(this).prop('selectedIndex', 0);
        selected.delete(index);
        example.guessFailTips('您已经为该战队排位，请更换该名次战队。');
        return;
    } else {
        selected.set(index, val)
    }
})
// 切换竞猜
$('.jclist li').on('click', function () {
    var $this = $(this);
    $this.addClass('cur').siblings().removeClass('cur');
    $('.distab').removeClass('dis').eq($this.index()).addClass('dis');
});
$(".fz_nav_box").on("click", ".sc_nav1", function () {
    var index = $(this).index()
    $(this).addClass("on").siblings(".sc_nav1").removeClass("on");
    $('.sc_fz_box1').eq(index).addClass('show').siblings().removeClass('show');
})

$(".fz_nav_box").on("click", ".sc_nav2", function () {
    var index = $(this).index()
    $(this).addClass("on").siblings(".sc_nav2").removeClass("on");
    $('.sc_fz_box2').eq(index).addClass('show').siblings().removeClass('show');
})

$(".fz_nav_box").on("click", ".sc_nav3", function () {
    var index = $(this).index()
    $(this).addClass("on").siblings(".sc_nav3").removeClass("on");
    $('.sc_fz_box3').eq(index).addClass('show').siblings().removeClass('show');
})