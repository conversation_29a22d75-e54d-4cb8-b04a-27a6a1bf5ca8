@charset "utf-8";

html,
body,
div,
p,
ul,
li,
dl,
dt,
dd,
em,
i,
span,
a,
img,
input,
h1,
h2,
h3,
h4,
h5 {
  margin: 0;
  padding: 0;
}

a,
img,
input {
  border: none;
  outline: none;
}

a {
  text-decoration: none;
}

img {
  display: block;
}

ul,
li {
  list-style: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

body {
  font: 24px/1.5 -apple-system, "Helvetica Neue", Helvetica, Arial, sans-serif;
  -webkit-user-select: none;
  background-color: #ece1d3;
}

.none {
  display: none;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}

input[type="number"] {
  -moz-appearance: textfield;
}

* {
  -webkit-appearance: none;
  -webkit-text-size-adjust: none;
}

html {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

#cy_bot {
  padding: 10px !important;
  display: none;
}

.cyLogo {
  margin-top: 10px;
}

html,
body {
  width: 100%;
}

.pr {
  position: relative;
}

.pa {
  position: absolute;
}
.show{
  display: block !important;
}
.wrap {
  color: #2a2a2a;
  font-size: 20px;
  width: 750px;
  overflow-x: hidden;
}

.head {
  width: 100%;
  height: 822px;
  background: url(../img/head.jpg) no-repeat;
  background-size: 100% 100%;
  position: relative;
  z-index: 1;
}
.logo {
  width: 165px;
  height: 76px;
  background: url(../img/logo.png) no-repeat;
  display: block;
  text-indent: -9999em;
  position: absolute;
  top: 24px;
  left: 14px;
  z-index: 2;
}
.logo2 {
  pointer-events: none;
  width: 129px;
  height: 86px;
  background: url(../img/logo2.png) no-repeat;
  display: block;
  text-indent: -9999em;
  position: absolute;
  top: 18px;
  left: 190px;
  z-index: 1;
}
.arr {
  position: absolute;
  z-index: 3;
  width: 235px;
  height: 66px;
  left: 50%;
  margin-left: -118px;
  text-align: center;
  top: 750px;
  text-indent: -9999em;
  background: url(../img/arr.png) no-repeat;
  animation: flow 2s linear infinite both;
  -webkit-animation: flow 2s linear infinite both;
}

@keyframes flow {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(10px);
  }

  100% {
    transform: translateY(0px);
  }
}

@-webkit-keyframes flow {
  0% {
    -webkit-transform: translateY(0px);
  }

  50% {
    -webkit-transform: translateY(10px);
  }

  100% {
    -webkit-transform: translateY(-0px);
  }
}

.cont {
  position: relative;
  height: auto;
  width: 100%;
  background: url(../img/cont.jpg) no-repeat center top;
  background-size: 100% 100%;
  padding-bottom: 350px;
}

.tit {
  margin: 0 auto;
  width: 750px;
  background: url(../img/tit.png) no-repeat;
  text-indent: -9999em;
  background-size: 750px auto;
}
.part1{
	height: auto;
  padding-bottom: 100px;
}

.course_nav_box{
	display: flex;
	justify-content: space-between;
	width: 750px;
  margin-top: 38px;
}
.course_nav_box a{
	display: block;
	width: 246px;
	height: 56px;
	background: url(../img/tabcom.png) no-repeat;
	text-indent: -9999em;
}

.course_nav_box .nav0 {
	background-position: -250px 0;
}
.course_nav_box .nav1 {
	background-position: -250px -100px;
}
.course_nav_box .nav2 {
	background-position: -250px -200px;
}
.course_nav_box .nav0.on {
	background-position: 0 0;
}
.course_nav_box .nav1.on {
	background-position: 0 -100px;
}
.course_nav_box .nav2.on {
	background-position: 0 -200px;
}
.course_item{
	display: none;
}
.part1 .time{
	width: 641px;
	height: 53px;
	background: url(../img/time_bg.png) no-repeat;
	color: #ffefcc;
	font-size: 24px;
	line-height: 53px;
	text-align: center;
	margin: 26px auto 0;
}
.subgroup_box{
	display: flex;
	justify-content: space-around;
  flex-wrap: wrap;
	width: 641px;
	margin: 28px auto;
}
.subgroup{
	width: 305px;
	height: 409px;
	background: url(../img/group_bg.png) no-repeat;
	padding-top: 30px;
	box-sizing: border-box;
}
.group_name{
	font-size: 34px;
	color: #ffefcc;
	text-align: center;
	line-height: 1;
}
.team_list{
	width: 247px;
	margin: 10px auto 0;
}

.team_list li{
	margin-bottom: 13px;
}
.team_list li a{
	display: block;
	position: relative;
	width: 247px;
	height: 55px;
	font-size: 20PX;
	line-height: 50px;
	text-align: center;
}
.team_list li:last-child{
	margin-bottom: 0;
}
.team_list .team_cx{
	color: #7a0903;
	background: url(../img/team_bg1.png) no-repeat;
}
.team_list .team_obsolete{
	color: #ddb689;
	background: url(../img/team_bg2.png) no-repeat;
}

.team_list .team_cx::after{
	content:"";
	position: absolute;
	top: -10px;
	right: -10px;
	width: 65px;
	height: 29px;
	background: url(../img/info_01.png) no-repeat;
}
.team_list .team_obsolete::after{
	content:"";
	position: absolute;
	top: -10px;
	right: -10px;
	width: 60px;
	height: 27px;
	background: url(../img/info_02.png) no-repeat;
}
.tips_txt{
	font-size: 20px;
	text-align: center;
	color: #cba074;
}
.tips_txt2{
	font-size: 20px;
	color: #cba074;
  text-align: center;
  margin-top: 28px;
}
.sc_tips{
	display: flex;
	align-items: center;
  justify-content: center;
  margin: 0 auto;
}
.sc_time{
	font-size: 19px;
	color: #cba074;
	line-height: 1.4;
}
.sc_tips .tips_line{
	width: 2px;
	height: 44px;
	background: url(../img/tips_line.png) no-repeat;
	margin: 0 6px;
}
.sc_ti{
	font-size: 26px;
	color: #cba074;
	line-height: 1;
}
.sc_ti span{
	font-size: 16px;
	color: #e92829;
	vertical-align: middle;
}
.team_one{
	width: 174px;
}

.team_box{
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  align-content: space-between;
  margin-top: 12px;
}
.h_188{
  height: 188px;
}
.team_one a{
	display: block;
	position: relative;
	width: 174px;
	height: 39px;
	font-size: 16px;
	line-height: 39px;
	text-align: center;
	
}
.team_one .team_win{
	color: #7a0903;
	background: url(../img/team_bg3.png) no-repeat;
	background-size: 100% auto;
}
.team_one .team_lose{
	margin-top: 12px;
	color: #ddb689;
	background: url(../img/team_bg4.png) no-repeat;
	background-size: 100% auto;
}
.team_one .team_win::after{
	content:"";
	position: absolute;
	top: -8px;
	left: -8px;
	width: 46px;
	height: 22px;
	background: url(../img/info_03.png) no-repeat;
}
.team_one .team_lose::after{
	content:"";
	position: absolute;
	top: -11px;
	right: -8px;
	width: 75px;
	height: 22px;
	background: url(../img/info_04.png) no-repeat;
}
.lose_sc .team_lose::after{
	content:"";
	position: absolute;
	top: -8px;
	right: -8px;
	width: 48px;
	height: 20px;
	background: url(../img/info_05.png) no-repeat;
}
.champion{
	width: 380px;
	height: 414px;
	background: url(../img/champion.png) no-repeat;
  margin: 0 auto;
}
.sc_line{
  width: 5px;
  height: 38px;
  background: url(../img/sc_line.png)no-repeat;
  margin: 0 auto;
}
.sc_line2{
  width: 282px;
  height: 103px;
  background: url(../img/sc_line2.png)no-repeat;
  margin: 0 auto;
}
.sc_line3{
  width: 562px;
  height: 110px;
  background: url(../img/sc_line3.png)no-repeat;
  margin: 0 auto;
}
.mt-47{
  margin-top: -47px;
}
.w-423{
  width: 423px;
  margin:  0 auto;
}
.jijun{
	width: 380px;
	height: 414px;
	background: url(../img/jijun.png) no-repeat;
  margin: 0 auto;
}

.losesc4 i{
	display: inline-block;
	vertical-align: middle;
	font-style: normal;
	width: 93px;
	height: 16px;
	background: url(../img/first.png) no-repeat;
	margin-left: 4px;
}
.losesc3 i{
	display: inline-block;
	vertical-align: middle;
	font-style: normal;
	width: 91px;
	height: 16px;
	background: url(../img/second.png) no-repeat;
	margin-left: 4px;
}
.lose_sc_line1{
  width: 5px;
  height: 38px;
  background: url(../img/sc_line.png)no-repeat;
  margin: 0 auto;
}
.lose_sc_line2{
  width: 444px;
  height: 160px;
  background: url(../img/sc_line4.png)no-repeat;
  margin: 0 auto;
}
.lose_sc_line3{
  width: 562px;
  height: 110px;
  background: url(../img/sc_line3.png)no-repeat;
  margin: 0 auto;
}
.stit {
	margin-left: 1px;
	margin-bottom: 5px;
	height: 48px;
	background: url(../img/stit.png) no-repeat;
	text-indent: -9999em;
}

.stit1,
.stit2,
.stit3,
.stit4 {
	margin-top: 5px;
}
.stit0{
	width: 176px;
	background-position: 0 0;
}
.stit1{
	width: 219px;
	background-position: 0 -50px;
}
.stit2 {
	width: 278px;
	background-position: 0 -100px;
}
.stit3 {
	width: 592px;
	background-position: 0 -150px;
}
.stit4 {
	width: 176px;
	background-position: 0 -200px;
}


.allbox {
  height: 406px;
  position: relative;
}

.allbox a {
  width: 459px;
  height: 462px;
  background: url(../img/allbg.png) no-repeat center top;
  margin: -56px auto 0;
  display: block;
  text-indent: -9999em;
  background-size: 459px auto;
}

.allbox a.cham {
  background: url(../img/allbgcham.png) no-repeat;
  text-indent: 0;
  text-align: center;
  padding-top: 269px;
  cursor: default;
  background-size: 459px auto;
}

.allbox a.cham:hover {
  filter: brightness(1);
}

.allbox a.cham span {
  color: #a44f03;
  font-size: 22px;
  font-weight: bold;
  display: inline-block;
  vertical-align: top;
  line-height: 29px;
  background-image: -webkit-linear-gradient(top, #774a1b, #a44f03);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stepsbox {
  height: 544px;
  background: url(../img/linebg.png) no-repeat;
  width: 750px;
  background-size: 100% auto;
  position: relative;
}

.stepsbox a {
  width: 209px;
  height: 230px;
  position: absolute;
  text-indent: -9999em;
  cursor: default;
  transition: none;
  top: 0;
}

.stepsbox a:hover {
  filter: brightness(1);
}

.allbox a.normallink,
.stepsbox a.stepslink {
  transition: all .5s;
  cursor: pointer;
}

.allbox a.normallink:hover,
.stepsbox a.stepslink:hover {
  filter: brightness(1.1);
}

.stepsbox a.step0 {
  background: url(../img/step0.png) no-repeat;
  left: 34px;
}

.stepsbox a.step1 {
  background: url(../img/step1.png) no-repeat;
  left: 272px;
}

.stepsbox a.step2 {
  background: url(../img/step2.png?v0.2) no-repeat;
  left: 510px;
}

.stepsbox a.step3 {
  background: url(../img/step3.png?v0.32) no-repeat;
  top: 259px;
  left: 510px;
}

.stepsbox a.step4 {
  background: url(../img/step4.png?v01) no-repeat;
  top: 259px;
  left: 272px;
}

.stepsbox a.step5 {
  background: url(../img/step5.png?v20240815) no-repeat;
  top: 259px;
  left: 34px;
}
.tit.t0 {
  height: 129px;
  background-position: -3px 0;
  
}
.tit.t1 {
  height: 129px;
  background-position: -3px -150px;
}

.tit.t2 {
  margin-top: 14px;
  background-position: -10px -292px;
}

.newsbox {
  margin: 2px 12px 2px 4px;
  width: 750px;
  height: 435px;
  background: url(../img/newbox.png) no-repeat;
  background-size: 100% 100%;
}

.newsbox div {
  padding-top: 60px;
}

.newsbox ul {
  width: 620px;
  height: 324px;
  padding: 0 10px;
  overflow-y: auto;
  margin: 0 auto;
  box-sizing: border-box;
}

.newsbox ul li {
  height: 64px;
  border-bottom: 1px dashed #f6cc9b;
  font-size: 22px;
  line-height: 64px;
}
.newsbox ul li:last-child{
  border-bottom: none;
}
.newsbox a {
  color: #f6cc9b;
  display: block;
}

.newsbox span {
  width: 448px;
  float: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.newsbox em {
  float: right;
  width: 110px;
  padding-right: 4px;
  text-align: right;
}

.newsbox span,
.newsbox em {
  color: #f6cc9b;
  /* background-image: -webkit-linear-gradient(top, #fffced, #cbc29d);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent; */
  font-weight: bold;
}

.swibox {
  margin-top: 34px;
  width: 750px;
  height: 780px;
  position: relative;
}

.swiper-button-next,
.swiper-button-prev {
  top: 756px;
  width: 58px;
  height: 57px;
}
.swiper-button-prev{
  left: 180px;
  background: url(../img/prevbtn.png) no-repeat;
  background-size: 100% 100%;
}
.swiper-button-next{
  right: 180px;
  background: url(../img/nextbtn.png) no-repeat;
  background-size: 100% 100%;
}
.vlist {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.vlist li {
  margin: 0 20px;
  width: 314px;
}

.vlist li a {
  width: 100%;
  height: 100%;
  display: block;
}

.vlist div {
  width: 100%;
  height: 183px;
  background: url(../img/vbg.jpg) no-repeat;
  position: relative;
  overflow: hidden;
  background-size: 100% 100%;
}

.vlist div::after {
  content: '';
  position: absolute;
  width: 48px;
  height: 48px;
  background: url(../img/vbtn.png) no-repeat;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -24px;
  margin-left: -24px;
  background-size: 100% 100%;
}

.vlist span {
  color: #c05702;
  font-size: 22px;
  display: block;
  text-align: center;
  padding-bottom: 20px;
  height: 38px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: -webkit-linear-gradient(top, #c05702, #c37624);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
  line-height: 38px;
}

.pg2 .cont {
  height: 4090px;
  background: url(../img/cont2.jpg) no-repeat center top;
  background-size: 100% 100%;
  padding-top: 10px;
  position: relative;
  z-index: 1;
}
.pg2 .cont::before{
  background: url(../img/head2.png) no-repeat;
  background-size: 100% 100%;
}
.guanjunbox {
  margin: -49px 0 0 22px;
  width: 719px;
  height: 215px;
  background: url(../img/guanjunbox.png) no-repeat;
  background-size: 100% 100%;
}

.qiansanbox {
  margin: 23px auto 0;
  width: 750px;
  height: 1268px;
  background: url(../img/qiansanbox.png?v0.1) no-repeat;
  background-size: 100% 100%;
  position: relative;
}

.dianjunbox {
  margin: 60px auto 0;
  width: 750px;
  height: 575px;
  background: url(../img/dianjunbox.png) no-repeat;
  background-size: 100% 100%;
  position: relative;
}

.pjzrlink,
.dfzjlink {
  width: 64px;
  height: 64px;
  position: absolute;
  z-index: 2;
}
.lihelink1{
  top: 881px;
  left: 99px;
}
.lihelink2 {
  top: 962px;
  left: 343px;
}

.lihelink3 {
  top: 880px;
  right: 103px;
}

.dianjunbox .lihelink1 {
  top: 356px;
  left: 77px;
}

.rewardbox .lihelink1{
  top: 186px;
  left: 429px;
}
.rewardbox,
.rewardbox1,
.rewardbox2,
.rewardbox3 {
  margin: 0 auto ;
  width: 750px;
  height: 575px;
  background: url(../img/rewardbox.png) no-repeat;
  background-size: 100% 100%;
  position: relative;
}
.rewardbox1 .lihelink1,
.rewardbox2 .lihelink1 {
  top: 185px;
  left: 432px;
}

.rewardbox1 {
  margin-top: 27px;
  background: url(../img/rewardbox1.png?v2024) no-repeat;
  background-size: 100% 100%;
}

.rewardbox2 {
  background: url(../img/rewardbox2.png?v2024) no-repeat;
  background-size: 100% 100%;
}

.rewardbox3 {
  margin-top: 32px;
  background: url(../img/rewardbox3.png?v2024) no-repeat;
  background-size: 100% 100%;
  height: 399px;
}
.newreward{
	position: absolute;
	bottom: 226px;
	left: 61px;
	height: 68px;
	width: 150px;
	display: flex;
	align-items: center;
	justify-content: center;
}
.newreward img{
  /* width: 99px; */
  /* height: 25px; */
}
.newreward3 img,
.newreward4 img{
  /* width: 117px; */
  /* height: 31px; */
}
.newreward1{
	bottom: 137px;
	left: 300px;
}
.newreward2{
	left: 539px;
}
.newreward3{
	left: 547px;
	bottom: 122px;
}
.newreward4{
	left: 526px;
	bottom: 124px;
}


.pg3 .cont {
  height: auto;
  background: url(../img/cont3.jpg) no-repeat center top;
  background-size: 100% 100%;
}
.pg3 .cont::before{
  background: url(../img/head3.png) no-repeat;
  background-size: 100% 100%;
}
.tit.t3 {
  height: 129px;
  background-position: 0 -300px;
}

.rulebox {
  position: relative;
  margin: -4px auto 100px;
  width: 750px;
  height: 8035px;
  padding: 70px 64px 0;
  color: #fff6d8;
  font-size: 20px;
  line-height: 21px;
  background: url(../img/rule_bg.png) no-repeat;
  box-sizing: border-box;
}

.rulebox p {
  margin: 2px 0;
  padding-left: 10px;
  background: url(../img/pbg.png) no-repeat left top;
  background-size: 133px 26px;
  line-height: 28px;
}

.rulebox ul {
  margin-top: 4px;
}

.rulebox li {
  margin-left: 10px;
}

.rulebox li span,
.rulebox p span,
.rulebox dd span {
  color: #fff29d;
}

.rulebox li::before,
.rulebox dd::before {
  content: '';
  display: inline-block;
  vertical-align: top;
  margin: 4px 10px 0 10px;
  width: 16px;
  height: 16px;
  background: url(../img/icon.png) no-repeat;
  background-size: 100% auto;
}

.rulebox p.pg1 {
  background: url(../img/pbg1.png) no-repeat left top;
  background-size: 392px 26px;
}

.rulebox p.pg2 {
  background: url(../img/pbg2.png) no-repeat left top;
  background-size: 117px 26px;
}

.rulebox p.pg3 {
  background: url(../img/pbg3.png) no-repeat left top;
  background-size: 161px 26px;
}

.rulebox p.pg4 {
  background: url(../img/pbg4.png) no-repeat left top;
  background-size: 96px 26px;
}

.rulebox p.pg5 {
  background: url(../img/pbg5.png) no-repeat left top;
  background-size: 250px 26px;
}

.rulebox p.pg6 {
  background: url(../img/pbg6.png) no-repeat left top;
  background-size: 342px 26px;
}

.rulebox p.pg7 {
  background: url(../img/pbg7.png) no-repeat left top;
  background-size: 341px 26px;
}

.rulebox p.pg8 {
  background: url(../img/pbg8.png) no-repeat left top;
  background-size: 189px 26px;
}

.rulebox p.pg9 {
  background: url(../img/pbg9.png) no-repeat left top;
  background-size: 281px 26px;
}

.rulebox p.pg10 {
  background: url(../img/pbg10.png) no-repeat left top;
  background-size: 174px 26px;
}

.rulebox p.pg11 {
  background: url(../img/pbg11.png) no-repeat left top;
  background-size: 256px 26px;
}
.rulebox p.pg12 {
  background: url(../img/pbg12.png) no-repeat left top;
  background-size: auto 26px;
}
.rulebox .nobg p {
  background: none;
  padding-left: 6px;
  text-indent: 60px;
  line-height: 24px;
}
.rulebox .ml1 p {
	text-indent: 16px;
}

.rulebox p.nobg {
  background: none;
}

.rulebox dd {
  margin-left: 12px;
}

.rulebox dd::before {
  margin: 7px 10px 0 36px;
  height: 8px;
  width: 8px;
  background: url(../img/icon1.png) no-repeat;
  background-size: 100% auto;
}

.tablebox {
  margin: 11px 0 17px;
  width: 622px;
  position: relative;
}

.tablebox::before {
  content: '';
  width: 680px;
  height: 5px;
  background: url(../img/topbg.png) no-repeat;
  position: absolute;
  z-index: 2;
  top: 0;
  left: 50%;
  margin-left: -340px;
  background-size: 100% 100%;
}

.tablebox table {
  width: 100%;
}

.tablebox th,
.tablebox td {
  border: 1px solid #db4349;
  text-align: center;
  width: 50%;
  font-size: 22px;
}

.tablebox td {
  height: 28px;
}

.tablebox th {
  height: 32px;
  background: #e9aa57;
}

.linedl {
  margin-left: 56px;
}

.linedl dd {
  width: 147px;
  display: inline-block;
  vertical-align: top;
  margin-left: 0;
}

.linedl dd::before {
  margin-left: 0;
}

.mt {
  margin-top: 9px;
}

.rulebox p.ml {
  padding-left: 75px;
}

.tablebox1 td {
  width: 25%;
}

.tablebox1 th {
  background: #e9aa57;
}

.pg5 .cont {
  height: 1228px;
  background: url(../img/cont5.jpg) no-repeat;
  background-size: 100% 100%;
  padding-bottom: 0;
}
.tit4{
  height: 129px;
  background-position: 0 -450px;
}



.livehd {
  padding-top: 50px;
  width: 100%;
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  align-items: center;
}

.livehd a {
  margin: 16px 11px;
  width: 282px;
  height: 64px;
  background: url(../img/livehd.png) no-repeat;
  background-size: 282px auto;
  background-position:left bottom;
  display: inline-block;
  vertical-align: top;
  font-weight: bold;
  text-align: center;
}

.livehd a.cur {
  background-position: 0 0;
}
.livehd a span{
	color: #913a00;
	font-size: 30px;
	line-height: 58px;
	background-image: -webkit-linear-gradient(top, #7a3205, #431b05);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}
.livehd a.cur span {
	background-image: -webkit-linear-gradient(top, #d8600c, #972c01);
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
}
.livebox {
  margin: 47px auto 0;
  width: 750px;
  height: 454px;
  background: url(../img/livebox.png) no-repeat;
  background-size: 100% 100%;
  position: relative;
}

.livebox a {
	display: block;
  width: 100%;
  height: 100%;
  background: url(../img/livebtn.png) no-repeat center;
  background-size: 282px auto;
  text-indent: -9999em;
}

.livetip {
  color: #c3b69a;
  font-size: 24px;
  margin-top: 34px;
  line-height: 2;
  padding: 0 30px;
}

.livetip span {
  display: block;
  color: #ffde98;
}

.ffimg,
.longimg,
.qilinimg {
  position: absolute;
  top: 664px;
  left: 89px;
  z-index: 2;
}

.longimg {
  top: 754px;
  left: 330px;
}

.qilinimg {
  left: 568px;
}

.szlink,
.gjsz,
.yjsz {
  position: absolute;
  width: 180px;
  height: 150px;
  z-index: 2;
  top: 172px;
  left: 528px;
}

.gjsz {
  top: 113px;
  left: 284px;
}

.yjsz {
  left: 40px;
}

.dianjunbox .szlink,
.rewardbox .szlink,
.rewardbox1 .szlink,
.rewardbox2 .szlink,
.rewardbox3 .szlink {
  top: 128px;
  left: 66px;
  width: 150px;
}
.rewardbox3 .szlink{
  left: 66px;
}
/*nav*/
.navbtn1 {
  display: none;
}

.flobox {
  z-index: 12;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 750px;
  height: 116px;
  background: url(../img/bot_nav.png) no-repeat;
  background-size: 750px auto;
}

.flobox .openbtn {
  display: none;
}

.s_nav,
.snavbox {
  width: 100%;
  height: 100%;  
}
.snavbox a{
    width: 150px;
    height: 116px;
    text-indent: -999em;
}
.snavbox .snav0 {
  position: absolute;
  left: 50%;
  margin-left: -75px;
  background-position:-300px 0;
}

.snavbox .snav1,
.snavbox .snav2,
.snavbox .snav3,
.snavbox .snav4 {
  position: relative;
  height: 102px;
  width: 150px;
  float: left;
  text-indent: -9999em;
  z-index: 1;
  margin-top: 11px;
}
.snavbox .snav3 {
  margin-left: 150px;
}

.pg1 .snavbox a.snav1 {
  background: url(../img/bot_nav.png) no-repeat;
  background-size: 750px auto;
  background-position: -300px -135px;
}
.pg2 .snavbox a.snav1 {
  background: url(../img/bot_nav.png) no-repeat;
  background-size: 750px auto;
  background-position: 0 -135px;
}

.pg3 .snavbox a.snav2 {
  background: url(../img/bot_nav.png) no-repeat;
  background-size: 750px auto;
  background-position: -150px -135px;
}

.pg4 .snavbox a.snav3 {
  background: url(../img/bot_nav.png) no-repeat;
  background-size: 750px auto;
  background-position: -450px -135px;
}

.pg5 .snavbox a.snav4 {
  background: url(../img/bot_nav.png) no-repeat;
  background-size: 750px auto;
  background-position: -600px -135px;
}

.addbg {
  margin: 40px auto 0;
  width: 750px;
  height: 562px;
  background: url(../img/addbg.png) no-repeat;
  padding: 186px 0 0 0;
  box-sizing: border-box;
}

.teamscrollbox {
  overflow-y: auto;
  height: 340px;
  margin-right: 80px;
  margin-left: 55px;
}

.teamscrollbox a {
  cursor: pointer;
  margin: 0 0 20px 31px;
  float: left;
  display: inline;
  width: 257px;
  height: 54px;
  background: url(../img/colbg.png) no-repeat;
  line-height: 54px;
  text-align: center;
}

.teamscrollbox a span {
	font-size: 24px;
	color: #7a3b03;
}

.teamscrollbox::-webkit-scrollbar {
  width: 5px;
	background: #d7ab7d;
}

.teamscrollbox::-webkit-scrollbar-thumb {
	background: #eb8f1b;
}

.infbox {
  padding-top: 19px;
	width: 665px;
	height: 563px;
	background: url(../img/infbg.png) no-repeat;
	color: #172746;
	z-index: 1;
  display: none;
}

.infbox .popclosebtn {
  position: absolute;
  width: 37px;
  height: 36px;
  background: url(../img/close.png);
  top: -18px;
  right: -37px;
  text-indent: -9999em;
}

.teambox {
    text-align: center;
    font-size: 30px;
    line-height: 40px;
	color: #541114;
}

.infbox ul {
	margin: 6px 52px 0 52px;
}

.infbox li {
	height: 71px;
	text-align: center;
}

.touxian {
	float: left;
	margin-top: 6px;
	width: 33px;
	height: 65px;
	text-indent: 0;
	color: #632800;
	line-height: 20px;
	font-size: 20px;
	text-align: center;
	background: url(../img/touxian.png) no-repeat;
	padding: 8px 5px 0;
	box-sizing: border-box;
}

.detailbox {
	float: left;
	line-height: 24px;
	color: #ffefcc;
	font-size: 20px;
	padding-top: 12px;
}

.detailbox div {
	padding-left: 8px;
	width: 400px;
	text-align: left;
	float: left;
	box-sizing: border-box;
}

.detailbox div em {
	float: left;
	width: 300px;
}

.detailbox div i {
	float: left;
	font-style: normal;
	color: #feda7a;
	width: 92px;
	text-align: center;
}

.detailbox span {
	width: 90px;
	text-align: center;
	float: left;
}

/* pop-bili */
#video_pop {
  margin: 0 auto;
  background: #f4e8b6;
  width: 730px;
  height: 410px;
  padding: 3px;
}

#video_pop .video_bili {
  width: 730px;
  height: 410px;
  background: #000;

}

#video_pop iframe {
  width: 100%;
  height: 100%;
}

#video_pop .popclosebtn {
  right: 10px;
  top: -50px;
}
.pop1 .popclosebtn,
.pop2 .popclosebtn,
.pop3 .popclosebtn,
.pop4 .popclosebtn,
.pop5 .popclosebtn {
  top: -52px;
  right: 30px;
}
.pop6 .popclosebtn{
  top: 46px !important;
  right: 34px;
}
