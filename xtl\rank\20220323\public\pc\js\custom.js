;(function (window) {
    var that,
        name = 'ja';
    var _glob = {
        config: {
            APP: 'xtl',
            ACTIVITY: 'meeting',
            VERSIONCODE: '20210317',
            PLAT: 'changyou',
            "share": {
                "title": "你的好友邀您共享新服福利",
                "desc": "《新天龙八部》新门派“从刃”强势来袭，新服重磅好礼加成！更有多年旧友重新归来，与君共战新服！",
                "imgUrl": "http://i0.cy.com/xtl/logo/share/20210917/icon.jpg"
            },
            // domain: 'https://admin.csfullspeed.com',
            domain: '',
            path: '/changyou/activity/',
            apis: {
                checkLogin: 'index',
                login: 'login',
                getRole: 'getRole',
                roleBind: 'roleBind',
                personalRank: 'personalRank',
                totalRank: 'totalRank',
                giftReceive: 'giftReceive',
            },
            userInfo: null
        }
    }

    // 构造函数合并
    function Mixin(recClass, giveClass) {
        if (arguments.length > 2) {
            for (var i = 2; i < arguments.length; ++i) {
                var name = arguments[i];
                recClass.prototype[name] = giveClass.prototype[name];
            }
        } else {
            for (var key in giveClass.prototype) {
                if (!recClass.prototype[key]) {
                    recClass.prototype[key] = giveClass.prototype[key];
                }
            }
        }
    }

    /**
     * @desc 公用方法
     */
    function Utils() {
    }

    Utils.prototype = {
        isIos: /iphone|ipod|ipad/i.test(window.navigator.userAgent.toLowerCase()),
        isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
        isWecha: window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i) == 'micromessenger',
        getUrl: function () {
            return window.location.protocol + '//' + window.location.host;
        },
        getPath: function () {
            var pathname = window.location.pathname;
            var arr = pathname.split('\/');
            var filename = arr[arr.length - 1];
            var url = window.location.protocol + '//' + window.location.host;
            return url + pathname.replace(filename, '');
        },
        getFilename: function () {
            var pathname = window.location.pathname;
            var arr = pathname.split('\/');
            var filename = arr[arr.length - 1];
            return filename;
        },
        getCurrentUrl: function () {
            return location.href.split('\?')[0]
        },
        getRequest: function GetRequest() {
            var url = location.search; //获取url中"?"符后的字串
            var theRequest = new Object();
            if (url.indexOf("?") != -1) {
                var str = url.substr(1);
                strs = str.split("&");
                for (var i = 0; i < strs.length; i++) {
                    theRequest[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
                }
            }
            return theRequest;
        },
        getParam: function (variable) {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == variable) {
                    return pair[1];
                }
            }
            return ('');
        },
        /**
         * @desc 函数节流
         * @param func 函数
         * @param wait 延迟执行毫秒数
         * @param type 1 表时间戳版(立即执行)，2 表定时器版(非立即执行)
         */
        throttle: function (func, wait, type) {
            if (type === 2) {
                var timeout;
            } else {
                var previous = 0;
            }
            return function () {
                var context = this;
                var args = arguments;
                if (type === 2) {
                    if (!timeout) {
                        timeout = setTimeout(function () {
                            timeout = null;
                            func.apply(context, args)
                        }, wait)
                    }
                } else {
                    var now = Date.now();
                    if (now - previous > wait) {
                        func.apply(context, args);
                        previous = now;
                    }
                }
            }
        },
        /**
         * @desc 函数防抖
         * @param func 函数
         * @param wait 延迟执行毫秒数
         * @param immediate true 表立即执行，false 表非立即执行
         */
        debounce: function (func, wait, immediate) {
            var timeout;
            return function () {
                var context = this;
                var args = arguments;
                if (timeout) clearTimeout(timeout);
                if (immediate) {
                    var callNow = !timeout;
                    timeout = setTimeout(function () {
                        timeout = null;
                    }, wait);
                    if (callNow) func.apply(context, args)
                }
                else {
                    timeout = setTimeout(function () {
                        func.apply(context, args)
                    }, wait);
                }
            }
        },
        // 弹窗
        win: {
            // 打开
            open: function (width, height, src) {
                var iframeHeight = height;
                var marginLeft = width / 2;
                var marginTop = height / 2;
                var inntHtml = '';
                inntHtml += '<div id="mask" style="width:100%; height:100%; position:fixed; top:0; left:0; z-index:1999;background:#cccccc; filter:alpha(opacity=50); -moz-opacity:0.5; -khtml-opacity: 0.5; opacity:0.5;"></div>';
                inntHtml += '<div id="maskTop" style="width: ' + width + 'px; height: ' + height + 'px;  color: #333; position: fixed; top: 50%; left: 50%; margin-left: -' + marginLeft + 'px; margin-top: -' + marginTop + 'px; z-index: 2999; ">';
                inntHtml += '<div id="maskTitle" style="height: 0;position: relative;">';
                inntHtml += '<div id="winClose" style="width: 28px; height: 28px; cursor: pointer; position: absolute; top: -12px; right: -9px; background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAJeSURBVEhLvZbPq2lRFMf9B4bSTTIxZiBSMlCI9ycoKX+Bod7w/il3YIL4NyhFmYmBKD2Sp0ix3vqes/e529n74t33Op9astevr3PO2tvxvcLtdquzfbAtyAV8IlYX6d+DG7yxvbP9Fr2fglxR8ybavAYX/GD7Jfr8NahFD9HuMZz4U9Q5jEYjqlarFA6HiVPuDD7EkOMGvTjna9xi8/mcstmsJvKVIRc1Kl+K4haIHItut0t+v9/Y+JGhBrUq6M2xT9iBAXGeGQrY/U+miqI3NNhvw4t3EbNuyXeuzG3ood5eaLDfhhfO6JueWbPZtGKFQkGLNRoN2u/3FI/HtRh6SaDBPkusLnzWpMlkaRC7XC5WfLVaUTqddmKVSoVOp5MVG4/HlEql7mph6vRCC4IfYm2Nt7vAzW63o2KxSLVaja7Xq/DatFotrR49JdCCoHNcmfZZPp+n9XotMmxwVVwnVjbD4ZAikYhWj54SaN1dgjtZWiaToe12K7J0JpOJUUyaykuCsFwuR8fjUWR+slgsKBAIGGukqbwsiGdmElwul5RIJIw10lReEsQ0ns9nkaVzOBys226qhak8HRrsM7ktJLPZjDabjVjZYLBKpZJWrw0NfzzcFvj1KtPp1HpmsVjM2iIq/X5fqzdti4cbHycINjUYDAYUCoWcGA4BHAag1+tRMBi8q4VpGx/wl4dHWzKZpHa7TdFoVIuVy2XqdDrGSTUebYAXnh/e3v49AXZ49wcs4YB3rxgStyjApGG8TfsUPsTUaZQ8FZPgFrB585oo4QLvXoTdcIP/9Krv8/0BDUSOirKWU6wAAAAASUVORK5CYII=);"></div>';
                inntHtml += '</div>';
                inntHtml += '<iframe width="' + width + '" height="' + iframeHeight + '" frameborder="0" scrolling="no" src="' + src + '"></iframe>';
                inntHtml += '</div>';
                $("body").append(inntHtml);

                $("#winClose")
                    .mouseenter(function () {
                        $(this).css("background-image", "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAJwSURBVEhLvZbLSiNBFIb7DVyKiIgb17oQRRAXgor6CIIIeQKXMksfxYUbFbMZRh0Yb6ODMgEddCVmoWkRLzFekukxfay/+lRbqSqTVob+4CyqzuVPV59TaS8JYRhmhM0Ly5MB9tiX4fDPIQq0CpsT9sC1G4JYzmnlMskQCRPCrrnOh0EuanC5+ojAL5wXc5/LUW5qitba2ynreTWGPfgQY4JaXNaNKfZ0dkY7g4OWyHuGWOTovCuKI+AYib+8TF+bmpyF6xlykKuD2iwTITbQIPE7Q4Kr2EdMF0VtaLCcFJxjnzySzzyZaaihHy80WE4Kxq3vemcns7PStzsyYvn+zMxQUCzSRne35UMtBTSUWIb3ZKeZSRCrBoH0lwsF2u7vj32/JyepWi5L3/3hIW319dXkwvTuhRYE53kt29tMMAlub2lvdJRy09MUVqu8G3GxsGDlo6YCWhCMryvXnO0OD1PF9zkiQj5VGPIqonhwQOsdHVY+aiqgVfMIZrCy7YEBCm5uOMqmdHTkFFOmk0gQ9nNoiF4eHznyjed8nr41NztzlOkkFsQ7cwmWz89ps6fHmaNMJ5Gg7MZKhaNs/pVK8thduTCdhk2DOVNjoXg6PaW/V1e8ikBj7Y2NWflW06BVee0cC/x6nYfjY/nOfnR1yRHRucxmrXzXWNQdfNwgGGpwt79Pa21tsQ+XAC4D4K+s0GpLS00uzBp8vm3qXm1bvb1UWFyk752dlu/X+Dj5S0vOTnVebUAsUr+80/17AmIjvT9ghXCk94mhMEUBOg3t7ZpT7MGnd6OioZgCRyAsnc9EhUhI70PYRBT4T5/6nvcKYG1hElXAZggAAAAASUVORK5CYII=)");
                    })
                    .mouseleave( function () {
                        $(this).css("background-image", "url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAJeSURBVEhLvZbPq2lRFMf9B4bSTTIxZiBSMlCI9ycoKX+Bod7w/il3YIL4NyhFmYmBKD2Sp0ix3vqes/e529n74t33Op9astevr3PO2tvxvcLtdquzfbAtyAV8IlYX6d+DG7yxvbP9Fr2fglxR8ybavAYX/GD7Jfr8NahFD9HuMZz4U9Q5jEYjqlarFA6HiVPuDD7EkOMGvTjna9xi8/mcstmsJvKVIRc1Kl+K4haIHItut0t+v9/Y+JGhBrUq6M2xT9iBAXGeGQrY/U+miqI3NNhvw4t3EbNuyXeuzG3ood5eaLDfhhfO6JueWbPZtGKFQkGLNRoN2u/3FI/HtRh6SaDBPkusLnzWpMlkaRC7XC5WfLVaUTqddmKVSoVOp5MVG4/HlEql7mph6vRCC4IfYm2Nt7vAzW63o2KxSLVaja7Xq/DatFotrR49JdCCoHNcmfZZPp+n9XotMmxwVVwnVjbD4ZAikYhWj54SaN1dgjtZWiaToe12K7J0JpOJUUyaykuCsFwuR8fjUWR+slgsKBAIGGukqbwsiGdmElwul5RIJIw10lReEsQ0ns9nkaVzOBys226qhak8HRrsM7ktJLPZjDabjVjZYLBKpZJWrw0NfzzcFvj1KtPp1HpmsVjM2iIq/X5fqzdti4cbHycINjUYDAYUCoWcGA4BHAag1+tRMBi8q4VpGx/wl4dHWzKZpHa7TdFoVIuVy2XqdDrGSTUebYAXnh/e3v49AXZ49wcs4YB3rxgStyjApGG8TfsUPsTUaZQ8FZPgFrB585oo4QLvXoTdcIP/9Krv8/0BDUSOirKWU6wAAAAASUVORK5CYII=)");
                    })
                    .click(this.close);
            },
            // 关闭
            close: function () {
                $("#mask, #maskTop").fadeOut(function () {
                    $(this).remove();
                });
            }
        },
        // 存储cookie
        cookie: {
            get: function (cname) {
                var name = cname + '=';
                var ca = document.cookie.split(';');
                for (var i = 0; i < ca.length; i++) {
                    var c = ca[i];
                    while (c.charAt(0) == ' ') c = c.substring(1);
                    if (c.indexOf(name) != -1) return c.substring(name.length, c.length);
                }
                return '';
            },
            set: function (cname, cvalue, exdays, domain) {
                var d = new Date();
                var timestemp = d.getTime() + (exdays * 24 * 60 * 60 * 1000)
                d.setTime(timestemp);
                var expires = ' expires=' + d.toUTCString() + ';';
                var c_domain = ' domain=' + domain + ';'
                var c_path = ' path=/'
                var cookie = cname + '=' + cvalue + '; ' + expires + c_domain + c_path
                console.log(cookie);
                document.cookie = cookie;
            },
            del: function (name, domain) {
                this.set(name, '', -1, domain ? domain : '');
            }
        },
        // 存储sessionStorage，自动转换对象存储和获取
        session: {
            set: function (name, value) {
                if (Object.prototype.toString.call(value) === '[object Object]') value = JSON.stringify(value);
                sessionStorage.setItem(name, value)
            },
            get: function (name) {
                var value = sessionStorage.getItem(name);
                return utils.isJSON(value) ? JSON.parse(value) : value;
            }
        },

    }
    var utils = new Utils()

    /**
     * == ajax 请求，Http 模块 ==
     * ajax
     *  参数@o，为对象，可选属性有url, sp, load, method, contentType
     *  参数@cb 请求完成后的回调
     * */
    function Http() {
    }

    Http.prototype = {
        ajax: function (o, cb) {
            var config = _glob.config
            if(o.data){
                Object.assign(o.data, {verifyToken: getToken()})
            } else{
                o.data = {verifyToken: getToken()}
            }
            var t = {
                method: o.method || 'post',
                url: o.sp ? o.url : (config.domain + config.path + o.url),
                data: o.data || {},
                dataType: o.dataType || 'json',
                contentType: o.contentType || 'application/x-www-form-urlencoded'
            };
            var load = null;
            (o.load) && (load = lay.load(1, {content: '加载中，请稍后...', shade: 0.5}));
            // console.log(t);
            $.ajax({
                type: t.method,
                url: t.url,
                data: t.data,
                dataType: t.dataType,
                contentType: t.contentType,
                xhrFields: {withCredentials: true},
                crossDomain: true,
                cache: false,
                success: function (d) {
                    if (o.console) {
                        console.log('ajax: ' + o.url + ' | ' + JSON.stringify(t.data) + ' | ' + (new Date()).valueOf(), d);
                    }
                    setTimeout(function () {
                        o.load && lay.close(load);
                    }, 300);
                    ;(cb) && cb(d);
                },
                error: function (xhr, type) {
                    setTimeout(function () {
                        o.load && lay.close(load);
                    }, 500);
                    var d = {
                        code: -505,
                        msg: '系统错误',
                        message: '系统错误'
                    };
                    if (o.console) console.log('ajax: ' + o.url + ' | ' + JSON.stringify(t.data) + ' | ' + (new Date()).valueOf(), d);
                    ;(cb) && cb(d);
                }
            });
        },
    }
    var http = new Http();

    function User() {
    }

    User.prototype = {
        checkLogin: function (cbLogin, cbUnLogin) {
            if (!getToken()) {
                cbUnLogin()
            } else {
                http.ajax({
                    url: _glob.config.apis.checkLogin,
                    data: {verifyToken: getToken()}
                }, function (res) {
                    console.log(res);
                    if (res.code === 0) {
                        cbLogin(res.data)
                    } else {
                        cbUnLogin()
                    }
                })
            }
        },
        login: function () {
            utils.cookie.del('CASGTC', '.changyou.com');
            var s_back = encodeURIComponent( utils.getUrl() + _glob.config.path + _glob.config.apis.login + '?s_url=' + encodeURIComponent(location.href))
            // encodeURIComponent( _glob.config.domain + _glob.config.path + _glob.config.apis.login + '?s_url=' + encodeURIComponent(location.href))
            if(utils.isMobile){
                var url = 'https://auth.changyou.com/wap_login.jsp?s=' + s_back
                location.href = url;
            } else{
                var url = 'https://auth.changyou.com/interfaceLogin?project=activity&s=' + s_back
                console.log(url);
                utils.win.open(496, 360, url);
            }
        },
        getToken: function (cb) {
            var s_url = encodeURI(location.href)
            var ticket = utils.getParam('ticket')
            http.ajax({url: _glob.config.apis.login, data: {s_url: s_url, ticket: ticket}}, function (res) {
                cb(res)
            })
        }
    }
    var user = new User()

    function getToken() {
        return utils.cookie.get('verifyToken')
    }

    var Activity = function () {
    }
    Activity.fn = Activity.prototype = {
        glob: _glob,
        utils: utils,
        http: http,
        user: user
    }
    /* == 工具类模块 == */
    // Mixin(App, Utils);
    window[name] = new Activity();
})(window);

var App = function (){
    var that = this;
    this.rank1 = new Swiper('#rank1', {
        allowTouchMove: false,
        effect: 'fade',
        pagination: {
            el: '#rank1 .swiper-pagination',
            bulletElement: 'li',
            clickable: true
        },
    });
    this.rank2 = new Swiper('#rank2', {
        allowTouchMove: false,
        effect: 'fade',
        pagination: {
            el: '#rank2 .swiper-pagination',
            bulletElement: 'li',
            clickable: true
        },
    });
    this.rank3 = new Swiper('#rank3', {
        allowTouchMove: false,
        effect: 'fade',
        pagination: {
            el: '#rank3 .swiper-pagination',
            bulletElement: 'li',
            clickable: true
        },
    });
    this.rank4 = new Swiper('#rank4', {
        allowTouchMove: false,
        effect: 'fade',
        pagination: {
            el: '#rank4 .swiper-pagination',
            bulletElement: 'li',
            clickable: true
        },
    });
    var config = ja.glob.config
    return {
        takeGift: function (giftid){
            console.log(giftid);
            ja.http.ajax({
                url: config.apis.giftReceive,
                data: {giftid: giftid}
            }, function (ret){
                if(ret.code === 0){
                    lay.open('pop3');
                    $('#pop3 .pop-gift-name').text(ret.giftName);
                    $('.point-item').eq(giftid - 1).addClass('got')
                } else if(ret.code === -60001){
                    lay.open('pop2')
                } else{
                    lay.msg(ret.msg)
                }
            })
        },
        personalRank: function (){
            var $tbody = $('.pop-table tbody');
            $tbody.empty();
            var html = []
            ja.http.ajax({url: config.apis.personalRank}, function (ret){
                var totalPoint = 0
                var list = ret.code === 0 ? ret.data : []
                list.forEach(function (it) {
                    totalPoint+=(it['point'])*1
                    var date = it['date'].substring(4, 6) + '月' + it['date'].substring(6, 8) + '日'
                    html.push('<tr>\n' +
                      '     <th>'+ date +'</th>\n' +
                      '     <th>'+ it['floor'] +'/'+ it['floor_point'] +'</th>\n' +
                      '     <th>'+ it['kill'] +'/'+ it['kill_point'] +'</th>\n' +
                      '     <th>'+ it['normal'] +'/'+ it['normal_point'] +'</th>\n' +
                      '     <th>'+ it['high'] +'/'+ it['high_point'] +'</th>\n' +
                      '     <th>'+ it['point'] +'</th>\n' +
                      '     <th>'+ it['equip'] +'</th>\n' +
                      '</tr>')
                })
                $tbody.append(html.join(''));
                $('.total-point').text(totalPoint)
            })
        },
        totalRank: function (pageNo){
            var swiper = that['rank' + pageNo]
            ja.http.ajax({url: config.apis.totalRank, data:{pageNo: pageNo}}, function (ret){
                if(ret.code === 0){
                    var rankList = ret.data;
                    new Array(0,1,2,3).forEach(function (it, index) {
                        var list = rankList.splice(index * 25, (index + 1) * 25);
                        var $tbody = $(swiper.slides.eq(index)).find('.table-body tbody');
                        $tbody.empty();
                        var trs = []
                        list.forEach(function (it, i) {
                            var sortNo = index * 25 + i + 1
                            var sortNoHtml = sortNo <= 3 ? ('<i class="ico-top top' + sortNo + '"></i>' + sortNo) : sortNo
                            trs.push('<tr>' +
                              '<td>' + sortNoHtml + '</td>' +
                              '<td>' + it.role_name + '</td>' +
                              '<td>' + it.server_name + '</td>' +
                              '<td>' + it.point + '</td>' +
                              '<td>' + it.equip + '</td>' +
                              '</tr>')
                        })
                        trs.join('');
                        $tbody.html(trs);
                    })
                } else{
                    lay.msg(ret.msg)
                }
            })
        },
        getRole: function () {
            var $select = $('#roleList')
            ja.http.ajax({url: config.apis.getRole}, function (ret) {
                $select.empty();
                var options = ['<option value="">请选择角色</option>']
                if (ret.code === 0) {
                    ret.data.forEach(function (it) {
                        options.push('<option value="' + it.ROLE_ID + '">' + it.ROLE_NAME + '</option>')
                    })
                }
                $select.html(options.join(''))
            })
        },
        roleBind: function (){
            var role_id = $('#roleList option:selected').val()
            ja.http.ajax({url: config.apis.roleBind, data:{role_id: role_id}}, function (ret){
                if(ret.code === 0){
                    that.initLogin()
                } else{
                    lay.msg(ret.msg)
                }
            })

        },
        initLogin: function (){
            ja.user.checkLogin(
              function (userInfo) {
                  console.log(userInfo)
                  ja.glob.userInfo = userInfo
                  loginAfter()
              },
              function () {
                  console.log('未登录');
                  loginBefore();
              })
        },
        init: function () {
            this.initLogin();
            for(var i=1; i<=4; i++){
                this.totalRank(i);
            }
            bind();
        }
    }
}

var app = new App();
app.init();

function bind() {
    var onPointItem = function (fun, wait) {
        var timeout
        return function () {
            var args = arguments;
            var context = this;
            if (timeout) clearTimeout(timeout);
            if ($(context).hasClass('on')) {
                return;
            }
            fun.apply(context, args)
            timeout = setTimeout(function () {
                $(context).parents('.point-item').removeClass('on')
            }, wait)
        }
    }
    $('.point-item').on('click', '.point-img-box', onPointItem(function () {
        $(this).parents('.point-item').addClass('on').siblings().removeClass('on');
    }, 2000));
    var onTakeGift = function (){
        var index = $(this).parents('.point-item').index();
        var giftid = index + 1;
        var userInfo = ja.glob.userInfo
        if(!userInfo){
            login();
            return;
        }
        if(userInfo.giftRecord[giftid] === 1){ return; }
        if($(this).hasClass('wating')){
            return;
        } else{
            app.takeGift(giftid);
        }
        $(this).addClass('wating');
    }

    $('.point-item').on('click', '.btn-get', ja.utils.throttle(onTakeGift, 1000));

    $('.btn-bindRole').click(function (){
        lay.open('pop4')
        app.getRole()
    })

    $('.btn-personCenter').click(function (){
        lay.open('pop1')
    })

    $('.btn-submit-bind').click(function (){
        app.roleBind()
    })

    $('.pop .btn-close').on('click', function () {
        var popId = $(this).parents('.pop').attr('id')
        lay.close(popId)
    })
}

function loginBefore() {
    // 未登录
    console.log('loginBefore')
    $('#logBefore').show().siblings().hide();
    $('.point-item .btn-get').removeClass('got');
}

function loginAfter() {
    // 已登录
    console.log('loginAfter')
    var userInfo = ja.glob.userInfo
    var is_bind = userInfo.is_bind === 1
    $('.bind-wp')[!is_bind ? 'show' : 'hide']();
    $('.person-center-wp')[!is_bind ? 'hide' : 'show']();
    if(is_bind) app.personalRank();
    $('#logAfter .username').text(userInfo.cn);
    $('#logAfter').show().siblings().hide();
    // 领奖按钮状态渲染
    $('.point-item').eq(0).find('.btn-get')[userInfo.giftRecord['1'] === 1 ? 'addClass' : 'removeClass']('got');
    $('.point-item').eq(1).find('.btn-get')[userInfo.giftRecord['2'] === 1 ? 'addClass' : 'removeClass']('got');
}

function login() {
    console.log(ja);
    ja.user.login();
}

function logout() {
    ja.utils.cookie.del('verifyToken')
    location.reload();
}
