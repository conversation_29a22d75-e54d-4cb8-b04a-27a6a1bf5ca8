.top-box{width: 100%;height: 54px;display: flex;flex-direction: row;justify-content:flex-end;align-items: center; padding-top: 174px;margin-bottom: 240px; box-sizing: border-box; padding-right: 10px;}
.top-box a{display: inline-block; color: #ffefd8;margin-left: 14px;}
.top-box{margin-bottom: 390px;}
.page .btn-logout{display: none;}
.page2 .btn-logout,.page3 .btn-logout,.page4 .btn-logout,.page5 .btn-logout,.page6 .btn-logout{width: 144px;color: #ffffff;text-align: start;}
.page{position: relative;}
.page .btn-rule{width:160px;height:54px;background: url(../img/btn-rule2.png) no-repeat center;background-size: 100% auto;margin-left:10;margin-right:25px;margin-bottom:22px;}
.page .btn-home{width:160px;height:54px;background: url(../img/btn-home.png) no-repeat center;background-size: 100% auto;margin-bottom:22px;}
.page .user-info {width:240px;height: 56px;display: flex;flex-direction: row;align-items: center;margin-right: 100px;margin-left:25px;font-size: 20px;margin-bottom: 35px;}
.page .user-info .head-logo{width:95px;height:56px;background: url(../img/logo.png) no-repeat center;background-size: 100% auto;margin-right: 10px;}
.page .user-info .user-name{width: 144px;height: 56px;display: flex;flex-direction: row;align-items: center;justify-content: center;color:#ffffff;}
.page .user-info .btn-login{width:auto;height:54px;text-align: center;line-height: 54px;margin:0;color:#ffffff;}
.page .title{width: 640px;min-height: 48px;font-size: 26px;color: #433223;font-weight: bold;margin: 0 auto;}
.page .title span{color: #777168}
.page .title2{width: 557px;height:57px;background: url(../img/title-main-mass.png) no-repeat center;background-size: 100% auto;margin-bottom: 8px;margin:0 96.5px;}
.page .schedule-box{width:624px;height:270px;background: url(../img/mass-scheduleBg.png) no-repeat center;background-size: 100% auto;margin: 0 auto 20px; display: flex;flex-direction: row;gap:15px;position: relative;}
.page5 .schedule-box{width:624px;height:270px;background: url(../img/mass-scheduleBg.png) no-repeat center ;background-size: 100% auto;margin: 10px auto 30px; display: flex;flex-direction: row;position: relative;}
.page .schedule-box .item{width:194px;height: 180px;display: flex;justify-content: center;}
.page .schedule-box .item:nth-child(1){margin-left: 17px;}
.page .schedule-box .item:nth-child(3){margin-left: 7px;}
.page .schedule-box .item .xz{width:136px;height:145px;background: url(../img/mass-gift.png) no-repeat center;background-size: 100% auto;}
.page .schedule-box .item .xz.get{width:136px;height:145px;background: url(../img/mass-gift2.png) no-repeat center;background-size: 100% auto;}
.page .schedule-box .item .xz.gray{width:140px;height:156px;background: url(../img/mass-gift3.png) no-repeat center;background-size: 100% auto;}
.page .schedule-box .item .people.active{position: absolute;bottom: 0;}
.page .schedule-box .item:nth-child(1) .people.active{width:121px;height:46px;background: url(../img/num2-ac.png) no-repeat center;background-size: 100% auto;left:45px;}
.page .schedule-box .item:nth-child(2) .people.active{width: 121px;;height: 46px;;background: url(../img/num4-ac.png) no-repeat center;background-size: 100% auto;left:253px;}
.page .schedule-box .item:nth-child(3) .people.active{width: 121px;;height: 46px;;background: url(../img/num6-ac.png) no-repeat center;background-size: 100% auto;left:457px;}
.page .schedule{height: 180px;background: url(../img/mass-schedule.png) no-repeat center left;max-width: 100%;background-size: 583px auto;position: absolute;top: 108px;left: 22px;pointer-events: none;}
.add-list{width:596px;font-size: 19px;color: #ffefd8;line-height: 37px;font-weight: bold;display: flex;flex-direction: row;flex-wrap: wrap;justify-content: space-evenly;margin: 0 auto 0;}
.add-list li{width: 75px;height: 97px;position: relative;}
.avatar{width: 75px;height: 75px;background: url(../img/btn-massAdd.png) no-repeat center;background-size: 100% auto;position: absolute;}
.avatar.dragon::after{content: "";display: block;position: absolute;top: 0;right: 0;bottom: 0;left: 0;width: 150px;height: 150px;background: url(../img/mass-avatarDragon.png) no-repeat center;background-size: 100% auto;}
.avatar.tk::after{content: "";display: block;position: absolute;top:-5px;left:0;width: 27px;height: 25px;background: url(../img/icon-tk.png) no-repeat center;background-size: 100% auto;}
.avatar img{width: 84px;height: 84px;border-radius: 50%;overflow: hidden;margin: 0 auto;}/*padding-top: 33px;*/
.avatar img[src=""] {display: none;}
.name{font-size: 20px;color: #58504c;line-height: 38px;}

.add-list li .name{font-size: 18px;line-height: 32px;width: 75px;height: 31px;margin: 81px auto 0;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 1;overflow: hidden;}
.bt-box{width: 380px;height: auto;display: flex;flex-direction: column;justify-content:center;margin:40px auto 0;}
.bt-box a{margin-bottom: 26px;width: 379px;height: 74px;transform: scale(1.2);}
.bt-box .btn-massF{background: url(../img/btn-massF.png) no-repeat center;background-size: 100% auto;}
.bt-box .btn-massM{background: url(../img/btn-massM.png) no-repeat center;background-size: 100% auto;}
.bt-box .btn-massT{background: url(../img/btn-massT.png) no-repeat center;background-size: 100% auto;}
.bt-box .btn-apply{background: url(../img/btn-apply.png) no-repeat center;background-size: 100% auto;}
.bt-box .btn-createSelf{background: url(../img/btn-createSelf.png) no-repeat center;background-size: 100% auto;}
.gift-detail{width: 284px;height: 195px;background: url(../img/mass-giftDetail.png) no-repeat center;background-size: 100% auto;position: absolute;bottom: 280px;display: none;}
.gift-detail ul{width: 284px;height: 120px;overflow-y: scroll;margin-top: 48px;display: flex;flex-direction: column;align-items: center;justify-content: center;}
.gift-detail ul li{font-size: 22px;color: #ffeecf;line-height: 30px;}

/*.btn-logout{width: 120px;height: 70px;background: url(../img/btn-loginOut.png) no-repeat center;background-size: 100% auto;position: absolute;right: 34px;top: 74px;}*/
/* pop */
.pop h1,h2,p{text-align: center;color: #4c4537;}
.pop h1,.txt-box{margin: 0 auto;font-size: 42px;}
.pop h1{padding-top: 30px;font-weight: bold;}
.pop .txt-box{width: 500px;height: 130px;position: absolute;top: 50%;left: 50%;transform: translate(-50%,-50%); color: #4c4537;}
.pop-common .txt-box{transform: translate(-50%,-65%);font-size:32px;display: flex;flex-direction: column; align-items: center; justify-content: center;}
.pop .btn-boxPop{display: flex;justify-content: center;align-items: center;position: absolute;bottom: 75px;left: 50%;transform: translateX(-50%);}
.pop .btn-boxPop>a:nth-child(1){margin-right: 20px;}
.pop .txt1{width: 580px; font-size: 24px;color: #4c4537;font-weight: bold;line-height: 44px;padding-top:70px;margin: 0 auto 14px;}
.pop .ipt-box>input{margin-left: 130px;text-align: center;font-size: 28px;color: #4c4537;line-height: 70px;background: none;border: none;}

.pop .user-list {width: 560px;height:446px;overflow-y: scroll;margin: 0 auto;}
.user-list li{width: 520px;height: 140px;display: flex;flex-direction: row; justify-content: flex-end;position: relative;margin-bottom: 26px; margin-left: 20px;}
.pop .no-data::after{content: '暂无数据~'; position: absolute; top: 50%; left: 50%; transform: translate(-50%,-50%); font-size: 24px;color: #4c4537;}
.pop .user-list li>div{width: 428px;height: 140px;background: url(../img/apply-itemBg.png) no-repeat center;background-size: 100% auto;}
.pop .user-list .user{width: 134px;height: 140px;position: relative;left: -88px;}
.pop .user .avatar {width: 134px;height: 134px;background: url(../img/apply-avatarBg.png) no-repeat center;background-size: 100% auto;}
.pop .user .avatar img {width: 110px;height: 110px;padding-top: 14px;}
.pop .user .name{width: 125px;height: 38px;background: url(../img/apply-nameBg.png) no-repeat center;background-size: 100% auto;position: absolute;bottom: 0; padding: 0 10px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;color:#ffffff;}

/*重置lay.js的toast弹窗样式*/
body .lay-msg{
    font-size: 24px;
    padding: 10px 22px;
}

body .lay-load{
    font-size: 24px;
}
body .lay-load .lay-load-icon{
    width: 60px;
    height: 60px;
    background-size: 100% auto;
    background-position: center center;
}