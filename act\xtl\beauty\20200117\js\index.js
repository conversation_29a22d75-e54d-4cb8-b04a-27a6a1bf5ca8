// $(function () {
var example = {
    base_url: '/goddess/',
    loginFlag: false, //登录标识
    banFlag: false, //封停标识
    clickFlag: false, //防抖标识
    isLongmen: location.href.indexOf('longmen') != -1,
    initParam: {
        app: 'xtl',
        activity: 'goddess',
        version: location.href.indexOf('longmen') != -1 ? '2020011701' : '20200117',
        platform: 'changyou',
    },
    isActivity: true, // 是否在活动时间内
    maxPage_1: 1,
    maxPage_2: 2,
    resData_1: {},
    resData_2: {},
    maxSubmit: '',
    myVoteNum: Number,
    isSigned: false,
    queryData_1: {
        pageNum: 1,
        pageSize: !ja.glob.isMobile ? 10 : 4,
        orderby: 'ssn',
        authlevel: 1,
        keyword: '',
        zone: '',
        groupNo: ''
    },
    queryData_2: {
        pageNum: 1,
        pageSize: !ja.glob.isMobile ? 10 : 4,
        orderby: 'ssn',
        authlevel: 2,
        keyword: '',
        zone: '',
        groupNo: ''
    },
    init: function () {
        ja.config({
            /* config配置项详见 表 3.2 */
            app: this.initParam.app, // 必填，应用标识
            activity: this.initParam.activity, // 必填，活动标识
            version: this.initParam.version, // 必填，活动版本标识
            platform: this.initParam.platform, // 必填，登陆平台标识
            type: 1,
            isWxLogin: true,
            // isWxInit: true,
            ready: function (glob) { // 非必填，初始化完成回调
                /*
                * 通过判断 glob.code 值，判断是否登录成功
                *       0 初始化未完成；
                *       1 已登录；
                *       2 未登录；
                *       1106 活动未开始；
                *       1103 活动已结束；
                *       1102 活动无效;
                * 返回全局对象 glob,详见 表 3.3
                */
                example.queryGoddessList(1)
                example.queryGoddessList(2)
                example.actvInfo()
                example.rankList()
                glob.code === 1 ? example.loginAfter() : example.loginBefore();

                ja.glob.isWechat && ja.wx.init(function () {
                    ja.wx.share({
                        link: location.href,
                        title: '芳菲满园觅佳人，《新天龙八部》全服女神大赛火热进行中！',
                        desc: '上传自拍照片及视频，即可获得稀有时装！参与视频验证，还有机会获得永久时装及坐骑：绮梦蝶！',
                        imgUrl: 'http://i0.cy.com/tlpt/beauty/share.jpg',
                    })
                })

                if (example.isLongmen) {
                    ja.utils.longmen({
                        // color: 'white',
                        top: (ja.glob.isMobile ? 240 : 170),
                        left: 20
                    });
                }
            }
        });

        $('#login').on('click', function () {
            if (ja.glob.code == 0) {
                example.tips('网络繁忙，请稍后再试')
                return
            } else if (ja.glob.code == 1) {
                example.loginAfter()
                return
            } else if (ja.glob.code == 2) {
                ja.login()
                return
            } else if (ja.glob.code == 1106) {
                example.tips('活动未开始')
                return
            } else if (ja.glob.code == 1103) {
                example.tips('活动已结束')
                return
            } else if (ja.glob.code == 1102) {
                m.tips('活动无效')
                return
            }
        })
        $('#logout').on('click', function () {
            ja.logout()
        })

        // 分页按钮
        $('.prevbtn, .nextbtn').on('click', function () {
            if (this.title == '上一页') {
                if ($(this).attr('pageType') == 1) {
                    if (example.queryData_1.pageNum <= 1) return;
                    example.queryData_1.pageNum--
                    example.queryGoddessList(1)
                } else {
                    if (example.queryData_2.pageNum <= 1) return;
                    example.queryData_2.pageNum--
                    example.queryGoddessList(2)
                }
            } else {
                if ($(this).attr('pageType') == 1) {
                    if (example.queryData_1.pageNum >= example.maxPage_1) return;
                    example.queryData_1.pageNum++
                    example.queryGoddessList(1)
                } else {
                    if (example.queryData_2.pageNum >= example.maxPage_2) return;
                    example.queryData_2.pageNum++
                    example.queryGoddessList(2)
                }
            }
        })

        $('#video_addr').on('blur', function () {
            example.splitVideo(this)
        })

        // 个人中心
        $('#centerBtn').on('click', function () {
            if (example.commHandle()) return;
            example.queryTicket()
            example.personCenter()
        })

        // 我要参赛
        $('.join').on('click', function () {
            if (example.commHandle()) return;
            if (example.isSigned) { example.tips('您已报名参赛，请勿重复报名!'); return }
            if (!example.isActivity) { example.tips('当前不在报名时间段内!'); return }
            showDia('pop_bm')
            $('.submit').attr('data-subType', 1)
        })

        // 查看详情
        $('.gostlist').on('click', 'img', function () {
            example.details(this)
        })

        // 发送验证码
        $('.yzbtn').on('click', function () {
            if ($('#phone').val() == '') {
                alert('请填写您的手机号码')
                return
            } else if (!example.codeVerification($('#phone').val())) {
                alert('请填写正确的手机号码')
                return
            } else {
                example.sendcode($('#phone').val())
            }
        })

        // 关键字搜索
        $('.search').on('click', function () {
            var box = $(this).parents('.searchbox');
            var data = {
                keyword: box.find('.keyword').val()
            }

            if ($(this).attr('pageType') == 1) {
                example.searchClear(1)
                $.extend(example.queryData_1, data);
                example.queryGoddessList(1)
            } else {
                example.searchClear(2)
                $.extend(example.queryData_2, data)
                example.queryGoddessList(2)
            }
        })
        // 大区
        $('.zone').on('change', function () {
            example.searchClear()
            var box = $(this).parents('.searchbox');
            var data = {
                zone: box.find('.zone option:selected').text() == '所在大区' ? '' : box.find('.zone option:selected').text(),
            }
            if ($(this).attr('pageType') == 1) {
                example.searchClear(1)
                $.extend(example.queryData_1, data);
                example.queryGoddessList(1)
            } else {
                example.searchClear(2)
                $.extend(example.queryData_2, data)
                example.queryGoddessList(2)
            }
        })
        // 服务器搜索
        $('.server').on('change', function () {
            example.searchClear()
            var box = $(this).parents('.searchbox');

            if (box.find('.server option:selected').text() == '所在服务器') {
                var data = {
                    zone: box.find('.zone option:selected').text() == '所在大区' ? '' : box.find('.zone option:selected').text(),
                }
            } else {
                var data = {
                    groupNo: box.find('.server option:selected').text() == '所在服务器' ? '' : box.find('.server option:selected').attr('group_no'),
                }
            }
            if ($(this).attr('pageType') == 1) {
                example.searchClear(1)
                $.extend(example.queryData_1, data);
                example.queryGoddessList(1)
            } else {
                example.searchClear(2)
                $.extend(example.queryData_2, data)
                example.queryGoddessList(2)
            }
        })

        // 排序
        $('.sort').on('click', function () {
            // 重置下区服的选中状态
            $(this).parents('.searchbox').find('.zone').children().eq(0).prop('selected', true)
            $(this).parents('.searchbox').find('.server').children().eq(0).prop('selected', true)

            if ($(this).attr('pageType') == 1) {
                example.searchClear(1)
                example.queryData_1.orderby = $(this).attr('sortType')
                example.queryGoddessList(1)
                return false;
            } else {
                example.searchClear(2)
                example.queryData_2.orderby = $(this).attr('sortType')
                example.queryGoddessList(2)
                return false;
            }
        })

        // 投票
        $('body').on('click', '.vote', function () {
            if (example.commHandle()) return;
            if (!example.isActivity) { example.tips('当前不在投票时间段内!'); return }
            example.vote($(this))
        })

        // 提交报名表
        $('.submit').on('click', function () {
            // 角色
            if ($('#nickname').val() == '') {
                alert('请选择角色');
                return
            }

            var reg = /[^\u4e00-\u9fa5]/g;
            if (reg.test($(this).val())) {
                alert('省市名只能输入中文！')
            }
            //省份
            if ($('#province').val() == '') {
                alert('请填写省份');
                return
            } else if (reg.test($('#province').val())) {
                alert('省市名只能输入中文！')
                return
            }

            //城市
            if ($('#city').val() == '') {
                alert('请填写城市');
                return
            } else if (reg.test($('#city').val())) {
                alert('省市名只能输入中文！')
                return
            }

            //年龄
            if ($('#age').val() == '') {
                alert('请填写年龄');
                return
            }

            //星座
            if ($('#yy').val() == '') {
                alert('请选择星座');
                return
            }

            //参赛宣言
            if ($('#entry').val() == '') {
                alert('请填写20字以内的参赛宣言');
                return
            }

            // 是否愿意直播
            if (example.IsPC()) {
                var val = $('input:radio[name="live"]:checked').val();
            } else {
                var val = $('#isLl span').find("em.active").attr('zb')
            }
            if (val == null) {
                alert("请选择是否愿意参加官方露脸直播活动");
                return;
            }

            //上传参选照片
            if ($('#imageList1 img[src]').length < 2) {
                alert('请上传2张以上5张以内参选照片');
                return
            }

            //上传游戏形象照
            if ($('#imageList2 img[src]').length < 1) {
                alert('请上传游戏形象照片');
                return
            }

            //视频地址
            if ($('#video_addr').val() == '' || $('#video_addr').val().length >= 250) {
                alert('请上传美拍视频链接！');
                return
            }

            //电话号码验证
            function codeVerification(phone) {
                let phoneCodeVerification = /^[1][3,4,5,7,8][0-9]{9}$/;
                return phoneCodeVerification.test(phone);
            };

            //手机号
            if ($('#phone').val() == '') {
                alert('请填写手机号');
                return
            }
            if (!codeVerification($('#phone').val())) {
                alert('请填写正确的手机号');
                return;
            }

            //验证码正则验证
            var number = $('#number').val();
            var re = /^\d{6}$/;
            if (!re.test(number)) {
                alert('请填写正确的验证码');
                return;
            }

            //qq
            if ($('#qq').val() == '') {
                alert('请填写QQ号');
                return
            }

            // 组装数据
            var images = [];
            for (var i = 0; i < $('#imageList1 img[src]').length; i++) {
                images.push($('#imageList1 img[src]')[i].src)
            }
            images = images.join('@@@@@')
            var role = $('#nickname option:selected').text().split('_');

            var data = {
                zone: role[0],
                server: role[1],
                roleName: role[2],
                roleGuid: $('#nickname option:selected').attr('roleGuid'),
                province: $('#province').val(),
                city: $('#city').val(),
                age: $('#age').val(),
                constellation: $('#yy').val(),
                qq: $('#qq').val(),
                phone: $('#phone').val(),
                video: $('#video_addr').val(),
                gameimage: $('#imageList2 img[src]')[0].src,
                image: images,
                declaration: Base64.encode($('#entry').val()),
                code: number,
                exif: val,
                groupNo: $('#nickname').val()
            }

            example.submit(data, $(this).attr('data-subType'))
        })

        // 查看周榜
        $('rankbtn').click()

        // 周榜分页
        $('#rankPage').on('click', 'span', function () {
            $(this).siblings().removeClass('on')
            $(this).addClass('on')
            if ($(this).text() == 1) {
                $('#rankBox tr:gt(4)').hide()
                $('#rankBox tr:lt(5)').show()
            } else {
                $('#rankBox tr:gt(4)').show()
                $('#rankBox tr:lt(5)').hide()
            }
        })

        // 为自己拉票
        $('#persinf1').on('click', '.getQR', function () {
            if (example.IsPC()) {
                example.getQRCode($(this).attr('data-sn'));
                showDia('pop_myself');
                return;
            } else {
                example.wxShare($(this).attr('data-sn'))
            }
        })

        $('#goWx').on('click', function () {
            example.wxShare($(this).attr('data-sn'))
        })

        // 修改信息时候改动提交为调用更新信息接口
        $('.jxbtn').on('click', function () {
            $('.submit').attr('data-subType', 2)
        })

        $('#province').blur(function () {
            var reg = /[^\u4e00-\u9fa5]/g;
            if (reg.test($(this).val())) {
                alert('省市名只能输入中文！')
            }
            return false
        })
        $('#city').blur(function () {
            var reg = /[^\u4e00-\u9fa5]/g;
            if (reg.test($(this).val())) {
                alert('省市名只能输入中文！')
            }
            return false
        })
    },
    loginAfter: function () {
        $('.login_box').hide()
        $('.user_name').text(ja.glob.userInfo.openid)
        $('.logout_box').show()

        example.queryTicket();
        example.queryRole();
        example.personCenter(1)
        example.loginFlag = true;
    },
    loginBefore: function () {
        // nothing
        example.loginFlag = false;
    },
    /**
    * 通用错误码处理
    * @param {*} data 返回的对象
    */
    commonErrPop: function (data) {
        var flag = false;
        example.clickFlag = false;
        if (data.code == '1202' || data.code == '1209' || data.code == '1207' || data.code == '1007') { //1202请求参数缺失或者为空  1209操作频繁
            example.tips('系统繁忙，请稍后重试！');
            return;
        } else if (data.code == '1019' || data.code == '1012') { //登录状态检测失败
            showDialog.hide()
            $('#login').click();
            return;
        } else if (data.code == '3103') { //账号封停
            example.tips('您的账号已停权')
            example.banFlag = true;
            return;
        } else if (data.code == 3304) {
            alert(res.message)
            return
        } else if (data.code == 5074) {
            alert('少侠，今日已完成投票，请明日再来')
            return
        } else {
            flag = true;
        }
        return flag;
    },
    /**
    * 通用的处理方法  处理未登录与封停账号
    */
    commHandle: function () {
        var flag = true;
        if (ja.glob.code == 0) {
            example.tips('网络繁忙，请稍后再试')
            return flag;
        } else if (ja.glob.code == 1106) {
            example.tips('活动未开始')
            return flag;
        } else if (ja.glob.code == 1103) {
            example.tips('活动已结束')
            return flag;
        } else if (ja.glob.code == 1102) {
            example.tips('活动无效')
            return flag;
        } else if (!example.loginFlag) {
            $('#login').click();
            return flag;
        } else if (example.banFlag) {
            example.tips('您的账号已停权')
            return flag;
        } else {
            flag = false;
            return flag;
        }
    },
    /**
    * 通用提示
    */
    tips: function (msg) {
        $('#tips p').html(msg);
        showDia('tips')
    },
    /**
    * 判断设备
    */
    IsPC: function () {
        var userAgentInfo = navigator.userAgent;
        var Agents = new Array('Android', 'iPhone', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod');
        var flag = true;
        for (var v = 0; v < Agents.length; v++) {
            if (userAgentInfo.indexOf(Agents[v]) > 0) { flag = false; break; }
        }
        return flag;
    },
    /**
     * 初始化搜索条件
     * @param {*} a
     */
    searchClear: function (a) {
        if (a == 1) {
            example.queryData_1 = {
                pageNum: 1,
                pageSize: !ja.glob.isMobile ? 10 : 4,
                orderby: 'ssn',
                authlevel: 1,
                keyword: '',
                zone: '',
                groupNo: ''
            }
        } else {
            example.queryData_2 = {
                pageNum: 1,
                pageSize: !ja.glob.isMobile ? 10 : 4,
                orderby: 'ssn',
                authlevel: 2,
                keyword: '',
                zone: '',
                groupNo: ''
            }
        }
    },
    /**
    * 获取活动个性化配置信息
    */
    actvInfo: function () {
        ja.ajax({
            type: 'GET',
            url: this.base_url + 'actvInfo',
            async: true,
            success: function (res) {
                if (res.code == 10000) {
                    var date = new Date();
                    if (res.data.applyEndTime < date.getTime() || res.data.applyStartTime > date.getTime()) {
                        example.isActivity = false;
                    }
                    example.maxSubmit = res.data.maxSubmitTimes
                } else if (example.commonErrPop(res)) {
                    //alert(res.message);
                }
            }
        })
    },
    /**
    * 个人中心
    */
    personCenter: function (a) {
        if (example.clickFlag) {
            return;
        }
        example.clickFlag = true;
        ja.ajax({
            type: 'POST',
            url: this.base_url + 'personCenter',
            success: function (res) {
                if (res.code == 10000) {
                    example.clickFlag = false;
                    // $('.join').hide() //已报名就隐藏报名按钮
                    example.isSigned = true;
                    example.myVoteNum = res.data.voteNum;
                    $('#mySn').text(res.data.sn ? res.data.sn : '暂无');
                    $('#myName').text(res.data.server + '_' + res.data.roleName);
                    var status = '';
                    switch (res.data.status) {
                        case 0:
                            $('#myStatus').next().remove()
                            $('#myStatus').text('初级认证审核中,请耐心等待');
                            $('.laPiao').remove()
                            break;
                        case 1:
                            $('#myStatus').next().remove()
                            $('.laPiao').remove()
                            $('#myStatus').text('初级认证失败').after('<a href="javascript:showDia(\'failApply\');" class="btn renZheng xgbtn" title="修改信息">修改信息</a>');
                            // example.isSigned = false;
                            break;
                        case 2:
                            $('#myStatus').next().remove() // 认证状态后的按钮
                            $('.stats').next().remove() // 移除拉票
                            $('#myStatus').text('初级认证').after('<a href="javascript:example.applyHigh();" class="btn sqbtn renZheng" title="申请高级认证">申请高级认证</a>');
                            $('.stats').after('<a href="javascript:;" title="为自己拉票" class="btn lpbtn getQR sp laPiao" data-sn="' + res.data.sn + '" >为自己拉票</a>')
                            break;
                        case 9:
                            $('#myStatus').next().remove()
                            $('.laPiao').remove()
                            $('#myStatus').text('初级认证失败');
                            break;
                    }

                    if (example.maxSubmit <= res.data.submitTimes && res.data.status != 2) {
                        $('#myStatus').next().remove()
                        $('.stats').next().remove()
                        $('#myStatus').append('(已达到修改次数上限)');
                    }

                    if (res.data.authlevel == 2) {
                        $('#myStatus').next().remove()
                        $('#myStatus').text('高级认证')
                    }
                    var gift = '';
                    if (res.data.awardStatus == 0) {
                        // gift = '暂无'
                        $('#gift').parents('li').hide()
                    } else if (res.data.awardStatus == 1) {
                        gift = '初级认证礼包'
                        $('#gift').parents('li').show()
                        $('#gift').text(gift)
                    } else if (res.data.awardStatus == 10) {
                        gift = '高级认证礼包'
                        $('#gift').parents('li').show()
                        $('#gift').text(gift)
                    }
                    !a && showDia('persinf1')
                } else if (example.commonErrPop(res)) {
                    if (res.code == 1000) {
                        !a && showDia('persinf1');
                        return
                    }
                }
            }
        })
    },
    /**
     * 申请高级认证
     */
    applyHigh: function () {
        if (example.commHandle()) return;

        if (!example.isActivity) { example.tips('当前不在报名时间段内!'); return }
        if (example.myVoteNum >= 50) {
            showDia('applyHigh');
        } else {
            $('#nowVote').text(example.myVoteNum)
            showDia('cantApply');
        }
    },
    /**
     * 上传图片
     * @param {*} a 图片base64编码  多个用'@@@@@'分割
     */
    uploadImg: function (code) {
        if (example.clickFlag) {
            return;
        }
        example.clickFlag = true;
        ja.ajax({
            type: 'POST',
            url: example.base_url + 'uploadImg',
            dataType: "JSON",
            processData: false,
            contentType: false,
            data: code,
            success: function (res) {
                if (res.code == 10000) {
                    example.clickFlag = false;
                    if (example.IsPC()) {
                        var t = imageCropper.attr('len');
                        $('.imageList li').eq(t).find('img').attr('src', res.data.imgPathList[0]).show();
                        $('.imageList a').eq(t).hide();
                        $('.imageList .mcbox').eq(t).show();
                        $('.imageList li').eq(t).find('.delebtn').show()
                        showDialog.hide();
                        showDia('pop_bm')
                    } else {
                        // t 为上传的图片个数
                        var t = imageCropper.attr('len');
                        if (mage_inpt) {
                            $('.mage_inpt').parent().find('span').show()
                            $('.mage_xx_box img').eq(t).show().attr('src', res.data.imgPathList[0]);
                        } else {
                            _this.show()
                            $('#imageList1 img').eq(t).show().attr('src', res.data.imgPathList[0]);
                        }
                        a.hide()
                        $('#cropperPop').hide()
                    }
                } else if (example.commonErrPop(res)) {
                    alert('上传失败');
                }
            },
            error: function () {
                example.clickFlag = false;
                alert('上传失败')
            }
        })
    },
    /**
     * 首次报名/更新资料
     * @param {*} a 信息
     * @param {*} type 1首次 2更新
     */
    submit: function (a, type) {
        if (example.clickFlag) {
            return;
        }
        example.clickFlag = true;
        ja.ajax({
            type: 'POST',
            url: this.base_url + (type == 1 ? 'createRegUser' : 'updateRegUser'),
            data: a,
            success: function (res) {
                if (res.code == 10000) {
                    example.clickFlag = false;
                    example.isSigned = true;
                    // console.log(res);
                    showDia('submitSuc')
                } else if (example.commonErrPop(res)) {
                    if (res.code == 3002) {
                        alert('短信验证码错误！')
                    } else {
                        alert(res.message);
                    }
                }
            }
        })
    },
    /**
     * 查询女神列表
     * @param {*} a
     */
    queryGoddessList: function (a) {

        if (typeof a == 'number') {
            if (a == 1) {
                var data = example.queryData_1;
            } else {
                var data = example.queryData_2;
            }
        } else if (typeof a == 'object') {
            if (a.authlevel == 1) {
                $.extend(example.queryData_1, a);
                var data = example.queryData_1
            } else {
                $.extend(example.queryData_2, a);
                var data = example.queryData_2
            }
        }

        ja.ajax({
            type: 'GET',
            url: this.base_url + 'queryGoddessList',
            data: data,
            success: function (res) {
                if (res.code == 10000) {
                    var info = res.data.xtlGoddessUserList;
                    // 设置分页
                    if (data.authlevel == 1) {
                        example.maxPage_1 = info.pages
                        $('.pageNum[pageType=1]').text((info.pages == 0 ? '0' : example.queryData_1.pageNum) + '/' + info.pages)
                    } else {
                        example.maxPage_2 = info.pages
                        $('.pageNum[pageType=2]').text((info.pages == 0 ? '0' : example.queryData_2.pageNum) + '/' + info.pages)
                    }

                    // 数据渲染
                    if (data.authlevel == 1) {
                        var box = $('#level_1')
                        example.resData_1 = info.list;
                        $(example.resData_1).each(function (i, v) {
                            v.image = v.image.split('@@@@@')
                        })
                    } else {
                        var box = $('#level_2')
                        example.resData_2 = info.list;
                        $(example.resData_2).each(function (i, v) {
                            v.image = v.image.split('@@@@@')
                        })
                    }

                    if (box.next().text() == '暂无数据') {
                        box.next().remove()
                    };
                    if (info.list.length == 0) {
                        box.empty()
                        box.after('<p style="color: #8a7577;font-size: 24px;font-weight: bold;line-height: 50px;margin-top:15px;text-align: center;margin-bottom: 0;">暂无数据</p>')
                        return;
                    }

                    var list = ''
                    $(info.list).each(function (i, v) {
                        if (example.IsPC()) {
                            list += '<li>' +
                                '<div class="gostbox">' +
                                '<p>编号:' + v.sn + '</p>' +
                                '<img src="' + v.image[0] + '" width="190" height="190" pageType="' + data.authlevel + '" alt="' + v.roleName + '">' +
                                '</div>' +
                                '<h3>' + v.roleName + '</h3>' +
                                '<div class="infbox">' +
                                '<p>' + v.server.split('_')[0] + '</p>' +
                                '<p>' + v.server.split('_')[1] + '</p>' +
                                '</div>' +
                                '<div class="zhicbox">' +
                                '<p>票数：<span class="ticketCount">' + v.voteNum + '</span></p>' +
                                '<a href="javascript:;" class="btn zcbtn vote" title="支持她" data-sn="' + v.sn + '">支持她</a>' +
                                '</div>' +
                                '</li>';
                        } else {
                            list += '<li>' +
                                '' + (data.authlevel == 1 ? '' : '<h5>官方认证</h5>') + '' +
                                '<div class="img_box">' +
                                '<strong>编号:' + v.sn + '</strong>' +
                                '<img src="' + v.image[0] + '" title="" alt="" width="267" height="267" pageType="' + data.authlevel + '" alt="' + v.nickName + '">' +
                                '</div>' +
                                ' <div class="message_box">' +
                                '<h6>' + v.roleName + '</h6>' +
                                '<p>' + v.server.split('_')[0] + '</p>' +
                                '<p>' + v.server.split('_')[1] + '</p>' +
                                '</div>' +
                                '<div class="vote_box">' +
                                '<strong>票数：<span class="ticketCount">' + v.voteNum + '</span></strong>' +
                                '<a href="javascript:void(0);" class="sp vote" title="支持她" data-sn="' + v.sn + '">支持她</a>' +
                                '</div>' +
                                '</li>';
                        }
                    })

                    box.html(list)

                } else if (example.commonErrPop(res)) {
                    //alert(res.message);
                }
            }
        })
    },
    /**
     * 裁剪视频地址
     * @param {*} a
     */
    splitVideo: function (a) {
        var text = $(a).val();
        var reg = 'http://www.meipai.com/media/';
        var index = text.indexOf(reg);
        if (index == -1) {
            alert('请上传美拍视频链接！')
            $(a).val('')
        } else {
            $(a).val(text.substr(index))
        }
    },
    /**
     * 获取周榜
     */
    rankList: function () {
        ja.ajax({
            type: 'GET',
            url: '/vote/getWeekVotes',
            success: function (res) {
                if (res.code === 10000) {
                    var tr = '', rank = '';
                    if (example.IsPC()) {
                        $(res.data).each(function (i, v) {
                            if (i >= 10) return;
                            switch (i) {
                                case 0:
                                    rank = '<i class="com pa diyi">第一</i>';
                                    break;
                                case 1:
                                    rank = '<i class="com pa dier">第二</i>'
                                    break;
                                case 2:
                                    rank = '<i class="com pa disan">第三</i>'
                                    break;
                                default:
                                    rank = '';
                                    break
                            }
                            tr += '<tr>' +
                                '<td width="72"><span>' + (i + 1) + '</span></td>' +
                                '<td width="108">' +
                                '<div class="bdimgbox">' +
                                '<img src="' + v.image.split('@@@@@')[0] + '" width="65" height="65" alt="' + v.roleName + '">' +
                                '<em class="com zhez pa">遮罩</em>' +
                                '' + rank + '' +
                                '</div>' +
                                '</td>' +
                                '<td width="230">' + v.roleName + '</td>' +
                                '<td class="ft18">' + v.count + '</td>' +
                                '</tr>';
                        })
                    } else {
                        $(res.data).each(function (i, v) {
                            if (i >= 10) return;
                            switch (i) {
                                case 0:
                                    rank = 'one';
                                    break;
                                case 1:
                                    rank = 'tow'
                                    break;
                                case 2:
                                    rank = 'three'
                                    break;
                                default:
                                    rank = '';
                                    break
                            }
                            tr += '<tr>' +
                                '<td>' +
                                '<span class="fl">' + (i + 1) + '</span>' +
                                '<div class="head sp fl ' + rank + '">' +
                                '<img src="' + v.image.split('@@@@@')[0] + '" alt="' + v.roleName + '">' +
                                '</div>' +
                                '</td>' +
                                '<td>' + v.roleName + '</td>' +
                                '<td>' + v.count + '</td>' +
                                '</tr>';
                        })
                    }

                    $('#rankBox').html(tr);

                    if ($('#rankBox').children().length > 5) {
                        $('#rankBox tr:gt(4)').hide()
                        $('#rankPage').append('<span>2</span>')
                    }
                } else if (example.commonErrPop(res)) {
                    //alert(res.message);
                }
            }
        })
    },
    /**
     * 查看选手详情
     * @param {*} a
    */
    details: function (a) {
        var $this = $(a),
            data;
        if ($this.attr('pageType') == 1) {
            data = example.resData_1
        } else {
            data = example.resData_2
        }
        var info = data[$this.parents('li').index()];

        if (example.IsPC()) {
            $('#pop_inf .le_box').html('<h3>选手编号：' + info.sn + '</h3><p> ' + info.roleName + ' <br> ' + info.server.split('_')[0] + ' <br> ' + info.server.split('_')[1] + '</p><img src="' + info.image[0] + '" width="190" height="190" alt="' + info.nickName + '">')
            $('#pop_inf .useinf').html('城市：' + info.province + info.city + '<br>年龄：' + info.age + '<br>星座：' + info.constellation + '<br>参赛宣言：')
            $('#pop_inf .csxy').html(Base64.decode(info.declaration));
            example.getQRCode(info.sn)
            var li = '';
            li += '<li><img src="' + info.gameimage + '"  alt="' + info.roleName + '"><p>游戏角色</p></li>'
            $(info.image).each(function (i, v) {
                li += '<li><img src="' + v + '" alt="' + info.roleName + '"></li>'
            })
            $('#pop_inf .mzlist').html(li)
            $('#pop_inf .vlist li').attr('data-video', info.video)
        } else {
            $('#playerSn').text('选手编号：' + info.sn)
            $('#playerName').text(info.roleName)
            $('#playerZone').text(info.server.split('_')[0])
            $('#playerServer').text(info.server.split('_')[1])
            $('#playerCity').text(info.province + info.city)
            $('#playerAge').text(info.age)
            $('#playerCon').text(info.constellation)
            $('#playerXy').text(Base64.decode(info.declaration))
            $('#goWx').attr('data-sn', info.sn);
            var div = '';
            div += '<div class="swiper-slide"><img src="' + info.gameimage + '" alt="' + info.roleName + '"></div>'

            $(info.image).each(function (i, v) {
                div += '<div class="swiper-slide"><img src="' + v + '" alt="' + info.roleName + '"></div>'
            })
            $('#playerImg').html(div);
            $('.play_btn').attr('data-video', info.video);
        }
        $('#pop_inf .vote').attr('data-sn', info.sn).attr('data-index', $this.parents('li').index());
        showDia('pop_inf')
    },
    /**
     * 投票
     * @param {*} a jquery对象
    */
    vote: function (a) {
        if (example.clickFlag) {
            return;
        }
        example.clickFlag = true;
        ja.ajax({
            type: 'post',
            url: '/vote/pcVote',
            data: {
                userId: a.attr('data-sn').toString()
            },
            success: function (res) {
                if (res.code === 10000) {
                    example.clickFlag = false;
                    var $a = $($('a[data-sn="' + a.attr('data-sn') + '"]')[0]);
                    // var tic = $a.prev().find('.ticketCount').text()
                    // console.log(tic);

                    if ($a.prev().find('.ticketCount').length) {
                        var num = Number($a.prev().find('.ticketCount').text())
                        num++;
                        $a.prev().find('.ticketCount').text(num)
                    }
                    alert('助力成功')
                } else if (example.commonErrPop(res)) {

                }
            }
        })
    },
    /**
     * 分享页投票
     * @param {*} a 用户标识
    */
    shareVote: function (a) {
        if (example.clickFlag) {
            return;
        }
        example.clickFlag = true;
        ja.ajax({
            type: 'post',
            url: '/vote/wxVote',
            data: {
                userId: a.toString()
            },
            success: function (res) {
                example.clickFlag = false;
                if (res.code === 10000) {
                    alert('助力成功')
                } else if (example.commonErrPop(res)) {

                }
            }
        })
    },
    /**
     * 分享页票数查询
     */
    shareTicket: function () {
        ja.ajax({
            type: 'POST',
            url: '/vote/wxSelectTicketCount',
            success: function (res) {
                if (res.code === 10000) {
                    $('.ticket').text(res.data.count)
                } else if (example.commonErrPop(res)) {
                    //alert(res.message);
                }
            }
        })
    },
    /**
     * 发送短信验证码
     * @param {*} a 手机号码
    */
    sendcode: function (a) {
        var wait = 60;
        $('.yzbtn').hide()
        var that = $('.waitbtn');
        that.show();
        that.text('重新发送60s')
        var timeStop = setInterval(function () {
            wait--;
            if (wait > 0) {
                that.text('重新发送' + wait + 's');
                that.attr('disabled', 'disabled');//禁止点击
            } else {
                wait = 60;//当减到0时赋值为60
                $('.yzbtn').show()
                that.hide()
                clearInterval(timeStop);//清除定时器
                that.removeAttr('disabled');//移除属性，可点击
            }
        }, 1000)
        ja.ajax({
            type: 'POST',
            url: '/phone/core/zhpt/sendcode',
            data: { 'phone': a },
            success: function (res) {
                if (res.code == 10000) {

                } else if (example.commonErrPop) {
                    if (res.code == 3001) {
                        alert('发送失败')
                    } else {
                        // alert(res.message)
                    }
                }
            }
        })
    },
    /**
     * 电话号码正则校验
     * @param {*} phone
    */
    codeVerification: function (phone) {
        var phoneCodeVerification = /^[1][3,4,5,6,7,8,9][0-9]{9}$/;
        return phoneCodeVerification.test(phone);
    },
    /**
     * 当前票数查询
     */
    queryTicket: function () {
        ja.ajax({
            type: 'POST',
            url: '/vote/pcSelectTicketCount',
            success: function (res) {
                if (res.code === 10000) {
                    $('.ticket').text(res.data.count)
                } else if (example.commonErrPop(res)) {
                    //alert(res.message);
                }
            }
        })
    },
    /**
     * 查询当前账号下角色
     */
    queryRole: function () {
        ja.ajax({
            type: 'get',
            url: this.base_url + 'roleList',
            success: function (res) {
                if (res.code === 10000) {
                    var options = ''
                    $(res.data).each(function (i, v) {
                        options += '<option value="' + v.group_no + '" roleGuid="' + v.role_guid + '">' + v.server_name + '_' + v.role_name + '</option>'
                    })
                    $('#nickname').append(options)
                } else if (example.commonErrPop(res)) {
                    //alert(res.message);
                }
            }
        })
    },
    /**
     * 生产二维码
     * @param {*} a 选手编号
    */
    getQRCode: function (a) {
        $('.qrcode').empty()
        $('.qrcode').qrcode({
            text: location.origin + '/xtl/beauty/' + (example.isLongmen ? 'longmen' : '20200117') + '/m/share.shtml?sn=' + a,
            background: "#ffffff",
            foreground: "#000000",
            width: 172,
            height: 172,
            correctLevel: 1
        });
    },
    /**
     * 微信分享
     * @param {*} a 选手编号
     */
    wxShare: function (a) {
        if (!ja.glob.isWechat && ja.glob.isMobile) {
            example.tips('请在微信打开活动分享拉票');
            return
        } else if (ja.glob.isWechat) {
            ja.wx.share({
                link: location.origin + '/xtl/beauty/' + (example.isLongmen ? 'longmen' : '20200117') + '/m/share.shtml?sn=' + a,
                title: '我正在参加天龙全服女神大赛，快来投我一票吧！',
                desc: '《新天龙八部》全服女神大赛正火热进行中！参与即得稀有时装！',
                imgUrl: 'http://i0.cy.com/tlpt/beauty/share.jpg',
            })
            showDia('share');
            return;
        }
    }
}

example.init()
                                // })
