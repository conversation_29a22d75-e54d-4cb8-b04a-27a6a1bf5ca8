<!DOCTYPE HTML>
<html>

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="keywords" content="新天龙八部,天龙八部,武侠浪漫美学,网络游戏,浪漫武侠,武侠游戏,金庸,萧峰,段誉,虚竹,王语嫣,xtl,xtlbb,tlbb,天龙八部钟汉良版,天龙八部黄日华版" />
	<meta name="description" content="畅游2013年浪漫武侠网游巨作《新天龙八部》，以极致惊艳的唯美光影，对情节与角色的全面重铸，开创全新形态的浪漫江湖！" />
	<meta name="author" content="Design:CP; Web Layout:CP;" />
	<title>江湖魅力秀-《新天龙八部》官方网站</title>
	<link type="image/x-icon" href="//tl.changyou.com/favicon.ico" rel="icon" />
	<link type="image/x-icon" href="//tl.changyou.com/favicon.ico" rel="shortcut icon" />
	<link type="text/css" rel="stylesheet" href="/act/all/css/reset.css" />
	<link type="text/css" rel="stylesheet" href="/act/xtl/match/20230914/pc/css/cropper.css" />
	<link type="text/css" rel="stylesheet" href="/act/xtl/match/20230914/pc/css/index.css" />
	<link type="text/css" rel="stylesheet" href="/act/xtl/match/20230914/pc/css/pop.css" />
	<script>
		(function () {
			if (/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)) {
				window.location.href = "../m/single.shtml" + location.search
			};
		})();
	</script>
	<script src="https://www.changyou.com/cyouFile/loger/huodong.js"></script>
</head>

<body>
	<div class="wrap">
		<div class="head">
			<a href="http://tl.changyou.com/main.shtml" title="《新天龙八部》官方网站" target="_blank" class="logo">《新天龙八部》官方网站</a>
			<div class="nav">
				<a href="index.shtml" class="nav-item nav-item0">总赛道</a>
				<a href="single.shtml" class="nav-item nav-item1 act">门派个人赛</a>
				<a href="team.shtml" class="nav-item nav-item2">帮派团体赛</a>
				<a href="javascript:;" class="nav-item nav-item3">规则&奖励</a>
			</div>
		</div>
		<div class="bg2">
			<div class="bar">
				<h2 class="tit">
					<p>10.18-10.27</p>
				</h2>
				<p class="tip">论魅力，谁更强？</p>
				<div class="loginbk">
					<p class="fl ablesend">今日可投出火力值：<span class="myVotes">x</span>票</p>
					<div class="loginbox fr">
						<p class="logout_box" style="display: none;"><a href="javascript:;">个人中心</a> /<span class="user_name"></span> /<a href="javascript:;" id="logout" class="loginout">注销</a></p>
						<p class="login_box"><a href="javascript:;" id="login" class="loginout">登录</a></p>
					</div>
				</div>
				<div class="bmbk">
					<div class="daoyanbox fl">
						<p class="topp">充分发挥优势!展现过人才华!扩大号召力!让更多门派弟子信服于你吧!</p>
						<div class="botbox pr">
							<div class="livebox pa">
								<p>可通过官方畅秀阁、抖音直播间、虎牙直播间秀出自己，召唤直播间的观众宝宝们为你增加热度值!</p>
								<p class="icon">· 官方扶持 ·</p>
								<p>在此活动期间，可临时签约成官方主播，获取直播授权!</p>
								<a href="javascript:;" title="获取方式可点击查看">获取方式可点击查看</a>
							</div>
							<div class="livebox livebox1 pa">
								<p>可在指挥战役前，召唤您的兄弟们帮你增加热度值!</p>
							</div>
							<div class="livebox livebox2 pa">
								<p>可在帮会、结伴间请兄弟们帮你增加热度值!</p>
							</div>
							<div class="livebox livebox3 pa">
								<p>可录制游戏相关短视频发布在贴吧、论坛、抖音等各种玩家聚集地!秀出自己，拉动热度值!</p>
							</div>
							<div class="livebox livebox4 pa">
								<p>等你秀出来!</p>
							</div>
							<div class="livebox livebox5 pa">
								<p>可在官方论坛、贴吧等少侠们聚集的地方，发布各种内容向的攻略帖!吸路过的少侠们为你增加热度值!</p>
								<p class="icon">· 官方扶持 ·</p>
								<p>在此活动期间，若您的帖子十分优秀，被小八妹发现!还有机会得到官方公众号的转载哦!</p>
							</div>
						</div>
					</div>
					<div class="bminfbox pr">
						<!--重新报名，添加类名“ repeatbtn ”-->
						<a href="javascript:;" class="btn bmbtn pa" id="btn_joinSectsPersonal" title="立即报名">立即报名</a>
						<div class="pkinfbox none">
							<p class="rankpai">我的门派排行：<span id="myRanking">暂未上榜</span></p>
							<!--审核中-->
							<div class="examineing none">
								<div class="avtarbox">
									<img src="/act/xtl/match/20230914/pc/img/shz.jpg">
								</div>
								<div class="avtarinf">
									<p class="myServer"></p>
									<p class="myRoleName"></p>
								</div>
								<div class="shenheing">
									<p class="toptxt">参战宣言及作品正在审核中，请耐心等候！</p>
									<p class="bottxt">粉丝眼中的我：</p>
									<span class="nosign">暂无标签</span>
								</div>
							</div>
							<!--完成的状态-->
							<div class="examined none" id="myInfo">
								<div class="topbar">
									<div class="avtarbox">
										<img onerror="this.src=common.defaultAvatar">
									</div>
									<div class="avtarinf">
										<p class="myServer"></p>
										<p class="myRoleName"></p>
									</div>
									<div class="hotbox">
										<span class="hot"></span>
										<p class="hotnum">热度<span></span></p>
									</div>
								</div>
								<div class="sharebox">
									<p>呼朋唤友添把火：</p>
									<a href="javascript:;" class="btn wxbtn" title="微信分享"></a>
									<a href="javascript:;" class="btn qqbtn" title="QQ分享"></a>
								</div>
								<div class="shenheing">
									<p class="toptxt">参战宣言：<span></span></p>
									<p class="bottxt">我的作品：<span></span></p>
									<a href="javascript:;" class="cklink" title="点击此处查看作品">点击此处查看作品</a>
								</div>
								<div class="signbox">
									<div class="btnbox">
										<p class="showtxt">粉丝眼中的我：<a href="javascript:;" class="btn btn-ztgd" title="点击查看" style="display: none;"></a></p>
										<span class="nosign">暂无标签</span>
										<div class="roll" style="display: none;">
											<ul class="first-say-list">

											</ul>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<ul class="paiul">
					<li data-id="tl">
						<span>天龙</span>
					</li>
					<li data-id="mj">
						<span>明教</span>
					</li>
					<li data-id="gb">
						<span>丐帮</span>
					</li>
					<li data-id="wd">
						<span>武当</span>
					</li>
					<li data-id="em">
						<span>峨眉</span>
					</li>
					<li data-id="ts">
						<span>天山</span>
					</li>
					<li data-id="sl">
						<span>少林</span>
					</li>
					<li data-id="xy">
						<span>逍遥</span>
					</li>
					<li data-id="xx">
						<span>星宿</span>
					</li>
					<li data-id="mr">
						<span>慕容</span>
					</li>
					<li data-id="tm">
						<span>唐门</span>
					</li>
					<li data-id="gg">
						<span>鬼谷</span>
					</li>
					<li data-id="thd">
						<span>桃花岛</span>
					</li>
					<li data-id="jqg">
						<span>绝情谷</span>
					</li>
				</ul>
				<div class="starbox">
					<div class="meipaibox tl" data-sectID="6">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">
						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox mj" data-sectID="1">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox gb" data-sectID="2">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox wd" data-sectID="3">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox em" data-sectID="4">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox ts" data-sectID="7">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox sl" data-sectID="0">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox xy" data-sectID="8">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox xx" data-sectID="5">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox mr" data-sectID="10">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox tm" data-sectID="11">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox gg" data-sectID="12">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox thd" data-sectID="13">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox jqg" data-sectID="14">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
				</div>
				<h2 class="tit t1">门派个人战赛道奖励</h2>
				<ul class="jlhd">
					<li class="li0 on">首席奖</li>
					<li class="li1">参与奖</li>
				</ul>
				<div class="distab dis">
					<div class="jlnrbox">
						<p class="endtime">截止10月26日24点，当热度达到您所在门派的前三名!即可成为<span>“门派首席”!</span></p>
						<img src="/act/xtl/match/20230914/pc/img/sxjl.png" class="sxjl fl">
						<div class="sxjlribox fr pr">
							<div class="cdkbox pa">
								<div></div>
								<a href="javascript:common.tips(personal.isBack ? '您是回流玩家' : '您不是回流玩家');" class="btn cxbtn" title="前往查询">前往查询</a>
							</div>
							<p class="sxtip">分赛道前48位少年，可领取专属CDK福利码将福利码分发给自己的粉丝，争<br>夺全服唯一的“全门派第一人”</p>
							<ul class="sxlist">
								<li><span>1、</span>将获得的CDK码，发放给支持你的少侠们!当有角色激活了你的cdk后，他便成为你的粉丝!</li>
								<li>每个角色仅能激活1个精英首席的CDK码例如，角色A已经激活了首席A的CDK，若又收到了首席B的CDK时，便无法再次激活</li>
								<li><span>2、</span>最终”全门派第一人”将比拼粉丝数量，快去发挥魅力召集更多的 少侠成为你的粉丝吧!</li>
								<li>1个粉丝使用，将增加1分，若这位激活CDK的粉丝，属于故友归来，可增加分值会更高!</li>
							</ul>
							<div class="cdktable">
								<table>
									<tr>
										<td width="50%">故友归来角色激活CDK</td>
										<td>故友归来角色激活CDK</td>
									</tr>
									<tr>
										<td>装评3w以下</td>
										<td>1</td>
									</tr>
									<tr>
										<td>装评在3-7w以下</td>
										<td>2</td>
									</tr>
									<tr>
										<td>装评在7-15w以下</td>
										<td>3</td>
									</tr>
									<tr>
										<td>装评在15-30w以下</td>
										<td>6</td>
									</tr>
									<tr>
										<td>装评在50w以下</td>
										<td>10</td>
									</tr>
								</table>
							</div>
						</div>
					</div>
					<a href="javascript:;" class="btn ljbtn ylq" title="立即领取">立即领取</a>
				</div>
				<div class="distab">
					<div class="jlnrbox">
						<div class="zsgiftbox">
							<h3>热力赠送礼包</h3>
							<p>当送出12点火力值时，可领取</p>
							<img src="/act/xtl/match/20230914/pc/img/zsgift.png">
							<a href="javascript:;" class="btn ljbtn" id="joinPrize" title="立即领取">立即领取</a>
						</div>
						<div class="rizsbox">
							<p class="rizsboxname">热力值获取方式：</p>
							<ul class="sxlist">
								<li><span>1、</span>首次登陆活动页面，可获得热力值3点</li>
								<li>若属于“知交重逢”账号，可获取的热力值更多。</li>
								<li><span>2、</span>活动期间，将检测昨日游戏登陆情况，若昨日登陆过游戏，今日可获得热力值3</li>
								<li>若属于“知交重逢”账号，昨日若登陆过游戏，可获取的热力值更多</li>
							</ul>
							<p class="belong"><span>若属于知交重逢账号，将根据页面绑定角色的装评，给与对应的火力值!</span></p>
							<div class="cdktable">
								<table>
									<tr>
										<td width="36%">故友归来角色可获取的火力值</td>
										<td width="33%">首次登陆活动页面时</td>
										<td>前一日登录过游戏</td>
									</tr>
									<tr>
										<td>装评3w以下</td>
										<td>1</td>
										<td>1</td>
									</tr>
									<tr>
										<td>装评在3-7w以下</td>
										<td>2</td>
										<td>2</td>
									</tr>
									<tr>
										<td>装评在7-15w以下</td>
										<td>3</td>
										<td>3</td>
									</tr>
									<tr>
										<td>装评在15-30w以下</td>
										<td>6</td>
										<td>6</td>
									</tr>
									<tr>
										<td>装评在50w以下</td>
										<td>10</td>
										<td>10</td>
									</tr>
								</table>
							</div>
							<div class="cxbox">
								<a href="javascript:common.tips(personal.isBack ? '您是回流玩家' : '您不是回流玩家');" class="btn cxbtn" title="前往查询">前往查询</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="flobox">
		<ul class="sanv">
			<li data-id="tl">天龙</li>
			<li data-id="mj">明教</li>
			<li data-id="gb">丐帮</li>
			<li data-id="wd">武当</li>
			<li data-id="em">峨嵋</li>
			<li data-id="ts">天山</li>
			<li data-id="sl">少林</li>
			<li data-id="xy">逍遥</li>
			<li data-id="xx">星宿</li>
			<li data-id="mr">慕容</li>
			<li data-id="tm">唐门</li>
			<li data-id="gg">鬼谷</li>
			<li data-id="thd">桃花岛</li>
			<li data-id="jqg" class="last">绝情谷</li>
		</ul>
		<span class="backtop pa"></span>
	</div>
	<!--#include virtual="/xtl/match/20230914/pc/inc/pop.html"-->
	<script src="/act/all/cdn/jquery/3.4.1/jquery.min.js"></script>
	<script src="/act/all/cdn/dialog/dialog.js"></script>
	<script src="/act/all/cdn/qrcode/1.0/qrcode.min.js"></script>
	<script src="//qzonestyle.gtimg.cn/qzone/qzact/common/share/share.js"></script>
	<script src="/act/all/cdn/clipboard.js/2.0.4/clipboard.min.js"></script>
	<script src="/act/all/cdn/join-activity/2.6/join-activity.min.js"></script>
	<script src="/xtl/match/20230914/pc/js/cropper.js"></script>
	<script>
		//返回顶部
		$('.backtop').on('click', function () {
			$('body,html').animate({
				scrollTop: 0
			}, 1000);
		});
		//门派侧导航
		$(window).bind('load scroll', function () {
			var t = $(window).scrollTop();
			if ($('.paiul').offset().top > t) {
				$('.flobox').hide();
			} else {
				$('.flobox').show();
			};
		});
		$('.sanv li,.paiul li').on('click', function () {
			var thisele = $(this).data('id');
			// $(this).addClass('on').siblings().removeClass('on');
			$('html,body').animate({
				scrollTop: $('.meipaibox.' + thisele).offset().top
			});
		});
		//适配
		var isFirefox = false;
		if (navigator.userAgent.indexOf('Firefox') > -1) {
			isFirefox = true;
		};
		$(function () {
			if ($('.flobox').height() > $(window).height()) {
				$('.flobox').css('zoom', $(window).height() / $('.flobox').height());
				if (isFirefox) {
					$('.flobox').css({
						'transform': 'scale(' + $(window).height() / $(".flobox").height() + ')',
						'transform-origin': 'center right'
					});
				};
			};
		});
	</script>
	<script src="/xtl/match/20230914/pc/js/menu.js"></script>
	<!--<script src="/xtl/match/20230914/pc/js/index.js"></script>-->
	<!--<script src="/xtl/match/20230914/pc/js/personal.js"></script>-->
	<!--<script src="/xtl/match/20230914/pc/js/common.js"></script>-->

	<script>
		//名单滚动

		function ListRollInit(o, config) {
			var that = this;
			var ul = o.find('ul'),
					playbtn=o.prev().find('.btn-ztgd'),
					a = $.extend({
						col: 1, //每一行列数 默认1
						speed: 1, //速度 默认1
						delay: 0 //每一行停留时间 默认0
					}, config),
					t = 0,
					f = 1000 / 60,
					g = function() {
						console.log('gogogo')
						if(tm){
							clearTimeout(tm);
							tm = null;
						}
						var li = ul.find('li'),
								ph = parseInt(li.height()) || 0,
								ln = Math.ceil(li.length / a.col),
								pn = a.col - li.length % a.col;
						if (ln * ph > o.height()) {
							tm = setTimeout(run, 100);
							return;
						}

						function run(){
							// 补充列数
							if (pn != a.col) {
								for (var i = 0; i < pn; i++) {
									ul.append(li.eq(i).clone());
								}
							}
							// 补充行数
							if (!ul.find('[for="append"]').length) {
								ul.append(ul.find('li').clone().attr('for', 'append'));
								ln *= 2;
								console.log('补充行数')
							}
							t -= a.speed;
							if (-t % ph < a.speed) {
								if (-t >= ph * ln / 2) t = 0;
								tm = setTimeout(run, a.delay < f ? f : a.delay);
							}
							else {
								tm = setTimeout(run, f);
							}
							ul.css({
								marginTop: t
							});
						}
						tm = setTimeout(run, f);
					},
					tm;
				g();
			that.init = g;
			playbtn.click(function() {
				if (!playbtn.hasClass('open')) {
					playbtn.addClass('open');
					clearTimeout(tm);
				} else {
					playbtn.removeClass('open');
					tm = setTimeout(g, f);
				}
			})
		};
		var listRoll = new ListRollInit($('#pop_detail .roll'), {
			col: 1 //一行1列
		});


		// $('.roll').each(function (i, el) {
		// 	listRollInit($(el), {
		// 		col: 1 //一行1列
		// 	});
		// });
		$('#btn_joinSectsPersonal').click(()=>{
			openPopDetail()
		})
		function openPopDetail(){
			$('#pop_detail .roll').show();
			const listDoms = new Array(Math.ceil((Math.random() * 8) * 10)).fill('1').map((it, i)=>{
				return `<li><span>${i}${i}随便测试</span></li>`
			}).join('\n')
			$('.first-say-list').html(`${listDoms}`)
			$('#pop_detail .nosign').hide();
			listRoll.init();
			popup($('#pop_detail'));
		}
	</script>
	<!--#include virtual="/all/nav/xtl_dark.html"-->
	<!--#include virtual="/all/dma/dma_activity.html" -->
</body>

</html>
