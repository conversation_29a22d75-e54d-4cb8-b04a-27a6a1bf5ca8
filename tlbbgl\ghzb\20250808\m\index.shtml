<!DOCTYPE HTML>
<html>

<head>
    <meta charset="utf-8">
    <title></title>
    <meta name="keywords" content="" />
    <meta name="description" content="" />
    <meta content="width=750,minimum-scale=0,maximum-scale=1,user-scalable=no" name="viewport" />
    <meta name="referrer" content="no-referrer-when-downgrade">
    <meta name="mobile-web-app-capable" content="yes">
    <meta content="black" name="apple-mobile-web-app-status-bar-style" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="author" content="" />
    <meta name="robots" content="index, follow" />
    <meta name="googlebot" content="index, follow" />
    <link type="image/x-icon" href="//tlbbgl.changyou.com/favicon.ico" rel="icon" />
    <link type="image/x-icon" href="//tlbbgl.changyou.com/favicon.ico" rel="shortcut icon" />
    <!--	<script class="external" src="//zhcdn01.xoyo.com/xassets/lib/vconsole/v3/vconsole.min.js"></script>-->
    <!--	<script>-->
    <!--		new VConsole();-->
    <!--	</script>-->

    <script src="/act/all/cdn/jquery/3.4.1/jquery.min.js"></script>
    <script src="/act/tlbbgl/ghzb/20250407/m/js/tools.js"></script>
    <!--防屏蔽处理-->
    <script>
        $(function () {
            let validIndexArr = Array(50).fill(1).map((it, i) => '' + $.tools.fixNumer((i+1), 2));
            let invalidIndexArr = ['01']
            validIndexArr = validIndexArr.filter(it=>(!invalidIndexArr.includes(it)))
            console.log(validIndexArr);
            var url = $.tools.getPath + 'index' + validIndexArr[$.tools.getRnd(0, validIndexArr.length - 1)] + '.shtml';
            console.log(url);
            window.location.href = url + window.location.search;
        });
    </script>
</head>

<body>
</body>

</html>
