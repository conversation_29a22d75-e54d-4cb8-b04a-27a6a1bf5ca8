~(function () {
    function App() {
        const that = this
        const mock = false
        this.data = {
            pagesize: 4,
            page: 1,
            records: {total: 0, rows: []},
            userInfo: {
                guessRegulars: []
            }
        }

        function ajax(opt) {
            let loading
            return new Promise((resolve, reject) => {
                const env = 2  // 1-内网IP接口  2-内部服务器接口  3-畅游测试接口  4-畅游正式接口
                const domainMap = {
                    1: 'http://localhost:8883',
                    2: 'https://hbcloud.top:8388', // http://hbcloud.top:8388/changyou/tlzb/api/changyouLogin?CYSCID=&ticket=&s_url=
                    3: 'https://test-api-tlgl-zhjd.changyou.com',
                    4: 'https://api-tlbbglact.changyou.com'
                }
                const domain = domainMap[env]
                const activeName = 'tlzb'
                // const activeName = 'zhjd'
                const path = `/changyou/${activeName}/api/`
                const l = opt.url
                const url = domain + path + l

                console.log('api', l);
                console.log('api url', url);
                if (opt && opt.loading) loading = lay.load()
                const data = opt.data || {}
                const ajaxOptions = {
                    url: opt?.sp ? l : url,
                    type: opt?.method ? opt.method : 'POST',
                    data: data,
                    dataType: 'json',
                    headers: {
                        // 'Content-Type': 'application/json;charset=UTF-8'
                    },
                    success: function (res) {
                        if (opt && opt.loading) lay.close(loading)
                        // 通用错误码处理
                        if (res.code === -1) {
                            $.tools.removeStorage('token_tlzb')
                            $.tools.removeStorage('ticket')
                            // return that.alert('登录已超时，请重新登录', that.logout)
                            reject(res)
                        }
                        resolve(res)
                    },
                    error: function (err) {
                        if (opt && opt.loading) lay.close(loading)
                        console.log('post err res', err);
                        lay.msg('接口请求繁忙，请稍后重试')
                        reject(err)
                    }
                }
                console.log(ajaxOptions);
                let verifyToken = $.tools.getStorage('token_tlzb')
                if (!(['changyouLogin', 'course'].includes(l))) {
                    if (verifyToken) {
                        ajaxOptions.headers = {
                            'Authorization': verifyToken
                        }
                    }
                } else {
                    if (ajaxOptions.type.toUpperCase() === 'POST') {
                        if (verifyToken) ajaxOptions.headers = {
                            'Content-Type': 'application/json;charset=UTF-8',
                            'Authorization': verifyToken
                        }
                        if ('changyouLogin' !== l) {
                            ajaxOptions.data = JSON.stringify(ajaxOptions.data)
                        }
                    }
                }
                $.ajax(ajaxOptions)
            })
        }

        this.ajax = ajax

        this.loginPop = function (type) {
            if ($('.btn-login').hasClass('timeover')) return that.alert('当前不在活动时间内')
            const base_url = $.tools.getUrl
            const params = $.tools.getRequest()
            params.base_url = base_url
            const ml = $.tools.encodeBase64URLSafeString(JSON.stringify(params))
            const state = $.tools.encodeBase64URLSafeString(encodeURIComponent(location.search))
            let s = location.href.replace(location.search, '')
            const loginTypeMap = {
                'wegame': 'project=activitywegame'
            }
            const loginType = loginTypeMap[type] ? loginTypeMap[type] : 'project=activity'
            const coreSrc = 'https://auth.changyou.com/interfaceLogin';
            // const src = 'https://auth.changyou.com/interfaceLogin?project=tlglactivity&s=' + s + '&state=' + state + `&gameType=tlbb&ml=${ml}`;

            if (type === 'wegame') {
                let src = `${coreSrc}?${loginType}&s=${s}`;
                location.href = src
            } else {
                let src = `${coreSrc}?${loginType}&s=${s}&state=${state}&ml=${ml}`;
                $.tools.setStorage('URL_BEFORE_LOGIN', s)
                $('#popLogin .login-box').html(`<iframe src="${src}" frameborder="0" width="100%" height="100%" scrolling="no"></iframe>`);
                lay.closeAll()
                lay.open('popLogin')
            }
        }

        this.login = function () {
            const ticket = $.tools.getStorage('ticket')
            if (!ticket) return Promise.resolve({code: -1, msg: '没有ticket'})
            // 登录
            const origin = $.tools.getStorage('LOGIN_TYPE')
            const urlBeforeLogin = $.tools.getStorage('URL_BEFORE_LOGIN')
            if (!urlBeforeLogin) return Promise.resolve({code: -1, msg: '没有传登录前的url'})
            const s_url = urlBeforeLogin
            console.log('login接口传参：', s_url)
            const data = {ticket, s_url}
            data.origin = origin
            const CYSCID = $.tools.getParam('type')
            if (CYSCID) data.CYSCID = CYSCID
            if (origin === 'wx') data.code = $.tools.getParam('code')
            return new Promise((resolve, reject) => {
                ajax({url: 'changyouLogin', data})
                    .then(res => {
                        console.log('changyouLogin', res);
                        $.tools.removeStorage('ticket')

                        if (res.code === -20003) {
                            that.alert('您的账号已停权', that.logout)
                        }

                        console.log('login', res)
                        if (res.code === 0) {
                            const token = res.data.token
                            $.tools.setStorage('token_tlzb', token)
                            // 重新拉取初始话数据
                            _checkLogin().then(res => {
                                console.log('_checkLogin', res);
                                resolve(res)
                            })
                        } else {
                            reject(res)
                        }
                    })
                    .catch(res => {
                        if (res.code === -20003) {
                            that.alert('您的账号已停权', that.logout)
                        }

                        if (res.code === -21003) {
                            lay.msg('分享参数错误')
                            setTimeout(function () {
                                location.href = $.tools.removeParam(location.href, 'uid')
                            }, 1500)
                        }
                        reject(res)
                    })
            })
        }

        function logoutAuto() {
            // that.alert("登录失效,请重新登录！", that.logout)
            that.logout()
            $.tools.removeStorage('token_tlzb')
            location.reload()
        }

        function _checkLogin() {
            const uid = $.tools.getParam('uid')
            const data = {}
            if (uid) data.shareUid = uid
            const CYSCID = $.tools.getParam('CYSCID')
            if (CYSCID) data.CYSCID = CYSCID
            const url = 'index'
            return ajax({url, data})
        }

        this.checkLogin = function () {
            let verifyToken = $.tools.getStorage('token_tlzb');
            // 缓存是否有token
            //  -无 缓存token，则表示还未登录 =>
            if (!verifyToken) {
                if ($.tools.getParam('ticket')) {
                    $.tools.setStorage('ticket', $.tools.getParam('ticket'))
                    const href = $.tools.removeParam(location.href, 'ticket')
                    return window.location.href = $.tools.removeParam(href, 'state')
                }
                //       判断缓存中是否有 ticket 参数，有则
                let ticket = $.tools.getStorage('ticket')
                if (ticket) {
                    //       -如果有，则调login接口
                    //          -接口正常code=0,拿到token，并缓存
                    //          -接口异常，则登录失败返回错误信息
                    return this.login()
                } else {
                    //       -如果无ticket参数，返回未登录
                    return Promise.resolve({code: -1, msg: '未登录'})
                }
            }
            //  -有 缓存token，调初始话接口，拉取登录信息 =>
            //       -接口正常code=0，则登录成功，返回登录信息
            //       -接口异常，登录失败，返回错误信息
            return new Promise(resolve => {
                _checkLogin().then(res => {
                    if (res.code === -20003) {
                        that.alert('您的账号已停权', that.logout)
                    }

                    if (res.code === -21003) {
                        lay.msg('分享参数错误')
                        setTimeout(function () {
                            location.href = $.tools.removeParam(location.href, 'uid')
                        }, 1500)
                        return
                    }

                    if (res.code === 0) {
                        that.data = Object.assign(that.data, res.data)
                    }
                    resolve(res)
                }).catch(res => {
                    if (res.responseJSON) res = res.responseJSON
                    resolve(res)
                })
            })
        }

        this.logout = function () {
            $.tools.removeStorage('token_tlzb')
            const b = encodeURIComponent(location.href)
            const url = `https://auth.changyou.com/logout?b=${b} `
            location.href = url
        }

        function commonErrPop() {
            let flag = true
            if (!that.data.isLogin) {
                openLoginType()
                return false
            }
            return flag
        }

        // 获取赛程信息
        function getCourse() {
            ajax({
                method: 'GET',
                url: 'course'
            }).then(function (res) {
                console.log(res);
                if (res.code === 200) {
                    const data = res.data
                    console.log(data);
                    that.data.stage0 = data.filter(it => it.stage === 0)
                    that.data.stage1 = data.filter(it => it.stage === 1)
                    console.log(that.data.stage1);
                    that.data.stage2_win = data.filter(it => it.stage === 2 && it.category === 0)
                    that.data.stage2_fail = data.filter(it => it.stage === 2 && it.category === 1)
                    that.data.stage3_win = data.filter(it => it.stage === 3 && it.category === 0)
                    that.data.stage3_fail = data.filter(it => it.stage === 3 && it.category === 1)
                    that.data.stage4_win = data.filter(it => it.stage === 4 && it.category === 0)
                    that.data.stage4_fail = data.filter(it => it.stage === 4 && it.category === 1)
                    that.data.stage5 = data.filter(it => it.stage === 5)
                    that.data.stage6 = data.filter(it => it.stage === 6)
                    renderAllGuessStage()

                }
            })
        }

        function renderAllGuessStage() {
            renderGuessStage(0)
            renderGuessStage(1)
            renderGuessStage(2)
            renderGuessStage(3)
            renderGuessStage(4)
            renderGuessStage(5)
        }

        // 常规竞猜
        function renderGuessStage(stageIndex) {
            console.log('小组赛')
            const {userInfo} = that.data
            const {guessRegulars} = userInfo
            const statgeId = `#stage${stageIndex}`
            $(`${statgeId} ul li`).removeClass('yixuan win').addClass('disabled')
            const list = that.data[`stage${stageIndex}`]
            console.log(stageIndex, list);
            if(!list) return
            list.forEach((e, i) => {
                const {faceId, teamInfos, winTeam} = e
                const ul = $(`${statgeId} ul`).eq(i)
                const lis = ul.children('li')
                const tjbtn = $(`${statgeId} .tjbtn`).eq(i)
                if(teamInfos) {
                    lis.each((i, li) => {
                        const team = teamInfos[i]
                        if (!team) return
                        $(li).removeClass('disabled')
                        const {teamName, teamId} = team
                        const isWin = winTeam?.split(',').includes(teamId.toString())
                        $(li).text(teamName).attr('data-teamId', teamId).attr('data-faceId', faceId)
                        $(li)[isWin ? 'addClass' : 'removeClass']('win')
                    })
                }

                // 该组已出结果或者已提交过改组的竞猜，禁用提交和选择按钮
                const choosedFaceId = (guessRegulars.find(it => it.faceId === faceId))
                if ((!teamInfos || teamInfos.length < 1) || winTeam || choosedFaceId) {
                    console.log('禁用')
                    ul.addClass('disabled')
                    tjbtn.addClass('disabled gray')
                } else {
                    ul.removeClass('disabled ')
                    tjbtn.removeClass('disabled gray')
                }

                // 队伍中的每一支队伍的选中状态
                lis.each((i, li) => {
                    const team = teamInfos[i]
                    if (!team) return
                    const {teamName, teamId} = team
                    const isChoosedTeam = choosedFaceId?.team.includes(teamId + '')
                    $(li)[isChoosedTeam ? 'addClass' : 'removeClass']('yixuan')
                })
            })
        }

        function getRecord() {
            let page = that.data.page || 1
            ajax({
                method: 'get',
                url: 'prize/record',
                load: true
            }).then(res => {
                if (res.code === 200) {
                    const {total, rows} = res
                    that.data.records = {total, rows}
                    renderRecordList()
                    return
                }
            })
        }

        function renderRecordList() {
            const {pagesize} = that.data
            const {total, rows} = that.data.records
            if (total <= 0) return elList.html('')
            let page = that.data.page || 1
            page = page < 1 ? 1 : page
            const elList = $('#exchangeLog .tablebox tbody')
            const pageData = rows.filter((v, i) => i >= pagesize * (page - 1) && i < pagesize * (page));
            const html = pageData.map(it => `<tr><td>${it.createTime}</td><td>${it.giftName}</td></tr>`).join('\n')
            elList.html(html)
        }

        // 登录类型选择弹窗
        function openLoginType() {
            if ($.tools.browser.versions.wx) {
                lay.open('pop_login2')
            } else {
                lay.open('pop_login')
            }
        }

        function loginBefore() {
            bgVideoPlay()
            $(`.saiqubox ul li`).removeClass('yixuan win')

            $('.login_box').show()
            $('.logout_box').hide()

            console.log('loginBefore');
            $('#login').click(function () {
                openLoginType()
            })
            $('#logout').click(function () {
                that.logout()
            })

            // 登录方式选择
            $('.logintype')
                .on('click', '.J_plaLogin', function () {
                    $.tools.setStorage('LOGIN_TYPE', 'changyou')
                    that.loginPop('changyou')
                })
                .on('click', '.J_wgLogin', function () {
                    $.tools.setStorage('LOGIN_TYPE', 'wegame')
                    that.loginPop('wegame')
                })
                .on('click', '.J_wxLogin', function () {
                    $.tools.setStorage('LOGIN_TYPE', 'wx')
                    that.login('wx')
                })

            // 侧导航-返回顶部
            $('.navbtn1').on('click', function () {
                $('body,html').animate({
                    scrollTop: 0
                }, 1000);
            });
            $(window).bind('load scroll', function () {
                var t = $(window).scrollTop();
                if (t > $('.navbox').offset().top) {
                    $('.flobox').show();
                } else {
                    $('.flobox').hide();
                }
            });

            $('#stage0')
                // 小组赛选择竞猜的队伍
                .on('click', 'li', function () {
                    if (!commonErrPop()) return;
                    if ($(this).attr('data-teamId') === '' || (!$(this).hasClass('yixuan') && $(this).parents('ul').children('li.yixuan').length >= 2)) return
                    $(this).toggleClass('yixuan')
                })
                // 小组赛选择提交竞猜
                .on('click', '.tjbtn', $.tools.throttle(function () {
                    if (!commonErrPop()) return;
                    const lis = $(this).siblings('ul').children('li.yixuan');
                    const teamIds = []
                    if (lis.length < 2) return lay.msg('请先选择2支您要竞猜的队伍')
                    lis.each(function (i) {
                        teamIds.push($(this).attr('data-teamid'))
                    })
                    const faceId = ($(lis).eq(0).attr('data-faceid')) * 1
                    const team = teamIds.join(',')
                    const data = {
                        faceId,
                        team
                    }
                    ajax({
                        url: 'guess/regular',
                        method: 'post',
                        data
                    }).then(res => {
                        if (!commonErrPop()) return;
                        if (res.code === 200) {
                            // 将选择的数据更新到 that.data.userInfo.guessRegulars
                            const item = {
                                faceId: faceId,
                                team: teamIds
                            }
                            that.data.userInfo.guessRegulars.push(item)
                            renderGuessStage0()
                            that.alert('竞猜提交成功')
                        }
                    })
                }, 1000, 1))

            // 其他阶段选择竞猜的队伍
            $('#stage1, #stage2, #stage3, #stage4, #stage5').on('click', 'li', function () {
                if (!commonErrPop()) return;
                const $this = $(this)
                $this.addClass('yixuan').siblings().removeClass('yixuan')
                const faceId = $(this).attr('data-faceid')
                const teamId = $(this).attr('data-teamid')
                if (!faceId || !teamId) return console.warn('faceId or teamId is null')
                that.data.faceId = faceId
                that.data.teamId = teamId
                lay.open('guessSure')
            })
            $('#guessSure .popclosebtn').one('click', function () {
                const stageIndex = $('.jclist li.cur').index()
                $('#stage1 .saiqubox li, #stage2 .saiqubox li, #stage3 .saiqubox li, #stage4 .saiqubox li, #stage5 .saiqubox li').removeClass('yixuan')
            })
            // 确认提交竞猜
            $('#guessSure .surebtn').one('click', $.tools.throttle(function () {
                if (!commonErrPop()) return;
                const ul = $(`${statgeId} ul`)
                const stage = $('.jclist li.cur').index()
                const {faceId, teamId} = that.data
                if (!faceId || !teamId) return console.warn('faceId or teamId is null')
                const lis = $(this).siblings('ul').children('li.yixuan');
                const team = teamId
                const data = {faceId: faceId, team: team}
                console.log(data);
                return
                ajax({
                    url: 'guess/regular',
                    method: 'post',
                    data
                }).then(res => {
                    if (!commonErrPop()) return;
                    if (res.code === 200) {
                        // 将选择的数据更新到 that.data.userInfo.guessRegulars
                        const item = {
                            faceId: faceId,
                            team: teamIds
                        }
                        that.data.userInfo.guessRegulars.push(item)
                        renderGuessStage(stage)
                        that.alert('竞猜提交成功')
                    }
                })
            }, 1000, 1))


            // 选择投币数量
            $('.tbnum li').on('click', function () {
                if (!commonErrPop()) return;

                if ($(this).parents('.jjjclist').find('.jcbtn').hasClass('gray') ||
                    $(this).parents('.jjjclist').find('.jcbtn').hasClass('committed')) return;
                $(this).addClass('cur').siblings().removeClass('cur');
            });
            $('select').on('mousedown focus', function (e) {
                if (commonErrPop()) return false;
                // if (example.betTipsHandle($(this))) return false;
            });
            var selected = new Map();
            $('#selectUl select').on('change', function (e) {

                var val = $(this).val(),
                    index = $(this).index('#selectUl select');

                if (!val) {
                    selected.delete(index);
                } else if (Array.from(selected.values()).includes(val)) {
                    $(this).prop('selectedIndex', 0);
                    selected.delete(index);
                    example.guessFailTips('您已经为该战队排位，请更换该名次战队。');
                    return;
                } else {
                    selected.set(index, val)
                }
            })


            // 侧导航
            $('.openbtn').on('click', function () {
                if ($(this).hasClass('sq')) {
                    $(this).removeClass('sq');
                    $('.flobox').removeClass('none');
                } else {
                    $(this).addClass('sq');
                    $('.flobox').addClass('none');
                }
            });

            // 胜者组/败者组切换
            $(".fz_nav_box").on("click", ".sc_nav1", function () {
                var index = $(this).index()
                $(this).addClass("on").siblings(".sc_nav1").removeClass("on");
                $('.sc_fz_box1').eq(index).addClass('show').siblings().removeClass('show');
            })

            // 胜者组/败者组切换
            $(".fz_nav_box").on("click", ".sc_nav2", function () {
                var index = $(this).index()
                $(this).addClass("on").siblings(".sc_nav2").removeClass("on");
                $('.sc_fz_box2').eq(index).addClass('show').siblings().removeClass('show');
            })

            // 胜者组/败者组切换
            $(".fz_nav_box").on("click", ".sc_nav3", function () {
                var index = $(this).index()
                $(this).addClass("on").siblings(".sc_nav3").removeClass("on");
                $('.sc_fz_box3').eq(index).addClass('show').siblings().removeClass('show');
            })

            // 切换竞猜阶段
            $('.jclist li').on('click', function () {
                var $this = $(this);
                $this.addClass('cur').siblings().removeClass('cur');
                $('#contbg').removeClass().addClass('cont cont' + $this.index());
                $('.distab').removeClass('dis').eq($this.index()).addClass('dis');
            });

            function takePrize(exchangeCode) {
                ajax({
                    url: 'prize/exchange',
                    method: 'POST',
                    load: true,
                    data: {exchangeCode}
                }).then(res => {
                    if (res.code === 200) {
                        lay.msg('兑换成功')
                        return
                    }
                    lay.msg(res.msg)
                })
            }

            // 兑换奖励
            $('.dhbtn').on('click', function () {
                if (!commonErrPop()) return
                const exchangeCode = $(this).data('id')
                if (that.data.prizeInfo[exchangeCode]?.unused === 0) return
                takePrize(exchangeCode)
            })
            // 领取奖励
            $('.getbtn').on('click', function () {
                const exchangeCode = $(this).data('id')
                takePrize(exchangeCode)
            })
            // 兑换记录
            $('.recordbtn').on('click', function () {
                lay.open('exchangeLog')
                getRecord()
            })
            $('#exchangeLog .tpage .prev').on('click', function () {
                that.data.page--
                renderRecordList()
            })
            $('#exchangeLog .tpage .next').on('click', function () {
                const {pagesize, page} = that.data
                const {total, rows} = that.data.records
                if (page * pagesize < total) {
                    that.data.page++
                    renderRecordList()
                }
            })


            $('.popclosebtn').click(function () {
                const popId = $(this).parents('.pop').attr('id')
                lay.close(popId)
            })
        }

        function loginAfter() {
            // that.data.userInfo.prizeInfo.ex3.unused = 0
            console.log('loginAfter', that.data);
            const {cn, score, totalGuess, CorrectGuess, prizeInfo} = that.data.userInfo
            $('#userName').text(cn)
            $('.login_box').hide()
            $('.logout_box').show()
            $('#points').text(score)
            $('#totalGuess').text(totalGuess)
            $('#rightGuess').text(CorrectGuess)
            $('.unused_count1').text(prizeInfo.ex3.unused)
            $('.unused_count2').text(prizeInfo.ex4.unused)
            $('.unused_count3').text(prizeInfo.ex5.unused)
            $('.unused_count4').text(prizeInfo.ex6.unused)
            $('[data-id="ex3"]')[prizeInfo.ex3.unused > 0 ? 'removeClass' : 'addClass']('disabled')
            $('[data-id="ex4"]')[prizeInfo.ex4.unused > 0 ? 'removeClass' : 'addClass']('disabled')
            $('[data-id="ex5"]')[prizeInfo.ex5.unused > 0 ? 'removeClass' : 'addClass']('disabled')
            $('[data-id="ex6"]')[prizeInfo.ex6.unused > 0 ? 'removeClass' : 'addClass']('disabled')
        }

        function bgVideoPlay() {
            //首页视频
            var videoBg = document.getElementById('videoBg');
            var canPlayVideo = videoBg.canPlayType,
                isMob = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            if (!isMob && canPlayVideo) {
                videoBg.style.display = "none";
                videoBg.src = "/act/xtl/guess/20250611/pc/video/video.mp4";
                videoBg.addEventListener("canplaythrough", function () {
                    videoBg.style.display = "block";
                    videoBg.play();
                });
            }
        }

        this.init = function () {
            console.log('init');

            const ticket = $.tools.getParam('ticket')
            if (ticket) {
                $.tools.setStorage('ticket', ticket)
                const state = $.tools.getParam('state')
                const LOGIN_TYPE = $.tools.getStorage('LOGIN_TYPE')
                let href = location.href
                if (LOGIN_TYPE === 'wegame') {
                    href = $.tools.removeParam(href, 'state')
                } else {
                    const search = decodeURIComponent($.tools.decodeBase64URLSafeString(state))
                    href = $.tools.getUrl + search + location.hash
                }
                href = $.tools.removeParam(href, 'ticket')
                return location.href = href
            }

            document.addEventListener('touchstart', function (event) {
                if (event.touches.length > 1) {
                    event.preventDefault();
                }
            }, {passive: false});

            document.addEventListener('touchmove', function (event) {
                if (event.touches.length > 1) {
                    event.preventDefault();
                }
            }, {passive: false});


            loginBefore()


            this.checkLogin().then(res => {
                console.log(res);
                if (res.code === 0) {
                    this.data.isLogin = true
                    this.data.userInfo = res.data
                    loginAfter()
                } else {
                    this.data.isLogin = false
                }
            }).finally(() => {
                console.log('finally')
                getCourse()
            })
        }

        this.alert = function (msg, cbConfirm, cbCancel) {
            lay.open('tips')
            $('#tips .jcsustxt').html(msg)
            if (cbCancel && cbConfirm) {
                $('#tips .surebtn').one('click', cbConfirm)
                $('#tips .popclosebtn').one('click', cbCancel)
            } else {
                if (cbConfirm) {
                    $('#tips .popclosebtn').one('click', cbConfirm)
                } else {
                    $('#tips .popclosebtn').one('click', function () {
                        lay.closeAll()
                    })
                }
            }
        }
    }

    window.app = new App()
    window.app.init()
}());