@charset "utf-8";
body{background-color:#fff;}
.none{display:none;}
.pr{position:relative;}
.pa{position:absolute;}
.t{text-indent:-9999em; font-size: 0;}
#cy_bot{padding:20px!important;}
html,body{width:100%;height:100%; padding:0; margin: 0;}
html{-webkit-tap-highlight-color:rgba(0, 0, 0, 0);}
table {
	border-collapse: collapse;
	border-spacing: 0;
}

.com, .tab .tab-i, .sel-wrap select, .tit::before, .tit::after, .table tbody tr .ico-cup {background:url(../img/com.png) no-repeat;background-size:640px auto;}
.xtl_logo_lk{position:absolute;height:107px;top:29px;left:38px;width:152px; z-index:101; width: 185px; height: 123px; background-position: 0 0;}
.xtl_logo_lk a{display:block; width:100%; height: 100%; z-index:999;}

.wrap{width:100%;position:relative;}
.slogan{text-indent:-9999em;width:547px;height:144px;background:url(../img/slogan.png) no-repeat;top:505px;left:50%; margin-left: -273px; -webkit-animation:scaleA 1s 0.5s ease both;animation:scaleA 1s 0.5s ease both;z-index:2;background-size:100% auto;}
.head{height:650px;background:url(../img/bg-head.jpg) no-repeat center top;z-index:1;position:relative;}
.cont{height:auto;background:url(../img/bg-con.jpg) no-repeat center top;z-index:2;position:relative; padding-bottom: 60px; top:-2px;}

.tab-wrap{ width:100%; overflow: hidden; padding-top: 6px; }
.tab{width: 640px; height: 114px; display: flex; margin: 0 auto; }
.tab .tab-i{width:91px; height: 119px; background-position-y: 150px; display: inline-block; float:left;}
.tab .tab-i:hover, .tab .tab-i.active{background-position-y: -143px; }
.tab .tab-i:nth-child(1){background-position-x:-0px; }
.tab .tab-i:nth-child(2){background-position-x:-92px; margin-left: 1px; }
.tab .tab-i:nth-child(3){background-position-x:-183px; }
.tab .tab-i:nth-child(4){background-position-x:-275px; margin-left: 1px; }
.tab .tab-i:nth-child(5){background-position-x:-366px;}
.tab .tab-i:nth-child(6){background-position-x:-458px; margin-left: 1px;}
.tab .tab-i:nth-child(7){background-position-x:-549px; }

.sel-wrap{ padding-top: 60px; height: 47px; text-align: center;}
.sel-wrap select{width:274px; height: 47px; display: inline-block; text-align: left; text-indent: 6px; background-position: -183px 0; border:none; color:#b9afa0; font-size: 16px; -webkit-appearance: none; margin: 0 10px;}

.tit-wrap{text-align: center;}
.tit{ font-size: 34px; color:#c3a15c; font-weight: bold; display: inline-block; height: 52px; margin: 63px auto 0;}
.tit::before{content: ''; display: inline-block;  background-position: 0 -297px; width: 160px; height: 34px; position: relative; top:4px; }
.tit::after{content: ''; display: inline-block;  background-position: right -297px; width: 160px; height: 34px; position: relative; top:4px; }
.tit-tip{text-align: center; font-size: 14px; color:#705b52; margin-top: 10px;}

.table{width:600px; margin: 26px auto 0;}
.table thead tr td{background: url("../img/bg-tab-h.png") repeat-x; height: 43px; border: 1px solid #4f2b04; font-weight: bold; font-size: 20px; color:#372106; text-align: center;}
.table thead tr td:nth-child(1){ width: 87px; }
.table thead tr td:nth-child(2){ width: 178px; }
.table thead tr td:nth-child(3){ width: 178px; }

.table tbody tr:nth-child(even){ background-color: #281c14;}
.table tbody tr:nth-child(odd){ background-color: #2e2019;}
.table tbody tr td{height: 43px; line-height: 43px; border: 1px solid #522e07; font-size: 14px; color:#fdf4eb; text-align: center;  }
.table tbody tr td .cup-wrap{width:100%; height: 100%; position: relative;}
.table tbody tr.cup1 td{background-color:#7d6841;}
.table tbody tr.cup2 td{background-color: #59452e;}
.table tbody tr.cup3 td{background-color: #433323;}
.table tbody tr .ico-cup{width: 15px; height: 25px; position: absolute;  top:8px; left:20px;}
.table tbody tr.cup1 .ico-cup{background-position: -482px 0;}
.table tbody tr.cup2 .ico-cup{background-position: -509px 0;}
.table tbody tr.cup3 .ico-cup{background-position: -536px 0;}


@-webkit-keyframes lpAni{
    0%{-webkit-transform:translateY(0);}
    50%{-webkit-transform:translateY(-8px);}
    100%{-webkit-transform:translateY(0);}
}
@keyframes lpAni{
    0%{transform:translateY(0);}
    50%{transform:translateY(-8px);}
    100%{transform:translateY(0);}
}
@keyframes fadeUp{0%{opacity:0;transform:translateY(50px);}}
@-webkit-keyframes fadeUp{0%{opacity:0;-webkit-transform:translateY(50px);}}
@-webkit-keyframes scaleA{
	0%{opacity:0;-webkit-transform:scale(1.1,1.1);transform:scale(1.1,1.1);}
	80%{-webkit-transform:scale(.9,.9);transform:scale(.9,.9);}
	100%{opacity:1;-webkit-transform:scale(1,1);transform:scale(1,1);}
}
@keyframes scaleA{
	0%{opacity:0;-webkit-transform:scale(1.1,1.1);transform:scale(1.1,1.1);}
	80%{-webkit-transform:scale(.9,.9);transform:scale(.9,.9);}
	100%{opacity:1;-webkit-transform:scale(1,1);transform:scale(1,1);}
}
@-webkit-keyframes fadeInDown {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(0,-100%,0);
		transform: translate3d(0,-100%,0)
	}
	to {
		opacity: 1;
		-webkit-transform: translateZ(0);
		transform: translateZ(0)
	}
}
@keyframes fadeInDown {
	0% {
		opacity: 0;
		-webkit-transform: translate3d(0,-100%,0);
		transform: translate3d(0,-100%,0)
	}
	to {
		opacity: 1;
		-webkit-transform: translateZ(0);
		transform: translateZ(0)
	}
}

@-webkit-keyframes fc{from{-webkit-transform:rotate(0deg)}to{-webkit-transform:rotate(360deg)}}
@keyframes fc{from{transform:rotate(0deg)}to{transform:rotate(360deg)}}
.popVideo{width:750px;position:relative;display:none;height:740px;text-align:center;line-height:384px;}
.popClose{width:40px;height:40px;font:40px/60px simsun;color:#fff;text-align:center;position:absolute;top:-45px;right:0;z-index:99;overflow: hidden;line-height:40px;}
.video_wrap_mp4{width:750px;height:740px;}
.popVideo video{width:100%;height:100%;}
.bgv{width:750px;height:1650px;position:absolute;top:50%;left:0;margin-top:-825px;z-index:1;}
