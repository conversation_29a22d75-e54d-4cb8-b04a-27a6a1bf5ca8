@charset "utf-8";
html,body,div,p,ul,li,dl,dt,dd,em,i,span,a,img,input,h1,h2,h3,h4,h5{margin:0;padding:0;}
a,img,input{border:none;outline:none;}
a{text-decoration:none;}
img{display:block;}
ul,li{list-style:none;}
table{border-collapse:collapse;border-spacing:0;}
body{font:24px/1.5 -apple-system,"Helvetica Neue",Helvetica,Arial,sans-serif;-webkit-user-select: none;background-color:#fff;}
.none{display:none;}
input::-webkit-outer-spin-button,input::-webkit-inner-spin-button{-webkit-appearance: none !important;}
input[type="number"]{-moz-appearance:textfield;}
*{-webkit-appearance:none;-webkit-text-size-adjust:none;}
html{-webkit-tap-highlight-color:rgba(0,0,0,0);}
#cy_bot{padding:10px!important;position: relative;z-index: 2}
.cyLogo{margin-top:10px;}
.orientLayer {
    display: none;
    position: fixed;
    z-index: 1000;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, .9);
    color: #fff;
    font-size: 30px;
}

/* 页面样式 */
html,body{width:100%;}
.pr{position: relative;}
.pa{position: absolute;}
.hide{font-size: 0; text-indent: -9999em; width: 0; height: 0;}
.t,.btn{font-size: 0; text-indent: -9999em;display: block; background-repeat: no-repeat; background-color: transparent; background-size: 100% auto;}
.btn{ background-image: url("../img/btn.png"); background-size: 750px auto; width: 333px; height: 119px;}
.flex{ display: flex;}
.justify-center{ justify-content: center}
/**/
html,body,.container{ width: 100%; height: 100%; font-size: 28px; background: #983135; color: #1a1919; line-height: 1.5;}
.container{ max-width: 750px; height: 100%; margin: 0 auto; display: flex; align-items: center; justify-content: center;}
.page{ width: 100%; height: 100%; min-height: 13.34rem; overflow: hidden; background: url("../img/bg1.jpg") no-repeat center center/100% auto; display: none; align-items: center; justify-content: center;}
.page.on{ display: flex;}
.page2,.page3,.page4{ background-image: url("../img/bg2.jpg");}
.wrap{ height: 1334px; width: 100%; background: rgba(255,0,0,0); box-sizing: border-box; position: relative;}
.logo{ background-image: url("../img/logo.png?1.0"); width: 160px; height: 92px; position: absolute; left: 28px; top: 0;}
.slogan{ background-image: url("../img/slogan.png"); width: 750px; height: 260px; margin: 780px auto 0;}
.btn-login{ margin: 16px auto 30px;}
.btn-logout{ position: absolute; right: .3rem; top: .2rem; width: 100px; height: 43px; background-position: -550px -300px;}
.btn-rule,.btn-center{ width: 135px; height: 26px; margin: 0 20px; background-position: -293px -387px;}
.btn-center{ background-position-x: -437px;}
.page1 .wrap{ padding-top: 814px;}
.btn-create{ background-position-x: -343px; margin: 30px auto;}
.btn-join{ background-position-y: -129px; margin: 30px auto 50px;}
.btn-joins{ background-position-y: -258px; margin: 30px auto;}
.btn-rule2,.btn-center2{ width: 163px; height: 32px; margin-top: 26px; background-position: -343px -258px;}
.btn-center2{ background-position-x: -515px;}
.invite-text{ background: url("../img/text.png") no-repeat; background-size: 100% auto; width: 750px; height: 143px; margin: 198px auto 80px; text-align: center; font-size: 28px; color: #1a1919; box-sizing: border-box; padding-top: 34px;}
.red{ color: #701a1b;}
.ipt .red{ color: red;}
.guild-code{ font-size: 24px; color: #463726; background: url("../img/msg.png") no-repeat center center/100% auto; width: 664px; height: 360px; margin: 20px auto 6px; box-sizing: border-box; padding: 20px 44px 14px; text-align: center; display: flex; flex-direction: column; justify-content: space-evenly;}
.guild-code2{ background-image: url("../img/msg2.png"); padding: 54px 44px 48px;}
.guild-nickname{ font-size: 22px; color: #701a1b; text-decoration: underline; text-underline-offset: 6px;}
.guild-line{ display: flex; align-items: center; margin: 2px 0;}
.guild-code2 .guild-line,.guild-code3 .guild-line{ justify-content: center;}
.guild-code3 .btn-modify{ display: none;}
.guild-line p{ width: 130px; text-align: left;}
.guild-line input{ width: 188px; padding: 0 16px; line-height: 36px; border: 1px solid #d7c7ab; background: #fff; text-align: left; font-size: 22px; color: #701a1b;}
.btn-copy,.btn-modify{ width: 98px; height: 40px; background-position-y: -387px; display: inline-block; vertical-align: middle; margin-left: 10px;}
.btn-modify{ background-position-x: -582px;}
.btn-detail{ width: 197px; height: 50px; margin: 10px auto; background-position: -343px -300px; flex-shrink: 0;}
.tip{ text-align: center; font-size: 20px; color: #7e291b;}
.guild-num{ font-size: 22px; font-weight: bold; color: #000; text-align: center;}
.guild-num{ font-size: 24px;}
.guild-num2{ line-height: 1.3; margin-top: 20px;}
.guild-num2 .red{ color: #bb1313;}
.btn-guild{ background-position: -343px -129px; margin: 20px auto 34px;}
.tit{ background: url("../img/tit.png") no-repeat center center/auto 100%; height: 111px; margin-bottom: 30px;}
.rank{ width: 618px; margin: 16px auto; height: 540px; border-radius: 6px; background: linear-gradient(to bottom, rgba(0,0,0,.6),rgba(0,0,0,0) 90%); padding: 14px 16px;}
.rank2{ height: 1100px;background: linear-gradient(to bottom, rgba(0,0,0,.5) 70%,rgba(0,0,0,0) 90%);}
.rank-tab{ display: flex; justify-content: space-between; margin-bottom: 24px;}
.rank-tab a{ width: 306px; height: 65px; background-position-y: -599px;}
.rank-tab a:last-of-type{ background-position-y: -681px;}
.rank-tab a.on{ background-position-x: -316px;}
.rank-tab2 a:first-of-type{ background-position-y: -756px;}
.rank-con table{ width: 624px; text-align: center; color: #fff;}
.rank-table{ height: 250px; padding-top: 4px; overflow-x: hidden; overflow-y: auto;}
/*定义滚动条高宽及背景
 高宽分别对应横竖滚动条的尺寸*/
.rank-table::-webkit-scrollbar{
  width: 16px;
}
/*定义滚动条轨道 内阴影+圆角*/
.rank-table::-webkit-scrollbar-track{
  background: url("../img/scrollbar.png") no-repeat center center/100% 100%;
}
.rank2 .rank-table::-webkit-scrollbar-track{
    background: url("../img/scrollbar2.png") no-repeat center center/100% 100%;
}
/*定义滑块 内阴影+圆角*/
.rank-table::-webkit-scrollbar-thumb{
  background: url("../img/scrollbar-thumb.png") no-repeat center center/100% 100%;
}
.rank2 .rank-table::-webkit-scrollbar-thumb{
    background: url("../img/scrollbar-thumb2.png") no-repeat center center/100% 100%;
}
.rank-con th{ width: 28%; height: 40px; background: #000; font-size: 24px; color: #fff; text-align: center;}
.rank-con td{ width: 28%; height: 44px; font-size: 22px; color: #fff;}
.rank-con tr th:nth-of-type(2),.rank-con tr td:nth-of-type(2){ width: 42%;}
.rank-con tr th:nth-of-type(3),.rank-con tr td:nth-of-type(3){ width: 30%;}
.btn-back{ background-position: -468px -518px; width: 138px; height: 65px; margin-left: 52px;}
.rank2 .rank-table{ height: 820px; margin-bottom: 10px;}
.rank2 .rank-con.nonext .rank-table{ height: 858px;}
.rank2 .rank-con.nonext .paging{ display: none;}
.user-list{ min-height: 798px; margin-bottom: 20px;}
.paging{ display: flex; align-items: center; text-align: center; font-size: 24px; color: #fff; }
.paging p{ flex: 1;}
.paging input{ background: transparent; border: none; border-bottom: 1px solid #fff; width: 50px; text-align: center; font-size: 24px; color: #fff;}
.btn-prev,.btn-next,.btn-up{ width: 98px; height: 40px; background-position: -632px -599px;}
.btn-next{ background-position-y: -681px; margin-right: 16px;}
.btn-up{ background-position-y: -756px; margin-right: 16px;}
.btn-follow{ background: url("../img/ew.png") no-repeat; background-size: 100% auto; width: 49px; height: 252px; position: absolute; right: 0; top: 1rem;}
/* pop */
.lay-msg-con{ font-size: 24px;}
.pop{ display: none; background: url("../img/pop.png") no-repeat; background-size: 100% auto; width: 750px; height: 373px; box-sizing: border-box; padding: 90px 114px 0; position: relative;}
.pop1{ background-image: url("../img/pop1.png"); height: 412px; padding: 90px 140px 0;}
.pop2{ background-image: url("../img/pop2.png"); height: 1076px;}
.pop3{ background-image: url("../img/pop3.png?1.0"); height: 455px; padding: 90px 140px 0;}
.pop4{ background-image: url("../img/pop4.png?1.0"); height: 761px; padding: 88px 114px 0;}
.pop5{ background-image: url("../img/pop5.png"); height: 510px; padding: 190px 114px 0;}
.pop6{ background-image: url("../img/pop6.png?1.1"); height: 761px; padding: 90px 114px 0 124px;}
.pop7{ background-image: url("../img/adp.png"); width: 620px; height: 1065px;}
.pop7 .btn-close{ top: 0; right: 6px;}
.pop-share{ display: none; width: 100%; height: 100%; position: fixed; z-index: 9999; top: 0; left: 0; background: rgba(0,0,0,.7) url("../img/pop-share.png") no-repeat center top/100% auto;}
.pop-login { width: 608px; height: 624px; background: none; padding: 1px;}
.login-box{ width: 100%; height: 100%;}
.btn-close{ width: 54px; height: 46px; background-position-x: -696px; position: absolute; top: 14px; right: 70px;}
.pop3 .btn-close{ top: 10px; right: 70px;}
.pop4 .btn-close{ top: 14px; right: 70px;}
.pop5 .btn-close{ top: 80px;}
.pop-login .btn-close{ top: 0; right: 0;}
.pop-com{ height: 138px; display: flex; justify-content: center; align-items: center; text-align: center; font-size: 30px; color: #4c4537; line-height: 1.3; margin: 16px;}
.btn-confirm,.btn-cancel,.btn-create2,.btn-fillin{ width: 224px; height: 71px; background-position-y: -437px; margin: 0 6px;}
.btn-cancel{ background-position-x: -234px;}
.btn-create2{ background-position-y: -518px;}
.btn-fillin{ background-position: -234px -518px;}
.rule{ height: 850px; overflow-x: hidden; overflow-y: auto; list-style-type: none; counter-reset: sectioncounter; font-size: 22px; color: #000; margin: 44px auto;}
.rule li{ display: flex; margin-bottom: 14px;}
.rule li:before{ width: 30px; height: 30px; flex-shrink: 0; background: #7f6150; text-align: center; line-height: 30px; margin-right: 8px; color: #fff; content: counter(sectioncounter); counter-increment: sectioncounter;}
.join-ipt{ display: block; background: #fff url("../img/icon1.png") no-repeat 10px center/24px auto; border-radius: 4px; border: 1px solid #d1c3a5; width: 460px; height: 60px; line-height: 60px; margin: 50px auto; font-size: 24px; color: #3b3b3b; box-sizing: border-box; padding: 0 16px 0 42px;}
.copy-text{ background: url("../img/ipt.png") no-repeat; background-size: 100% auto; width: 479px; height: 67px; font-size: 22px; color: #fff; margin: 46px auto 20px; display: flex; align-items: center; box-sizing: border-box; padding: 0 30px;}
.copy-text input{ flex: 1; font-size: 22px; color: #fff; border: none; background: none;}
.copy-text i{ background: url("../img/icon2.png") no-repeat; background-size: 100% auto; width: 40px; height: 42px; margin-left: 10px;}
.btn-copy2{ display: block; background: url("../img/ipt.png") no-repeat; background-size: 100% auto; width: 479px; height: 67px; line-height: 67px; text-align: center; font-size: 30px; color: #fff; margin: 0 auto;}
.ipt-list{ width: 460px; margin: 42px auto;}
.ipt-list2{ display: flex; flex-direction: column; justify-content: space-evenly; height: 210px; align-items: center; margin: 20px 0;}
.ipt-list li{ display: flex; align-items: center; margin: 14px 0; width: 460px; font-size: 24px; color: #3b3b3b;}
.ipt-list2 li{ margin: 6px 0;}
.ipt{ display: flex; align-items: center; flex-grow: 1; height: 60px;  white-space: nowrap; line-height: 60px; background: #fff; border: 1px solid #d1c3a5; border-radius: 4px;}
.ipt input{ flex-grow: 1; padding-right: 10px; font-size: 24px; color: #3b3b3b; height: 100%; max-width: 276px;}
.ipt2 input{ max-width: 94px;}
.ipt3 input { max-width: 180px;}
::-webkit-input-placeholder{ font-size: 22px;}
.ipt i,.ipt p{ flex-shrink: 0;}
.ipt-list li.radio-li{ align-items: flex-start;}
.radio{ flex-shrink: 0; border: 1px solid #494949; width: 24px; height: 24px; border-radius: 50%; margin-right: 10px; display: flex; align-items: center; justify-content: center; }
.radio.checked:after{ content: ''; width: 16px; height: 16px; border-radius: 50%; background: #7f6150; }
.btn-getcode{ width: 156px; height: 60px; line-height: 60px; text-align: center; font-size: 24px; color: #fff; margin-left: 10px; background: #8f785a; border-radius: 4px; flex-shrink: 0;}
.btn-getcode.disabled{ opacity: .8; pointer-events: none; user-select: none; -webkit-user-select: none;}
.icon1,.icon3,.icon4,.icon5,.icon6,.icon7,.icon8{ background: url("../img/icon1.png") no-repeat; background-size: 100% auto; width: 24px; height: 27px; margin: 0 10px;}
.icon3{ background-image: url("../img/icon3.png");}
.icon4{ background-image: url("../img/icon4.png");}
.icon5{ background-image: url("../img/icon5.png");}
.icon6{ background-image: url("../img/icon6.png");}
.icon7{ background-image: url("../img/icon7.png"); width: 26px; margin: 0 9px;}
.icon8{ background-image: url("../img/icon8.png");}
.record-text{ font-size: 20px; color: #000; height: 100px; margin: 30px 30px 105px;}
.record{ height: 400px; overflow-x: hidden; overflow-y: auto;}
.record table{ width: 100%; text-align: center; font-size: 22px; color: #000;}
.record tr td:last-of-type{ width: 53%; height: 50px; padding: .04rem 0;}
.tit1{ background: url("../img/tit1.png") no-repeat center center/ auto 100%; height: 41px; }
.tit2{ background: url("../img/tit2.png") no-repeat center center/ auto 100%; height: 36px; }
.rank-tip{ color: #fff; font-size: 20px; text-align: center; margin: 10px 0;}
/*.red{ color: red;}*/