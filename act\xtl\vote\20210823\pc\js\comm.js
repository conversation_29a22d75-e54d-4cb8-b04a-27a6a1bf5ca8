$(function () {
    // 龙门服还是正式服
    window.urlName = location.href.indexOf('longmen') > -1 ? 'longmen' : 'public';
    var example = {
        // 公共路径前缀
        base_url: '/xtl/card/',
        // 初始化用户参数
        configParams: {
            app: 'xtl', // 必填，应用标识
            activity: 'vote', // 必填，活动标识
            version: window.urlName == 'longmen' ? '2021080901' : '20210809', // 必填，版本标识
            platform: 'changyou', // 必填，平台标识
        },
        // 活动首页 分享
        indexShare: {
            link: location.origin + '/xtl/vote/20210823/' + window.urlName + '/m/' + (window.urlName == 'longmen' ? 'index' : 'index') + '.shtml',
            title: '你的好友邀你一起当策划',
            desc: '《新天龙八部》卡级服定制活动开启，点击参与，即可定制天龙首个卡！',
            imgUrl: 'http://i0.cy.com/tlpt/vote/20210823/share.jpg'
        },
        // 防抖标识
        clickFlag: false,
        // 当前微信是否有绑定账号
        wxUser: null,
        // 等级定制列表
        levelList: null,
        // 玩法定制列表
        playList: null,
        // 当前账号选择的等级类型
        myLevel: null,
        // 当前账号选择的玩法类型
        myPlay: [],
        // 当前账号是否提交新服名
        isSubmitName: 0,
        // 当前账号选择的玩法类型是否小于5
        isMyPlay: 1,
        // 定制等级对应
        levelObj: {
            'level_A': 69,
            'level_B': 79,
            'level_C': 89,
        },
        // 是否可以提交新服名
        isName: 0,
        // 等级是否是30级
        isLevel: true,
        // 动态定制玩法数量
        playLimitNum: 0,
        // 已发奖
        isAwards: 0,
        // 页面初始化
        init: function () {
            /*
             * 初始化
             * config配置项详见 表 3.1
             */
            ja.config({
                app: example.configParams.app,
                activity: example.configParams.activity,
                version: example.configParams.version,
                platform: example.configParams.platform,
                isWxLogin: true,
                isWxStatus: true,
                // isWxInit: true,
                // isVconsole: true
            });
            /*
              * 初始化完成后执行函数
              * 返回全局对象 glob,详见 表 3.2
              */
            ja.ready(function (glob) {
                $('.name-input').val('');
                example.getList();
                // 用户是否登录
                glob.isLogin ? example.loginAfter() : example.loginBefore();
                // 龙门添加样式
                if (window.urlName == 'longmen') {
                    // 龙门添加样式
                    ja.utils.longmen({
                        top: '100px',
                        left: '5px',
                    });
                    if (ja.code == 10000 && glob.isLogin) {
                        example.tips('此活动页面为龙门测试地址,<br/>活动奖励发放到龙门服,请各位玩家注意');
                    };
                };
                if (ja.isWechat) {
                    // 微信端
                    example.wxShareConfig();
                };
                // 登录
                $('#login').on('click', function () {
                    example.commHandle();
                });
                // 二选一登录 畅游平台登录
                $('#plaLogin').on('click', function () {
                    ja.login();
                });
                // 二选一登录 微信登录
                $('#wxLogin').on('click', function () {
                    if (example.wxUser) {
                        ja.wxAutoLogin();
                    } else {
                        location.href = ja.glob.urlWxBind;
                    }
                });
                // 注销
                $('#logout').on('click', function () {
                    $('.login_err').show()
                    $('.login_succ').hide()
                    ja.logout();
                });
                // 等级定制点击
                $('#levelList').on('click', '.level-btn', function () {
                    if (!example.commHandle()) return;
                    if ($(this).hasClass('gray') || $(this).hasClass('hui')) return;
                    var _this = this;
                    if (example.throttle(1000)) {
                        example.levelVote(_this);
                    };
                });
                // 玩法定制点击
                $('#playList').on('click', '.play-btn', function () {
                    if (!example.commHandle()) return;
                    if ($(this).hasClass('gray') || $(this).hasClass('hui')) return;
                    if (example.isMyPlay) {
                        var _this = this;
                        if (example.throttle(1000)) {
                            example.playVote(_this);
                        };
                    };
                });
                // 填写新服名
                function nameRead() {
                    if (example.isSubmitName) return;
                    var val = $('.name-input').val().trim();
                    var reg = /^[\u4e00-\u9fa5]{1,}$/;
                    $('.name-input').val($('.name-input').val().trim());
                    if (reg.test(val)) {
                        if (val.length > 5) {
                            $('.name-input').focus();
                            $(".name-tips").css({
                                opacity: 1,
                            }).text('仅可填写3-5个汉字，已超出输出范围');
                            example.isName = 0;

                        };
                        if (val.length < 3) {
                            $('.name-input').focus();
                            $(".name-tips").css({
                                opacity: 1,
                            }).text('未填写满3~5个汉字');
                            example.isName = 0;
                        };
                        if (val.length >= 3 && val.length <= 5) {
                            $('.name-input').focus();
                            $(".name-tips").css({
                                opacity: 0,
                            }).text('');
                            example.isName = 1;
                            var serverName = $('.name-input').val().trim();
                            if (example.throttle(1000)) {
                                example.submitName(serverName);
                            };
                        };
                    } else {
                        $(".name-tips").css({
                            opacity: 1,
                        }).text('请填写汉字');
                        example.isName = 0;
                    };
                }
                $('.name-input').on('click', function () {
                    if (ja.isMobile) {
                        var offTop = $(this).offset().top - 200;
                        $(window).scrollTop(offTop);
                    };
                });
                // 提交新服名
                $('.name-btn').on('click', function () {
                    if (!example.commHandle()) return;
                    if ($(this).hasClass('gray')) return;
                    if (!example.isLevel) {
                        popup('#pop-unable');
                        return;
                    };
                    nameRead();
                });
            });
        },
        /**
         * 登录前执行
         */
        loginBefore: function () {
            if (ja.isWechat) {
                example.wxUser = ja.wxBindName;
                if (example.wxUser) {
                    $('.w-txt').show() && $('#wxLogin #isBind').text(example.wxUser).show();
                    $('#wxLogin #unBind').hide();
                };
            };
        },
        /**
         * 登录后执行
         */
        loginAfter: function () {
            $('.login_err').hide();
            $('#userName').text(ja.userInfo.openid);
            $('.login_succ').show();
            example.getUserInfo(); //初始化用户信息
        },
        /**
         * 通用错误码处理
         * param {*} data 返回的对象
         */
        commonErrPop: function (data) {
            var flag = false;
            switch (parseInt(data.code)) {
                case 1202:
                case 1209:
                case 1207:
                case 1007: //1202请求参数缺失或者为空  1209操作频繁
                    example.tips('系统繁忙，请稍后重试！');
                    break;
                case 1019:
                case 1012:
                    ja.isLogin = false; // 重置下ja的登录状态标识以触发登录操作
                    $('#login').click();
                    break;
                case 3301:
                    // 用户创建失败
                    example.tips('用户创建失败');
                    break;
                case 3304:
                    // 用户信息更新失败，请刷新后重试
                    example.tips('用户信息更新失败，请刷新后重试');
                    break;
                case 1210:
                    // 数据异常，请重试
                    example.tips('数据异常，请重试');
                    break;
                case 3103:
                    // 账号封停
                    popup('#pop-stop');
                    break;
                case 2131:
                    example.isLevel = false;
                    // 等级不符合
                    popup('#pop-unable');
                    break;
                case 1106:
                    // 活动未开始
                    ja.code = 1106;
                    example.commHandle();
                    break;
                case 1103:
                    ja.code = 1103;
                    // 活动已结束
                    example.commHandle();
                    break;
                default:
                    flag = true;
                    break;
            }
            return flag;
        },
        /**
         * 通用的前置处理方法
         */
        commHandle: function () {
            var flag = false;
            if (ja.code == 1102) {
                example.tips('活动无效');
            } else if (ja.code == 1106) {
                if (window.urlName == 'longmen') {
                    example.tips('活动未开始');
                } else {
                    example.tips('活动未开始');
                };
            } else if (ja.code == 1103) {
                example.tips('活动已结束');
            } else if (ja.code == 1008) {
                example.tips('配置错误，请联系管理员');
            } else if (!ja.isLogin) {
                ja.isWechat ? popup('#pop-login') : ja.login();
            } else {
                flag = true;
            }
            return flag;
        },
        /**
        * 通用提示
         */
        tips: function (msg) {
            $('#pop-tips p.p-tips').html(msg);
            popup('#pop-tips');
        },
        /**
        * 通用节流
        */
        throttle: function (msc) {
            var oldtime;
            function preventclick(msc) {
                if (!oldtime) {
                    oldtime = new Date().getTime();
                    return true;
                } else {
                    var newtime = new Date().getTime();
                    if (newtime - oldtime > msc) {
                        oldtime = new Date().getTime();
                        return true;
                    } else {
                        return false;
                    }
                }
            }
            return preventclick(msc);
        },
        /**
         * 登录后用户信息初始化
         */
        getUserInfo: function () {
            $.ajax({
                type: "POST",
                url: this.base_url + 'initUser',
                async: false,
                success: function (res) {
                    if (res.code == 10000) {
                        example.myLevel = res.data.myLevel;
                        example.myPlay = res.data.myPlay ? res.data.myPlay.split(',') : [];
                        example.isSubmitName = res.data.isSubmitName;
                        example.playLimitNum = res.data.playLimitNum;
                        $('.playLimitNum').text(example.playLimitNum);
                        example.listState();
                        example.canAwards();
                    } else if (example.commonErrPop(res)) {
                        example.tips(res.message);
                    } else {
                        // 已处理所有情况
                    }
                }
            });
        },
        /**
         * 获取定制列表
         */
        getList: function () {
            $.ajax({
                type: "GET",
                url: this.base_url + 'voteList',
                async: false,
                success: function (res) {
                    if (res.code == 10000) {
                        example.levelList = res.data.levelList;
                        example.playList = res.data.playList;
                        example.listStatic();
                    } else if (example.commonErrPop(res)) {
                        example.tips(res.message);
                    } else {
                        // 已处理所有情况
                    }
                }
            });
        },
        /**
         * 渲染定制列表
         */
        listStatic: function () {
            var $levelList = $('#levelList li'),
                $playList = $('#playList li'),
                levelList = example.levelList,
                playList = example.playList;
            $.each($levelList, function (i, v) {
                $(v).find('.level-btn').attr('type-code', levelList[i].typeCode);
                $(v).find('.level-btn').attr('index', i);
                $(v).find('.level-num').text(levelList[i].num);
            });
            $.each($playList, function (i, v) {
                $(v).find('.play-btn').attr('type-code', playList[i].typeCode);
                $(v).find('.play-btn').attr('index', i);
                $(v).find('.play-num').text(playList[i].num);
            });
        },
        /**
         * 等级选中状态
         */
        levelState: function () {
            var $levelList = $('#levelList .level-btn'),
                myLevel = example.myLevel,
                $levelIcon = $('.level-icon');
            if (myLevel) {
                example.canAwards();
                $levelList.each(function () {
                    if ($(this).attr('type-code') == myLevel) {
                        $(this).addClass('gray');
                    } else {
                        $(this).addClass('hui');
                    }
                });
                $levelIcon.addClass('light');
            } else {
                $levelIcon.removeClass('light');
            };
        },
        /**
         * 玩法选中状态
         */
        playState: function () {
            var $playList = $('#playList .play-btn'),
                myPlay = example.myPlay,
                $playIcon = $('.play-icon'),
                playLimitNum = example.playLimitNum;
            if (myPlay.length > 0) {
                $playList.each(function () {
                    var typeCode = $(this).attr('type-code');
                    if (myPlay.length == playLimitNum) {
                        example.canAwards();
                        $playIcon.addClass('light');
                        example.isMyPlay = 0;
                        if (myPlay.indexOf(typeCode) > -1) {
                            $(this).addClass('gray');
                        } else {
                            $(this).addClass('hui');
                        };
                    };
                    if (myPlay.length < playLimitNum) {
                        $playIcon.removeClass('light');
                        example.isMyPlay = 1;
                        if (myPlay.indexOf(typeCode) > -1) {
                            $(this).addClass('gray');
                        };
                    };

                });
            } else {
                $playIcon.removeClass('light');
            };
        },
        /**
         * 名称选中状态
         */
        nameState: function () {
            var isSubmitName = example.isSubmitName,
                $nameBtn = $('.name-btn');
            if (isSubmitName) $nameBtn.addClass('gray');
        },
        /**
         * 选中状态
         */
        listState: function () {
            example.levelState();
            example.playState();
            example.nameState();
        },
        /**
         * 等级定制投票
         */
        levelVote: function (param) {
            var typeCode = $(param).attr('type-code');
            $.ajax({
                type: "POST",
                url: this.base_url + 'levelVote',
                data: {
                    'typeCode': typeCode
                },
                success: function (res) {
                    if (res.code == 10000) {
                        var $levelNum = $(param).parent('li').find('.level-num');
                        $levelNum.text(Number($levelNum.text()) + 1);
                        example.myLevel = typeCode;
                        example.levelState();
                        var title = example.levelObj[typeCode];
                        $('#djdz').text(title);
                        popup('#pop-select');
                    } else if (example.commonErrPop(res)) {
                        example.tips(res.message);
                    } else {
                        // 已处理所有情况
                    }
                }
            });
        },
        /**
         * 玩法定制投票
         */
        playVote: function (param) {
            var typeCode = $(param).attr('type-code');
            $.ajax({
                type: "POST",
                url: this.base_url + 'playVote',
                data: {
                    'typeCode': typeCode
                },
                success: function (res) {
                    if (res.code == 10000) {
                        var $playNum = $(param).parent('li').find('.play-num');
                        $playNum.text(Number($playNum.text()) + 1);
                        example.myPlay.push(typeCode);
                        example.playState();
                        var title = $(param).parent('li').find('strong').text();
                        $('#wfdz').text(title);
                        popup('#pop-tags');
                    } else if (example.commonErrPop(res)) {
                        switch (parseInt(res.code)) {
                            case 2102:
                                example.tips('已点赞该玩法');
                                break;
                            default:
                                example.tips(res.message);
                                break;
                        }
                    } else {
                        // 已处理所有情况
                    }
                }
            });
        },
        /**
         * 提交新服名
         */
        submitName: function (param) {
            $.ajax({
                type: "POST",
                url: this.base_url + 'submitName',
                data: {
                    'serverName': param
                },
                success: function (res) {
                    if (res.code == 10000) {
                        $('.name-input').val('');
                        $('.name-input').blur();
                        $(".name-tips").css({
                            opacity: 0,
                        }).text('');
                        example.isSubmitName = 1;
                        example.nameState();
                        popup('#pop-name');
                    } else if (example.commonErrPop(res)) {
                        switch (parseInt(res.code)) {
                            default:
                                example.tips(res.message);
                                break;
                        }
                    } else {
                        // 已处理所有情况
                    }
                }
            });
        },
        /**
         * 是否发奖
         */
        canAwards: function () {
            var myLevel = example.myLevel,
                myPlay = example.myPlay,
                playLimitNum = example.playLimitNum;
            if (myLevel && myPlay && myPlay.length == playLimitNum) {
                example.isAwards = 1;
                $('span.brand').show();
            } else {
                example.isAwards = 0;
                $('span.brand').hide();
            };
        },
        /**
         * 微信分享设置
         * param {*} isShare 是否是引导分享
         */
        wxShareConfig: function () {
            ja.share({
                link: example.indexShare.link,//分享链接
                title: example.indexShare.title,//分享标题
                desc: example.indexShare.desc,  //分享描述
                imgUrl: example.indexShare.imgUrl,//分享 icon
                hideMenuList: [
                    'menuItem:share:qq',
                    'menuItem:favorite',
                    'menuItem:share:QZone',
                    'menuItem:openWithSafari',
                    'menuItem:share:weiboApp',
                    'menuItem:openWithQQBrowser',
                ],
                success: function () {
                    example.shareLog(2);
                }
            })
        },
        // 记录分享日志
        shareLog: function (type) {
            $.ajax({
                url: '/shareLog',
                type: 'POST',
                data: {
                    event: type
                },
                success: function (data) {
                    console.log(data.code);
                }
            })
        },
    };
    window.example = example;
    example.init();
});