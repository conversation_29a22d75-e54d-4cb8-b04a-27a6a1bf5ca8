<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="keywords" content="新天龙八部,天龙,怀旧版" />
	<meta name="description" content="《新天龙八部》怀旧服 8月7日首测震撼开启" />
	<meta name="author" content="Design:CP; Web Layout:CP;" />
	<title>怀旧服排行榜-《新天龙八部》怀旧服官方网站</title>
	<link type="image/x-icon" href="http://xtl.changyou.com/favicon.ico" rel="icon" />
	<link type="image/x-icon" href="http://xtl.changyou.com/favicon.ico" rel="shortcut icon" />
	<link type="text/css" rel="stylesheet" href="/all/css/reset.css" />
	<link rel="stylesheet" href="css/style.css">
	</head>
	<script>
		if (!/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)) {
			window.location.href = "../pc/index.shtml";
		};
		var phoneWidth = parseInt(window.screen.width);
		var phoneScale = phoneWidth / 640;
		var ua = navigator.userAgent;
		if (/Android (\d+\.\d+)/.test(ua)) {
			var version = parseFloat(RegExp.$1);
			// andriod 2.3
			if (version > 2.3) {
				document.write('<meta name="viewport" content="width=640, minimum-scale = ' + phoneScale + ', maximum-scale = ' +
					phoneScale + ', target-densitydpi=device-dpi">');
				// andriod 2.3
			} else {
				document.write('<meta name="viewport" content="width=640, target-densitydpi=device-dpi">');
			}
		} else {
			document.write('<meta name="viewport" content="width=640, user-scalable=no, target-densitydpi=device-dpi">');
		}

		var deviceWidth = document.documentElement.clientWidth;
		if (deviceWidth > 640) {
			deviceWidth = 640
		};
		document.documentElement.style.fontSize = deviceWidth / 640 + 'px';
	</script>
	<body>
		<h1 class="none">怀旧服排行榜</h1>
		<div class="wrap">
			<div class="head">
				<div class="xtl_logo_lk pa com">
					<a href="http://tlhj.changyou.com/index.shtml" target="_blank" title="新天龙八部"></a>
				</div>
				<div class="slogan pa"></div>
			</div>

			<div class="cont">
				<div class="tab-wrap">
					<div class="tab">
						<a href="index.shtml" class="tab-i "></a>
						<a href="page2.shtml" class="tab-i "></a>
						<a href="page3.shtml" class="tab-i active"></a>
						<a href="page4.shtml" class="tab-i"></a>
						<a href="page5.shtml" class="tab-i"></a>
						<a href="page6.shtml" class="tab-i"></a>
						<a href="page7.shtml" class="tab-i"></a>
					</div>
				</div>

				<div class="sel-wrap">
					<select name="area" id="area">
						<option>大区</option>
						<option value="area1">大区1</option>
						<option value="area2">大区2</option>
						<option value="area3">大区3</option>
					</select>
					<select name="server" id="server">
						<option>服务器</option>
						<option value="server1">大区1</option>
						<option value="server2">大区2</option>
						<option value="server3">大区3</option>
					</select>
				</div>

				<div class="tit-wrap">
					<h3 class="tit">纵横双线 不忘初心</h3>
					<div class="tit-tip">数据最后更新时间：2020-08-30  00:00:00</div>
				</div>

				<table class="table">
					<thead>
					<tr>
						<td>排名</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					</thead>
					<tbody>
					<tr class="cup1">
						<td><div class="cup-wrap"><i class="ico-cup"></i>1</div></td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr class="cup2">
						<td><div class="cup-wrap"><i class="ico-cup"></i>2</div></td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr class="cup3">
						<td><div class="cup-wrap"><i class="ico-cup"></i>3</div></td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					<tr>
						<td>1</td>
						<td>门派</td>
						<td>角色名</td>
						<td>等级</td>
					</tr>
					</tbody>
				</table>
			</div>
		</div>

		<script src="/all/js/jquery-1.8.3.min.js"></script>

		<!--#include virtual="/all/nav/cy_public_js_dark.html"-->
		<!--#include virtual="/all/dma/dma_static.html"-->
	</body>
</html>
