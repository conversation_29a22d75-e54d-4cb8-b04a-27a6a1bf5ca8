~(function () {
	function App() {
		const that = this

		this.data = {
			apis: ''
		}

		this.ajax = function (l, d, opt) {
			let loading
			return new Promise((resolve, reject) => {
				const env = 3  // 1-内网IP接口  2-内部服务器接口  3-畅游测试接口  4-畅游正式接口
				const domainMap = {
					1: 'http://localhost:8883',
					2: 'http://hbcloud.top:8388',
					3: 'https://api-tlgl-zhjd.changyou.com',
					4: 'https://api-tlbbglact.changyou.com'
				}
				const domain = domainMap[env]
				const activeName = 'tlzb'
				// changyou/tlzb/api/course
				const path = `/changyou/${activeName}/api/`
				const url = domain + path + l

				// console.log('api', l);
				// console.log('api url', url);
				if (opt && opt.loading) loading = lay.load()
				const data = d || {}
				const ajaxOptions = {
					url: opt?.sp ? l : url,
					type: opt?.method ? opt.method : 'POST',
					data: data,
					dataType: 'json',
					headers: {
						// 'Content-Type': 'application/json;charset=UTF-8'
					},
					success: function (res) {
						if (opt && opt.loading) lay.close(loading)
						resolve(res)
					},
					error: function (err) {
						if (opt && opt.loading) lay.close(loading)
						console.log('post err res', err);
						lay.msg('接口请求繁忙，请稍后重试')
						reject(err)
					}
				}
				let verifyToken = $.tools.getStorage('token_zhjd')
				if (l !== 'changyouLogin' && verifyToken) {
					if (mockToken) verifyToken = mockVerifyToken
					ajaxOptions.headers = {
						// 'Content-Type': 'application/json;charset=UTF-8',
						'Authorization': verifyToken
					}
				}
				$.ajax(ajaxOptions)
			})
		}

		this.loginPop = function (type) {
			if ($('.page1 .btn-login').hasClass('timeover')) return that.alert('当前不在活动时间内')
			const s = encodeURIComponent(window.location.href)
			const loginTypeMap = {
				'wegame': 'project=activitywegame'
			}
			const loginType = loginTypeMap[type] ? loginTypeMap[type] : 'project=activity'
			const coreSrc = 'https://auth.changyou.com/interfaceLogin';
			let src = `${coreSrc}?${loginType}&s=${s}`;
			// console.log('登录组件链接：', src)
			$.tools.setStorage('URL_BEFORE_LOGIN', window.location.href)
			if(type === 'wegame') {
				location.href = src
			} else {
				$('#popLogin .login-box').html(`<iframe src="${src}" frameborder="0" width="100%" height="100%" scrolling="no"></iframe>`);
				lay.open('popLogin')
			}
		}

		this.login = function () {
			const ticket = $.tools.getStorage('ticket')
			if (!ticket) return Promise.resolve({code: -1, msg: '没有ticket'})
			// 登录
			// 'eyJhbGciOiJIUzUxMiJ9.eyJKT0lOX0FQUCI6Inh0bCIsIkpPSU5fV1giOiJ3eGRlZDJlMmFlY2M5MTZhMWEiLCJBVVRPX0xPR0lOIjowLCJUIjoxNzUyNTcyOTMwNzQ5LCJWIjoiMjAyNTAzMjUiLCJJUCI6IjE3NTAxMDAzODIxMyIsIkpPSU5fVVNFUiI6IjY4NzYyNDAyMjAyZTRmMDAwMWVhM2M1MyIsIkFDVFYiOiJmbGFnIn0.a33lwW8aY91wbsDk7HBNJZkhJOE-setRPwlcMQruwuIZjSqRwNZdzGp9qSG62vOY4Y552Tgea-X9H4p79LmyIg'
			// 'eyJhbGciOiJIUzUxMiJ9.eyJKT0lOX0FQUCI6Inh0bCIsIkpPSU5fV1giOiJ3eGRlZDJlMmFlY2M5MTZhMWEiLCJBVVRPX0xPR0lOIjowLCJUIjoxNzUyNTcyOTMwNzQ5LCJWIjoiMjAyNTAzMjUiLCJJUCI6IjE3NTAxMDAzODIxMyIsIkpPSU5fVVNFUiI6IjY4NzYyNDAyMjAyZTRmMDAwMWVhM2M1MyIsIkFDVFYiOiJmbGFnIn0.a33lwW8aY91wbsDk7HBNJZkhJOE-setRPwlcMQruwuIZjSqRwNZdzGp9qSG62vOY4Y552Tgea-X9H4p79LmyIg'
			const urlBeforeLogin = $.tools.getStorage('URL_BEFORE_LOGIN')
			if (!urlBeforeLogin) return Promise.resolve({code: -1, msg: '没有传登录前的url'})
			const s_url = encodeURIComponent(urlBeforeLogin)
			const sid = $.tools.getParam('sid')
			const sign = $.tools.getParam('sign')
			const data = {ticket, s_url}
			if (sid) data.sid = sid
			if (sign) data.sign = sign
			const CYSCID = $.tools.getParam('type')
			if (CYSCID) data.CYSCID = CYSCID
			return new Promise(resolve => {
				that.ajax('changyouLogin', data)
					.then(res => {
						$.tools.removeStorage('ticket')
						if (mock) res = {
							"code": 0,
							"msg": "success",
							"data": {
								"userid": "078661e77fcc5ec4e13af24b2135d333",
								"verifyToken": "773cH5N6ftVo2vklF2xX-ih1QuQAxaSz3DwWSyZyKDlDtj3dgyef6znaNtjEu2TKJ24ZXmvLX31LIvAeS-XYc3iVAiDpR-Xgk2oKV8dhlsw30cRaKys3NT17p0AMRm3mVMjc7gXQpZ72Eoju9-x1F8ZVq8okOqJA96akEB73Swfw5g4F2w-1K3T6JXwVziDqBg",
								"expire": 1648494989,
								"role_name": "摩登兄弟最帅",
							}
						}

						if (res.code === -20003) {
							lay.close(that.loading_index)
							that.alert('您的账号已停权', that.logout)
							return;
						}

						if (res.code === -90001 || res.code === -90002) {
							lay.close(that.loading_index)
							that.alert('当前不在活动时间内')
							$('.page1 .btn_start').addClass('timeover')
							return;
						}

						if (res.code === 0) {
							// 拿到了 verifyToken 信息，存到 localStorage
							$.tools.setStorage('token_zhjd', res.data.token)
						} else {
							logoutAuto()
						}

						resolve(res)
					})
					.catch(res => {
						if (mock) {
							res = {
								"code": 0,
								"msg": "success",
								"data": {
									"userid": "078661e77fcc5ec4e13af24b2135d333",
									"verifyToken": "773cH5N6ftVo2vklF2xX-ih1QuQAxaSz3DwWSyZyKDlDtj3dgyef6znaNtjEu2TKJ24ZXmvLX31LIvAeS-XYc3iVAiDpR-Xgk2oKV8dhlsw30cRaKys3NT17p0AMRm3mVMjc7gXQpZ72Eoju9-x1F8ZVq8okOqJA96akEB73Swfw5g4F2w-1K3T6JXwVziDqBg",
									"expire": 1648494989,
									"role_name": "摩登兄弟最帅",
								}
							}

							resolve(res)
						}
					})
			})
		}
		function logoutAuto() {
			// that.alert("登录失效,请重新登录！", that.logout)
			// that.logout()
			$.tools.removeStorage('token_zhjd')
			location.reload()
		}

		this.checkLogin = function () {
			try {
				let verifyToken = $.tools.getStorage('token_zhjd');
				if (!verifyToken) return Promise.resolve({code: -2, msg: '没有verifyToken'})

				function mockData() {
					return {
						"code": 0,
						"msg": "success",
						"data": {
							"userId": "8941c3807876ef1ff0d4b06735eec6a5",
							"expire": 1743132501,
							"cn": "qs***<EMAIL>",
							"nickname": "我是第一",//昵称
							"headNo": 3,//头像
							"level": 0,
							"isLoginGame": 1,
							"isLeader": 0,//1创建小队，队长
							"memberNum": 0,//队伍数
							"taskCompleteNum": 0,
							"leaderUin": "896a628ea29e2043a9ced1624ff6f418",//是否加入小队
							"menpaiNo": 0,//门派id
							"token": "a9c7ePovYDughB1VMgJgzXcd2bt0lfAckyDxDv4KQcaFXkUHsbHIPVrb2JtxdwqQCtrCD3vgb_Fj6_J1KzHbWBUFhQ6DL7Yp-7JduIUxHzBlaGPdYpdMVmaHrdV-3So_e8cyaM7KW2xUDZLLmlqU1w34C_sF5V-Wb_OF79hAJ9ezIkDLVp2gICOkEObA2Q",
							"menpaiCount": [//门派数据，如果门派无人选择加入，则不会出现在数组中
								{
									"count": 18,
									"menpaiNo": 1//门派id
								},
								{
									"count": 20,
									"menpaiNo": 2
								},
								{
									"count": 42,
									"menpaiNo": 3
								},
								{
									"count": 9,
									"menpaiNo": 4
								}
							],
							"teamData": [//队伍列表
								{
									"userId": "078661e77fcc5ec4e13af24b2135d333",
									"nickname": null,
									"headNo": 6,
									"isLeader": 0
								},
								{
									"userId": "8941c3807876ef1ff0d4b06735eec6a5",
									"nickname": "我是第一",
									"headNo": 3,
									"isLeader": 0
								},
								{
									"userId": "896a628ea29e2043a9ced1624ff6f418",
									"nickname": null,
									"headNo": 1,
									"isLeader": 1//是否队长：1是
								}
							]
						}
					}
				}

				function handler(res) {

					if (res.code === -20003) {
						lay.close(that.loading_index)
						that.alert('您的账号已停权', that.logout)
						return;
					}

					if (res.code === -90001 || res.code === -90002) {
						that.data.activity_status = res.code
						lay.close(that.loading_index)
						that.alert('当前不在活动时间内')
						$('.page1 .btn_start').addClass('timeover')
						return;
					}

					if (res.code === 0) {
						// 登录成功
						const user_info = res.data
						$.tools.setStorage('token', res.data.token)
						that.data.user_info = user_info
					} else {
						logoutAuto()
					}
				}

				return new Promise(resolve => {
					that.ajax('index').then(res => {
						if (mockUserInfo) res = mockData()
						handler(res)
						resolve(res)
					}).catch(res => {
						console.log('checkLogin catch---:', res);
						if (mockUserInfo) res = mockData()
						console.log('checkLogin catch mock res：', res);
						if (res.responseJSON) res = res.responseJSON
						handler(res)
						resolve(res)
					})
				})
			} catch (error) {
				return Promise.resolve({code: -1, msg: '登录超时', error})
			}
		}

		function handlerLoginSuccess() {
			bindAfter()
		}

		this.init = function () {
			console.log('init');

			document.addEventListener('touchstart', function (event) {
				if (event.touches.length > 1) {
					event.preventDefault();
				}
			}, {passive: false});

			document.addEventListener('touchmove', function (event) {
				if (event.touches.length > 1) {
					event.preventDefault();
				}
			}, {passive: false});

			bgVideoPlay()

			bindBefore()

			this.checkLogin().then(res => {

				if (res.code === 0) {
					handlerLoginSuccess()
				}
			})

		}

		function bindAfter() {
			if(!that.binded) {

			}
			that.binded = true
		}

		function bindBefore() {
			$('#login').click(function () {
				lay.open('pop_login')
			})

			$('.navbtn1').on('click', function () {
				$('body,html').animate({
					scrollTop: 0
				}, 1000);
			});
			$(window).bind('load scroll', function () {
				var t = $(window).scrollTop();
				if (t > $('.navbox').offset().top) {
					$('.flobox').show();
				} else {
					$('.flobox').hide();
				}
			});
			$('.openbtn').on('click', function () {
				if ($(this).hasClass('sq')) {
					$(this).removeClass('sq');
					$('.flobox').removeClass('none');
				} else {
					$(this).addClass('sq');
					$('.flobox').addClass('none');
				};
			});
			$(".coursebox .course_nav_box").on("click", ".course_nav", function() {
				var index = $(this).index()
				$(this).addClass("on").siblings(".course_nav").removeClass("on");
				$('.course_item').eq(index).addClass('show').siblings().removeClass('show');
			})
			$('.allbox a.normallink').on('click', function () {
				lay.open('pkPop3')
			});
			$(".step2").click(function () {
				// return alert('还未到此赛段，未有名单')
				lay.open('sqPop')
			})
			$(".step3").click(function () {
				// return alert('还未到此赛段，未有名单')
				lay.open('pkPop')
			})
			$(".step4").click(function () {
				// return alert('还未到此赛段，未有名单')
				lay.open('pkPop1')
			})
			$('.step5').on('click', function () {
				// return alert('还未到此赛段，未有名单')
				lay.open('pkPop2')
			});
			var vsrc = [
				'https://v.douyin.com/ihdLU5eW/ <EMAIL> :3pm',
				'https://v.douyin.com/ihdRF4WV/',
				'https://v.douyin.com/iheWLsMo/ <EMAIL> :9pm',
				'https://v.douyin.com/iheqoU6r/ <EMAIL> :1pm',
			];
			$('.livehd a').on('click', function () {
				$(this).addClass('cur').siblings().removeClass('cur');
				$('.livebox a').attr('href', vsrc[$(this).index()]);
			});
			var mySwiper = new Swiper('#swiper1', {
				prevButton: '.swiper-button-prev',
				nextButton: '.swiper-button-next',
				autoplay: 5000, //可选选项，自动滑动
				loop: true
			});
			$('.team_list').on('click', 'a', function () {
				var id = $(this).data('id')
				if (!id) return;
				var info = findTeam(id)
				$('#infpop').html(info)
				if (info) {
					lay.open('infpop')
				}
				else {
					alert("暂无信息");
				}
			});
			$('#video_pop .close').click(function () {
				$('#video_pop iframe').attr('src', '');
			})
		}
		function bgVideoPlay() {
			var videoBg = document.getElementById('videoBg');
			var canPlayVideo = videoBg.canPlayType,
				isMob = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
			if (!isMob && canPlayVideo) {
				videoBg.style.display = "none";
				videoBg.src = "video/video.mp4";
				videoBg.addEventListener("canplaythrough", function () {
					videoBg.style.display = "block";
					videoBg.play();
				});
			}
		}

	}

	window.app = new App()
	app.init()
}());



