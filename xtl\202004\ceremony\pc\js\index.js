var cp_h,cp_w,scroll_h,_s,_ss,toplen;
$(window).on('load resize',function(){
	cp_h=$(window).height()-165;
	cp_w=$(window).width();
	_s = cp_w / 1920;
	$('.wrap').css("zoom", _s);
	$('.cont_box').css('padding-top',55/_s);
	$('.wrapi').addClass('active');
	if(cp_w<1760){
		if(cp_w<1200){
			cp_w=1200;
		};
		_s = cp_w / 1760;
		$('.wrap').css("zoom", _s);
		$('.cont_box').css('padding-top',55/_s);
		_ss = Math.min(_s, cp_h / 766);
		toplen=55/_ss;
		$('.nav_box').css({"zoom":_ss,'top':toplen});
	}else if(cp_w>1760 && cp_h<766){
		_ss = Math.min(_s, cp_h / 766);
		toplen=55/_ss;
		$('.nav_box').css({"zoom":_ss,'top':toplen});
	}else{
		_ss=1;
		toplen=55/_ss;
		$('.nav_box').css({"zoom":_ss,'top':toplen});
	};
});
//pop(弹窗)
var popIsShow = false;
var popDom = null;
function popShow(id) {
	popHide();
	var p = $('#' + id);
	popDom = p;
	if (p) {
		p.show().css({
			position: 'fixed',
			top: '50%',
			left: '50%',
			marginTop: -popDom.height() / 2 + 'px',
			marginLeft: -popDom.width() / 2 + 'px',
			zIndex: 998
		});
		p.attr('for', 'pop');
		popIsShow = true;
		if ($('[for="' + id + '"]').length >= 1) return;
		$('body').append('<div name="overlay" for=' + id +
			' style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:997;background:rgba(0,0,0,0.8);"></div>');
	}
}
function popHide() {
	$('[for="pop"]').hide().attr('style', '');
	$('[name="overlay"]').remove();
}
window.addEventListener("resize", function() {
	if (!popIsShow)
		return;
	setTimeout(function() {
		popDom.css({
			marginTop: -popDom.height() / 2 + 'px',
			marginLeft: -popDom.width() / 2 + 'px',
			zIndex: 998
		});
	}, 400)
});