<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta content="width=750,minimum-scale=0,maximum-scale=5,user-scalable=no" name="viewport" />
    <meta content="yes" name="apple-mobile-web-app-capable" />
    <meta content="black" name="apple-mobile-web-app-status-bar-style" />
    <meta content="telephone=no" name="format-detection" />
    <meta name="robots" content="index, follow" />
    <meta name="googlebot" content="index, follow" />
    <title>组队同往战新服-《新天龙八部》官方网站</title>
    <meta name="keywords" content="天龙,新天龙,天龙八部,新天龙八部,天龙八部3,tl,tlbb,tl3,xtl,网络游戏,网游,武侠,浪漫武侠,武侠游戏,金庸,<PERSON><PERSON>,天龙八部OL,萧峰,段誉,虚竹,王语嫣,天龙八部钟汉良版,天龙八部黄日华版,8周年,八周年,周年庆,全民代言,纵情四海" />
    <meta name="description" content="《新天龙八部》组队同归，加入小队前往新服，共度新春佳节，同拿金喜礼包!" />
    <link type="image/x-icon" href="http://www.tl.changyou.com/favicon.ico" rel="icon" />
    <link type="image/x-icon" href="http://www.tl.changyou.com/favicon.ico" rel="shortcut icon" />
    <link type="text/css" rel="stylesheet" href="/act/all/css/reset.css" />
    <link type="text/css" rel="stylesheet" href="/act/xtl/newserver/20210820/return/m/css/index.css" />
    <link type="text/css" rel="stylesheet" href="/act/xtl/newserver/20210820/return/m/css/share.css" />
		<link href="/xtl/newserver/20210820/common/myCenter/center.css" rel="stylesheet" type="text/css">
		<link type="text/css" rel="stylesheet" href="/act/xtl/newserver/20210820/return/pc/css/returnpop.css" />
    <style>
        #pop-creat-fail .pop-tit {
            background: url(/act/xtl/newserver/20210820/return/m/img/pop1-3.png) no-repeat center;
        }
    </style>
    <script>
        // 设置额头 防止滚动条
        var h = document.documentElement.clientHeight,
            html = document.getElementsByTagName('html')[0];
        if (h < 1650) {
            var sh = (-1650 + h) / 2;
            document.documentElement.style.marginTop = sh + 'px';
            html.style.height = h + Math.abs(sh) + 'px';
        }
    </script>
</head>

<body>
    <!-- <img src="/act/xtl/newserver/20210820/return/m/img/pop2.png" alt=""> -->
    <div class="wrapper">
        <div class="btn_box">
            <a href="javascript:;" class="sp" title="返回首页" id="backHome"><i>返回首页</i></a>
            <a href="javascript:;" class="sp login_box" title="登录" id="login"><i>登录</i></a>
            <div href="javascript:;" class="btn-logout logout_box log" style="display:none;">
                <i class="user_name"></i>
                【<span class="logout" id="logout">注销</span>】
            </div>
        </div>

        <div class="title_box">
            <!-- 加入小队 -->
            <!-- 暂时去掉sp -->
        </div>

        <!-- 验证码 -->
        <div class="code">
            <div>
                <input type="text" id="teamCodeInput" maxlength="9">
            </div>
            <p>您可以选择更改验证码，加入其它小队</p>
            <a href="javascript:;" class="sp" id="joinTeamBtn">接受邀请</a>
        </div>
        <!-- 介绍 -->
        <div class="introduce">
            <!-- <h2 class="sp"></h2> -->
            <p><span>活动简介：</span><em>即日起至2月19日19:00</em>，所有玩家均可创建或加入小队，<em>小队至少含2人</em>。<em>新服开启后至3月24日24:00</em>，队内<em>所有玩家新服角色等级到达50级、70级、90级</em>均可领取礼包，含有至少1名老友还可以领取额外奖励。</p>
        </div>
    </div>

    <!-- 提示-->
    <div class="pop pop-bg2 pop-lq" id="tips">
        <a href="javascript:;" class="closed close icon hh">关闭</a>
        <div class="pop-con">
            <!-- <h2 class="pop-tit">提示</h2> -->
            <p class="pop-txt1 pop-txt"></p>
        </div>
    </div>
    <!-- 加入小队成功-->
    <div class="pop pop-bg2 pop-join" id="pop-join-succ">
        <a href="javascript:;" class="closed close icon hh">关闭</a>
        <div class="pop-con">
            <h2 class="pop-tit">加入小队成功</h2>
            <p class="pop-txt1 pop-txt">您可在个人中心，查看组队情况。</p>
        </div>
    </div>
    <!--加入小队失败1-->
    <div class="pop pop-bg2 pop-join" id="pop-join-fail">
        <a href="javascript:;" class="closed close icon hh">关闭</a>
        <div class="pop-con">
            <h2 class="pop-tit">加入小队失败</h2>
            <p class="pop-txt">您已经有相同身份证账号在小队里了，<br>快去邀请其他小伙伴一起组队吧！</p>
        </div>
    </div>
    <!--加入小队失败1-->
    <div class="pop pop-bg2 pop-join" id="pop-creat-fail">
        <a href="javascript:;" class="closed close icon hh">关闭</a>
        <div class="pop-con">
            <h2 class="pop-tit">组队失败</h2>
            <p class="pop-txt">您的账号已经绑定过小队，请前往个人中心查看组队情况</p>
        </div>
    </div>
    <!-- 封停-->
    <div class="pop pop-bg2 pop-lq" id="forbidden">
        <a href="javascript:;" class="closed close icon hh">关闭</a>
        <div class="pop-con">
            <!-- <h2 class="pop-tit">提示</h2> -->
            <p class="pop-txt1 pop-txt">您的账号已停权</p>
        </div>
    </div>
    <!-- 登录框 -->
    <!--#include virtual="/xtl/newserver/20210820/common/login/login.shtml"-->
    <script type="text/javascript" src="/act/all/js/jquery-1.8.3.min.js"></script>
    <script type="text/javascript" src="//www.cy.com/js/cy_public_dark.js"></script>
    <script src="/act/all/cdn/join-activity/2.5/join-activity.min.js"></script>
    <script src="/act/all/cdn/popout/1.0/popout_test.js"></script>
    <script src="/act/xtl/newserver/20210820/return/pc/js/index.js"></script>
    <script>
        // popup('#pop-join-fail')
        // <!-- popup("#pop-join-fail"); -->
        // 解决键盘弹出遮挡
        $(".code input").on('blur', function (e) {
            window._inputBlurTimer = setTimeout(function () {
                $(window).scrollTop(0);
            }, 100);
        });
        $(".code input").on('focus', function (e) {
            clearTimeout(window._inputBlurTimer);
        });
    </script>
    <!--#include virtual="/all/dma/dma_activity.html"-->
</body>
</html>
