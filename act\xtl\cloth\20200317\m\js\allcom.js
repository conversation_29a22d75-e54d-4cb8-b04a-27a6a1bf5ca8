// var isLongmen = location.href.indexOf('/longmen/') !== -1,
//     commPath = '/fashion',
//     isBind = undefined,     // 微信是否绑定账号
//     $btnZNQ = $('.znqbtn'),
//     $btnTool = $('.getbtn'),
//     $btnBox = $('#box_btn');

// // 龙门图标
// isLongmen && ja.longmen({ color: '#fff', top: 150 });
// // 礼包状态存储管理
// var statusGift = {
//     key: function() {
//         return ja.glob.userInfo.openid + ':' + ja.utils.formatDate().split(' ')[0] + ':toolGiftStatus'
//     },
//     // 领取道具 存储状态
//     toolSet: function (status) {
//         sessionStorage.setItem(this.key(), status);
//     },
//     // 领取道具 获取状态
//     toolGet: function () {
//         return sessionStorage.getItem(this.key());
//     }
// };

// ja.config({
//     app: 'xtl',                     // 必填，应用标识
//     activity: 'fashion',            // 必填，活动标识
//     version: isLongmen ? '2020031001' :'20200310',  // 必填，活动版本标识
//     platform: 'changyou',           // 必填，登陆平台标识
//     type: 1,
//     // isDebug: true,
//     isWxLogin: true,
//     ready: function (glob) {         // 非必填，初始化完成回调
//         if (glob.isWechat) {
//             glob.code === 1 ? loginAfter() : loginBefore();
//         } else {
//             alert('请在微信中访问');
//         }
//         ja.wx.init(function () {
//             th.indexShare()
//         })
//     }
// });
/**
 *  ================== 函数定义 ===================
 **/
window.onload = function () {
    var clientw = $(window).height() * .8;
    $('.wrap').addClass('active');
    $('.dialog_cont').css('max-height', clientw);
    $('.scrollbox').css('max-height', clientw - 189);
	$('.scrollbox_add').css('height', clientw - 189);
};
// 登录前
function loginBefore() {
    $('.login_box').show();
    $('.logout_box').hide();
    isWxBind();
    // 个人中心按钮隐藏
    $('.hd_center').children().eq(0).hide()
    $('#centerBtn').hide()
}

// 登录后
function loginAfter() {
    $('.login_box').hide();
    $('.logout_box').show();
    $('#userName').text(ja.glob.userInfo.openid);
    // 个人中心按钮显示
    $('.hd_center').children().show()
    $('#centerBtn').show()

    th.getUserinfo();
    th.getAwards()        // 刷新我的奖励
    // 如果本地没有查询到状态，同步请求接口（道具礼）并且阶段状态为正常
    if (!statusGift.toolGet() && th.phase_1 == 1) {
        getStatus();
    }
    // 拿到本地存储状态以及活动状态，对按钮添加样式（道具礼）
    if (statusGift.toolGet() == 2) {
        $btnTool.addClass('ylq')
    }
    // else if (th.phase_1 == 2) {
    //     $btnTool.addClass('dis_btn')
    // }

    // 拿到接口状态，对按钮添加样式（周年礼包）
    if (th.thGift) $btnZNQ.addClass('ylq');
}

// 统一点击
function $click(fun) {
    var flag = false;
    if (ja.glob.isWechat) {
        switch (ja.glob.code) {
            case 0:
                // alert('网络繁忙，请稍后再试');
                flag = true;
                break;
            case 1:
                if (typeof th.isBaned !== 'undefined') {
                    if (th.isBaned) {
                        popShow('forbidden');
                        flag = true;
                    } else if (th.isGrade) {
                        popShow('lowGrade');
                        flag = true;
                    } else {
                        if (fun && typeof fun === 'function') {
                            fun()
                        }
                    }
                }
                break;
            case 2:
                popShow('pop_login');
                flag = true;
                break;
            case 1106:
                alert('活动未开始');
                flag = true;
                break;
            case 1102:
                alert('活动无效');
                flag = true;
                break;
            case 1103:
                alert('活动已结束');
                flag = true;
                break;
        }
    } else {
        alert('请在微信中访问');
        flag = true;
    }
    return flag
}

// 查询微信是否绑定
function isWxBind() {
    if (ja.glob.isWechat) {
        ja.user.wxIsBind(function (res) {
            $('#wxLogin .text').text('');
            isBind = res.code === 10000;
            if (res.code === 10000 && res.data) {
                $('#wxLogin .bdbtn').hide();
                $('#wxLogin .p2_txt').show().html(res.data + '<br />已绑定');
            } else {
                $('#wxLogin .bdbtn').show();
                $('#wxLogin .p2_txt').hide()
            }
        })
    }
}

// 每日领取奖励状态
function getStatus() {

    ja.ajax({
        type: 'GET',
        url: commPath + '/loginTask/status',
        async: false,
        success: function (res) {
            if (res.code === 10000 && res.data && res.data.status) {
                statusGift.toolSet(res.data.status);
            } else {
                ja.utils.hint('领奖状态查询异常');
            }
        }
    })
}

// 页面打开执行
(function () {
    // 宝箱
    var swiper = new Swiper('#bx_swiper', {
        prevButton: '.bxprev',
        nextButton: '.bxnext',
        observer: true,
        observeParents: true
    });
    // 当日期大于 5月9日 12点，图标点亮
    if (Date.now() >= new Date('2020/5/9 12:00').getTime()) {
        $btnBox.addClass('kaix');
    }
    // 添加龙门标识
    isLongmen && ja.longmen({ color: '#fff', top: 150 });
})();

/**
*  ================== 事件绑定 ===================
**/
// 活动登录
$('#loginBtn').click(function () {
    $click()
});

// 账户登录
$('.login_account').click(ja.login);

// 微信登录
$('#wxLogin').click(function () {
    if (isBind) {
        ja.user.wxAutoLogin(function (res) {
            if (res.code === 10000) {
                ja.user.getUserInfo(function (res) {
                    if (res.code === 10000) {
                        loginAfter();
                        popHide()
                    }
                })
            } else {
                alert(res.message);
            }
        })
    } else {
        location.href = ja.glob.urlWxBind;
    }
});

// 退出登录
$('#logout').click(function () {
    sessionStorage.clear()
    ja.logout()
});

// 切换 绘图坊 制衣坊 许愿池
$('#tab').on('click', 'a', function () {
    var $this = $(this);
    // $click(function () {
    var index = $this.index();
    if (index == 0) {
        window.location.href = "/xtl/cloth/"+ (isLongmen?"longmen":"********") +"/m/index.shtml#tab"
    } else if (index == 1) {
        window.location.href = "/xtl/cloth/"+ (isLongmen?"longmen":"********") +"/m/cloth.shtml#tab"
    }
    // })
});
/**
*  ================== 没有对接接口代码没有整理部分 ===================
**/

//活动规则弹窗切换
// $('.jlhd li').on('click', function () {
//     $(this).addClass('on').siblings().removeClass('on');
//     $('.jltab').removeClass('dis').eq($(this).index()).addClass('dis');
// });
//个人中心切换
$('.hd_center li').on('click', function () {
    $(this).addClass('on').siblings().removeClass('on');
    $('.qiehbox').removeClass('dis').eq($(this).index()).addClass('dis');
});
$('#centerBtn').on('click', function () {
    if ($click()) return;
    th.getUserinfo(null, true);        // 刷新个人中心 防止影响绘制相关

    // $('.jlhd li').removeClass('on').eq(0).addClass('on');
    // $('.jltab').removeClass('dis').eq(0).addClass('dis');

    $('.hd_center li').removeClass('on').eq(0).addClass('on');
    $('.qiehbox').removeClass('dis').eq(0).addClass('dis');

    dialogUp('dia_center');
});
//活动规则弹出
$('#center_rule').on('click', function () {

    $('.hd_center li').removeClass('on');
    $('.hd_center li').eq(2).addClass('on');
    $('.qiehbox').removeClass('dis');
    $('.qiehbox').eq(2).addClass('dis');

    dialogUp('dia_center');
});
//我的材料弹出
$('#my_cail').on('click', function () {
    if ($click()) return;
    th.getUserinfo(null, true);        // 刷新个人中心 防止影响绘制相关
    
    $('.hd_center li,.qiehbox').css('dispaly', 'block');
    $('.hd_center li').removeClass('on');
    $('.hd_center li').eq(0).addClass('on');
    $('.qiehbox').removeClass('dis');
    $('.qiehbox').eq(0).addClass('dis');
    // $('.jlhd li').removeClass('on').eq(2).addClass('on');
    // $('.jltab').removeClass('dis').eq(2).addClass('dis');
    dialogUp('dia_center');

});
//分享隐藏 弹出
//dialogUp('dia_share');
$('#dia_share').on('click', function () {
    dialogClose();
});
//dialog 弹出函数
function dialogUp(_id) {
    $('.dialog').hide();
    $('#' + _id).show();
};
function dialogClose() {
    $('.dialog').hide();
};
//赠送
$('.zs_btn').on('click', function () {
    if ($click()) return;
    if (!th.methodForPhase_2()) return;

    th.share($(this).attr('itemid'))
});

// 弹框
var popIsShow = false;
var popDom = null;
function popShow(id) {
    popHide();
    var p = $('#' + id);
    popDom = p;
    if (p) {
        p.show().css({
            position: 'fixed',
            top: '50%',
            left: '50%',
            marginTop: -popDom.height() / 2 + 'px',
            marginLeft: -popDom.width() / 2 + 'px',
            zIndex: 998
        });
        p.attr('for', 'pop');
        popIsShow = true;
        if ($('[for="' + id + '"]').length >= 1) return;
        $('body').append('<div name="overlay" for=' + id + ' style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:997;background:rgba(0,0,0,0.8);"></div>');
    }
}
function popHide() {
    $('[for="pop"]').hide().attr('style', '');
    $('[name="overlay"]').remove();

}
window.addEventListener("resize", function () {
    if (!popIsShow)
        return;
    setTimeout(function () {
        popDom.css({
            marginTop: -popDom.height() / 2 + 'px',
            marginLeft: -popDom.width() / 2 + 'px',
            zIndex: 998
        });
    }, 400)
});

// 领取今日道具
$btnTool.on('click', function () {
    $click(function () {
        if (th.phase_1 == 2) { th.tips('此阶段已结束'); return false };
        if (th.phase_1 == -1) { th.tips('此阶段未开始'); return false};

        if (statusGift.toolGet() && statusGift.toolGet() == 1) {
            ja.ajax({
                type: 'POST',
                url: commPath + '/loginTask/prize',
                async: false,
                success: function (res) {
                    if (res.code === 10000) {
                        popShow('pop_huode');           // 领取成功弹层
                        statusGift.toolSet(2);       // 修改本地存储状态
                        $btnTool.addClass('ylq');           // 修改按钮样式
                        th.getUserinfo(null, true);        // 刷新个人中心 防止影响绘制相关
                        var str = '',
                            giftArr = [
                                { name: '毛笔', img: 'maobi' },
                                { name: '颜料', img: 'yanliao' },
                                { name: '水袖金粉', img: 'jinfen' },
                                { name: '布料', img: 'buliao' }
                            ];
                        if (res.data && res.data.itemIdList) {
                            var arr = res.data.itemIdList;
                            arr.forEach(function (item) {
                                var gift = giftArr[item.itemid - 1];
                                str += '<li>' +
                                    '<img src="/act/xtl/cloth/********/m/img/' + gift.img + '.png" />' +
                                    '<p>' + gift.name + '*' + item.itemnum + '</p >' +
                                    '</li>';
                            });
                            $('#pop_huode .hdlist').html(str);
                        }
                    } else {
                        alert(res.message)
                    }
                }
            })
        }
    })
});

// 领取周年庆礼包
$btnZNQ.on('click', function () {
    if ($(this).hasClass('ylq')) return;
    $click(function () {
        if (!th.thGift) {
            ja.ajax({
                type: 'POST',
                url: commPath + '/anniversary/get',
                async: false,
                success: function (res) {
                    if (res.code === 10000) {
                        popShow('pop_goux');            // 领取成功弹窗
                        $('#pop_goux .name').html(th.getAwards());            // 领取成功弹窗，刷新我的奖励
                        $btnZNQ.addClass('ylq');            // 领取按钮不可点击
                        th.thGift = false;                  // 可领取状态修改
                    } else {
                        alert(res.message);
                    }
                }
            });
        }
    })
});

// 悬浮宝箱
$btnBox.click(function () {
    popShow('pop_bx');
    ja.ajax({
        url: commPath + '/showLotcode',
        success: function (res) {
            if (res.code === 10000) {
                $('.bianmatip').val(res.data)
            }
        }
    })
});
