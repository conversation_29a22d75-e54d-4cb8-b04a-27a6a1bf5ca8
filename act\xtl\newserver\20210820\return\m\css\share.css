@charset "utf-8";

/***public***/
* { margin: 0; padding: 0;}
html, body { width: 100%; height: 100%; overflow: hidden; }
address, caption, cite, code, dfn, em, strong, th, var { font-style: normal; font-weight: normal; }
body, html { -webkit-tap-highlight-color: transparent; -webkit-text-size-adjust: none; }
ol, ul, li { list-style: none; }
caption, th { text-align: left; }
h1, h2, h3, h4, h5, h6 { font-size: 100%; font-weight: normal; }
input[type="text"], input[type="password"], select { -webkit-appearance: none; appearance: none; outline: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); border-radius: 0; box-sizing: border-box; }
a { text-decoration: none; outline: none; }
.c:after { content: '\20'; display: block; height: 0; line-height: 0; visibility: hidden; clear: both; }
.hh { display: block; text-indent: -999em; overflow: hidden; }
.hide { width: 0; height: 0; overflow: hidden; display: none; }
.pr { position: relative; }
.pa { position: absolute; }
html { font-size: 100px; }
.sp { width: 1000px; height: 1000px; background: url(../img/share_sp.png) no-repeat;background-size:564px auto;}
body { font: 14px/1.75 -apple-system, "Helvetica Neue", Helvetica, Arial, sans-serif; background: #fffaed; position: relative; overflow-x: hidden; }
a { text-decoration: none; blr: expression(this.onFocus=this.blur()); outline: none; }
html { -webkit-tap-highlight-color: rgba(0, 0, 0, 0); }
.btn-logout,.btn-logout a{color:#f6ebad;font-size:18px;}
/* 横屏提示 */
.landspace { display: none; }
@media(orientation:landscape) {
	.landspace { display: block; background: rgba(0, 0, 0, 0.8); width: 100%; height: 100%; position: fixed; left: 0; top: 0; z-index: 999; }
	.landspace .tips { width: 100%; text-align: center; height: .5rem; position: absolute; left: 0; top: 50%; margin-top: -.25rem; line-height: .5rem; color: #fff; font-size: .4rem; }
	.landspace .tips { -webkit-animation: shake  6s infinite linear; animation: shake 6s infinite linear; }
}
@-webkit-keyframes shake {
	0%, 100% { -webkit-transform: translateX(0); }
	10%, 30%, 50%, 70%, 90% { -webkit-transform: translateX(-10px); }
	20%, 40%, 60%, 80% { -webkit-transform: translateX(10px); }
}
@keyframes shake {
	0%, 100% { transform: translateX(0); }
	10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
	20%, 40%, 60%, 80% { transform: translateX(10px); }
}

/* wrapper */
.wrapper { width: 100%; height: 1295px; background: url(../img/sharebg.jpg) top center no-repeat; background-size: 100%; padding-top:355px; }
.btn_box { display: flex; justify-content: space-between; padding: 0 36px;}
.btn_box a { width: 95px; height: 30px; background-position: 0 -90px;text-indent:-9999em;}
.btn_box a.login_box{background-position: -231px -90px;}
.title_box { position: relative; height:687px;}
.title_box .main_t { position: absolute; right: 55px; top: -90px; width: 164px; height: 588px; background-position: -10px -10px; }
.title_box .colud { position: absolute; top: 0; left: 0; width: 100%; }
.title_box .colud .left { float: left; width: 374px; height: 88px; background-position: -607px -300px; margin-top: 164px; animation: left 1s infinite alternate linear }
.title_box .colud .right { float: right; width: 285px; height: 25px; background-position: -607px -459px; margin: 74px 18px 0 0; animation: right 1.5s 0s infinite alternate linear }
@keyframes vanishIn {
	0% { opacity: 0; -webkit-transform-origin: 50% 50%; transform-origin: 50% 50%; -webkit-transform: scale(2, 2); transform: scale(2, 2); -webkit-filter: blur(90px); filter: blur(90px); }
	100% { opacity: 1; -webkit-transform-origin: 50% 50%; transform-origin: 50% 50%; -webkit-transform: scale(1, 1); transform: scale(1, 1); -webkit-filter: blur(0px); filter: blur(0px); }
}
@keyframes left {
	0% { transform: translate(-5px, -5px); }
	100% { transform: translate(5px); }
}
@keyframes right {
	0% { transform: translate(-0px, -5px); }
	100% { transform: translate(5px); }
}

/* .enter_team{width: 434px;height: 97px;  margin-left:147px ;padding-top: 25px;} */
.enter_team p { width: 20px; color: #89281b; font-size: 24px; position: absolute; line-height: 26px; }
.enter_team p:first-child { right: 224px; top: -10px; }
.enter_team p:last-child { right: 273px; top: -45px; }
.enter_team p em { display: block; padding: 2px 2px 6px 0; margin: -15px -6px 0 0; text-align: right; }

/* 验证码 */
.code { text-align: center;}
.code label { color: #000; font-size: 34px; }

/* background: url('css_sprites.png')  */
.code div { background: url(../img/share_sp.png) no-repeat; background-position: 0px -162px; width: 487px; height: 45px; margin: auto; }
.code input { width: 313px; height: 45px; border: none; font-size: 28px; background-color: transparent; color: #23313a;margin-left: 174px;text-align:center;}
.code p { color: #851e1b; font-size: 18px; text-align: center;line-height:32px;margin-top:11px;}
.code a { display: block; margin:6px auto 0; width: 259px; height: 82px; background-position: 0px 0px; text-indent: -9999px; }

/* 介绍 */
.introduce h2 { width: 442px; height: 72px; margin: 0 auto; background-position: 0 -236px; }
.introduce p { font-size: 20px; color: #23313a; line-height:24px;padding:14px 100px 0;text-align:left;}
.introduce p em {color:#851e1b;}
.introduce span{font-size:24px;}
