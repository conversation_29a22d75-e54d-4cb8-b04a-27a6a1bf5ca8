server {
    listen 80;
    server_name   api-tlgl-rlr.changyou.com;
    include       /etc/nginx/proxy.conf;
    access_log    /home/<USER>/nginx/nba-gzm.acc.log main;
    error_log     /home/<USER>/nginx/nba-gzm.dist.err.log;
    gzip on;
    ssi on;
    gzip_min_length  1k;
    gzip_buffers     8 16k;
    gzip_http_version 1.1;
    gzip_comp_level 6;
    gzip_types  text/plain text/xml text/css text/javascript application/x-javascript application/javascript application/xml;
    gzip_disable        "MSIE [1-6]\.";
    ### deny SVN
    location ~ ^(.*)\/\.(svn|cvs|git|hg)\/{
       deny all;
       access_log off;
    }
    location /changyou/rlr/admin/ {
        proxy_pass http://**************:8181/;
        proxy_set_header Host $Host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header REMOTE-HOST $remote_addr;
        add_header X-Cache $upstream_cache_status;
        proxy_set_header X-Host $host:$server_port;
        proxy_set_header X-Scheme $scheme;
        proxy_connect_timeout 30s;
        proxy_read_timeout 86400s;
        proxy_send_timeout 30s;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    location /changyou/rlr/ {
        proxy_pass http://**************:8181/;
        proxy_set_header Host $Host:$server_port;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header REMOTE-HOST $remote_addr;
        add_header X-Cache $upstream_cache_status;
        proxy_set_header X-Host $host:$server_port;
        proxy_set_header X-Scheme $scheme;
        proxy_connect_timeout 30s;
        proxy_read_timeout 86400s;
        proxy_send_timeout 30s;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    location / {
       root /home/<USER>/dist/;
       index  index.html index.htm;
      try_files $uri $uri/ /index.html;
    }

   }