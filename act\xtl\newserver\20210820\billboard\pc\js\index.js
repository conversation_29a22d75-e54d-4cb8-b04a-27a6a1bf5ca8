// 是否龙门环境
var isLongmen = location.href.indexOf('/longmen/') !== -1;
ja.config({
	app: 'xtl',
	activity: 'kaserver',
	version: isLongmen ? '2021082401' :'20210824',
	platform: 'changyou',
	type: 1,
	ready: function(glob) {
		g.init();
	}
});
var g = {
	BASE_URL: '/xtl/kaserver/',
	apis: {
		menpaiRank: 'menpaiRank', //门派第一人
		getSectList: 'getSectList', //获取帮会排名列表
		getSectByName: 'getSectByName' //查询帮会
	},
	data: {
		isLongmen: 0,
		page0: 1,
		page1: 1,
		list:'',
		list1:'',
		size:10,
		total:0,
		total1:0,
		curIndex:0,
        isMobile: ja.glob.isMobile,
		groupNo: [
			[5143, 5144], //常规
			[9015, 9031] //龙门
		]
	},
	ajax: function(o, cb) {
		var d = {
			method: 'GET',
			dataType: 'json',
			data: {},
			sp: false
		};
		var t = $.extend(true, d, o);
		ja.ajax({
			url: (t.sp ? t.url : (g.BASE_URL + t.url)),
			type: t.method,
			dataType: t.dataType,
			data: t.data,
			success: function(ret) {
				if (cb) {
					cb(ret);
				}
			},
			error: function(e) {
				var ret = {
					code: -5000,
					msg: '请求出错',
					message: '请求出错'
				};
				if (cb) {
					cb(ret);
				}
			}
		})
	},
	init: function() {
		if (location.href.indexOf('/longmen/') !== -1) {
			//是龙门
			g.data.isLongmen = 1;
		} else {
			//不是龙门
			g.data.isLongmen = 0;
		};
		g.mpRank(g.data.groupNo[g.data.isLongmen][0]);
		g.getList(g.data.page0,g.data.groupNo[g.data.isLongmen][0]);
		g.getList1(g.data.page0,g.data.groupNo[g.data.isLongmen][1]);
        // 链接赋值/龙门
        if (isLongmen) {
            ja.utils.longmen({
                color: 'white',
                top: g.data.isMobile ? 100 : 170,
                left: g.data.isMobile ? 40 : 60
            });
            alert('亲爱的玩家您好，本次活动为测试，奖励仅在测试服可用，感谢您的支持！');
        }
	},
	mpRank: function(groupId) {
		g.ajax({
			url: g.apis.menpaiRank,
			data: {
				groupId: groupId
			}
		}, function(res) {
			if (res.code == 10000) {
				if (res.data.length !== 0) {
					var mplist = res.data,
						str = '';
					for (var i = 0; i < mplist.length; i++) {
						str += '<li class="mp' + mplist[i].menpai + '"><p >' + mplist[i].roleName +
							'</p><span>装评<br>' + mplist[i].zbValue + '</span></li>';
					};
					$('.mplist').html(str);
				};
			};
		});
	},
	getList: function(pageNum,groupNo) {
		var postData = {
			pageSize: g.data.size,
			pageNum: pageNum,
			groupNo:groupNo
		};
		g.ajax({
			url: g.apis.getSectList,
			data: postData
		}, function(ret) {
			if (ret.code === 10000) {
				g.data.total = ret.data.sectCount;
				g.data.list = ret.data.sectList;
				g.renderNewsPage(g.data.list,$('.jifenbox1'));
			}
			var $pagenation = $('.tpage1');
			var page_opt = {
				dom: $pagenation,
				page: g.data.page0,
				total: g.data.total,
				size: g.data.size,
				pageTotal: Math.ceil(g.data.total / g.data.size),
				showSkipInputFlag: false
			};
			g.renderPagenation(page_opt, function(page) {
				g.data.page0 = page;
				postData.pageNum = page;
				g.ajax({
					url: g.apis.getSectList,
					data: postData
				}, function(ret) {
					if (ret.code != 10000) {
						return;
					};
					var list = ret.data.sectList;
					g.renderNewsPage(list,$('.jifenbox1'));
				});
			});
		})
	},
	getList1: function(pageNum,groupNo) {
		var postData = {
			pageSize: g.data.size,
			pageNum: pageNum,
			groupNo:groupNo
		};
		g.ajax({
			url: g.apis.getSectList,
			data: postData
		}, function(ret) {
			if (ret.code === 10000) {
				g.data.total1 = ret.data.sectCount;
				g.data.list1 = ret.data.sectList;
				g.renderNewsPage(g.data.list1,$('.jifenbox2'));
			}
			var $pagenation = $('.tpage2');
			var page_opt = {
				dom: $pagenation,
				page: g.data.page1,
				total: g.data.total1,
				size: g.data.size,
				pageTotal: Math.ceil(g.data.total1 / g.data.size),
				showSkipInputFlag: false
			};
			g.renderPagenation(page_opt, function(page) {
				g.data.page1 = page;
				postData.pageNum = page;
				g.ajax({
					url: g.apis.getSectList,
					data: postData
				}, function(ret) {
					if (ret.code != 10000) {
						return;
					};
					var list = ret.data.sectList;
					g.renderNewsPage(list,$('.jifenbox2'));
				});
			});
		})
	},
	renderNewsPage:function(list,dom){
		var str='';
		var doms=dom.find('tbody');
		for(var i=0;i<list.length;i++){
			str+='<tr><td>'+list[i].rank+'</td><td>'+list[i].sectName+'</td><td>'+list[i].sectLeader+'</td><td>'+list[i].score+'</td><td>'+timestampToTime(list[i].lastWinTime)+'</td></tr>';
		};
		doms.html(str);
	},
	// 通用分页代码
	renderPagenation: function(opt, callback) {
		var that = this;
		if (opt.pageTotal === 0) {
			$(opt.dom).hide();
			return;
		};
		//分页
		var mypage = new myPagination({
			dom: opt.dom,
			curPage: opt.page || 1, //初始页码
			pageTotal: opt.pageTotal, //总页数
			pageAmount: 5, //一次显示多少页
			dataTotal: opt.total, //总共多少条数据
			pageSize: opt.num, //可选,分页个数
			showFirst: true, //是否显示首页
			showFinal: true, //是否显示尾页
			showPageTotalFlag: false, //是否显示数据统计
			showSkipInputFlag: false, //是否支持跳转
			prevText: '<',
			nextText: '>',
			showFirst: false,
			showFinal: false,
			textBeforeText: '前往',
			getPage: function(page) {
				// console.log(page)//当前点击的page
				// 获取下一页数据并展示
				// var cursor = (page-1)*size;
				// if(cursor<g.pagenation_opt.total-1){
				//     if(callback){ callback(cursor);}
				// }
				if (callback) {
					callback(page);
				}
			}
		})
		return mypage
	},
	ckTeam: function(groupNo, sectName, dom ,pagedom) {
		g.ajax({
			url: g.apis.getSectByName,
			data: {
				groupNo: groupNo,
				sectName: encodeURI(sectName)
			}
		}, function(res) {
			console.log(res);
			if (res.code == 1031) {
				alert('该帮会不存在！');
				return;
			};
			if (res.code == 10000) {
				console.log(res);
				var sectlist = res.data,
					str = '';
				var doms=dom.find('tbody');
				str = '<tr><td>' + sectlist.rank + '</td><td>' + sectlist.sectName +
					'</td><td>' + sectlist.sectLeader + '</td><td>' + sectlist.score +
					'</td><td>' + timestampToTime(sectlist.lastWinTime) + '</td></tr>';
				doms.html(str);
				pagedom.hide();
			};
		});
	}
};
$('.ckbtn').on('click', function() {
	var val = $('.searhbox input').val();
	if (val) {
		if(g.data.curIndex){
			g.ckTeam(g.data.groupNo[g.data.isLongmen][1], val,$('.jifenbox2'),$('.tpage2'));
			console.log(g.data.groupNo[g.data.isLongmen][1]);
		}else{
			g.ckTeam(g.data.groupNo[g.data.isLongmen][0], val,$('.jifenbox1'),$('.tpage1'));
			console.log(g.data.groupNo[g.data.isLongmen][0]);
		};
	} else {
		alert('请输入帮会名称');
	};
});
//门派第一人
$('#mpbtn a').on('click', function() {
	$(this).removeClass('bluebtn').siblings().addClass('bluebtn');
	g.mpRank(g.data.groupNo[g.data.isLongmen][$(this).index()]);
});
//排名切换
$('#rankbtns a').on('click', function() {
	g.data.curIndex=$(this).index();
	$(this).removeClass('bluebtn').siblings().addClass('bluebtn');
	$('.distab').removeClass('dis').eq(g.data.curIndex).addClass('dis');
});

function timestampToTime(timestamp) {
	var date = new Date(timestamp);
	Y = date.getFullYear() + '/';
	M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '/';
	D = date.getDate() + ' ';
	h = date.getHours() + ':';
	m = date.getMinutes();
	return Y + M + D + h + m;
};
