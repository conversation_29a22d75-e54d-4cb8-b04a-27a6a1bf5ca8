// $(function () {
var example = {
    base_url: '/xtl/guess/',
    isLongmen: location.href.indexOf('longmen') !== -1,
    initParam: { // 初始化参数
        app: 'xtl',
        activity: 'zbs_guess',
        version: '20240719',
        platform: 'wgchangyou'
    },
    errorMsg: { // 通用的错误码提示文案
        1102: '活动无效',
        1106: '活动未开始',
        1103: '活动已结束'
    },
    /** 等级限制 */
    levelLow: false,
    /** IP下账号超限 */
    IPLimit: false,
    /** 决赛结果缓存 */
    WINresult: [],
    init: function () {
        /* 初始化  config配置项详见 表 3.1 */
        ja.config({
            app: this.initParam.app, // 必填，应用标识
            activity: this.initParam.activity, // 必填，活动标识
            version: this.initParam.version + (example.isLongmen ? '01' : ''), // 必填，活动版本标识
            platform: this.initParam.platform, // 必填，登陆平台标识
            isWxLogin: true,
            isWxStatus: true,
            isAutoWxLogin: false
        });

        // logDebug依赖阿里云错误日志上报js
        $.ajaxSetup({
            error: function (jqXHR) {
                if (this.url.indexOf(location.hostname) != -1) {
                    alert('系统异常，请稍后再试！');
                    logDebug.error({
                        activity: example.initParam.activity,
                        message: [
                            'title：' + '全局ajax请求Error处理',
                            'Request URL：' + this.url,
                            'Request Method：' + this.type,
                            'header：' + JSON.stringify(this.headers),
                            'Status Code：' + jqXHR.status + jqXHR.statusText,
                            'data：' + this.data
                        ].join('\n')
                    })
                }
            }
        })
        window.onerror = function (message, source, lineno, colno, error) {
            logDebug.error({
                activity: example.initParam.activity,
                message: [
                    'title：' + 'window.onerror',
                    'errorMessage：' + message + '----in：' + source + '：' + lineno + '：' + colno
                ].join('\n')
            })
        }

        /*  初始化完成后执行函数 返回全局对象 glob,详见 表 3.2 */
        ja.ready(function (glob) {
            glob.isLogin ? example.loginAfter() : example.loginBefore();

            example.betTime('16');
            example.betTeams('16');
        });

        /* 龙门标志跟提示 */
        if (example.isLongmen) {
            ja.longmen({
                top: '50%',
                left: '0',
            })

            alert('此活动为龙门测试版本，奖品均发放到龙门服')
        };

        // 登录按钮
        $('#login').on('click', function () {
            example.commHandle()
        })

        // 二选一登录 畅游平台登录
        $('#plaLogin').on('click', function () {
            hideMask()
            ja.login({ w2c: true })
        })

        // 二选一登录 wegame登录
        $('#wgLogin').click(function () {
            hideMask()
            ja.login()
        })

        // 注销
        $('#logout').on('click', function () {
            ja.logout()
        })

        // 兑换
        $('.dhlist').on('click', '.dhbtn', function () {
            if (example.commHandle()) return;
            if ($(this).hasClass('gray')) return;


            example.exchange($(this).parents('li').data('code'));
        })

        // 兑换记录
        $('.recordbtn').on('click', function () {
            if (example.commHandle()) return;

            example.exchangeLog();
        })

        // 领取竞猜次数奖励
        $('.getbtn').on('click', function () {
            if (example.commHandle()) return;
            if ($(this).hasClass('ylq') || $(this).hasClass('gray')) return;

            example.receivePrize($(this).data('code'), $(this).data('flag'));
        })

        // 切换竞猜
        $('.jclist li').on('click', function () {
            var $this = $(this);
            example.betTime($(this).data('topic'), function (status) {
                if (status == '未开始') {
                    example.tips('该阶段竞猜' + status + '，<br>换一个场次参与吧~');
                } else {
                    example.betTeams($this.data('topic'));

                    $this.addClass('cur').siblings().removeClass('cur');
                    $('#contbg').removeClass().addClass('cont cont' + $this.index());
                    $('.distab').removeClass('dis').eq($this.index()).addClass('dis');
                }
            })
        });

        // 小组赛竞猜选中效果
        $('#16_box .saiqubox li').on('click', function () {
            if (example.commHandle()) return;

            var btn = $(this).parent('ul').next();

            if (btn.hasClass('committed') ||
                $(this).hasClass('win') ||
                $(this).siblings().hasClass('win')) return;

            if (btn.hasClass('gray')) {
                example.tips('本阶段竞猜已结束，<br>换一个场次参与吧~');
                return;
            }

            var selected = btn.data('selected') || [];
            if ($(this).hasClass('yixuan')) {
                $(this).removeClass('yixuan');

                var index = selected.indexOf($(this).data('id'));
                selected.splice(index, 1);
            } else {
                $(this).addClass('yixuan');
                selected.push($(this).data('id'));

                if (selected.length > 2) {
                    var index = selected.shift();
                    $(this).parent('ul').find('li[data-id="' + index + '"]').removeClass('yixuan')
                }
            }
            btn.data('selected', selected)
        })

        // 小组赛竞猜提交
        $('#16_box .saiqubox .tjbtn').on('click', function () {
            if (example.commHandle()) return;

            var $this = $(this)
            if (example.betTipsHandle($this)) return;

            if (!$this.data('selected') || $this.data('selected').length < 2) {
                return example.tips('请先选择两支要竞猜的队伍~')
            }

            example.guessSureTips('确认竞猜这两支队伍出线？')
            $('#guessSure .surebtn').off().one('click', function () {
                example.bet({
                    'topicId': $this.data('topic'),
                    'groupId': $this.data('group'),
                    'myBet': $this.data('selected').join('#'),
                    'groupType': 'A'
                }, function () {
                    $this.addClass('committed');
                });

            })
        })

        // 淘汰赛选中加下注效果
        $('.saiqubox li').on('click', function () {
            if (example.commHandle()) return;

            if ($(this).parents('#16_box').length) return;

            var $this = $(this)
            if ($this.hasClass('win') ||
                $this.siblings().hasClass('win') ||
                $this.hasClass('yixuan') ||
                $this.siblings().hasClass('yixuan')) return;

            if (example.betTipsHandle($this)) return;

            example.guessSureTips();
            $('#guessSure .surebtn').off().one('click', function () {
                example.bet({
                    'topicId': $this.data('topic'),
                    'groupId': $this.data('group'),
                    'myBet': $this.data('id'),
                    'groupType': 'A'
                }, function () {
                    $this.addClass('yixuan')
                });

            })
        });

        // 淘汰赛竞猜提交
        $('.jjjclist .jcbtn').on('click', function () {
            if (example.commHandle()) return;

            var $this = $(this)
            if (example.betTipsHandle($this)) return;

            if ($this.hasClass('gray') ||
                $this.hasClass('committed')) return;

            if (!$this.siblings('.selectbox').find('select').val()) {
                example.guessFailTips('请选择比分后再进行竞猜。')
                return;
            }

            if ($this.prev().find('li.cur').length != 1) {
                example.guessFailTips('请选择投币数量后再进行竞猜。')
                return;
            }

            example.bet({
                'topicId': $this.data('topic'),
                'groupId': $this.data('group'),
                'myBet': $this.siblings('.selectbox').find('select').val(),
                'groupType': 'B',
                'betScore': $this.prev().find('li.cur').text(),
            }, function () {
                $this.addClass('committed')
            });
        })

        // 决赛竞猜提交
        $('#championSubmit').on('click', function () {
            if (example.commHandle()) return;

            var $this = $(this)
            if (example.betTipsHandle($this)) return;

            if ($this.hasClass('gray') ||
                $this.hasClass('committed')) return;

            var betInfo = example.getChampionBetInfo();

            if (betInfo.length < 4) {
                example.guessFailTips('您未完成所有战队的竞猜，请先完成排位。')
                return;
            }
            // 检测下注的数据是否有重复项
            var duplicates = example.checkArray(betInfo);
            if (duplicates.length) {
                duplicates.forEach(function (item) {
                    $('#selectUl select').eq(item).prop('selectedIndex', 0); // 重置select的选择状态
                })
                example.guessFailTips('您选择的排位顺序有误，请重新选择。');
                return;
            }

            example.guessSureTips('确定竞猜此排位顺序？')
            $('#guessSure .surebtn').off().one('click', function () {
                example.bet({
                    'topicId': $this.data('topic'),
                    'groupId': $this.data('group'),
                    'myBet': betInfo.join('#'),
                    'groupType': 'A',
                }, function () {
                    $this.addClass('committed')
                });
            })
        })
    },
    /** 登录后 */
    loginAfter: function () {
        $('.login_box').hide()
        $('.user_name').text(ja.userInfo.nickname || ja.userInfo.openid) // wgchangyou二选一
        $('.logout_box').show()

        example.userInfo();
    },
    /** 登录前 */
    loginBefore: function () {
        if (ja.isWechat) {
            // 点击过授权的就直接拉起登录弹窗
            if (sessionStorage.getItem(example.initParam.activity + example.initParam.version + 'wLogin')) {
                ja.isWLogin && popup('#pop_login')
                sessionStorage.removeItem(example.initParam.activity + example.initParam.version + 'wLogin')
            }
            if (ja.wxBindName) {
                $('#account').text(ja.wxBindName)
            }
        }
    },
    /**
    * 通用错误码处理
    * @param {*} data 返回的对象
    */
    commonErrPop: function (data) {
        var flag = true;
        switch (parseInt(data.code)) {
            case 1202: case 1209: case 1207: case 1007: //1202请求参数缺失或者为空  1209操作频繁
                example.tips('系统繁忙，请稍后重试！');
                break;
            case 1019: case 1012:
                ja.isLogin = false; // 重置下ja的登录状态标识以触发登录操作
                $('#login').click();
                break;
            case 1102:
                ja.code = 1102;
                example.tips(example.errorMsg[1102]);
                break;
            case 1106:
                ja.code = 1106;
                example.tips(example.errorMsg[1106]);
                break;
            case 1103:
                ja.code = 1103;
                example.tips(example.errorMsg[1103]);
                break;
            case 3103:
                example.tips('账号已封停！');
                break;
            case 2131:
                example.tips('由于账号中的角色<30级，无法参与活动，请先升级再参与活动');
                example.levelLow = true;
                break;
            case 2004:
                example.tips('超出每个IP下账号限制！');
                example.IPLimit = true;
                break;
            case 0:
                example.tips('系统异常，请刷新后重试~');
                break;
            default:
                flag = false;
                break;
        }
        return flag;
    },
    /**
    * 通用的前置处理方法
    */
    commHandle: function () {
        var flag = true;
        if (ja.code == 1102) {
            example.tips(example.errorMsg[1102]);
        } else if (ja.code == 1106) {
            example.tips(example.errorMsg[1106]);
        } else if (ja.code == 1103) {
            example.tips(example.errorMsg[1103]);
        } else if (!ja.isLogin) {
            popup('#pop_login') // 弹出二选一登录弹窗
        } else if (example.levelLow) {
            example.tips('由于账号中的角色<30级，无法参与活动，请先升级再参与活动');
        } else {
            flag = false;
        }
        return flag;
    },
    betTipsHandle: function ($this) {
        var flag = true;

        if ($this.hasClass('gray')) {
            // example.tips('')
        } else if ($this.hasClass('committed')) {
            // example.tips('')
        } else if ($this.data('status') != 1) {
            example.tips('本阶段竞猜' + $this.data('status') + '，<br>换一个场次参与吧~');
        } else {
            flag = false;
        }

        return flag;
    },
    /**
    * 通用提示
    */
    tips: function (msg) {
        $('#tips p').html(msg);
        popup('#tips')
    },
    /**
    * 单向绑定 方便数据驱动视图的更新
    * @description 监听属性修改 set修改 get读取
    * @param {String} attr 属性名
    * @param {object} set 设置数据时触发的操作
    * @param {object} get 获取数据时触发的操作
    * @example
    * new example.dataObserver('param', function (val) {
    *	$('element').text(val);
    * })
    */
    dataObserver: function (attr, set, get) {
        var _value = example[attr];
        Object.defineProperty(example, attr, {
            get: function () {
                get && get(_value);
                return _value;
            },
            set: function (val) {
                set && set(val);
                _value = val;
            }
        })
        example[attr] = _value;
    },
    /**
    * 检测数组重复项 并返回它的索引
    * @param {Array} array
    */
    checkArray: function (array) {
        var duplicates = [];
        var indexMap = new Map();

        // 遍历数组，记录每个元素第一次出现的索引
        array.forEach((item, index) => {
            if (indexMap.has(item)) {
                // 如果Map中已经有这个元素，说明它是重复的
                duplicates.push(index);
            } else {
                // 如果Map中没有这个元素，将其添加到Map中
                indexMap.set(item, index);
            }
        });

        return duplicates;
    },
    /**
     * 获取决赛竞猜选择信息
     * @returns
     */
    getChampionBetInfo: function (getAll) {
        return $('#selectUl select').map(function () {
            if (getAll) {
                return $(this).val() ? $(this).val() : $(this);
            } else if ($(this).val()) {
                return $(this).val();
            }
        }).get();
    },
    /**
     * 分页初始化
     * @param {*} eleBox 需要被分页的容器
     * @param {*} size  每页的条数
     * @example new InitPagination('.box', 3)
     */
    initPagination: function (eleBox, size) {
        var box = $(eleBox),
            children = box.children(),
            total = children.length,
            pageBox = $('.tpage'),
            pageNum = pageBox.find('.page-num'),
            maxNum = !Math.ceil(total / size) ? 1 : Math.ceil(total / size);

        pageNum.text('1/' + maxNum);
        children.hide();
        children.slice(0, size).show();

        pageBox.off().on('click', '.prev, .next', function (e) {
            var nowNum = parseInt(pageNum.text().split('/')[0]);

            if ($(this).hasClass('prev')) {
                nowNum--;
                if (nowNum < 1) {
                    nowNum = 1
                    return;
                }
            } else {
                nowNum++;
                if (nowNum > maxNum) {
                    nowNum = maxNum
                    return;
                }
            }

            children.hide();
            children.slice(size * (nowNum - 1), nowNum * size).show();
            pageNum.text(nowNum + '/' + maxNum);
        })
    },
    /**
    * 用户初始化
    */
    userInfo: function (a) {
        $.ajax({
            type: 'POST',
            url: example.base_url + 'init',
            async: false,
            success: function (res) {
                // 前置过滤器 处理通用错误码
                if (example.commonErrPop(res)) return;

                if (res.code == 10000) {
                    var data = res.data;

                    $('#points').text(data.currPoint);
                    $('#totalGuess').text(data.totalHitnum);
                    $('#rightGuess').text(data.hitnum);

                    $('.getlist .getbtn').addClass('gray');
                    // 进度渲染
                    var step1 = [5, 10, 15, 20];
                    for (var i = 0; i < step1.length; i++) {
                        var e = step1[i];
                        if (data.totalHitnum >= e) {
                            $('.getlist:eq(0) .getbtn:eq(' + i + ')').removeClass('gray');
                            $('.steps:eq(0) li:eq(' + i + ')').addClass('cur');
                            $('.jindu:eq(0)').removeClass().addClass('jindu jindu' + i);
                        }
                    }

                    var step1 = [6, 12, 18];
                    for (var i = 0; i < step1.length; i++) {
                        var e = step1[i];
                        if (data.hitnum >= e) {
                            $('.getlist:eq(1) .getbtn:eq(' + i + ')').removeClass('gray');
                            $('.steps:eq(1) li:eq(' + i + ')').addClass('cur');
                            $('.jindu:eq(1)').removeClass().addClass('jindu jindu' + i);
                        }
                    }

                    // 兑换信息
                    $.each(data.exchange, function (i, v) {
                        var box = $('.dhlist li[data-code="' + v.prizeCode + '"]');

                        !v.num && box.find('.dhbtn').addClass('gray');
                        box.find('.infp').html('消耗代币：' + v.point + (v.num < 99 ? ('<br>剩余次数：' + v.num) : ''))
                    })

                    // 领奖信息
                    data.receive && data.receive.split('#').forEach(function (v) {
                        if (v) {
                            $('[data-code="' + v + '"]').addClass('ylq')
                        }
                    })

                } else {
                    switch (parseInt(res.code)) {
                        default:
                            example.tips(res.message);
                            break;
                    }
                }
            }
        })
    },
    /**
     * 兑换
     * @param {*} goodsId 商品id
     */
    exchange: function (goodsId) {
        $.ajax({
            type: 'POST',
            url: example.base_url + 'exchange',
            async: false,
            data: {
                'goodsId': goodsId
            },
            success: function (res) {
                // 前置过滤器 处理通用错误码
                if (example.commonErrPop(res)) return;

                if (res.code == 10000) {
                    var data = res.data;

                    example.userInfo();
                    popup('#exchangeSuc');

                } else {
                    switch (parseInt(res.code)) {
                        case 1104:
                            example.exchangeFailTips('兑换阶段未开始')
                            break;
                        case 1105:
                            example.exchangeFailTips('兑换阶段已结束')
                            break;
                        case 1010:
                            example.exchangeFailTips('少侠竞猜币不足。')
                            break;
                        case 3309:
                            example.exchangeFailTips('兑换失败，请重试')
                            break;
                        case 2003: case 1001:
                            example.exchangeFailTips('少侠此物品可兑换次数不足')
                            break;
                        default:
                            example.tips(res.message);
                            break;
                    }
                }
            }
        })
    },
    /**
     * 兑换失败提示
     * @param {*} msg
     */
    exchangeFailTips: function (msg) {
        $('#exchangeFail p').text(msg);
        popup('#exchangeFail');
    },
    /**
     * 兑换记录
     */
    exchangeLog: function () {
        $.ajax({
            type: 'GET',
            url: example.base_url + 'exchangeLog',
            async: false,
            success: function (res) {
                // 前置过滤器 处理通用错误码
                if (example.commonErrPop(res)) return;

                if (res.code == 10000) {
                    var data = res.data;

                    if (data.length) {
                        var tr = '';

                        $.each(data, function (i, v) {
                            tr += `<tr>
                                    <td>${ja.formatDateTime(v.createTime)}</td>
                                    <td>${v.prizeCode}</td>
                                </tr>`;
                        })

                        $('#exchangeLog tbody').html(tr);
                        new example.initPagination('#exchangeLog tbody', 3);

                        popup('#exchangeLog');
                    } else {
                        example.tips('暂无兑换记录~')
                    }

                } else {
                    switch (parseInt(res.code)) {
                        default:
                            example.tips(res.message);
                            break;
                    }
                }
            }
        })
    },
    /**
     * 领取竞猜奖
     */
    receivePrize: function (prizeCode, flag) {
        $.ajax({
            type: 'POST',
            url: example.base_url + 'receivePrize',
            async: false,
            data: {
                'prizeCode': prizeCode,
                'flag': flag
            },
            success: function (res) {
                // 前置过滤器 处理通用错误码
                if (example.commonErrPop(res)) return;

                if (res.code == 10000) {
                    var data = res.data;

                    example.userInfo();
                    popup('#gotSuc');

                } else {
                    switch (parseInt(res.code)) {
                        case 1218:
                            example.tips('您还没有达成领取条件~')
                            break;
                        default:
                            example.tips(res.message);
                            break;
                    }
                }
            }
        })
    },
    /**
     * 竞猜队伍列表
     * @param {*} topicid
     */
    betTeams: function (topicid) {
        $.ajax({
            type: 'GET',
            url: '/tlgl/guess/betTeams',
            async: false,
            data: {
                'topicid': topicid,
            },
            success: function (res) {
                // 前置过滤器 处理通用错误码
                if (example.commonErrPop(res)) return;

                if (res.code == 10000) {
                    var data = res.data;

                    example.teamInfoRenderer[topicid](data, topicid);

                    if (ja.isLogin && !example.levelLow && !example.IPLimit) {
                        example.myBet({
                            topicId: topicid,
                            groupType: 'A'
                        });
                        // 小组赛跟冠军赛没有b组竞猜
                        topicid != '16' && topicid != 'WIN' && example.myBet({
                            topicId: topicid,
                            groupType: 'B'
                        });
                    }

                } else {
                    switch (parseInt(res.code)) {
                        default:
                            example.tips(res.message);
                            break;
                    }
                }
            }
        })
    },
    /**
     * 竞猜队伍渲染器
     */
    teamInfoRenderer: {
        '16': function (data, topicid) {
            var box = $('#16_box .saiqubox');
            $.each(data, function (i, v) {
                box.eq(i).find('.tjbtn').attr({
                    'data-group': v.groupId,
                    'data-topic': topicid,
                })
                box.eq(i).find('li').each(function (j, k) {
                    var info = v.teamList[j];
                    $(k).text(info.teamname).attr({
                        'data-id': info.teamid,
                        'title': info.teamname, // 鼠标移上去显示的文字
                    });
                    if (info.status == 1) {
                        $(k).addClass('win');
                        box.eq(i).find('.tjbtn').addClass('gray');
                    }
                })

            })
        },
        '4_A': function (data, topicid) {
            this.default(data, topicid)
        },
        '8_A': function (data, topicid) {
            this.default(data, topicid)
        },
        'WIN': function (data, topicid) {
            var info = data[0];
            var list = `<option value="" >请选择竞猜队伍</option>`;

            $.each(info.teamList, function (i, v) {
                example.WINresult.length <= 4 && example.WINresult.push(v.gameresult);
                list += `<option value="${v.teamid}">${v.teamname}</option>`;
            })

            $('#selectUl').find('select').html(list)
                .end()
                .next('a').attr({
                    'data-group': info.groupId,
                    'data-topic': topicid
                });

            info.teamList[0].status != 0 && $('#selectUl').find('select').prop('disabled', 'true').children(':selected').text('未选择队伍')
        },
        default: function (data, topicid) {
            var box1 = $('#' + topicid + '_box .saiqubox'),
                box2 = $('#' + topicid + '_box .jjjclist');

            $.each(data, function (i, v) {
                var teamInfo = v.teamList;
                box1.eq(i).find('li:eq(0)').attr({
                    'data-topic': topicid,
                    'data-group': v.groupId,
                    'data-id': teamInfo[0].teamid
                }).text(teamInfo[0].teamname)
                    .attr('title', teamInfo[0].teamname)
                    .addClass(teamInfo[0].status == 1 ? 'win' : '');

                box1.eq(i).find('li:eq(1)').attr({
                    'data-topic': topicid,
                    'data-group': v.groupId,
                    'data-id': teamInfo[1].teamid
                }).text(teamInfo[1].teamname)
                    .attr('title', teamInfo[1].teamname)
                    .addClass(teamInfo[1].status == 1 ? 'win' : '');

                box2.eq(i).find('.teamname li:eq(0)').text(teamInfo[0].teamname).attr('title', teamInfo[0].teamname);
                box2.eq(i).find('.teamname li:eq(1)').text(teamInfo[1].teamname).attr('title', teamInfo[1].teamname);
                box2.eq(i).find('.jcbtn').attr({
                    'data-topic': topicid,
                    'data-group': v.groupId,
                }).addClass(teamInfo[0].status != 0 ? 'gray' : '');

                box2.eq(i).find('.bifen em:eq(0)').text(teamInfo[0].gameresult && teamInfo[0].gameresult.split('#')[0]);
                box2.eq(i).find('.bifen em:eq(1)').text(teamInfo[0].gameresult && teamInfo[0].gameresult.split('#')[1]);
                teamInfo[0].status != 0 && box2.eq(i).find('select').prop('disabled', 'true').children(':selected').text('未选择比分')
            })
        },
    },
    /**
     * 下注
     * @param {*} obj
    | topicId    string  | 是   | 题目类型id |
    | groupId    string  | 是   | 队伍分组id |
    | myBet      string  | 是   | 下注内容   |
    | groupType  string  | 是   | 下注类别   |
    | betScore   Integer | 否   | 下注分数   |
     */
    bet: function (obj, cb) {
        $.ajax({
            type: 'POST',
            url: example.base_url + 'bet',
            async: false,
            data: obj,
            success: function (res) {
                // 前置过滤器 处理通用错误码
                if (example.commonErrPop(res)) return;

                if (res.code == 10000) {
                    var data = res.data;

                    cb && cb();
                    example.myBet({
                        topicId: obj.topicId,
                        groupType: obj.groupType
                    })
                    example.userInfo();

                    popup('#guessSuc');
                } else {
                    switch (parseInt(res.code)) {
                        case 3308:
                            example.myBet({
                                topicId: obj.topicId,
                                groupType: obj.groupType
                            })
                            example.guessFailTips('该组对抗已选择竞猜，请选择其他小组进行竞猜。')
                            break;
                        case 1010:
                            example.guessFailTips('少侠竞猜币不足。')
                            break;
                        default:
                            example.tips(res.message);
                            break;
                    }
                }
            }
        })
    },
    /**
     * 竞猜失败提示
     * @param {*} msg
     */
    guessFailTips: function (msg) {
        $('#guessFail p').text(msg);
        popup('#guessFail');
    },
    /**
     * 确认竞猜提示
     * @param {*} msg
     */
    guessSureTips: function (msg) {
        $('#guessSure p').text(msg || '确定竞猜该队伍出线？');
        popup('#guessSure');
    },
    /**
     *  下注信息
     * @param {*} obj {topicId groupType}
     */
    myBet: function (obj) {
        $.ajax({
            type: 'GET',
            url: example.base_url + 'myBet',
            async: false,
            data: obj,
            success: function (res) {
                // 前置过滤器 处理通用错误码
                if (example.commonErrPop(res)) return;

                if (res.code == 10000) {
                    var data = res.data;

                    example.betInfoRenderer[obj.topicId](data, obj);

                } else {
                    switch (parseInt(res.code)) {
                        default:
                            example.tips(res.message);
                            break;
                    }
                }
            }
        })
    },
    /**
     * 下注信息渲染
     */
    betInfoRenderer: {
        '16': function (data) {
            $.each(data, function (i, v) {
                var btn = $('#16_box [data-group="' + v.groupId + '"]');
                btn.addClass('committed');
                var id = v.myBet.split('#');
                btn.prev().find('li').removeClass('yixuan');
                btn.prev().find('[data-id="' + id[0] + '"]').addClass('yixuan');
                btn.prev().find('[data-id="' + id[1] + '"]').addClass('yixuan');
            })
        },
        '4_A': function (data, obj) {
            this['default' + obj.groupType](data, obj.topicId);
        },
        '8_A': function (data, obj) {
            this['default' + obj.groupType](data, obj.topicId);
        },
        'WIN': function (data, obj) {
            $.each(data, function (i, v) {
                v.myBet.split('#').forEach(function (j, k) {
                    $('#selectUl select').eq(k).val(j).prop('disabled', 'true');

                    if (example.WINresult[k]) {
                        $('#selectUl li').eq(k).addClass(example.WINresult[k] == j ? 'succeed' : 'fail')
                    }
                })
                $('#championSubmit').addClass('committed');
            })
        },
        defaultA: function (data, topicid) {
            var box = $('#' + topicid + '_box')

            $.each(data, function (i, v) {
                box.find('[data-id="' + v.myBet + '"]').addClass('yixuan');
            })
        },
        defaultB: function (data, topicid) {
            var box = $('#' + topicid + '_box')
            $.each(data, function (i, v) {
                var btn = box.find('.jcbtn[data-group="' + v.groupId + '"]');

                btn.addClass('committed');
                btn.siblings('.tbnum').find('li').filter(function () {
                    return $(this).text().trim() == v.betScore;
                }).addClass('cur');
                btn.siblings('.selectbox').find('select').val(v.myBet).prop('disabled', 'true').children(':selected').text('您的选择：' + v.myBet.replace('#', ':'));
            })
        }
    },
    /**
     * 下注时间
     * @param {*} topicId
     */
    betTime: function (topicId, cb) {
        $.ajax({
            type: 'GET',
            url: example.base_url + 'betTime',
            async: false,
            data: {
                'topicId': topicId
            },
            success: function (res) {
                // 前置过滤器 处理通用错误码
                if (example.commonErrPop(res)) return;

                if (res.code == 10000) {
                    var data = res.data;

                    var status = null;
                    if (data.now >= data.start && data.now < data.end) {
                        status = 1
                    } else if (data.now < data.start) {
                        status = '未开始'
                    } else if (data.now > data.end) {
                        status = '已结束'
                        $('#' + topicId + '_box .tjbtn').addClass('gray'); // 小组赛按钮
                        $('#' + topicId + '_box .jcbtn').addClass('gray'); // 淘汰赛按钮
                        $('#' + topicId + '_box .jsjcbtn').addClass('gray'); // 决赛按钮
                    }

                    $('#' + topicId + '_box li').attr('data-status', status);
                    $('#' + topicId + '_box .tjbtn').attr('data-status', status);
                    $('#' + topicId + '_box .jcbtn').attr('data-status', status);
                    $('#' + topicId + '_box .jsjcbtn').attr('data-status', status);

                    $('#' + topicId + '_box select').attr('data-status', status);

                    cb && cb(status);
                } else {
                    switch (parseInt(res.code)) {
                        default:
                            example.tips(res.message);
                            break;
                    }
                }
            }
        })
    },
}

example.init()
// })
