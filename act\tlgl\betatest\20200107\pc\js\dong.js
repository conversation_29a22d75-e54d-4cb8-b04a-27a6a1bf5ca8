var dong = {
    url: '//activity2.changyou.com/appoint/',
    data: {
          game: "ldjsy",//游戏
          actvKey: "ldjsy20181228",//活动key
          userId: "",//用户标志
          phone: "",//手机号
          numCode: "",//验证码
          deviceType: "",//手机型号
          cdkType: "",//礼包码是否相同
          col01: "",//其他参数1(最大32字符)
          col02: "pc",//其他参数2(最大32字符)
          col03: "",//其他参数3(最大32字符)
    },
    timer: null,
    timerNum: 60,
    init:function(){
          qudao = dong.GetQueryString('rcc_id');
          type = dong.GetQueryString('type');
          if(qudao && qudao.length>32){
                qudao = 'err';//非法参数
          }
          if(type && type.length>32){
                type = 'err';//非法参数
          }
          dong.data.col01 = qudao;
          dong.data.col03 = type;
          //加载预约人数
    },
    //发送验证码
    getSendCode: function () {
          dong.data.phone = $.trim($("#phone_text").val());
          if (dong.timer != null) return;
          if (!dong.isPhone()) return;
          dong.ajax("getVerificationCode.ncdo", dong.data, function (res) {
                if (res.SendCodeStatus == 0) {
                      dong.alert("短信发送成功");
                      $("#getCodeBtn").text("*" + dong.timerNum + "s后再试");
                      var timerNum = dong.timerNum;
                      dong.timer = setInterval(function () {
                            timerNum--;
                            $("#getCodeBtn").text("*" + timerNum + "s后再试");
                            if (timerNum <= 0) {
                                  $("#getCodeBtn").text("获取验证码");
                                  clearInterval(dong.timer);
                                  timerNum = dong.timerNum;
                                  dong.timer = null;
                            }
                      }, 1000);
                } else {
                      dong.alert("短信发送失败");
                }
          });
    },
    //发送礼包
    getSendPackage: function () {
          dong.data.phone = $.trim($("#phone_text").val());
          dong.data.numCode = $.trim($("#code_text").val());
          dong.data.deviceType = $("#deviceType_div .curr").attr("val");
          if (!dong.isPhone()) return;
          if (!dong.isCode()) return;
          if (dong.data.deviceType != "android" && dong.data.deviceType != "ios") {
                dong.alert("请选择手机型号");
                return;
          }
          dong.ajax("verificationCode.ncdo", dong.data, function (res) {
                dong.alert(res.errMsg);
          });
    },
    //获取总人数
    getPeopleTotal: function () {
          dong.ajax("actvInfo.ncdo", dong.data, function (res) {
                $("#appointNum_text").text(res.appointNum); // res.appointNum
                
                setTimeout(function(){
                      $(".order_num").show().arctext({radius: 60, dir: -1});
                },500);
                var achive = document.querySelectorAll(".fl_icon")
                if (achive.length == 0) {
                      return
                }
                console.log(achive)
                var numbers = parseInt(res.appointNum/50000)
                if (numbers >0 && numbers <4) {
                      for (let i = 0; i < numbers; i++) {
                            achive[i].style.display = "block"
                      }
                }else if(res.appointNum>=200000 && res.appointNum <500000){
                      for (let i = 0; i < 4; i++) {
                            achive[i].style.display = "block"
                      }
                }else if(res.appointNum>=500000 && res.appointNum <1000000){
                      for (let i = 0; i < 5; i++) {
                            achive[i].style.display = "block"
                      } 
                }else if(res.appointNum>=1000000 && res.appointNum <2000000){
                      for (let i = 0; i < 6; i++) {
                            achive[i].style.display = "block"
                      } 
                }else if(res.appointNum>=2000000){
                      for (let i = 0; i < 7; i++) {
                            achive[i].style.display = "block"
                      } 
                }
          });

    },
    alert: function (msg) {
          alert(msg);
    },
    isPhone: function () {
          if (!(/^1[3456789]\d{9}$/.test(dong.data.phone))) {
                dong.alert("手机号格式错误，请检查");
                return false;
          }
          return true;
    },
    isCode: function () {
          if (!(/^[0-9]{6}$/.test(dong.data.numCode))) {
                dong.alert("验证码格式错误，请检查");
                return false;
          }
          return true;
    },
    ajax: function (url, data, fun) {
          $.ajax({
                type: "get",
                url: dong.url + url,
                dataType: "jsonp",
                jsonp: "callback",
                data: data,
                success: function (res) {
                      console.log(res);
                      if (res.switchStatus == 1) {
                            if (res.errCode == "000") {
                                  fun(res);
                            } else if (res.errCode == "016") {
                                  dong.alert("您已预约");
                            } else {
                                  dong.alert(res.errMsg);
                            }
                      } else if (res.switchStatus == 2) {
                            dong.alert("活动已结束");
                      } else if (res.switchStatus == 3) {
                            dong.alert("活动已关闭");
                      } else {
                            dong.alert("服务器异常");
                      }
                },
                error: function (e) {
                      dong.alert("系统繁忙，请稍后再试");
                }
          });
    },
    GetQueryString: function (name) {
          var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
          var r = window.location.search.substr(1).match(reg);
          if (r != null) return unescape(r[2]); return null;
    }
}
dong.init();