<!DOCTYPE HTML>
<html>
	<head>
		<meta charset="utf-8">
		<title>江湖魅力秀-《新天龙八部》官方网站</title>
		<meta name="applicable-device" content="mobile">
		<meta name="viewport"
			content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=0,maximum-scale=1,viewport-fit=cover" />
		<meta name="format-detection" content="telephone=no" />
		<meta content="yes" name="mobile-web-app-capable">
		<meta content="yes" name="apple-mobile-web-app-capable" />
		<meta name="robots" content="all" />
		<meta name="keywords"
			content="新天龙八部,天龙八部,武侠浪漫美学,网络游戏,浪漫武侠,武侠游戏,金庸,萧峰,段誉,虚竹,王语嫣,xtl,xtlbb,tlbb,天龙八部钟汉良版,天龙八部黄日华版" />
		<meta name="description" content="江湖魅力秀-《新天龙八部》官方网站" />
		<meta name="author" content="Design:CP; Web Layout:CP;" />
		<link type="image/x-icon" href="/xtl/match/20230914/pc/favicon.ico" rel="icon" />
		<link type="image/x-icon" href="/xtl/match/20230914/pc/favicon.ico" rel="shortcut icon" />
		<link type="text/css" rel="stylesheet" href="/act/all/css/reset.css" />
		<link type="text/css" rel="stylesheet" href="/act/all/cdn/Swiper/4.5.0/css/swiper.min.css" />
		<link type="text/css" rel="stylesheet" href="/act/xtl/match/20230914/pc/css/cropper.css" />
		<link type="text/css" rel="stylesheet" href="/act/xtl/match/20230914/m/css/index.css?2022102020" />
		<link type="text/css" rel="stylesheet" href="/act/xtl/match/20230914/m/css/pop.css?2022102020" />
		<script>
			var phoneWidth = parseInt(window.screen.width);
			var phoneScale = phoneWidth / 750;
			var ua = navigator.userAgent;
			if (/Android (\d+\.\d+)/.test(ua)) {
				var version = parseFloat(RegExp.$1);
				// andriod 2.3
				if (version > 2.3) {
					document.write('<meta name="viewport" content="width=750, minimum-scale = ' + phoneScale +
						', maximum-scale = ' +
						phoneScale + ', target-densitydpi=device-dpi">');
					// andriod 2.3
				} else {
					document.write('<meta name="viewport" content="width=750, target-densitydpi=device-dpi">');
				}
			} else {
				document.write(
					'<meta name="viewport" content="width=750, user-scalable=no, target-densitydpi=device-dpi">');
			}
		</script>
		<script type="text/javascript">
			var deviceWidth = document.documentElement.clientWidth;
			if (deviceWidth > 750) {
				deviceWidth = 750
			};
			document.documentElement.style.fontSize = deviceWidth / 750 + 'px';
		</script>
		<script src="https://www.changyou.com/cyouFile/loger/huodong.js"></script>
	</head>

	<body>
		<div class="wrap sharebg">
			<div class="stopbox">
				<a href="index.shtml" class="fhbtn">返回首页</a>
				<div class="loginbox">
					<!--登录前-->
					<div class="login_box">
						你好，请先<a href="javascript:;" id="login" class="login_btn login" title="登录">【登录】</a>
					</div>
					<!--登录后-->
					<div class="logout_box" style="display:none;">
						<p>
							欢迎您，<span class="user_name"></span>
							<a href="javascript:;" title="注销" id="logout" class="logout_btn logout">【注销】</a>
						</p>
					</div>
				</div>
			</div>
			<div class="bminfbox pr">
				<div class="pkinfbox">
					<p class="rankpai">我的门派排行：<span>暂未上榜</span></p>
					<div class="examined">
						<div class="topbar">
							<div class="avtarbox">
								<img src="/act/xtl/match/20230914/pc/img/props1.png">
							</div>
							<div class="avtarinf">
								<p>服务器:<span>XXXXXX</span></p>
								<p>角色名:<span>XXXXXX</span></p>
							</div>
							<div class="hotbox">
								<span class="hot"></span>
								<p class="hotnum">热度<span>12345</span></p>
							</div>
						</div>
						<div class="sharebox">
							<p>呼朋唤友添把火：</p>
							<a href="javascript:;" class="btn wxbtn" title="微信分享"></a>
							<a href="javascript:;" class="btn qqbtn" title="QQ分享"></a>
						</div>
						<div class="shenheing">
							<p class="toptxt">文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案</p>
							<p class="bottxt">文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案文案</p>
							<a href="javascript:;" class="cklink" title="点击此处查看作品">点击此处查看作品</a>
						</div>
						<div class="signbox">
							<div class="btnbox">
								<p class="showtxt"><a href="javascript:;" class="btn btn-ztgd" title="点击查看"></a></p>
								<div class="roll">
									<ul class="first-say-list">
										<li><span>#标签文案案#</span></li>
										<li><span>#标签文案案#</span></li>
										<li><span>#标签文案案#</span></li>
										<li><span>#标签文案案#</span></li>
										<li><span>#标签文案案#</span></li>
										<li><span>#标签文案案#</span></li>
										<li><span>#标签文案案#</span></li>
										<li><span>#标签文案案#</span></li>
										<li><span>#标签文案案#</span></li>
										<li><span>#标签文案案#</span></li>
										<li><span>#标签文案案#</span></li>
										<li><span>#标签文案案#</span></li>
									</ul>
								</div>
								<a href="javascript:;" class="btn tiebtn" title="给他贴标签">给他贴标签</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!--#include virtual="/xtl/match/20230914/pc/inc/pop.html"-->
		<script src="/act/all/cdn/jquery/3.4.1/jquery.min.js"></script>
		<script src="/act/all/cdn/popout/2.0/popout.min.js"></script>
		<script src="/act/all/cdn/Swiper/4.5.0/js/swiper.min.js"></script>
		<script src="/act/all/cdn/clipboard.js/2.0.4/clipboard.min.js"></script>
		<script src="/act/all/cdn/join-activity/2.6/join-activity.min.js"></script>
		<script src="/xtl/match/20230914/pc/js/cropper.js"></script>
		<script src="/xtl/match/20230914/pc/js/menu.js"></script>
		<script src="/xtl/match/20230914/pc/js/index.js"></script>
		<script src="/xtl/match/20230914/pc/js/main.js"></script>
		<script>
			$(function() {
				$('body,html').animate({
					scrollTop: $('.stopbox').offset().top
				}, 100);
			});
		</script>
		<!--#include virtual="/all/nav/cy_public_js_dark.html"-->
		<!--#include virtual="/all/dma/dma_activity.html" -->
	</body>
</html>
