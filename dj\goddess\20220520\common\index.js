'use strict';

$(function() {
    var status = undefined,     // 创建状态
        $btnGetCode = $('.btn_get_code'),
        $btnCreate = $('.btn_create');

    // 初始化配置
    ja.config({
        app: 'djyx',
        activity: 'family',
        version: '20220126',
        platform: 'dj'
    });

    // 初始化执行
    ja.ready(function(glob) {
        if(glob.isLogin) {
            $('.login_after').show();
            $('.user_info').show();
            $('.user_name').html(ja.glob.userInfo.openid);
            getStatus();
        }else {
            $btnGetCode.show();
        }

        if(window.screen.width < 390) {
            $('#closeLogin').css({
                right: 40,
                zIndex: 999,
                textIndent: 0
            })
        }
    });

    /* =========== 方法 =========== */
    // 获取服务器列表
    function getServerList() {
        $.get('/family/getServerList', function(res) {
            if(res.code === 10000) {
                var list = res.data.serverList;
                for(var i = 0; i < list.length; i++) {
                    var item = list[i];
                    $('<option />').attr('value', item.serverId).html(item.serverName)
                        .appendTo($('.server_list'));
                }
            }
        })
    }

    // 家族码获取状态
    function getStatus() {
        $.ajax({
            url:  '/family/status/familyCode',
            async: false,
            success: function(res) {
                if(res.code === 10000) {
                    var info = res.data;
                    status = info.status;
                    if(status === 1) {
                        copyEvent();
                        $btnGetCode.hide();
                        $('.user_info').hide();
                        $('.info_list').show();
                        $('.your_family').html(info.familyName);
                        $('.your_server').html(info.serverName);
                        $('.your_phone').html(info.phone.substring(0, 3) + '****' + info.phone.substring(7, 11));
                        $('.your_family_code').html(info.familyCode);
                    }else {
                        $btnGetCode.show();
                        getServerList();
                    }
                }else {
                    ja.error('家族码获取状态查询异常');
                }
            }
        })
    }

    // 复制家族码
    function copyEvent() {
        var clipboard = new ClipboardJS('.btn_copy');
        clipboard.on('success', function (e) {
            ja.hideMask($('#pop1'));
            alert("已复制");
        });
        clipboard.on('error', function (e) {
            console.log(e);
        });
    }

    /* =========== 执行 =========== */
    // 领取家族码
    $btnGetCode.click(function() {
        if(ja.glob.isLogin) {
            ja.popup($('#pop2'));

            if (ja.glob.isIos) {
                var isReset;
                document.body.addEventListener('focusin', function() {
                    // 软键盘弹出的事件处理
                    isReset = false
                })
                document.body.addEventListener('focusout', function() {
                    // 软键盘收起的事件处理
                    isReset = true
                    setTimeout(function() {
                        // 当焦点在弹出层的输入框之间切换时先不归位
                        if (isReset) {
                            window.scroll(0, 0) // 失焦后强制让页面归位
                        }
                    }, 300)
                })
            }
        }else {
            ja.login();
        }
    });

    // 创建家族码
    $btnCreate.click(function() {
        var familyName = $('.input_family_name').val(),
            serverId = $('.server_list').val(),
            phone = $('.input_phone').val(),
            $createError = $('.create_error');
        if(!familyName) return $createError.html('请填写家族名');
        if(ja.phone(phone)) return $createError.html(ja.phone(phone));
        $createError.html('');
        $btnCreate.css('pointer-events', 'none');
        $.ajax({
            url: '/family/getFamilyCode',
            async: false,
            data: {
                familyName: familyName,
                serverId: serverId,
                phone: phone
            },
            success: function(res) {
                if(res.code === 10000 && res.data) {
                    ja.hideMask($('#pop2'));
                    ja.popup($('#pop1'));
                    $('.family_code').html(res.data.familyCode);
                    getStatus();
                }else {
                    $btnCreate.css('pointer-events', 'auto');
                    alert(res.message);
                }
            }
        })
    });

    // 退出
    $('.btn_logout').click(ja.logout)

});