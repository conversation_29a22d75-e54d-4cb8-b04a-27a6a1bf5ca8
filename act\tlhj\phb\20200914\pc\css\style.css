@charset "utf-8";
body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,form,fieldset,input,textarea,p,blockquote,th,td{margin:0;padding:0;}
table{border-collapse:collapse;border-spacing:0;}
address,caption,cite,code,dfn,em,strong,th,var{font-weight:normal;font-style:normal;}
ol,ul{list-style:none;}
caption,th{text-align:left;}
h1,h2,h3,h4,h5,h6{font-weight:normal;font-size:100%;}
q:before,q:after{content:'';}
fieldset,img,abbr,acronym{border:0;}
/*页面样式*/
.c:before,.c:after {content:"";display:table;}
.c:after{clear:both;}
.c{zoom:1;}
.none{display:none;}
.pr{position:relative;}
.pa{position:absolute;}
body{font:12px/1.5 '\5FAE\8F6F\96C5\9ED1';padding-top:55px;text-align:justify;min-width:1200px;}
a{text-decoration:none;overflow:visible;}
a:hover{text-decoration:none;}
.com,.btn,.lblist dd,.s_con a:hover,.fclict div,.s_con a.on{background:url(../img/com.png) no-repeat;display:block;overflow:hidden;text-indent:-9999em;text-align:center;}
.btn{transition:filter 0.5s linear;}
.btn:hover{-webkit-filter: brightness(1.15);}
.xtl_logo_lk{top:20px;left:4px;z-index:101; width: 185px; height: 123px; background-position: 0 0;}
.xtl_logo_lk a{display:block;z-index:999;}
.bar{width:1200px;margin:0 auto;position:relative;}
.wrap{background:url(../img/wbg.jpg) no-repeat center top;color:#fff;}
.head{height:763px;background:url(../img/bg-head.jpg) no-repeat center top;z-index:1;position:relative;}
.cont{height:1800px;background:url(../img/cont.jpg) no-repeat center top;z-index:2;position:relative;}



.pop{position:relative;width:513px;height:288px;background:url(../img/pop.png) no-repeat;color:#3f3e45;display:none;}
.dia_com,.pop2 h2:before,.pop2 h2:after,.tabhd li,.pop4 h2.lbh2:after{background:url(../img/dia_com.png) no-repeat;display:block;text-indent:-9999em;text-align:center;transition:filter .5s;}
.dia_com:hover{filter:brightness(1.15);}
.gbbtn{width:45px;height:45px;top:-10px;right:-9px;}
.yzbox{padding-top:39px;}
.yzbox div{padding-top:23px;height:32px;}
.yzbox div label,.yzbox div input,.yzbox div a{float:left;}
.yzbox div label{color:#3f3e45;font-size:18px;line-height:32px;padding-right:2px;width:162px;text-align:right;}
.yzbox div input{width:230px;height:30px;background:#fff;border:1px solid #de735d;padding:0 5px;}
.yzbox div input.yzminput{width:125px;}
.yzbox div a{margin-left:9px;width:99px;height:32px;background-position:0 -53px;text-indent:0;color:#5e2e0e;font-size:16px;line-height:32px;}
.yzbox div a.dis_btn{background-position:-115px -53px;color:#999;}
.tjbtn{width:222px;height:55px;margin:31px auto 0;background-position:0 -94px!important;}
.fri_str{color:#803b20;font-size:36px;display:block;font-weight:bold;text-align:center;line-height:48px;padding-top:68px;}
.fri_str1{padding-top:80px;}
.fri_str span,.sus_tip span{color:#e15439;}


.pop2{width:553px;height:248px;background:url(../img/pop2.png) no-repeat;}
.pop2 h2,.pop3 h2{color:#9796a0;font-size:36px;font-weight:bold;text-align:center;padding-top:45px;}
.pop2 h2:before,.pop2 h2:after{content:'';display:inline-block;vertical-align:top;width:79px;height:29px;}
.pop2 h2:before{background-position:0 -172px;margin:16px 16px 0 0;}
.pop2 h2:after{background-position:-150px -170px;margin:4px 0 0 16px;}
.fail_tip{text-align:center;color:#3f3e45;font-size:20px;padding-top:21px;line-height:32px;}
.pop3{width:553px;height:298px;background:url(../img/pop3.png) no-repeat;}
.pop3 h2{padding-top:37px;color:#e35d52;}
.pop3 h2 span{background-image:-webkit-linear-gradient(top,#d2211b,#ef8255);-webkit-background-clip:text;-webkit-text-fill-color:transparent;}
.sus_tip{color:#3f3e45;font-size:20px;text-align:center;line-height:28px;padding-top:26px;}
.sus_tip1{padding-top:34px;}
/* 个人中心、登录弹框的样式代码已经放到共用部分 */
.pop1{width:553px;height:248px;background:url(../img/pop1.png) no-repeat;}

.active .slotxt{-webkit-animation:sloganAni .4s 0s linear both;}
.active .qqqun,.active .daqu,.active .yybtn,.active .login{-webkit-animation:fadeInUp 1s .2s ease both;}
.active .qqqun{-webkit-animation-delay:.4s;}
.active .daqu{-webkit-animation-delay:.6s;}
.active .yybtn{-webkit-animation-delay:.8s;}
.active .login{-webkit-animation-delay:1s;}
@-webkit-keyframes fadeInUp{
0%{opacity:0;
-webkit-transform:translateY(20px)}
100%{opacity:1;
-webkit-transform:translateY(0)}
}
@-webkit-keyframes sloganAni{
    0%{-webkit-transform:scale(1.8);opacity:0;}
    60%{-webkit-transform:scale(1);opacity:1;}
    65%{-webkit-transform:translate(-4px,-4px);}
    70%{-webkit-transform:translate(0,0);}
    75%{-webkit-transform:translate(4px,4px);}
    80%{-webkit-transform:translate(0,0);}
    85%{-webkit-transform:translate(-4px,4px);}
    90%{-webkit-transform:translate(0,0);}
    95%{-webkit-transform:translate(4px,-4px);}
    100%{-webkit-transform:translate(0,0);opacity:1;}
}
/* 新添加 */
.disabled{pointer-events: none;}
