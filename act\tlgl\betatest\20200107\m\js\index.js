$(function () {
    var cp_index,//门派全局变量
        isClick = true,
        lotteryCnt = '0',
        inviteCode = '', //邀请码
        getLuckDrawStatus = true,//抽奖状态
        xxcy = false,
        pType = '',
        isAddLuckdraw = 0, // 是否获取一次抽奖机会 0：未获取 1：已获取
        zid = '',
        statusArr = [],//点击关注数组
        stopAccount = false,
        yyData = { //预约参数配置
            game: "tlgl",
            actvKey: "tlgl20200114",
            userId: "",//用户标志
            phone: "",//手机号
            numCode: "",//验证码
            deviceType: "",//手机型号
            cdkType: "",//礼包码是否相同
            col01: "",//其他参数1(最大32字符)
            col02: "betatest-m",//其他参数2(最大32字符)
            col03: "",//其他参数3(最大32字符)
        },
        nickName = '',
        headImgUrl = '',
        countdown = 60;//倒计时
    ja.config({
        app: 'tlgl', // 必填，应用标识
        activity: 'backtogether', // 必填，活动标识
        version: '********', // 必填，活动版本标识
        platform: 'changyou', // 必填，登陆平台标识
        type: 1,
        isInitAlert: false,
        isWxLogin: true,
        ready: function (glob) { // 非必填，初始化完成回调
            if (glob.code == '1106') {
                popTip('活动暂未开始，请稍后再试！');
            } else if (glob.code == '1102') {
                popTip('活动无效');
            } else if (glob.code == '1103') {
                popTip('活动已结束');
            } else if (glob.code != '0' && glob.code != '1' && glob.code != '2') {
                popTip('网络繁忙，请稍后再试' + glob.code);
            }
            glob.code === 1 ? loginAfter() : loginBefore();
            // 获取地址栏中的预约参数
            var channel = ja.utils.getUrlParams().channel,
                channelType = ja.utils.getUrlParams().type;
            if (channel && channel.length > 32) {
                channel = 'err';//非法参数
            }
            if (channelType && channelType.length > 32) {
                channelType = 'err';//非法参数
            }
            yyData.col01 = channel;
            yyData.col03 = channelType;

            if (ja.glob.isWechat) {
                nickName = ja.glob.wechatInfo.nickname;
                headImgUrl = ja.glob.wechatInfo.headimgurl;
                wxShare('iCode', 'nickName', 'headImgUrl', 'dataOrder', 'index');
            }

        }
    });

    /**
     * ************* 函数封装 **************
     * */

    // 登录前
    function loginBefore() {
        $('#LoginBefor').show();
        $('#LoginAfter').hide();
    }

    // 登录后
    function loginAfter() {
        $('#LoginBefor').hide();
        $('#LoginAfter').show();
        $('#UserName').text(ja.glob.userInfo.openid);

        invitecodeGet();
        querylottimesGet();
    }
    /**
     * 获取邀请码
     * */
    function invitecodeGet() {
        ja.get(commPath + 'getinvitecode', function (res) {
            if (res.code === 10000 && res.data) {
                if (res.data.invite_code == null) {
                    $('.yqbtn').attr('data-code', 'inviteCode');
                    return;
                }
                inviteCode = res.data.invite_code;
                $('.yqbtn').attr('data-code', inviteCode);
                queryhelpersGet(inviteCode);
            } else if (res.code == 3103) {
                stopAccount = true;
            } else {
                $('.yqbtn').attr('data-code', 'inviteCode');
            }
        })
    }

    /**
     * 增加一次抽奖
     * */
    function addlottimesGet(order) {
        ja.ajax({
            url: commPath + 'addlottimes',
            type: 'get',
            async: false,
            timeout: 4000,
            success: function (res) {
                if (res.code === 10000) {
                    // 用户剩余抽奖次数
                    // querylottimesGet();
                    ja.ajax({
                        url: commPath + 'querylottimes',
                        type: 'get',
                        async: false,
                        timeout: 4000,
                        success: function (objs) {
                            if (objs.code === 10000 && objs.data) {
                                // 0：未获取 1：已获取
                                isAddLuckdraw = objs.data.added;
                                $('.lotteryCnt').html(objs.data.lotteryCnt);
                                if (order == 2) {
                                    window.open("//jq.qq.com/?_wv=1027&k=5lVEVKi");
                                } else if (order == 3) {
                                    window.open("//weibo.com/u/7382350616");
                                }
                            }
                        }
                    })

                } else if (res.code == 5075) {
                    if (order == 2) {
                        window.open("//jq.qq.com/?_wv=1027&k=5lVEVKi");
                    } else if (order == 3) {
                        window.open("//weibo.com/u/7382350616");
                    }
                } else if (res.code == 1012 || res.code == 1019) {
                    window.location.reload();
                }
            }
        })
    }

    /**
     * 用户剩余抽奖次数
     * */
    function querylottimesGet() {
        ja.get(commPath + 'querylottimes', function (res) {
            if (res.code === 10000 && res.data) {
                lotteryCnt = res.data.lotteryCnt;
                // 0：未获取 1：已获取
                isAddLuckdraw = res.data.added;
                $('.lotteryCnt').html(lotteryCnt)
            }
        })
    }

    /**
     * 中奖纪录
     * */

    function querywinsPost() {
        isClick = false;
        var o = {
            pageNum: '1',
            pageSize: '20'
        }
        ja.post(commPath + 'querywins', o, function (res) {
            if (res.code === 10000 && res.data) {
                if (res.data.list.length != 0) {
                    var list = res.data.list;
                    var tr = '';
                    for (var i = 0; i < list.length; i++) {
                        if (list[i].prizeType == 'R') {
                            $('#FillAdress').show().attr('data-priceid', list[i].id);
                        }
                        tr += '<tr>' +
                            '<td width="239">' + addDate(list[i].insertTime) + '</td>' +
                            '<td>' + list[i].prizeName + '</td>' +
                            '</tr>'
                    }
                    $('#PriceList').html(tr);

                }
            } else if (res.code == 1012 || res.code == 1019) {
                window.location.reload();
            } else {
                popTip(res.message);
            }
            isClick = true;
        })
    }


    /**
     * 时间格式化
     * */
    function addDate(time) {
        var date = new Date(time),
            year = date.getFullYear(),
            month = date.getMonth() + 1,
            day = date.getDate();
        return year + '-' + month + '-' + day;
    }

    /**
     * 收货地址
     * */
    function setaddressPost(id, realName, phone, address) {
        var obj = {
            logId: id,
            realName: realName,
            phone: phone,
            address: address
        }
        ja.post(commPath + 'setaddress', obj, function (res) {
            if (res.code === 10000 && res.data) {
                popTip('填写成功，请耐心等待奖励发放！');
            } else if (res.code == 1012 || res.code == 1019) {
                window.location.reload();
            } else {
                popTip(res.message)
            }
            isClick = true;
        })
    }


    // 统一点击
    function $click(fun) {
        // if (ja.glob.isWechat) {
        if (stopAccount) {
            popTip('账号已被停权，暂不能参与活动')
        } else {
            switch (Number(ja.glob.code)) {
                case 0:
                    popTip('网络繁忙，请稍后再试');
                    break;
                case 1:
                    if (fun && ja.utils.objType(fun) === 'function') fun();
                    break;
                case 2:
                    ja.login();
                    break;
                case 1106:
                    popTip('活动暂未开始，请稍后再试！');
                    break;
                case 1102:
                    popTip('活动无效');
                    break;
                case 1103:
                    popTip('活动已结束');
                    break;
            }
        }
        // } 
        // else {
        //     popTip('请使用微信参与本次活动');
        // }
    }

    /**
     * 获取验证码
     * */
    var xh = null;
    function settime(obj) {
        if (countdown == 0) {
            clearInterval(xh)
            obj.attr('disabled', false);
            //obj.removeattr("disabled"); 
            obj.val("获取验证码");
            countdown = 60;
            return;
        } else {
            obj.attr('disabled', true);
            obj.val("" + countdown + "s后重试");
            countdown--;
        }
        xh = setTimeout(function () {
            settime(obj)
        }, 1000)
    }

    /**
     * 好友助力情况
     * */
    function queryhelpersGet(inviteCode) {
        ja.get(commPath + 'queryhelpers?pageNum=1&pageSize=20&invite_code=' + inviteCode + '', function (res) {
            if (res.code === 10000 && res.data) {
                $('#AllHelps').html(res.data.total)
            }
        })
    }

    /**
     * 清空填写地址的值
     * */
    function emptyVal() {
        $('#usename,#mobile,#address_input').val('');
    }

    // 删除首尾空格
    function strim(obj) {
        return obj.val().trim().replace(/\s/g, "");
    }
    // 检测手机格式
    function checkPhone(str) {
        var myreg = /^(0|86|17951)?(13[0-9]|15[012356789]|17[678]|18[0-9]|14[57]|19[0-9]|16[0-9])[0-9]{8}$/;
        if (!myreg.test(str)) {
            return false;
        } else {
            return true;
        }
    }
    // 公共提示
    function popTip(msg) {
        popShow('pop_com');
        $('#com_tip').html(msg);

    }
    /**
       * ************* 事件绑定 **************
       * */

    $('.yy-pop-close').click(function () {
        countdown = 0;
        popHide();
    })
    // 登录
    $('#Login').click(function () {
        window.localStorage.clear();
        $click();
    });

    // 登出
    $('#Logout').click(ja.logout);

    // 预约
    $('.yuyuebtn').click(function () {
        $('#UserFillPhone').val('');
        $('#MessageCode').val('');
        popShow('yy-pop-1');
    })
    // 邀请好友 
    $('.yqbtn').click(function () {
        var _this = $(this);
        $click(function () {
            if (ja.glob.isWechat) {
                var dataOrder = $('.tgbox').attr('data-order');
                if (dataOrder == 'false' || dataOrder == undefined) {
                    popTip('请先选择门派后再号召好友同归！')
                    return;
                }
                var iCode = _this.attr('data-code');
                $('.fxovlay').fadeIn();
                wxShare(iCode, nickName, headImgUrl, dataOrder, 'beshared', function () {
                    wxShare('iCode', 'nickName', 'headImgUrl', 'dataOrder', 'index');
                    $('.fxovlay').fadeOut();
                });
            } else {
                popTip('请使用微信参与本次活动');
            }



        })
    })

    $('.fxovlay').click(function () {
        $('.fxovlay').fadeOut();
    })
    // 增加一次抽奖
    $('.gzlist').on('click', 'li a', function () {
        var _this = $(this);
        $click(function () {
            var order = _this.attr('data-order');
            // 没有领过奖励
            if (isAddLuckdraw == 0) {
                if (statusArr.length == 0) {
                    statusArr.push(order);
                    if (order == 2) {
                        window.open("//jq.qq.com/?_wv=1027&k=5lVEVKi");
                    } else if (order == 3) {
                        window.open("//weibo.com/u/7382350616");
                    }
                } else {
                    if (statusArr.indexOf(order) == -1) {
                        statusArr.push(order);
                    }
                    // 任击了两个不同按钮
                    if (statusArr.length >= 2) {
                        addlottimesGet(order);
                    }
                }
            }else{
                if (order == 2) {
                    window.open("//jq.qq.com/?_wv=1027&k=5lVEVKi");
                } else if (order == 3) {
                    window.open("//weibo.com/u/7382350616");
                }
            }

        })
    })

    // 发送验证码
    $("#GetCode").click(function () {
        var UserFillPhone = strim($('#UserFillPhone'));
        if (!checkPhone(UserFillPhone)) {
            alert('手机号输入有误');
            return;
        }
        yyData.phone = UserFillPhone;
        $.ajax({
            type: "get",
            url: '//activity2.changyou.com/appoint/getVerificationCode.ncdo',
            dataType: "jsonp",
            jsonp: "callback",
            data: yyData,
            success: function (res) {
                if (res.SendCodeStatus == 0) {
                    alert('验证码已发送至您的手机')
                    settime($('#GetCode'));
                } else if (res.errCode == "016") {
                    alert("您已经预约过了");
                } else {
                    alert("短信发送失败，请您稍后再试");
                }

            },
            error: function (e) {
                alert("系统繁忙，请稍后再试");
            }
        });

    })

    // 提交预约
    $('#SureBtn').click(function () {
        var UserFillPhone = strim($('#UserFillPhone')),
            MessageCode = strim($('#MessageCode'));
        if (!checkPhone(UserFillPhone)) {
            alert('手机号输入有误');
            return;
        }
        if (MessageCode == '') {
            alert('请输入正确的验证码');
            return;
        }
        yyData.phone = UserFillPhone;
        yyData.numCode = MessageCode;
        $.ajax({
            type: "get",
            url: '//activity2.changyou.com/appoint/verificationCode.ncdo',
            dataType: "jsonp",
            jsonp: "callback",
            data: yyData,
            success: function (res) {
                console.log(res)
                if (res.switchStatus == 1) {
                    if (res.errCode == "000") {
                        // alert('预约成功')
                        popShow('yy-pop-2');
                    } else if (res.errCode == "016") {
                        alert("您已经预约过了");
                    } else if (res.errCode == "020") {
                        alert("请输入正确的验证码");
                    } else {
                        alert(res.errMsg);
                    }
                } else if (res.switchStatus == 2) {
                    alert("活动已结束");
                } else if (res.switchStatus == 3) {
                    alert("活动已关闭");
                } else {
                    alert("服务器异常");
                }
            },
            error: function (e) {
                alert("系统繁忙，请稍后再试");
            }
        });
    })


    // 抽奖
    $('#LuckDraw').bind('click', function () { //点击需要登录
        $click(function () {
            if (isClick && getLuckDrawStatus) {
                isClick = getLuckDrawStatus = false;
                // 防止有多次抽奖机会 置空上次抽奖数据
                xxcy = false;
                pType = '';
                ja.get(commPath + 'lot', function (res) {
                    if (res.code === 10000 && res.data) {
                        var prizeType = res.data.prizeType;
                        if (prizeType == "R") { //实物奖
                            pType = 'show';
                            $('#FillAdress').attr('data-priceid', res.data.id);
                            $('#FillAdress,.alertFillAdress').show();
                        } else if (prizeType == "C") { //虚拟奖
                            $('.alertFillAdress').hide();
                            pType = 'XN';
                        } else if (prizeType == "N") {
                            xxcy = true;
                            pType = 'show';
                        }
                        lottObj.startRoll(Number(res.data.prizeCode) - 1);
                        zid = res.data.zid;
                        $('#PriceName').html(res.data.prizeName)
                    } else if (res.code == 2002 || res.code == 2003) { //谢谢参与
                        xxcy = true;
                        lottObj.startRoll(3);
                    } else if (res.code == 2007) {
                        popTip('您的抽奖机会不足，请先去获取抽奖机会吧！');
                        getLuckDrawStatus = true;
                    } else if (res.code == 1012 || res.code == 1019) {
                        window.location.reload();
                    } else if (res.code == 1106) {
                        popTip('活动暂未开始，请稍后再试！');
                    } else if (res.code == 1103) {
                        popTip('幸运抽奖活动已结束');
                    } else {
                        popTip(res.message);
                        getLuckDrawStatus = true;
                    }
                    isClick = true;
                })
            }
        })

    })
    //抽奖
    var LottOBJ = function (o) {
        getLuckDrawStatus = true;
        var conf = {
            obj: '',
            cover: '',
            count: 0,
            position: '',
            complete: function () { }
        }
        conf = $.extend(conf, o);
        conf.cover.hide();
        var pos = [], timer = null, spd = 100, curr = 0, round = 0;
        for (var i = 0; i < conf.position.split(',').length; i++) {
            var s = conf.position.split(',')[i];
            pos.push({ left: parseInt(s.split('_')[0]), top: parseInt(s.split('_')[1]) });
        }
        if (pos.length != conf.count) return;
        this.startRoll = function (n) {
            if (typeof (n) != 'number' && n > pos.length) n = 0;
            spd = 100;
            round = Math.ceil(Math.random() * 4) + 1;
            conf.cover.show().css({ left: pos[curr].left, top: pos[curr].top });
            timer = setTimeout(function () { roll(n); }, spd);

        };
        function roll(e) {
            clearTimeout(timer);
            if (curr == pos.length - 1) {
                if (round > 0) {
                    round--;
                    curr = 0;
                }
            } else
                curr++;
            conf.cover.css({ left: pos[curr].left, top: pos[curr].top });
            if (curr == e && round == 0) {
                setTimeout(conf.complete, 500);
                return;
            }
            if (round == 0) spd += 80;
            timer = setTimeout(function () { roll(e); }, spd);
            getLuckDrawStatus = false;
        }
    };
    var lottObj = new LottOBJ({
        obj: $('#lottcon'),
        cover: $('#lottcon .lott-cover'),
        count: 8,
        position: '0_8,232_8,464_8,464_198,464_388,232_388,0_388,0_198',
        complete: function () {
            //抽奖完成
            //测试资格
            //popShow('pop_com');
            //$('#com_tip').html('恭喜您获得《天龙八部·归来》不删档测试资格！');
            //实物奖励

            if (xxcy) {
                popTip('很遗憾，您本次未抽中任何奖品！');
            } else {
                if (pType == "XN") {
                    popShow('pop_xnPrice');
                } else if (pType == "show") {
                    popShow('pop_lot');
                }
            }

            querylottimesGet();
            isClick = true;
            getLuckDrawStatus = true;
        }
    });

    // 点击查询好友助力情况
    var nowPageNum = 1;
    $('#PopNum').bind('click', function () {
        $click(function () {
            nowPageNum = 1;
            $('.tabnum').empty();
            if (isClick == false) return;
            friendHelpRecordGet(1);
            popShow('pop_num');
        })
    })
    // 好友应援记录
    function friendHelpRecordGet(pageNum) {
        if (isClick) {
            isClick = false;
            ja.get(commPath + 'queryhelpers?invite_code=' + inviteCode + '&pageNum=' + pageNum + '&pageSize=20', function (res) {
                if (res.code == 10000 && res.data && res.data.list) {
                    var data = res.data, arr = data.list;
                    allPages = data.pages;
                    $('.tlNumber').html(data.total);
                    $('#AllHelps').html(data.total);
                    if (arr && arr.length == 0) {
                        $('.recordbox1').hide();
                    } else {
                        $('.recordbox1').show();
                        var s1 = '', imgs = '';
                        for (var i = 0; i < arr.length; i++) {
                            imgs = '/act/xtl/competition/20191115/m/img/wxDefaultImg.png';
                            if (arr[i].helperImgUrl && arr[i].helperImgUrl != '' && arr[i].helperImgUrl != 'undefined') {
                                imgs = arr[i].helperImgUrl;
                            }
                            var createTime = '';
                            if (arr[i].createTime) {
                                createTime = arr[i].createTime.split(' ')[0];
                            }
                            s1 += '<tr>' +
                                '<td class="tdle"><img src=' + imgs + ' width="38" height="38" alt="头像"></td>' +
                                '<td class="texl">' + decodeURI(arr[i].helperName.replace(/\+/g, '%20')) + '</td>' +
                                '<td width="160">' + createTime + '</td>' +
                                '</tr>';
                        }
                        $('.tabnum').append(s1);
                    }
                } else if (res.code == 1012 || res.code == 1019) {
                    window.location.reload();
                } else {
                    popTip(res.message);
                }
                isClick = true;
            })
        }

    }
    // 分页 滚动加载
    $(".tcrule1_1").bind("scroll", function (e) {
        var sum = this.scrollHeight;
        if (sum <= $(this).scrollTop() + $(this).height()) {
            // 滑动到底部加载
            if (nowPageNum >= allPages) return;
            nowPageNum++;
            friendHelpRecordGet(nowPageNum);
        }
    });

    // 提交收货地址
    $('#tjbtn').on('click', function () {
        if ($(this).attr('data-status') == 'false') return;
        if (isClick) {
            check();
            if ($('.tj_tip p').css('display') == 'block') return;
            var realName = $('#usename').val(),
                phone = $('#mobile').val(),
                address = $('#address_input').val();
            isClick = false;
            var id = $('#FillAdress').attr('data-priceid')
            setaddressPost(id, realName, phone, address);
        }
    });

    // 中奖填写收货地址
    $('.alertFillAdress').click(function () {
        emptyVal();
        popShow('pop_address');
        $('.hideInput').show();
        $('.showP').hide();
    })
    // 展示时候地址
    $('#FillAdress').click(function () {
        var priceId = $(this).attr('data-priceid');
        if (!priceId || priceId == '') {
            popTip('没有中实物奖');
            return;
        }
        //  查询是否设置了地址
        ja.post(commPath + 'getaddress', { logId: priceId }, function (res) {
            if (res.code === 10000 && res.data) {
                var data = res.data;
                if (data.addressStatus != 0) { //已有地址
                    $('.hideInput').hide();
                    $('.showP').show();
                    $('#usenameShow').html(data.rname);
                    $('#mobileShow').html(data.phone);
                    $('#address_inputShow').html(data.address);
                    $('#tjbtn').attr('data-status', 'false');
                    $('#tjbtn').hide();
                } else { //没有填写收货地址
                    $('.hideInput').show();
                    $('.showP').hide();
                }
                // 收货地址弹框
                popShow('pop_address');
            } else if (res.code == 1012 || res.code == 1019) {
                window.location.reload();
            } else {
                popTip(res.message);
            }
        })
    })
    // 抽奖记录
    $('#LuckDrawList').click(function () {
        $click(function () {
            if (isClick) {
                querywinsPost();
                popShow('pop_record');
            }

        })

    })
    //选择门派
    $('.tgbox').on('click', '.xzbtn', function () {
        var $this = $(this),
            order = $this.parents('li').index();
        $click(function () {
            if ($this.hasClass('xzstyle')) {
                $this.removeClass('xzstyle');
                $('.tgbox').attr('data-order', 'false');
            } else {
                cp_index = $this.parent().index();
                $('.tgbox li').find('.xzbtn').removeClass('xzstyle');
                $('.tgbox li').eq(cp_index).find('.xzbtn').addClass('xzstyle');
                $('.tgbox').attr('data-order', order);
            };
        })
    });

})


//实物收货地址
function __changeUserName(of) {
    var username = $('#' + of).val();
    if (username == '' || !isMobilePhone(username)) {
        $('.tj_tip p').show();
        $('.tj_tip p').html('请输入正确的手机号码!');
        return;
    }
};
function checkMobilePhone(telphone) {
    if (telphone == '' || !isMobilePhone(telphone)) {
        $('.tj_tip p').show();
        $('.tj_tip p').html('请输入正确的手机号码!');
    } else {
        $('.tj_tip p').hide();
        $('.tj_tip p').html('您还有必填项未填写!');
    }
};
if ($('#mobile').val() == '' || !isMobilePhone($('#mobile').val())) {
    $('.tj_tip p').html('手机号码不正确!');
    ckh_result = false;
};
function isMobilePhone(value) {
    if (value.search(/^(\+\d{2,3})?\d{11}$/) == -1)
        return false;
    else
        return true;
};
function __changeCom(comname) {
    if (comname == "") {
        $('.tj_tip p').show();
        $('.tj_tip p').html('您还有必填项未填写!');
    } else {
        $('.tj_tip p').hide();
    }
};
function check() {
    $('.tj_tip p').hide();
    var ckh_result = true;
    if ($('#mobile').val() == '' || !isMobilePhone($('#mobile').val())) {
        $('.tj_tip p').show();
        $('.tj_tip p').html('请输入正确的手机号码!');
        ckh_result = false;
    }
    if ($('#usename').val() == '') {
        $('.tj_tip p').show();
        $('.tj_tip p').html('您还有必填项未填写!');
        ckh_result = false;
    }
    if ($('#address_input').val() == '') {
        $('.tj_tip p').show();
        $('.tj_tip p').html('您还有必填项未填写!');
        ckh_result = false;
    }
    return ckh_result;
};

//视频
$('.v_btn').on('click', function () {
    $('#pop_video').find('video').attr('src', $(this).attr('data-video'));
    popShow('pop_video');
    document.getElementById('videoTz').play();
});
$('#pop_video .close').click(function () {
    popHide();
    document.getElementById('videoTz').pause();
});

//侧导航

$('.arrow').on('click', function () {
    $('.navbox').removeClass('yinc');
});
$(document).scroll(function () {
    if ($(document).scrollTop() <= 100) {
        $('.navbox').removeClass('yinc');
    } else {
        $('.navbox').addClass('yinc');
    };
});





