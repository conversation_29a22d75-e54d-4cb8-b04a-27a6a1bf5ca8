$(function () {
    var example = {
        base_url: '/base_url/',
        isLongmen: location.href.indexOf('longmen') !== -1,
        /** @deprecated  与ja节流方法冲突 如需使用请先将对应接口加入ja排除 防重点击 接口列表 */
        clickFlag: false, //防抖标识
        initParam: { // 初始化参数
            app: 'xtl',
            activity: 'eightstart',
            version: '20230904',
            platform: 'wgchangyou'
        },
        errorMsg: { // 通用的错误码提示文案
            1102: '活动无效',
            1106: '活动未开始',
            1103: '活动已结束'
        },
        levelLow: false,
        init: function () {
            /* 初始化  config配置项详见 表 3.1 */
            ja.config({
                app: this.initParam.app, // 必填，应用标识
                activity: this.initParam.activity, // 必填，活动标识
                version: this.initParam.version + (example.isLongmen ? '01' : ''), // 必填，活动版本标识
                platform: this.initParam.platform, // 必填，登陆平台标识
                isWxLogin: true,
                isWxStatus: true,
                isAutoWxLogin: false
            });

            // logDebug依赖阿里云错误日志上报js
            $.ajaxSetup({
                error: function (jqXHR) {
                    example.clickFlag = false;
                    if (this.url.indexOf(location.hostname) != -1) {
                        alert('系统异常，请稍后再试！');
                        logDebug.error({
                            activity: example.initParam.activity,
                            message: [
                                'title：' + '全局ajax请求Error处理',
                                'Request URL：' + this.url,
                                'Request Method：' + this.type,
                                'header：' + JSON.stringify(this.headers),
                                'Status Code：' + jqXHR.status + jqXHR.statusText,
                                'data：' + this.data
                            ].join('\n')
                        })
                    }
                }
            })
            window.onerror = function (message, source, lineno, colno, error) {
                example.clickFlag = false;
                logDebug.error({
                    activity: example.initParam.activity,
                    message: [
                        'title：' + 'window.onerror',
                        'errorMessage：' + message + '----in：' + source + '：' + lineno + '：' + colno
                    ].join('\n')
                })
            }

            /*  初始化完成后执行函数 返回全局对象 glob,详见 表 3.2 */
            ja.ready(function (glob) {
                glob.isLogin ? example.loginAfter() : example.loginBefore();
            });

            /* 龙门标志跟提示 */
            if (example.isLongmen) {
                ja.longmen({
                    top: '50%',
                    left: '0',
                })

                alert('此活动为龙门测试版本，奖品均发放到龙门服')
            };

            // 登录按钮
            $('#login').on('click', function () {
                example.commHandle()
            })

            // 二选一登录 畅游平台登录
            $('#cyLogin').on('click', function () {
                ja.login({ w2c: true })
            })

            // 二选一登录 畅游平台登录
            $('#wgLogin').on('click', function () {
                ja.login()
            })

            // 二选一登录 微信登录
            $('#wxLogin').click(function () {
                if (ja.wxBindName) {
                    ja.wxAutoLogin();
                } else {
                    location.href = ja.urlWxBind;
                }
            })

            // 注销
            $('#logout').on('click', function () {
                ja.logout()
            })

            // 示例
            $('#centerBtn').on('click', function () {
                if (example.commHandle()) return;
                popup('#pop-mycenter');
            })
        },
        /** 登录后 */
        loginAfter: function () {
            $('.login_box').hide()
            $('.user_name').text(ja.userInfo.nickname || ja.userInfo.openid) // wgchangyou二选一
            $('.logout_box').show()
        },
        /** 登录前 */
        loginBefore: function () {
            if (ja.isWechat) {
                // 点击过授权的就直接拉起登录弹窗
                if (sessionStorage.getItem(example.initParam.activity + example.initParam.version + 'wLogin')) {
                    ja.isWLogin && lay.open('loginBox')
                    sessionStorage.removeItem(example.initParam.activity + example.initParam.version + 'wLogin')
                }
                if (ja.wxBindName) {
                    $('#account').text(ja.wxBindName)
                }
            }
        },
        /**
        * 通用错误码处理
        * @param {*} data 返回的对象
        */
        commonErrPop: function (data) {
            var flag = true;
            switch (parseInt(data.code)) {
                case 1202: case 1209: case 1207: case 1007: //1202请求参数缺失或者为空  1209操作频繁
                    example.tips('系统繁忙，请稍后重试！');
                    break;
                case 1019: case 1012:
                    ja.isLogin = false; // 重置下ja的登录状态标识以触发登录操作
                    $('#login').click();
                    break;
                case 1102:
                    ja.code = 1102;
                    example.tips(example.errorMsg[1102]);
                    break;
                case 1106:
                    ja.code = 1106;
                    example.tips(example.errorMsg[1106]);
                    break;
                case 1103:
                    ja.code = 1103;
                    example.tips(example.errorMsg[1103]);
                    break;
                case 3103:
                    example.tips('账号已封停！');
                    break;
                case 2131:
                    example.tips('由于账号中的角色<30级，无法参与活动，请先升级再参与活动');
                    example.levelLow = true;
                    break;
                case 0:
                    example.tips('系统异常，请刷新后重试~');
                    break;
                default:
                    flag = false;
                    break;
            }
            if (flag) {
                example.clickFlag = false;
            }
            return flag;
        },
        /**
        * 通用的前置处理方法
        */
        commHandle: function () {
            var flag = true;
            if (!ja.isWechat) {
                alert('请在微信中访问本活动！');
            } else if (ja.code == 1102) {
                example.tips(example.errorMsg[1102]);
            } else if (ja.code == 1106) {
                example.tips(example.errorMsg[1106]);
            } else if (ja.code == 1103) {
                example.tips(example.errorMsg[1103]);
            } else if (!ja.isLogin) {
                // 微信绑定登录，需要先判断是否拉取过授权
                if (ja.isWechat) {
                    if (!ja.isWLogin) { // 未授权
                        ja.login('wechat'); // 拉起授权
                        sessionStorage.setItem(example.initParam.activity + example.initParam.version + 'wLogin', '1');
                    } else {
                        lay.open('loginBox') // 弹出登录弹窗
                    }
                } else {
                    ja.login({ w2c: true }); // 直接拉起畅游登录
                }
            } else if (example.levelLow) {
                example.tips('由于账号中的角色<30级，无法参与活动，请先升级再参与活动');
            } else {
                flag = false;
            }
            return flag;
        },
        /**
        * 通用提示
        */
        tips: function (msg) {
            $('#tips p').html(msg);
            popup('#tips')
        },
        /**
        * 单向绑定 方便数据驱动视图的更新
        * @description 监听属性修改 set修改 get读取
        * @param {String} attr 属性名
        * @param {object} set 设置数据时触发的操作
        * @param {object} get 获取数据时触发的操作
        * @example
        * new example.dataObserver('param', function (val) {
        *	$('element').text(val);
        * })
        */
        dataObserver: function (attr, set, get) {
            var _value = example[attr];
            Object.defineProperty(example, attr, {
                get: function () {
                    get && get(_value);
                    return _value;
                },
                set: function (val) {
                    set && set(val);
                    _value = val;
                }
            })
            example[attr] = _value;
        },
        /**
        * 示例
        * @param {String} a 示例
        */
        example: function (a) {
            if (example.clickFlag) return;
            example.clickFlag = true;
            $.ajax({
                type: 'GET',
                url: example.base_url + '',
                async: false,
                data: {
                    'data': a
                },
                success: function (res) {
                    // 前置过滤器 处理通用错误码
                    if (example.commonErrPop(res)) return;

                    if (res.code == 10000) {
                        example.clickFlag = false;

                    } else {
                        switch (parseInt(res.code)) {
                            case 5111:
                                example.tips('创建队伍失败，请刷新页面后重试');
                                break;
                            default:
                                example.tips(res.message);
                                break;
                        }
                    }
                }
            })
        },
    }


    example.init()
})
