<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="keywords" content="新天龙八部,天龙,怀旧版"/>
    <meta name="description" content="《新天龙八部》怀旧服，江湖梦回年少，端游，江湖梦，好礼相送！"/>
    <title>江湖梦回年少-《新天龙八部》怀旧服</title>
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,viewport-fit=cover"/>
    <meta content="yes" name="apple-mobile-web-app-capable"/>
    <meta content="black" name="apple-mobile-web-app-status-bar-style"/>
    <meta content="telephone=no" name="format-detection"/>
    <meta name="author" content="Design:CP; Web Layout:CP;"/>
    <meta name="robots" content="index, follow"/>
    <meta name="googlebot" content="index, follow"/>
    <link type="text/css" rel="stylesheet" href="/act/all/css/reset.css"/>
    <link rel="stylesheet" href="/act/all//css/swiper.min.css">
    <link type="text/css" rel="stylesheet" href="/act/tlhj/loading/20230912/m/css/index.css?0310_002"/>

    <script>
        var phoneWidth = parseInt(window.screen.width);
        var phoneScale = phoneWidth / 750;
        var ua = navigator.userAgent;
        if (/Android (\d+\.\d+)/.test(ua)) {
            var version = parseFloat(RegExp.$1);
            // andriod 2.3
            if (version > 2.3) {
                document.write('<meta name="viewport" content="width=750, minimum-scale = ' + phoneScale + ', maximum-scale = ' + phoneScale + ', target-densitydpi=device-dpi">');
                // andriod 2.3
            } else {
                document.write('<meta name="viewport" content="width=750, target-densitydpi=device-dpi">');
            }
        } else {
            document.write('<meta name="viewport" content="width=750, user-scalable=no, target-densitydpi=device-dpi">');
        }
    </script>
    <script type="text/javascript">
        var deviceWidth = document.documentElement.clientWidth;
        if (deviceWidth > 750) {
            deviceWidth = 750
        };
        document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
    </script>


    <script>
        function getQueryString(name, url) {
            var reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)'),
                search = unescape((url && url.split('?')[1]) || window.location.search.substr(1)),
                r = search.match(reg);
            if (r != null) return r[2];
            return null;
        }
        var paramsH = JSON.parse(sessionStorage.getItem('tlgl-toufang-20200909-changyou:params')) || {},
            CYSCID = getQueryString('CYSCID') || paramsH.CYSCID,
            UID =  getQueryString('UID') || paramsH.UID;

        switch (CYSCID) {
            case 'dy':      // 抖音
            case 'tt':      // 头条
                if(UID) {
                    (function(r,d,s,l){var meteor=r.meteor=r.meteor||[];meteor.methods=["track","off","on"];meteor.factory=function(method){return function(){
                        var args=Array.prototype.slice.call(arguments);args.unshift(method);meteor.push(args);return meteor}};for(var i=0;i<meteor.methods.length;i++){
                        var key=meteor.methods[i];meteor[key]=meteor.factory(key)}meteor.load=function(){var js,fjs=d.getElementsByTagName(s)[0];js=d.createElement(s);
                        js.src="https://analytics.snssdk.com/meteor.js/v1/"+l+"/sdk";fjs.parentNode.insertBefore(js,fjs)};meteor.load();if(meteor.invoked){return}
                        meteor.invoked=true;meteor.track("pageview")})(window,document,"script", UID);
                }
                break;
            case 'tx':      // 腾讯
            case 'pyq':     // 朋友圈
                if(UID) {
                    !function(g,d,t,e,v,n,s){if(g.gdt)return;v=g.gdt=function(){v.tk?v.tk.apply(v,arguments):v.queue.push(arguments)};v.sv='1.0';v.bt=0;v.queue=[];n=d.createElement(t);n.async=!0;n.src=e;s=d.getElementsByTagName(t)[0];s.parentNode.insertBefore(n,s);}(window,document,'script','//qzonestyle.gtimg.cn/qzone/biz/gdt/dmp/user-action/gdtevent.min.js');
                    gdt('init', UID);
                    gdt('track','PAGE_VIEW');
                }
                break;
            case 'uc':      // UC
                (function(w,d,t,s,q,m,n){if(w.utq)return;q=w.utq=function(){q.process?q.process(arguments):q.queue.push(arguments);};q.queue=[];m=d.getElementsByTagName(t)[0];n=d.createElement(t);n.src=s;n.async=true;m.parentNode.insertBefore(n,m);})(window,document,'script','https://image.uc.cn/s/uae/g/0s/ad/utracking.js');utq('set', 'convertMode', true);utq('set', 'trackurl', 'huichuan.sm.cn/lp');
                break;
            case 'bd':      // 百度
                window._agl = window._agl || [];
                (function () {
                    _agl.push(
                        ['production', '_f7L2XwGXjyszb4d1e2oxPybgD']
                    );
                    (function () {
                        var agl = document.createElement('script');
                        agl.type ='text/javascript';
                        agl.async = true;
                        agl.src = 'https://fxgate.baidu.com/angelia/fcagl.js?production=_f7L2XwGXjyszb4d1e2oxPybgD';
                        var s = document.getElementsByTagName('script')[0];
                        s.parentNode.insertBefore(agl, s);
                    })();
                })();
                break;
        }
    </script>
</head>

<body>
    <div class="header">
        <img src="/act/tlhj/loading/20230912/m/img/hLogo.png" alt="">
        <div class="txt">
            <h3>新天龙八部端游</h3>
            <p>十六年经典金庸正版授权</p>
        </div>
    </div>
    <!-- logged -->
<div class="wrap">
    
    <!-- swiper开始  -->
            <div class="part1 ">
                <a href="javascript:;" class="logo"></a>
                <div class="little">19:00特色服大区 [龙争虎斗]</div>
                
                 <div class="login login_after">
                    <!--登录前状态 Start-->
                    <!-- <p id="unlogin">亲爱的用户，请 <a href="javascript:;" id="dologin">[登录]</a></p> -->
                    <!--登录前状态 End-->
                    <!--登录后状态 Start-->
                    <p id="logined"><span id="login_qq_span"></span><a href="javascript:;"
                            id="dologout">【注销】</a></p>
                    <!--登录后状态 End-->
                    <a href="javascript:;" class="draw_btn"></a>
                </div>
                <div class="box_head_prop"></div>
                <div class="part3 ">
                    <div class="cont">
                        <div class="pr">
                            <div class="git2 pr input_box combox">
                                <div class="input-box2 pr">
                                    <input type="text" placeholder="请输入手机号" maxlength="11" oninput="value=value.replace(/[^\d]/g,'')" class="input_hm input_phone input_">
                                    <div class="pr yz_box">
                                        <input type="text" placeholder="请输入验证码" maxlength="6" oninput="value=value.replace(/[^\d]/g,'')"
                                               class="input_yz input_phone_code input_">
                                        <a href="javascript:;" class="got_yz">获取验证码</a>
                                    </div>
                                </div>
                                <!--添加on类名变为隐私选中状态-->
                                <div class="box_ys">
                                    <a href="javascript:;" class="chiose_ys"></a>
                                    <a href="https://www.changyou.com/about_us/privacy.shtml" class="link_ys" target="_blank"></a>
                                </div>
                                <a href="javascript:;" class="submit_t2 pa btn_submit"></a>
                            </div>
                        </div>
    
                    </div>
                </div>
            </div>
         
            <div class="part4">
                <div class="cont">
                    <div class="pr">
                    <div class="title"></div>
                        <div class="swiper-box swiper-container-pic-4">
                            <div class="swiper-container swiper-container-pic ">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide">
                                        <img src="/act/tlhj/loading/20230912/m/img/slide1.png" alt="">
                                    </div>
                                    <div class="swiper-slide">
                                        <img src="/act/tlhj/loading/20230912/m/img/slide1.png" alt="">
                                    </div>
                                    <div class="swiper-slide">
                                        <img src="/act/tlhj/loading/20230912/m/img/slide1.png" alt="">
                                    </div>
                                    <div class="swiper-slide">
                                        <img src="/act/tlhj/loading/20230912/m/img/slide1.png" alt="">
                                    </div>
                                </div>
                            </div>
                            <div class="page-box pr">
                                <div class="swiper-pagination-pic">

                                </div>
                                <div class="line"></div>
                            </div>

                        </div>
                    </div>

                </div>
            </div>
            <div class="part4 part5">
                <div class="cont">
                    <div class="pr">
                    <div class="title"></div>
                        <div class="swiper-box swiper-container-pic-5">
                            <div class="swiper-container swiper-container-pic ">
                                <div class="swiper-wrapper">
                                    <div class="swiper-slide">
                                        <img src="/act/tlhj/loading/20230912/m/img/slide2.png" alt="">
                                    </div>
                                    <div class="swiper-slide">
                                        <img src="/act/tlhj/loading/20230912/m/img/slide2.png" alt="">
                                    </div>
                                    <div class="swiper-slide">
                                        <img src="/act/tlhj/loading/20230912/m/img/slide2.png" alt="">
                                    </div>
                                    <div class="swiper-slide">
                                        <img src="/act/tlhj/loading/20230912/m/img/slide2.png" alt="">
                                    </div>
                                </div>
                            </div>
                            <div class="page-box pr">
                                <div class="swiper-pagination-pic">

                                </div>
                                <div class="line"></div>
                            </div>

                        </div>
                    </div>

                </div>
            </div>

    <!--<div class="arr"></div>-->
    <!--<a href="javascript:;" class="btn_lq">立即领取</a>-->
    <!-- swiper结束 -->
</div>
<!-- 以下部分为弹窗  -->
<!-- 恭喜获得 -->
<div class="congr_pop pop1" id="congr_pop" style="display: none;">
    <a href="javascript:showDialog.hide();" class="yy-pop-close close">关闭</a>
    <div class="pop-cont pr">
        <h2 class="pop-tit p-titi1"></h2>
        <p class="txt success_text">
            恭喜你已获得助力重返江湖礼包，礼包卡已短信形式发送<br> 您手机号，请注意查收手机短信，您还可以登录游戏账号。 <br>
领取青葱岁月礼包哦，更有天马等你拿~ </p>
<p class="pop_remind">登录游戏账号获取以下物品</p>
        <p class="CDK">CDK：<span class="cdk_show">XXXX-XXXX-XXXX-XXXX</span></p>
      
        <ul class="dj-box">
            <img src="/act/tlhj/loading/20230912/m/img/pop_dj1.png">
            <!-- <li>
                <div class="dj"></div>
                <p class="dj-name">道具×1</p>
            </li>
            <li>
                <div class="dj"></div>
                <p class="dj-name">道具×1</p>
            </li>
            <li>
                <div class="dj"></div>
                <p class="dj-name">道具×1</p>
            </li>
            <li>
                <div class="dj"></div>
                <p class="dj-name">道具×1</p>
            </li> -->
        </ul>
        <div class="btn-box pr">
            <a href="javascript:;" class="onload_t btn_login"></a>
            <a href="javascript:;" class="got_gift_t btn_login"></a>
        </div>
    </div>
</div>
<!-- 领取成功 -->
<div class="inti_pop pop2" id="inti_pop" style="display: none;">
    <a href="javascript:;" class="yy-pop-close close btn_close">关闭</a>
    <div class="pop-cont pr">
        <h2 class="pop-tit p-tit2"></h2>
        <p class="txt" >获得价值XXX元礼包一份， <br>
            请到大理龚彩云(XXX,XXX)进行领取~</p>
    </div>
    <a href="javascript:;" class="sure_t btn_close"></a>
</div>
<!-- 获得 -->
<div class="got_pop pop2" id="got_pop" style="display: none;">
    <a href="javascript:;" class="yy-pop-close close btn_close">关闭</a>
    <div class="pop-cont pr" style="padding-left: 30px;">
        <p class="txt">您已获得价值X礼包了哦， <br>别忘了去大理龚彩云(XXX,XXX)处领取哦~</p>
    </div>
    <a href="javascript:;" class="sure_t btn_close"></a>
</div>
<!-- 通用提示 -->
<div class="got_pop pop3" id="commTip" style="display: none;">
    <a href="javascript:;" class="yy-pop-close close btn_close">关闭</a>
    <div class="pop-cont pr" >
        <p class="txt"></p>
    </div>
    <a href="javascript:;" class="sure_t btn_close"></a>
</div>
<!--分享提示蒙层-->
<div class="sharetip"></div>
<script src="/act/all//js/jquery-1.8.3.min.js"></script>
<script src="/act/all//js/swiper-3.4.2.min.js"></script>
<script src="/act/tlhj/loading/20230912/m/js/showDialog.min.js"></script>
<script>
    //全屏切换
    // window.swiper
//  window.swiper = new Swiper('.swiper-container-main', {
//      direction: 'vertical',
//      followFinger: false,
//      speed: 500,
//      effect: 'fade',
//      parallax: true,
//      noSwiping: true,
//      initialSlide: 0,
//      onSlideChangeStart: function (swiper) {
//          console.log(swiper.activeIndex); //切换结束时，告诉我现在是第几个slide
//          if (swiper.activeIndex == 6) {
//              $(".arr").hide();
//                  $('#cy_bot').show();
//          } else {
//              $(".arr").show();
//                  $('#cy_bot').hide();
//          }
//      },
//      onInit: function (swiper) {
//          //Swiper初始化了
//          //alert(swiper.activeIndex);提示Swiper的当前索引
//          var swiper2 = new Swiper('.swiper2', {
//              direction: 'horizontal',
//              autoplay: 500, //可选选项，自动滑动
//          });
//      },
//      observer: true,
//      observeParents: true,
//      mousewheelControl: true
//  });
    // window.swiper.slideTo(3)
    // $(function () {
    //     //获得当前<ul>
    //     var $uList = $(".scroll-box ul");
    //     var timer = null;
    //     //触摸清空定时器
    //     $uList.hover(function () {
    //             clearInterval(timer);
    //         },
    //         function () { //离开启动定时器
    //             timer = setInterval(function () {
    //                     scrollList($uList);
    //                 },
    //                 2000);
    //         }).trigger("mouseleave"); //自动触发触摸事件
    //     //滚动动画
    //     function scrollList(obj) {
    //         //获得当前<li>的高度
    //         var scrollHeight = $("ul li:first").height();
    //         //滚动出一个<li>的高度
    //         $uList.stop().animate({
    //                 marginTop: -scrollHeight
    //             },
    //             600,
    //             function () {
    //                 //动画结束后，将当前<ul>marginTop置为初始值0状态，再将第一个<li>拼接到末尾。
    //                 $uList.css({
    //                     marginTop: 0
    //                 }).find("li:first").appendTo($uList);
    //             });
    //     }
    // });
    var mySwiper1 = new Swiper('.swiper-container-pic-4 .swiper-container-pic', {
        autoplay: 5000, //可选选项，自动滑动
        pagination: ' .swiper-container-pic-4 .swiper-pagination-pic',
    })
    var mySwiper1 = new Swiper('.swiper-container-pic-5 .swiper-container-pic', {
        autoplay: 5000, //可选选项，自动滑动
        pagination: ' .swiper-container-pic-5 .swiper-pagination-pic',
    })
    $('.chiose_ys').on('click',function () {
    	$(this).parent('.box_ys').toggleClass('on');
    })
     // 解决键盘弹出遮挡
//   $(".input_").on(' focus', function (e) {
//      // $('#cy_bot').hide();
//      $('.arr').hide();
//      $('.gift1').addClass('fied');
//      });
//      $(".input_").on('blur', function (e) {
//          // $('#cy_bot').show();
//      $('.arr').show();
//      $('.gift1').removeClass('fied');
//      })
//监听页面的滚动
window.onscroll = function() {
	var scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
	var windowHeight = document.documentElement.clientHeight || document.body.clientHeight;
	var scrollHeight = document.documentElement.scrollHeight || document.body.scrollHeight;
	if ((scrollTop + windowHeight) > scrollHeight - 30) {
	   $('#cy_bot').show();
	}else{
		$('#cy_bot').hide();
	}
}
</script>
<!--#include virtual="/all/nav/cy_public_js_dark.html"-->
<!--#include virtual="/all/dma/dma_activity.html" -->
<!-- <script src="https://s1.hdslb.com/bfs/cm/cm-sdk/static/js/track-collect.js"></script> -->
<script src="/act/all//cdn/join-activity/2.6/join-activity.min.js"></script>
<script src="/act/tlhj/loading/20230912/m/js/index.js?0914"></script>
</body>

</html>
