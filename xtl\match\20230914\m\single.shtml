<!DOCTYPE HTML>
<html>
	<head>
		<meta charset="utf-8">
		<title>江湖魅力秀-《新天龙八部》官方网站</title>
		<meta name="applicable-device" content="mobile">
		<meta name="viewport"
			content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=0,maximum-scale=1,viewport-fit=cover" />
		<meta name="format-detection" content="telephone=no" />
		<meta content="yes" name="mobile-web-app-capable">
		<meta content="yes" name="apple-mobile-web-app-capable" />
		<meta name="robots" content="all" />
		<meta name="keywords"
			content="新天龙八部,天龙八部,武侠浪漫美学,网络游戏,浪漫武侠,武侠游戏,金庸,萧峰,段誉,虚竹,王语嫣,xtl,xtlbb,tlbb,天龙八部钟汉良版,天龙八部黄日华版" />
		<meta name="description" content="江湖魅力秀-《新天龙八部》官方网站" />
		<meta name="author" content="Design:CP; Web Layout:CP;" />
		<link type="image/x-icon" href="/xtl/favicon.ico" rel="icon" />
		<link type="image/x-icon" href="/xtl/favicon.ico" rel="shortcut icon" />
		<link type="text/css" rel="stylesheet" href="/act/all/css/reset.css" />
		<link type="text/css" rel="stylesheet" href="/act/xtl/match/20230914/pc/css/cropper.css" />
		<link type="text/css" rel="stylesheet" href="/act/xtl/match/20230914/m/css/index.css?2022102020" />
		<link type="text/css" rel="stylesheet" href="/act/xtl/match/20230914/m/css/pop.css?2022102020" />
		<script>
			//H5里跳转到PC
			if (!/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
				window.location = "../pc/single.shtml" + location.search;
			}
		</script>
		<script>
			var phoneWidth = parseInt(window.screen.width);
			var phoneScale = phoneWidth / 750;
			var ua = navigator.userAgent;
			if (/Android (\d+\.\d+)/.test(ua)) {
				var version = parseFloat(RegExp.$1);
				// andriod 2.3
				if (version > 2.3) {
					document.write('<meta name="viewport" content="width=750, minimum-scale = ' + phoneScale +
						', maximum-scale = ' +
						phoneScale + ', target-densitydpi=device-dpi">');
					// andriod 2.3
				} else {
					document.write('<meta name="viewport" content="width=750, target-densitydpi=device-dpi">');
				}
			} else {
				document.write(
					'<meta name="viewport" content="width=750, user-scalable=no, target-densitydpi=device-dpi">');
			}
		</script>
		<script type="text/javascript">
			var deviceWidth = document.documentElement.clientWidth;
			if (deviceWidth > 750) {
				deviceWidth = 750
			};
			document.documentElement.style.fontSize = deviceWidth / 750 + 'px';
		</script>
		<script src="https://www.changyou.com/cyouFile/loger/huodong.js"></script>
	</head>

	<body>
		<div class="wrap">
			<div class="head">
				<a href="//tl.changyou.com/main.shtml" title="《新天龙八部》官方网站" target="_blank" class="logo">《新天龙八部》官方网站</a>
			</div>
			<div class="bg2">
				<h2 class="tit">
					<p>10.18-10.27</p>
				</h2>
				<p class="tip">论魅力，谁更强？</p>
				<div class="loginbk">
					<p class="fl ablesend">今日可投出火力值：<span class="myVotes">x</span>票</p>
					<div class="loginbox fr">
						<p class="logout_box" style="display: none;"><a href="javascript:;">个人中心</a> /<span class="user_name"></span> /<a href="javascript:;" id="logout" class="loginout">注销</a></p>
						<p class="login_box"><a href="javascript:;" id="login" class="loginout">登录</a></p>
					</div>
				</div>
				<div class="bminfbox pr">
					<!--重新报名，添加类名“ repeatbtn ”-->
					<a href="javascript:;" class="btn bmbtn pa" id="btn_joinSectsPersonal" title="立即报名">立即报名</a>
					<div class="pkinfbox none">
						<p class="rankpai">我的门派排行：<span id="myRanking">暂未上榜</span></p>
						<!--审核中-->
						<div class="examineing none">
							<div class="avtarbox">
								<img src="/act/xtl/match/20230914/pc/img/shz.jpg">
							</div>
							<div class="avtarinf">
								<p class="myServer"></p>
								<p class="myRoleName"></p>
							</div>
							<div class="shenheing">
								<p class="toptxt">参战宣言及作品正在审核中，请耐心等候！</p>
								<p class="bottxt">粉丝眼中的我：</p>
								<span class="nosign">暂无标签</span>
							</div>
						</div>
						<!--完成的状态-->
						<div class="examined none" id="myInfo">
							<div class="topbar">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p class="myServer"></p>
									<p class="myRoleName"></p>
								</div>
								<div class="hotbox">
									<span class="hot"></span>
									<p class="hotnum">热度<span></span></p>
								</div>
							</div>
							<div class="sharebox">
								<p>呼朋唤友添把火：</p>
								<a href="javascript:;" class="btn wxbtn" title="微信分享"></a>
								<a href="javascript:;" class="btn qqbtn" title="QQ分享"></a>
							</div>
							<div class="shenheing">
								<p class="toptxt">参战宣言：<span></span></p>
								<p class="bottxt">我的作品：<span></span></p>
								<a href="javascript:;" class="cklink" title="点击此处查看作品">点击此处查看作品</a>
							</div>
							<div class="signbox">
								<div class="btnbox">
									<p class="showtxt">粉丝眼中的我：<a href="javascript:;" class="btn btn-ztgd" style="display: none;" title="点击查看"></a></p>
									<span class="nosign">暂无标签</span>
									<div class="roll" style="display: none;">
										<ul class="first-say-list">

										</ul>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="canlebox">
					<p>充分发挥优势!展现过人才华！<br>扩大号召力!让更多门派弟子信服于你吧！</p>
					<a class="btn zsbtn pa">可展示才华参考</a>
				</div>
				<ul class="paiul">
					<li class="on" data-id="tl">
						<span>天龙</span>
					</li>
					<li data-id="mj">
						<span>明教</span>
					</li>
					<li data-id="gb">
						<span>丐帮</span>
					</li>
					<li data-id="wd">
						<span>武当</span>
					</li>
					<li data-id="em">
						<span>峨眉</span>
					</li>
					<li data-id="ts">
						<span>天山</span>
					</li>
					<li data-id="sl">
						<span>少林</span>
					</li>
					<li data-id="xy">
						<span>逍遥</span>
					</li>
					<li data-id="xx">
						<span>星宿</span>
					</li>
					<li data-id="mr">
						<span>慕容</span>
					</li>
					<li data-id="tm">
						<span>唐门</span>
					</li>
					<li data-id="gg">
						<span>鬼谷</span>
					</li>
					<li data-id="thd">
						<span>桃花岛</span>
					</li>
					<li data-id="jqg">
						<span>绝情谷</span>
					</li>
				</ul>
				<div class="starbox">
					<div class="meipaibox tl" data-sectID="6">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox mj" data-sectID="1">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox gb" data-sectID="2">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox wd" data-sectID="3">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox em" data-sectID="4">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox ts" data-sectID="7">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox sl" data-sectID="0">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox xy" data-sectID="8">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox xx" data-sectID="5">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox mr" data-sectID="10">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox tm" data-sectID="11">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox gg" data-sectID="12">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox thd" data-sectID="13">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
					<div class="meipaibox jqg" data-sectID="14">
						<div class="qiansbox">
							<div class="fribox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="secbox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
							<div class="thibox pa">
								<div class="avtarbox">
									<img onerror="this.src=common.defaultAvatar">
								</div>
								<div class="avtarinf">
									<p>&nbsp;</p>
									<p>&nbsp;</p>
								</div>
								<div class="hotbox">
									<!--未点击，添加类名 “ on ”-->
									<span class="hot"></span>
									<p class="hotnum">热度<span>&nbsp;</span></p>
								</div>
							</div>
						</div>
						<ul class="ranklist">

						</ul>
						<div class="tpage">
							<a href="javascript:;" class="prev">上一页</a>
							<span class="cur page-num">1/1</span>
							<a href="javascript:;" class="next">下一页</a>
						</div>
					</div>
				</div>
				<h2 class="tit t1">门派个人战赛道奖励</h2>
				<ul class="jlhd">
					<li class="li0 on">首席奖</li>
					<li class="li1">参与奖</li>
				</ul>
				<div class="distab dis">
					<div class="jlnrbox">
						<p class="endtime">截止10月26日24点，当热度达到您所在门派的前三名!<br>即可成为<span>“门派首席”!</span></p>
						<div class="sxjlribox pr">
							<div class="cdkbox pa">
								<div></div>
								<a href="javascript:common.tips(personal.isBack ? '您是回流玩家' : '您不是回流玩家');" class="btn cxbtn" title="前往查询">前往查询</a>
							</div>
							<p class="sxtip">分赛道前48位少年，可领取专属CDK福利码将福利码分发<br>给自己的粉丝，争夺全服唯一的“全门派第一人”</p>
							<ul class="sxlist">
								<li><span>1、</span>将获得的CDK码，发放给支持你的少侠们!当有角色激活了你的cdk后，他便成为你的粉丝!</li>
								<li>每个角色仅能激活1个精英首席的CDK码例如，角色A已经激活了首席A的CDK，若又收到了首席B的CDK时，便无法再次激活</li>
								<li><span>2、</span>最终”全门派第一人”将比拼粉丝数量，快去发挥魅力召集更多的  少侠成为你的粉丝吧!</li>
								<li>1个粉丝使用，将增加1分，若这位激活CDK的粉丝，属于故友归来，可增加分值会更高!</li>
							</ul>
							<div class="cdktable">
								<table>
									<tr>
										<th width="50%">故友归来角色激活CDK</th>
										<th>故友归来角色激活CDK</th>
									</tr>
									<tr>
										<td>装评3w以下</td>
										<td>1</td>
									</tr>
									<tr>
										<td>装评在3-7w以下</td>
										<td>2</td>
									</tr>
									<tr>
										<td>装评在7-15w以下</td>
										<td>3</td>
									</tr>
									<tr>
										<td>装评在15-30w以下</td>
										<td>6</td>
									</tr>
									<tr>
										<td>装评在50w以下</td>
										<td>10</td>
									</tr>
								</table>
							</div>
						</div>
						<img src="/act/xtl/match/20230914/m/img/sxjl.png" class="sxjl">
					</div>
					<a href="javascript:;" class="btn ljbtn ylq" title="立即领取">立即领取</a>
				</div>
				<div class="distab">
					<div class="jlnrbox">
						<div class="rizsbox">
							<p class="rizsboxname">热力值获取方式：</p>
							<ul class="sxlist">
								<li><span>1、</span>首次登陆活动页面，可获得热力值3点</li>
								<li>若属于“知交重逢”账号，可获取的热力值更多。</li>
								<li><span>2、</span>活动期间，将检测昨日游戏登陆情况，若昨日登陆过游戏，今日可获得热力值3</li>
								<li>若属于“知交重逢”账号，昨日若登陆过游戏，可获取的热力值更多</li>
							</ul>
							<p class="belong"><span>若属于知交重逢账号，将根据页面绑定角色的装评，给与对应的<br>火力值!</span></p>
							<div class="cdktable mt">
								<table>
									<tr>
										<th width="43%">故友归来角色可获取的火力值</th>
										<th width="30%">首次登陆活动页面时</th>
										<th>前一日登录过游戏</th>
									</tr>
									<tr>
										<td>装评3w以下</td>
										<td>1</td>
										<td>1</td>
									</tr>
									<tr>
										<td>装评在3-7w以下</td>
										<td>2</td>
										<td>2</td>
									</tr>
									<tr>
										<td>装评在7-15w以下</td>
										<td>3</td>
										<td>3</td>
									</tr>
									<tr>
										<td>装评在15-30w以下</td>
										<td>6</td>
										<td>6</td>
									</tr>
									<tr>
										<td>装评在50w以下</td>
										<td>10</td>
										<td>10</td>
									</tr>
								</table>
							</div>
							<div class="cxbox">
								<a href="javascript:common.tips(personal.isBack ? '您是回流玩家' : '您不是回流玩家');" class="btn cxbtn" title="前往查询">前往查询</a>
							</div>
						</div>
						<div class="zsgiftbox">
							<h3>热力赠送礼包</h3>
							<p>当送出12点火力值时，可领取</p>
							<img src="/act/xtl/match/20230914/m/img/zsgift.png">
							<a href="javascript:;" class="btn ljbtn" id="joinPrize" title="立即领取">立即领取</a>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="navbot navbot1">
			<a href="index.shtml">总赛道</a>
			<a href="single.shtml">门派个人赛</a>
			<a href="index.shtml">帮派团体赛</a>
			<a>规则&奖励</a>
		</div>
		<!--#include virtual="/xtl/match/20230914/pc/inc/pop.html"-->
		<script src="/act/all/cdn/jquery/3.4.1/jquery.min.js"></script>
		<script src="/act/all/cdn/dialog/dialog.min.js"></script>
		<script src="/act/all/cdn/qrcode/1.0/qrcode.min.js"></script>
		<script src="//qzonestyle.gtimg.cn/qzone/qzact/common/share/share.js"></script>
		<script src="/act/all/cdn/clipboard.js/2.0.4/clipboard.min.js"></script>
		<script src="/act/all/cdn/html2canvas/1.0.0-rc.4/html2canvas.min.js"></script>
		<script src="/act/all/cdn/join-activity/2.6/join-activity.min.js"></script>
		<script src="/xtl/match/20230914/pc/js/cropper.js"></script>
		<script src="/xtl/match/20230914/pc/js/index.js"></script>
		<script src="/xtl/match/20230914/pc/js/personal.js"></script>
		<script src="/xtl/match/20230914/pc/js/common.js"></script>
		<script>
			$('.paiul li').on('click', function() {
				var thisele = $(this).data('id');
				$(this).addClass('on').siblings().removeClass('on');
				$('.starbox .meipaibox').hide();
				$('.' + thisele).show();
			});
		</script>
		<!--#include virtual="/all/nav/cy_public_js_dark.html"-->
		<!--#include virtual="/all/dma/dma_activity.html" -->
	</body>

</html>
