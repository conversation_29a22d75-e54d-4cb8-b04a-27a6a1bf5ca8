@charset "utf-8";
@import url(dj_logoonline_main.css);
body,div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,form,fieldset,input,textarea,p,blockquote,th,td{margin:0;padding:0;}
table{border-collapse:collapse;border-spacing:0;}
address,caption,cite,code,dfn,em,strong,th,var{font-weight:normal;font-style:normal;}
ol,ul{list-style:none;}
caption,th{text-align:left;}
h1,h2,h3,h4,h5,h6{font-weight:normal;font-size:100%;}
q:before,q:after{content:'';}
fieldset,img,abbr,acronym{border:0;}
/*页面样式*/
.c:before,.c:after {content:"";display:table;}
.c:after {clear:both;}
.c {zoom:1;}
body{font:12px/1.5 '\5FAE\8F6F\96C5\9ED1';padding-top:55px;text-align:justify;min-width:1200px;background: #000108;}
a {text-decoration:none;overflow:visible;}
a:hover {text-decoration:none;}
select,input{appearance:none;-moz-appearance:none;-webkit-appearance:none;}
select::-ms-expand { display: none; }
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button { -webkit-appearance: none; }
input[type="number"]{ -moz-appearance: textfield; }
.none{display:none;}
.pa{position:absolute;}
.bar{width:1200px;margin:0 auto;}
.bg2 .bar img{margin: 10px 0;}
.dj_logo_lk,.dj_logo_lk a,.dj_logo_bg{top:15px;left:50%; z-index: 2;margin-left:-92px;}
.dj_logo_bg{width: 184px; height: 140px; background-image: url("../img/dj-logo.png"); }
.dj_logo_lk a{display: block; z-index: 1;}
.wrap{background:url(../img/wbg.jpg) no-repeat center top;}
.btn,.gzname::after,.gzbox li em,.piclist span::after{background:url(../img/com.png) no-repeat;text-indent:-9999em;display:block;}
.btn{transition:all .2s;}
.btn:hover{filter:brightness(1.1);}
.head{height:961px;width:100%;background:url(../img/head.jpg) no-repeat;position:relative;}
.share{position:absolute;top:16px;left:956px;}
.share a{float:left;width:42px;height:36px;line-height:36px;color:#fff;text-align:center;position: relative;overflow: initial;}
.share a:hover{color:#ff4545;}
.share-wrap{float:left;color:#fff;line-height:36px;height:36px;padding-left:18px;}
.shareTowx{margin:0 14px 0 13px;}
.share a.shareTowx,.share a.shareToqq{width:16px;height:16px;float:left;margin-top:11px;}
.share a.shareToqq{background-position:-30px 0;}
.share a .tip-qrcode{
    width: 100px;
    height: 100px;
    position: absolute;
    left: 50%;
    bottom: -116px;
    margin-left: -50px;
    border: 1px solid #ff4545;
    display: none;
    padding: 2px;
    background: #fff;
}
.share a .tip-qrcode img, .tip-qrcode canvas{
    width:100%;
    height: 100%;
    display: block;
}
.share a:hover .tip-qrcode{
    display: block;
}
.pop .tip-qrcode{
    width: 160px;
    height: 160px;
    border: 1px solid #ff4545;
    padding: 4px;
    background: #fff;
    margin: 20px auto;
}
.pop .tip-share-txt{
    color:#333;
    text-align: center;
}
.share-wrap span{float:left;}
.btn-status{text-align: center; top:529px; width: 100%;}
.btn-status a{display: none; margin: 0 10px;}
.btn-status a.show{display: inline-block;}
.bmbtn, .centerbtn{width:277px;height:72px;background-position:0 -28px;}
.centerbtn{background-position:0 -297px;}
.loginbox{font-size:14px;text-align:center;left:0;width:100%;top:615px;}
.loginbox,.loginbox a{color:#ff4949;}
.loginbox a{text-decoration:underline;}
.logined{display:none;}
.cont{width:100%;background:url(../img/contt.jpg) no-repeat center top;height:1036px;}
.searchbox{width:422px;height:42px;margin:0 auto;}
.selectbox,.inputbox{width:196px;height:42px;float:left;}
.selectbox select{width:100%;height:100%;background:none;border:none;color:#f9d8dc;font-size:16px;background:#890000 url(../img/arrbg.png) no-repeat right top;border-radius:21px;padding:0 60px 0 33px;}
.inputbox{float:right;background:#890000;border-radius:21px;}
.inputbox input{width:109px;float:left;padding-left:33px;height:42px;background:none;border:none;color:#f9d8dc;font-size:16px;}
.inputbox input::-webkit-input-placeholder{color:#f9d8dc;}
.inputbox input::-moz-placeholder{color:#f9d8dc;}
.inputbox input:-moz-placeholder{color:#f9d8dc;}
.inputbox input:-ms-input-placeholder{color:#f9d8dc;}
.searbtn{float:left;width:54px;height:42px;background-position:0 -124px;}
.pamlist-wrap{padding-top:22px; position: relative;}
.pamlist{width:1116px; min-height: 460px; margin:0 auto;}
.pamlist-tip{display: none; width: 100%; position: absolute; text-align: center; color:#f9d8dc; font-size: 18px; top: 10%;}
.pamlist-tip a{color:#f9d8dc; text-decoration: underline;}
.pamlist li{margin:-1px 24px 0;float:left;display:inline;width:324px;height:462px;background:url(../img/libg.png) no-repeat;position:relative;}
.nrbox{padding:50px 49px 0;color:#ffa2a2;font-size:14px;}
.nrbox p{height:32px;line-height:32px;padding:0 0 1px 41px;width:185px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;}
.nrbox p span,.nrbox p strong{padding-left:39px;}
.nrbox p strong{color:#ff2424;font-weight:bold;}
.swibox{width:225px;height:177px;margin:0 auto 11px;border-radius:3px;border:1px solid #b51c1c;background:#b51c1c;}
.swiper-slide img{display:block;width:100%;}
.swiper-prev,.swiper-next{position:absolute;bottom:6px;height:16px;width:18px;background:url(../img/arr.png) no-repeat;z-index:2;cursor:pointer;}
.swiper-next{right:6px;background-position:top right;}
.swiper-prev{right:28px;}
.swiper-prev:hover{background-position:left bottom;}
.swiper-next:hover{background-position:right bottom;}
.tpbtn{margin:6px auto 0;width:226px;height:40px;background-position:0 -226px;}
.tpbtn:hover{background-position:0 -178px;}
.tpage{color:#cc7b7c;text-align:center;font-size:16px;height:50px;line-height:50px;}
.tpage li{display:inline-block;vertical-align:top;margin:0 8px;cursor:pointer;}
.tpage li:hover,.tpage li.active{color:#ff4949;}
.tpage li.active,.tpage li.disabled{cursor:default;}
.bg1{background:url(../img/bg1.jpg) no-repeat center top;}
.bg2{background:url(../img/bg2.jpg) no-repeat center top;}
.bg2 .bar{padding:83px 0 50px;background:url(../img/contt1.jpg) no-repeat center top;}
.gzname{color:#c63131;font-size:24px;font-weight:bold;margin-left:94px;position:relative;}
.gzname::after{top:6px;left:-37px;position:absolute;content:'';width:24px;height:24px;background-position:-321px 0;}
.gzbox{color:#f1b3be;font-size:14px;line-height:24px;margin-top:2px;}
.gzbox p{padding:14px 0 0 117px;}
.gzbox span{color:#ff4949;}
.gzbox strong{font-weight:bold;}
.gzbox li{padding-left:125px;position:relative;width:973px;}
.gzbox li em{top:2px;left:95px;width:24px;height:24px;text-indent:0;text-align:center;line-height:24px;background-position:-321px -46px;position:absolute;}
.bgbox{margin:20px 0 20px 125px;width:900px;}
.bgbox table{width:100%;}
.bgbox table th,.bgbox table td{border:1px solid #f1b3be;padding:4px;text-align:center;}
.pop{width:563px;height:384px;background:url(../img/pop.png) no-repeat;position:relative;display:none;}
.close{top:40px;right:23px;width:31px;height:31px;background-position:-314px -206px;position:absolute;}
.popcont{padding:32px 15px 0;}
.loginbk{padding-top:87px;}
.loginbk div{height:29px;margin-top:6px;line-height:29px;font-size:16px;color:#595959;}
.loginbk div label{text-align:right;width:120px;padding-right:2px;float:left;}
.loginbk input{width:227px;height:27px;border:1px solid #b29292;padding:0 10px;color:#595959;float:left;}
.loginbk input.yzm{width:95px;}
.loginbk img.img_code{height:28px;}

.loginbk a{float:left;margin-left:9px;color:#e56666;font-size:16px;}
.loginbk a:hover{text-decoration:underline;}
.loginbk span{width:100px;float:left;margin-left:9px;width:123px;}
.loginbk span.input_area{width: 247px; margin-left: 0;}
.dlbtn{margin:34px auto 0;width:177px;height:51px;background-position:0 -412px;}
.pop1{height:524px;background:url(../img/pop1.png) no-repeat;}
.inflist{padding-top:90px;width:322px;margin:0 auto;}
.inflist li,.bmtips{padding:11px 0;border-bottom:1px dotted #580701;color:#595959;font-size:15px;line-height:24px;}
.bmtips,.sharebox{width:322px;margin:0 auto;}
.bmtips{width:312px;padding:11px 5px;}
.inflist li{padding-left:81px;width:242px;position:relative;}
.inflist li em{position:absolute;top:11px;left:5px;}
.inflist strong{color:#bb2a3e;font-weight:bold;}
.sharebox{padding:4px 5px 0;color:#595959;font-size:15px;line-height:24px;}
.sharebox div{padding:7px 0 0 5px;height:63px;}
.sharebox div a{width:63px;height:63px;float:left;margin:0 28px 0 0;background-position:0 -503px;}
.sharebox div a.shareToqq{background-position:-93px -503px;}
.pop2,.fcbox{width:455px;height:338px;background:url(../img/pop2.png) no-repeat;}
.comtip{padding:80px 10px;text-align:center;display:flex;justify-content:center;align-items:center;height:100px;font-size:16px;color:#bb293e;}
.fcbox{position:absolute;top:-270px;left:50%;margin-left:-227px;padding-top:49px;height:289px;display:none;}
.fcbox p{color:#595959;font-size:14px;line-height:34px;height:34px;padding: 0 0 1px 68px;width: 340px;}
.fcbox p span{padding-left:42px;color:#bb293e;}
.fcbox p span.desc{white-space: initial; word-break: break-word; }
/*sub 报名页*/
.wrapsub{background:url(../img/wbg1.jpg) no-repeat center top;color:#ffc5c5;}
.wrapsub .head{height:1035px;background:url(../img/headsub.jpg) no-repeat center top;}
.wrapsub .dj_logo_lk,.wrapsub .dj_logo_lk a,.wrapsub .dj_logo_bg{margin-left:-317px;}
.zhulist{color:#ffc5c5;font-size:18px;line-height:34px;top:904px;left:602px;}
.zhulist li{position:relative;}
.zhulist li span{position:absolute;left:-37px;top:0;}
.lebar{padding-top:167px;width:554px;float:left;}
.lebar div{padding-top:31px;}
.lebar label{color:#ffc5c5;font-size:19px;font-weight:bold;width:262px;padding-right:12px;float:left;text-align:right;line-height:36px;}
.lebar input,.lebar select,.lebar textarea{background:#440000;border:1px solid #ff0000;padding:0 8px;width:222px;height:34px;color:#fff;font-size:16px;resize:none;float:left;}
.lebar select{width:238px;background:#440000 url(../img/selbg.png) no-repeat right top;}
.lebar textarea{height:67px;padding:5px;}
.lebar span{float:left;color:#ff5d5d;font-size:18px;padding-left:12px;line-height:36px;}
.tishi{color:#ff5d5d;font-size:14px;padding:0 0 0 274px;width:240px;line-height:18px;margin:6px 0 -18px;}
.ribar{float:left;width:554px;padding-top:198px;}
.picbox{width:474px;height:304px;background:#fff;margin:0 auto;position:relative;overflow:hidden;}
.admin-tip {
    width: 100%;
    position: absolute;
    margin: auto;
    top: 0;
    bottom: 0;
    z-index: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ff5d5d;
    font-size: 16px;
}
.picbox .img-upload-tips{display: none; position: absolute; top:0; left:0; z-index: 3; background: rgba(0, 0, 0, .8); color:#fff;font-size: 20px; width: 100%; height: 100%;}
.img-upload-tips .tip-con{position: absolute; top: 50%; transform:translateY(-50%); width: 100%; left: 0; text-align: center;}
.tip-line{padding-left: 30px; text-align: left; color:#ffc5c5;}
.plus,.minus,.save{bottom:8px;width:40px;height:40px;z-index:2;position:absolute;cursor:pointer;background:url(../img/icon.png) no-repeat;text-indent:-9999em;}
.plus{right:64px;}
.minus{right:10px;background-position:right top;}
.tips{text-align:center;color:#ff5d5d;font-size:16px;margin-top:7px;}
.tjbtn{margin:12px auto 0;width:249px;height:72px;background-position:0 -654px;}
.tjbtn.canuse{background-position:0 -574px;}
.tjbtn.disabled{filter: grayscale(100%); cursor: not-allowed;}
.piclist{margin:21px auto 0;width:486px;height:118px;}
.piclist li{width:150px;height:118px;float:left;margin:0 6px;display:inline;background:#fff;position:relative;overflow:hidden;}
.piclist li em{position:absolute;top:0;right:0;z-index:10;cursor:pointer;display:none;width:34px;height:34px;background:url(../img/close.jpg) no-repeat;text-indent:-999em;}
.piclist input{width:100%;height: 100%;position: absolute;top: 0;left: 0;opacity: 0;cursor: pointer;z-index:3;}
.piclist span{cursor:pointer;width:100%;color:#999;font-size:14px;text-align:center;display:block;padding-top:74px;height:43px;position:relative;z-index:2;}
.piclist span::after{width:37px;height:37px;position:absolute;content:'';background-position:-308px -685px;top:29px;left:50%;margin-left:-18px;}
.piclist li div{position:absolute;top:0;left:0;z-index:1;background:#fff;width:150px;height:118px;}
.bigbmbtn{width:277px;height:72px;background-position:0 -754px;margin:31px auto 0;}
.piclist li.yypic input,.piclist li.yypic span{display:none;}
.piclist li.yypic em{display:block;}
.piclist li div.mrbox{display:none;position:absolute;z-index:6;}
.mrbox img{width:100%;display:block;}

#share{display: none;}

.text-center{text-align: center;}
.text-left{text-align: left;}
.txt-red{color:#ff4949;}

.bgbox td.text-left{text-align: left;}
