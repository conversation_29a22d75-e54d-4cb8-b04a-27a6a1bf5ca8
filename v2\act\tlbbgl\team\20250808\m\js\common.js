(function () {
  function App() {
    const that = this;

    this.data = {
      menpaiData: {
        1: { name: "峨嵋" },
        2: { name: "丐帮" },
        3: { name: "明教" },
        4: { name: "少林" },
        5: { name: "天龙" },
        6: { name: "天山" },
        7: { name: "武当" },
        8: { name: "逍遥" },
        9: { name: "星宿" },
      },
      avatars: {
        1: "avatar1.png",
        2: "avatar2.png",
        3: "avatar3.png",
        4: "avatar4.png",
      },
      giftInfo: {
        T1: {
          giftName: "金蚕丝*8、土灵珠*1",
          giftType: "2人",
        },
        T2: {
          giftName: "天罡强化精华*5、乾坤壶*1",
          giftType: "4人",
        },
        T3: {
          giftName: "缝纫材料自选礼盒*1、回天神石*2",
          giftType: "6人",
        },
        T4: {
          giftName: "珍兽技能书礼包*1、乾坤壶*1",
          giftType: "登录游戏",
        },
        T5: {
          giftName: "天罡强化精华*3、忘无石*3",
          giftType: "50级",
          userLevel: 50,
        },
        T6: {
          giftName: "金蚕丝*10、高级珍兽还童天书*3",
          giftType: "60级",
          userLevel: 60,
        },
        T7: {
          giftName: "工艺材料自选礼盒*1、润魂石·击（1级）*3",
          giftType: "68级",
          userLevel: 68,
        },
      },
    };

    function getAvatar(headNo) {
      const maxAvatar = Math.max.apply(null, Object.keys(that.data.avatars));
      const key = headNo > maxAvatar ? maxAvatar : headNo;
      return "/act/tlbbgl/team/20250808/m/img/" + that.data.avatars[key];
    }

    this.ajax = function (l, d, opt) {
      let loading;
      return new Promise((resolve, reject) => {
        const env = 3; // 1-内网IP接口  2-内部服务器接口  3-畅游测试接口  4-畅游正式接口
        const domainMap = {
          1: "http://changyoumenpai2.com",
          2: "https://admin.csfullspeed.com",
          3: "https://test-api-tlbbglact.changyou.com",
          4: "https://api-tlbbglact.changyou.com",
        };
        const domain = domainMap[env];
        const activeName = "menpai";
        const path = `${[3, 4].includes(env) ? "/public" : ""}/index.php${
          [3, 4].includes(env) ? "/" : "?s="
        }changyou/${activeName}/`;
        const url = domain + path + l;

        const data = d || {};
        let verifyToken = $.tools.getStorage("token");
        if (l !== "login" && verifyToken) {
          data.verifyToken = verifyToken;
        }

        if (opt && opt.loading) loading = lay.load();
        $.ajax({
          url: url,
          type: "POST",
          data: data,
          dataType: "json",
          success: function (res) {
            if (opt && opt.loading) lay.close(loading);
            resolve(res);
          },
          error: function (err) {
            if (opt && opt.loading) lay.close(loading);
            lay.msg("接口请求繁忙，请稍后重试");
            reject(err);
          },
        });
      });
    };

    
    this.getIsLogin = function () {
      const token = $.tools.getStorage("token");
      if (!token) {
        that.loginPop();
        return false;
      }
      return true;
    };

    this.loginPop = function () {
      if ($(".page2 .btn-login").hasClass("timeover"))
        return that.alert("当前不在活动时间内");
      const base_url = $.tools.getUrl;
      const params = $.tools.getRequest();
      params.base_url = base_url;
      const ml = $.tools.encodeBase64URLSafeString(JSON.stringify(params));
      const state = $.tools.encodeBase64URLSafeString(
        encodeURIComponent(location.search)
      );
      let s = location.href.replace(location.search, "");
      const src =
        "https://auth.changyou.com/interfaceLogin?project=tlglactivity&s=" +
        s +
        "&state=" +
        state +
        `&gameType=tlgl&ml=${ml}`;
      $.tools.setStorage("URL_BEFORE_LOGIN", s);
      $("#popLogin .login-box").html(
        `<iframe src="${src}" frameborder="0" width="100%" height="100%" scrolling="no"></iframe>`
      );
      lay.open("popLogin");
    };

    this.login = function () {
      const ticket = $.tools.getStorage("ticket");
      if (!ticket) return Promise.resolve({ code: -1, msg: "没有ticket" });
      // 登录
      const urlBeforeLogin = $.tools.getStorage("URL_BEFORE_LOGIN");
      if (!urlBeforeLogin)
        return Promise.resolve({ code: -1, msg: "没有传登录前的url" });
      const s_url = urlBeforeLogin;
      const data = { ticket, s_url };
      const uid = $.tools.getParam("uid");
      const CYSCID = $.tools.getParam("type");
      if (CYSCID) data.CYSCID = CYSCID;
      if (uid) data.shareUid = uid;
      return new Promise((resolve) => {
        that
          .ajax("login", data)
          .then((res) => {
            $.tools.removeStorage("ticket");
            if (res.code === -20003) {
              that.alert("您的账号已停权", that.logout);
            }
            if (res.code === -21003) {
              lay.msg("分享参数错误");
              setTimeout(function () {
                location.href = $.tools.removeParam(location.href, "uid");
              }, 1500);
              return;
            }
            if (res.code === 0) {
              const token = uid ? res.data.userData.token : res.data.token;
              $.tools.setStorage("token", token);
              // 重新拉取初始话数据
              _checkLogin().then((res) => {
                resolve(res);
              });
            } else {
              resolve(res);
            }
          })
          .catch((res) => {
            if (res.code === -20003) {
              that.alert("您的账号已停权", that.logout);
              return;
            }

            if (res.code === -21003) {
              lay.msg("分享参数错误");
              setTimeout(function () {
                location.href = $.tools.removeParam(location.href, "uid");
              }, 1500);
              return;
            }
            resolve(res);
          });
      });
    };

    function _checkLogin() {
      const uid = $.tools.getParam("uid");
      const data = {};
      if (uid) data.shareUid = uid;
      const CYSCID = $.tools.getParam("CYSCID");
      if (CYSCID) data.CYSCID = CYSCID;
      const url = uid ? "pageIndex" : "index";
      return that.ajax(url, data);
    }

    this.checkLogin = function () {
      let verifyToken = $.tools.getStorage("token");
      // 缓存是否有token
      //  -无 缓存token，则表示还未登录 =>
      if (!verifyToken) {
        if ($.tools.getParam("ticket")) {
          $.tools.setStorage("ticket", $.tools.getParam("ticket"));
          const href = $.tools.removeParam(location.href, "ticket");
          return (window.location.href = $.tools.removeParam(href, "state"));
        }
        //       判断缓存中是否有 ticket 参数，有则
        let ticket = $.tools.getStorage("ticket");
        if (ticket) {
          //       -如果有，则调login接口
          //          -接口正常code=0,拿到token，并缓存
          //          -接口异常，则登录失败返回错误信息
          return this.login();
        } else {
          //       -如果无ticket参数，返回未登录
          return Promise.resolve({ code: -1, msg: "未登录" });
        }
      }
      //  -有 缓存token，调初始话接口，拉取登录信息 =>
      //       -接口正常code=0，则登录成功，返回登录信息
      //       -接口异常，登录失败，返回错误信息
      else {
        return new Promise((resolve) => {
          _checkLogin()
            .then((res) => {
              if (res.code === -20003) {
                that.alert("您的账号已停权", that.logout);
              }

              if (res.code === -21003) {
                lay.msg("分享参数错误");
                setTimeout(function () {
                  location.href = $.tools.removeParam(location.href, "uid");
                }, 1500);
                return;
              }

              resolve(res);
            })
            .catch((res) => {
              if (res.responseJSON) res = res.responseJSON;
              resolve(res);
            });
        });
      }
    };

    function checkRedDot() {
      that.ajax("applyList", {}).then((res) => {
        const { code, msg } = res;
        that.data.applyList = code === 0 ? res.data : { list: [] };
        if (code !== 0) return $(".btn-massM").removeClass("red_dot");
        $(".btn-massM")[
          that.data.user_info.menberNum < 6 && res.data.list.length > 0
            ? "addClass"
            : "removeClass"
        ]("red_dot");
        that.data.teamMembers = res.data.teamMembers;
        that.data.giftData = res.data.giftData;
        renderApplyList();
      });
    }
    // TODO:init
    this.init = function () {
      const ticket = $.tools.getParam("ticket");
      if (ticket) {
        $.tools.setStorage("ticket", ticket);
        const state = $.tools.getParam("state");
        let href = location.href;
        const search = decodeURIComponent(
          $.tools.decodeBase64URLSafeString(state)
        );
        href = $.tools.getUrl + search + location.hash;
        return (location.href = href);
      }
      $(".btn-logout").hide();

      tracker.pv_uv();

      setWxShare();

      document.addEventListener(
        "touchstart",
        function (event) {
          if (event.touches.length > 1) {
            event.preventDefault();
          }
        },
        { passive: false }
      );

      document.addEventListener(
        "touchmove",
        function (event) {
          if (event.touches.length > 1) {
            event.preventDefault();
          }
        },
        { passive: false }
      );

      //默认打开页面
      open("page2");
      this.bind();

      setTimeout(() => {
        that.loading_index = lay.load();

        this.checkLogin()
          .then((res) => {
            // 判断ticket参数，如果存在，以及获取登录态信息
            if (res.code === -40001) {
              // 客态分享链接uid不是队长
              if ($.tools.getParam("uid")) {
                lay.msg(res.msg);
                setTimeout(() => {
                  window.location.href = $.tools.removeParam(
                    location.href,
                    "uid"
                  );
                }, 1500);
                return;
              }
            }

            if (res.code === -90001) {
              lay.close(that.loading_index);
              that.alert("当前不在活动时间内");
              $(".page2 .btn-login").addClass("timeover");
              return;
            }

            if (res.code !== 0) {
              this.login().then((res) => {
                lay.close(that.loading_index);

                if (res.code !== 0) {
                  $(".btn-logout").hide();
                  $(".user-info .user-name").show();
                  lay.close(that.loading_index);
                  // that.logout()
                } else {
                  that.refreshUserInfo();
                }
              });
            }

            if (res.code === 0) {
              $(".btn-logout").show();
              $(".user-info .user-name").hide();
              that.data.user_info = res.data;
              handlerLoginSuccess();
            }
          })
          .catch((e) => {
            lay.close(that.loading_index);
          });
      }, 300);
    };

    this.refreshUserInfo = function () {
      this.checkLogin().then((res) => {
        if (res.code === 0) {
          that.data.user_info = res.data;
          handlerLoginSuccess();
        }
      });
    };

    // 渲染页面
    function renderPage() {
      $(".ewmtn").show();
    }

    function handlerLoginSuccess() {
      lay.close(that.loading_index);
      $(".btn-logout").show();
      const uid = $.tools.getParam("uid");
      // 登录信息获取正常，拿到了用户信息
      // 判断是否已入队，判断是否为队长
      const user_info = !uid
        ? that.data.user_info
        : that.data.user_info.userData;

      const { leaderUin, isLeader } = user_info;

      // 清空原始内容
      $(".add-list .name").html("<span>待加入</span>");
      $(".add-list .avatar").empty();
      $(`.page .schedule`).css("width", `0px`);

      that.data.teamMembers = that.data.user_info.teamData;

      // 判断是否有uid邀请码，如果有，则打开受邀页面
      if (uid) {
        if (leaderUin)
          return (location.href = $.tools.removeParam(location.href, "uid"));
        let { nickname } = that.data.user_info.inviterUser;
        $(".J_username").text(`[${nickname}]`);
        open("page5");
        $(".page5 .user-info").css({
          marginRight: "250px",
        });
      } else {
        if (leaderUin) {
          const { nickname, cn } = that.data.user_info;
          $(".J_username").text(`[${nickname}]`);
          $(".J_myusername").text(`${cn}`);

          renderIndexGiftStatus();

          if (isLeader) {
            checkRedDot();
            open("page3");
          } else {
            open("page4");
          }
        } else {
          open("page2");
          renderMenpaiCount();
        }
      }
      handlerTeamMembers();
      renderPage();

      if ($.tools.browser.versions.weixin) {
        setWxShare();
      }
    }

    // 渲染门派当前加入人数数据
    function renderMenpaiCount() {
      const { menpaiCount, menpaiNo } = that.data.user_info;
      const ul = $("#page2 ul");
      ul.children("li").each(function (i, item) {
        const countDom = $(this).find("span");
        const menpaiNo = i + 1;
        const menpaiItem = menpaiCount.find(
          (item) => item.menpaiNo * 1 === menpaiNo
        );
        const count = menpaiItem ? menpaiItem.count : 0;
        countDom.text(count);
      });
    }

    // 跳转到指定模块
    function open(page) {
      $(".page").hide();
      $("#" + page).show();
    }

    this.open = open;

    this.goBind = function () {
      location.href =
        "//member.changyou.com/myphone/mobileModifyPhone.do?from=tlgl&status=1";
    };

    this.openPop = function (id) {
      lay.closeAll();
      lay.open(id);
    };

    this.ewm = function () {
      that.openPop("ewmPop");
    };

    function copyMyLink() {
      lay.closeAll();
      if (
        clipBordCopy(
          "你愿为哪个门派荣光而战？点击组队，必得三精、金蚕丝和强化等高级养成材料-《天龙八部·归来》！" +
            $(".lianjie").val()
        )
      ) {
        lay.msg("复制成功");
      } else {
        lay.msg("复制失败");
      }
    }

    function getTeamMembers(load) {
      that
        .ajax("taskComplete", {}, { loading: load !== "close" })
        .then((res) => {
          that.data.teamMembers = res.data.teamMembers;
          that.data.giftData = res.data.giftData;
          if (res.code === 0) {
            // 渲染小队成员
            renderTeamMemberList();
            handlerTeamMembers();
            renderGiftStatus();
          }
        });
    }

    // 渲染队伍信息，主态、客态同步渲染
    function handlerTeamMembers() {
      const { teamMembers } = that.data;

      // 渲染首页的队伍成员
      let nth = 1;
      teamMembers?.forEach((item, index) => {
        const { nickname, headNo } = item;
        const avatar = getAvatar(headNo);
        const html = `<img src="${avatar}" alt="${nickname}" />`;
        const li = $(
          `.add-list li:nth-child(${item.isLeader > 0 ? 1 : ++nth})`
        );
        li.find(".avatar").html(html);
        li.find(".name").html(`<span>${nickname}</span>`);
      });

      // 如果是主态渲染小队任务领奖状态
      if (!$.tools.getParam("uid")) {
        $(".btn-member").show();
        $(".btn-home").show();
      } else {
        // 客态页只展示未达到状态
        $(".task-list li .btn-taskGet").removeClass("get gray");
        $(".btn-member").hide();
        $(".btn-home").hide();
      }
    }

    // 渲染首页的任务领奖状态
    function renderIndexGiftStatus() {
      const {
        user_info: { giftData },
        teamMembers,
      } = that.data;
      let memberNum = teamMembers.length;
      // 1人：60px  /    2人：130px  /    3:216px  /    4:310px  /    5:310px  /    6:600px
      const number = ["0", "60px", "130px", "216px", "310px", "410px", "600px"];
      $(`.page .schedule`).css(
        "width",
        `${number[memberNum > 6 ? 6 : memberNum]}`
      );

      $(".schedule-box .item .xz").removeClass("gray get");
      const classMap = { 0: "", 1: "get", 2: "gray" };
      $(".schedule-box .item:nth-child(1) .xz").addClass(classMap[giftData.T1]);
      $(".schedule-box .item:nth-child(2) .xz").addClass(classMap[giftData.T2]);
      $(".schedule-box .item:nth-child(3) .xz").addClass(classMap[giftData.T3]);

      $(".schedule-box .people").removeClass("active");
      if (memberNum >= 6) {
        $(".schedule-box .people").addClass("active");
      } else if (memberNum >= 4) {
        $(".schedule-box .item:nth-child(-n + 2) .people").addClass("active");
      } else if (memberNum >= 2) {
        $(".schedule-box .item:nth-child(-n + 1) .people").addClass("active");
      }
    }

    // 渲染小队任务领奖状态
    function renderGiftStatus() {
      const { teamMembers, giftData } = that.data;
      const memberNum = teamMembers.length;

      // 渲染领取状态
      // $('.schedule-box .xz').addClass('gray')
      // 默认待领取（不可领），get-待领取，gray-已领取
      // 判断是否已领取
      const taskList = {
        T4: {
          got: false,
          get: false,
          count: 0,
        },
        T5: {
          got: false,
          get: false,
          count: 0,
        },
        T6: {
          got: false,
          get: false,
          count: 0,
        },
        T7: {
          got: false,
          get: false,
          count: 0,
        },
      };
      giftData.forEach((item) => {
        const giftId = item.giftId;
        if (Object.keys(taskList).includes(giftId)) {
          taskList[giftId].got = true;
        }
      });

      // 判断是否可领取，判断规则，任意3人达成任务，则算该完成任务
      teamMembers.forEach((item, index) => {
        if (item.isLoginGame === 1) taskList.T4.count++;
        if (item.level >= that.data.giftInfo.T5.userLevel) taskList.T5.count++;
        if (item.level >= that.data.giftInfo.T6.userLevel) taskList.T6.count++;
        if (item.level >= that.data.giftInfo.T7.userLevel) taskList.T7.count++;
      });

      Object.keys(taskList).forEach((key, index) => {
        const { got, count } = taskList[key];
        if (!got && count >= 3) taskList[key].get = true;
        const _class = taskList[key].get ? "get" : got ? "gray" : "";
        const li = $(`.task-list li:nth-child(${index + 1})`);
        li.find(".btn-taskGet").removeClass("get gray").addClass(_class);
      });
    }

    // 根据等级来判断当前已完成到哪个任务
    function getUserLevel(level) {
      const { giftInfo } = that.data;
      if (level >= giftInfo.T7.userLevel)
        return `达成${giftInfo.T7.userLevel}级`;
      if (level >= giftInfo.T6.userLevel)
        return `达成${giftInfo.T6.userLevel}级`;
      if (level >= giftInfo.T5.userLevel)
        return `达成${giftInfo.T5.userLevel}级`;
    }
    // 渲染小队成员
    function renderTeamMemberList() {
      const { teamMembers, giftInfo } = that.data;
      const ul = $("#state .user-list");
      ul.empty();
      if (!teamMembers) return;
      const html = teamMembers
        .map((item) => {
          const { nickname, headNo, level, isLoginGame } = item;
          const avatar = getAvatar(headNo);
          const userLevel = getUserLevel(level);
          return `<li>
                            <div>
                                <div class="user">
                                    <div class="avatar"><img src="${avatar}" alt=""></div>
                                    <p class="name">${nickname}</p>
                                </div>
                                <div class="btn-boxPop">
                                     ${
                                       level < giftInfo.T5.userLevel
                                         ? `<a href="javascript:;" class="btn-unLogin t ${
                                             isLoginGame === 1 ? "logined" : ""
                                           }">未/已登录</a>`
                                         : `<span class='user-level'>${userLevel}</span>`
                                     }
                                </div>
                            </div>
                        </li>`;
        })
        .join("\n");
      ul.append(html);
      ul[teamMembers.length <= 0 ? "addClass" : "removeClass"]("no-data");
    }

    // 申请列表
    this.applyList = function () {
      lay.open("apply");
      that.getApplyList();
    };

    this.getApplyList = function (load) {
      this.ajax("applyList", {}, { loading: load !== "close" }).then((res) => {
        const { code, msg } = res;
        that.data.applyList = code === 0 ? res.data : { list: [] };
        if (code !== 0) return that.alert(msg);
        renderApplyList();
      });
    };

    function renderApplyList() {
      const { list } = that.data.applyList;
      const ul = $("#apply .user-list");
      ul.empty();
      const html = list
        .map((it) => {
          return `
                        <li>
                            <div>
                                <div class="user">
                                    <div class="avatar"><img src="${getAvatar(
                                      it.headNo
                                    )}" alt=""></div>
                                    <p class="name">${it.nickname}</p>
                                </div>
                                <div class="btn-boxPop">
                                    <a href="javascript:;" class="btn-agree t" data-uid="${
                                      it.userId
                                    }" data-status="1">同意</a>
                                    <a href="javascript:;" class="btn-refuse t" data-uid="${
                                      it.userId
                                    }" data-status="0">拒绝</a>
                                </div>
                            </div>
                        </li>`;
        })
        .join("\n");
      ul.append(html);
      ul[list.length <= 0 ? "addClass" : "removeClass"]("no-data");
      $(".btn-massM")[
        that.data.user_info.menberNum < 6 && list.length > 0
          ? "addClass"
          : "removeClass"
      ]("red_dot");
    }

    // 客态点击申请入队
    this.applyJoin = function () {
      const { headNo, nickname } = that.data.user_info.userData;
      // 判断是否有填写昵称和选择头像
      if (nickname&&headNo) {
        // 未填写过昵称，则弹窗确认使用已有昵称头像加入小队
        const avatar = getAvatar(headNo);
        $("#userinfo-on .avatar img").attr("src", avatar);
        $("#userinfo-on .ipt-name").val(nickname);
        $(".team_type").text($.tools.getParam("uid") ? "加入" : "创建");
        lay.open("userinfo-on");
      } else {
        // 未填写过
        lay.open("userinfo");
      }
    };

    // 确认申请入队
    this.confirmApply = function () {
      const { leaderUin, phone } = that.data.user_info.userData;

      // 判断是否已入队，如果已入队，则弹窗提示已加入其他队伍，确定后返回主页
      if (leaderUin) {
        that.alert("您已加入其他队伍~", function () {
          lay.close("common");
          location.href = $.tools.removeParam(location.href, "uid");
        });
        return;
      }

      that
        .ajax("applyJoin", {
          inviterUserId: $.tools.getParam("uid"),
        })
        .then((res) => {
          const { code } = res;

          // 未绑定手机：
          if (code === -44444) return lay.open("common2");

          // 判断是否有绑定手机： -44440, '您的手机号已加入过其他小队，无法再次加入小队'
          if (code === -44440) {
            return lay.open("hasJoin");
          }

          // 判断当前小队是否满员：缺少错误码：
          if (code === -34006)
            return that.alert(
              "您申请小队已满，你可以创建自己的小队或者去加入其他小队~"
            );

          // 判断是否正在申请加入此小队：缺少错误码：
          if (code === -55555)
            return that.alert("您已申请加入此小队，请等待审核~");

          if (code === 0) {
            that.alert("您成功提交申请，请等待小队创建人的通过。");
            that.refreshUserInfo();
          }
        });
    };

    // 创建小队
    this.createTeamSelf = function () {
      that
        .ajax("createTeam", {
          nickname: that.data.user_info.nickname,
          headNo: that.data.user_info.headNo,
          menpaiNo: that.data.user_info.menpaiNo,
        })
        .then((res) => {
          that.refreshUserInfo();
          if (res.code !== 0) return that.alert(res.msg);
          // 队伍创建成功，跳转到队长首页
          // 需要重新拉取个人信息
          lay.msg("创建小队成功！");
          that.open("page3");
        });
    };

    // 保存昵称头像
    this.saveName = function () {
      const nickname = $("#userinfo .ipt-name").val();
      const headNo = $("#userinfo .avatar-list .active").data("headno");
      if (!nickname) {
        that.alert("请填写昵称");
        return Promise.reject({ code: -1, msg: "请填写昵称" });
      }
      if (!(nickname.length >= 2 && nickname.length <= 16)) {
        that.alert("请输入2-16位字符");
        return Promise.reject({ code: -1, msg: "请输入2-16位字符" });
      }
      //  $pattern = '/^[\x{4e00}-\x{9fa5}a-zA-Z0-9\p{P}]{2,16}$/u';
      return that.ajax("saveName", {
        name: nickname,
        headNo: headNo,
      });
    };

    this.giftReceive = function (giftId) {
      that.ajax("giftReceive", { giftId }).then((res) => {
        if (res.code !== 0) return that.alert(res.msg);
        if (res.code === 0) {
          let msg = `奖励领取成功`;
          if (["T1", "T2", "T3"].includes(giftId)) {
            const { giftName, giftType } = that.data.giftInfo[giftId];
            msg = `<div class="t-l"><div class="t-c">恭喜您，小队人数已达${giftType}！</div><br />获得${giftName}，请在7月25日《天龙八部·归来》公测开服后前往大理龚彩云处领取。<br />登陆游戏完成小队任务，还可领取三精等豪华大礼！</div>`;
          }
          that.alert(msg);
          that.refreshUserInfo();
        }
      });
    };

    // 获得分享链接
    function getShareLink() {
      let link = location.href;
      if (that.data.user_info?.leaderUin) {
        const { leaderUin } = that.data.user_info;
        link = $.tools.replaceParam(link, "uid", leaderUin);
      }
      return link;
    }
    // 获得微信分享参数
    function getWxShareData() {
      // ja方式
      return {
        isWxDebug: false,
        title: "玩轻松天龙，组队得三精",
        desc: "九大门派归来，组队必得三精、金蚕丝和强化等高级养成材料！",
        link: getShareLink(),
        imgUrl:
          "https://" +
          location.host +
          "/act/tlbbgl/team/20250319/m/img/share.png",
        success: function () {},
      };
    }
    // 设置微信分享参数
    function setWxShare() {
      if ($.tools.browser.versions.weixin) {
        const shareData = getWxShareData();
        $.wechat.share(shareData);
      }
    }

    //集结小队战友
    this.share = function () {
      tracker.click_share();
      // 预约集结，点击预约集结按钮，如果在微信内，则引导玩家进行分享，微信内分享后显示小卡片；如果在非微信内，则弹出分享链接提示
      if ($.tools.browser.versions.weixin) {
        setWxShare();
        lay.open("shareWX");
      } else {
        $(".ipt-copy").val(getShareLink());
        lay.open("share");
      }
    };

    this.logout = function () {
      $.tools.removeStorage("token");
      $.tools.removeStorage("LOGIN_INFO");
      const b = encodeURIComponent(location.href);
      location.href = `https://auth.changyou.com/logout?b=${b} `;
    };

    this.bind = function () {
      // 通用弹窗里的点击关闭按钮
      $("#common .btn-confirm").click(function () {
        lay.close("common");
      });

      // TODO:登录
      $(".btn-login").click(function () {
        that.loginPop();
      });

      // 退出登录
      $(".btn-logout").click(function () {
        that.logout();
      });

      //复制我的邀请链接
      $(".pop-share input").click(function () {
        copyMyLink();
      });

      //分享（微信内关闭
      $(".pop-shareWX").on("click", function () {
        lay.closeAll();
      });

      // 点击客态页中的创建自己的小队
      $(".btn-create, .btn-createSelf").click(function () {
        // 是否登录
        if (!that.getIsLogin()) return;

        that.applyJoin();
        location.href = $.tools.removeParam(location.href, "uid");
      });

      //创建队伍-选中
      $(".page2 li").click(function () {
        $(this).closest("ul").find("li").removeClass("active");
        $(this).addClass("active");
      });

      // 确认选择门派
      $(".btn-confirm-menpai").click(function () {
        if ($(".page2 .active").length === 0) return that.alert("请选择门派");
        const menpaiNo = $(".page2 .active").index() + 1;
        const menpaiName = that.data.menpaiData[menpaiNo].name;
        $(".menpaiName").text(menpaiName);
        that.data.user_info.menpaiNo = menpaiNo;

        if (that.data.user_info.nickname) {
          const { nickname, headNo } = that.data.user_info;
          // 未填写过昵称，则弹窗确认使用已有昵称头像加入小队
          const avatar = getAvatar(headNo);
          $("#userinfo-on .avatar img").attr("src", avatar);
          $("#userinfo-on .ipt-name").val(nickname);

          $(".team_type").text($.tools.getParam("uid") ? "加入" : "创建");
          lay.open("userinfo-on");
        } else {
          lay.open("userinfo");
        }
      });

      $(".avatar-list li").click(function () {
        $(this).addClass("active").siblings().removeClass("active");
      });

      // 没有头像确定使用新头像昵称
      function _saveName() {
        const uid = $.tools.getParam("uid");
        // 主态，保存后进行创建队伍，客态保存后走申请入队
        that.saveName().then((res) => {
          if (res.code !== 0) return lay.msg(res.msg);
          if (res.code === 0) {
            lay.close("userinfo");
            const nickname = $("#userinfo .ipt-name").val();
            const headNo = $("#userinfo .avatar-list .active").data("headno");
            if (!uid) {
              that.data.user_info.headNo = headNo;
              that.data.user_info.nickname = nickname;
              that.createTeamSelf();
            } else {
              that.data.user_info.userData.headNo = headNo;
              that.data.user_info.userData.nickname = nickname;
              that.confirmApply();
            }
          }
        });
      }
      $("#userinfo .btn-confirm").click(
        $.tools.debounce(_saveName, 1000, true)
      );

      // 使用已有的昵称和头像确定申请入队
      function _confirmUseName() {
        lay.close("userinfo-on");
        const uid = $.tools.getParam("uid");
        if (!uid) {
          that.createTeamSelf();
        } else {
          that.confirmApply();
        }
      }
      $("#userinfo-on .btn-confirm").click(
        $.tools.debounce(_confirmUseName, 1000, true)
      );

      // 取消使用已有昵称
      $("#userinfo-on .btn-cancel").click(function () {
        lay.close("userinfo-on");
        // lay.open('userinfo')
      });

      $(".btn-rule").click(function () {
        lay.open("rule");
      });

      $(".btn-home").click(function () {
        that.ajax("giftRecord", {}, { loading: true }).then((res) => {
          const ul = $(".gift-list");
          ul.empty();
          if (res.code !== 0) {
            ul.addClass("no-data");
            return lay.msg(res.msg);
          }
          if (res.code === 0) {
            const list = res.data.record;
            if (list.length === 0) {
              $(".record-txt").text(
                "您暂未获得奖励，请在页面参加活动领取奖励后查看获奖记录。"
              );
              return ul.addClass("no-data");
            }
            $(".record-txt").text(
              "您已成功获得以下奖励，请开服后前往游戏内领取"
            );
            if (list.length === 0) return ul.addClass("no-data");
            ul.removeClass("no-data");
            const html = list
              .map((item) => {
                const { giftId, giftName } = item;
                let { giftType: giftTypeName, giftName: giftNameLocal } =
                  that.data.giftInfo[giftId];
                if (["T1", "T2", "T3"].includes(giftId)) {
                  giftTypeName = `集结${giftTypeName}`;
                }
                return `
                                <li>
                                    <div class="col1">${giftTypeName}</div>
                                    <div class="col2">${giftNameLocal}</div>
                                </li>`;
              })
              .join("\n");
            ul.html(html);
          }
        });
        lay.open("userCenter");
      });

      // 申请小队
      $(".btn-massM").click($.tools.throttle(()=>{if (!that.getIsLogin()) return;that.applyList()}, 550, 2));

      // 同意拒绝
      $("#apply .user-list").on("click", ".btn-boxPop a", function () {
        const el = $(this);
        if (el.hasClass("disabled")) return;
        el.addClass("disabled");
        const status = $(this).data("status");
        const uid = $(this).data("uid");
        const load = lay.load(1);
        that
          .ajax("passApply", {
            applyUserId: uid,
            status: status,
          })
          .then((res) => {
            lay.close(load);
            el.removeClass("disabled");
            if ([-35002, -35003, -44440].includes(res.code)) {
              that.alert(res.msg);
              setTimeout(() => {
                that.refreshUserInfo();
              }, 1000);
              return;
            }
            if (res.code !== 0) {
              return that.alert(res.msg);
            }
            lay.msg(res.msg);
            setTimeout(() => that.refreshUserInfo(), 400);
          });
      });

      // 小队任务
      $(".btn-massT").click(function () {
        // 是否登录
        if (!that.getIsLogin()) return;
        getTeamMembers();
        that.open("page6");
      });

      // 小队成员
      $(".btn-member").click(function () {
        lay.open("state");
      });

      //进度-点击箱子查看-奖品明细
      var giftArr = {
        0: ["金蚕丝*8", "财富通宝*2"],
        1: ["天罡强化精华*5", "乾坤壶*1"],
        2: ["缝纫材料自选礼盒*1", "回天神石*2"],
      };
      $("body").click(function (e) {
        const target = $(e.target);
        if (target.hasClass("xz")) {
          const giftItem = $(target).closest(".item");
          const index = giftItem.index();
          const parentId = $(target).closest(".page").attr("id");

          $(".gift-detail ul").empty();
          const currentGifts = giftArr[index] || [];
          currentGifts.forEach((item) => {
            $(".gift-detail ul").append(`<li>${item}</li>`);
          });

          $(`.${parentId} .gift-detail`)
            .css(
              "left",
              `${index === 0 ? "-29px" : index === 1 ? "165px" : "367px"}`
            )
            .show();
        } else {
          $(`.gift-detail`).hide();
        }
      });
      //任务页-查看奖励说明-悬浮窗
      $(document).ready(function () {
        const $pop = $(".task-pop").clone();
        $(".task-gift").on("click", function (e) {
          var text = $(this).find(".name").text();
          var test = "工艺材料自选礼盒*1";
          text =
            text !== test
              ? text
              : "80级工艺材料礼盒*2或70级工艺材料礼盒*4或60级工艺材料礼盒*6任选其一";
          $pop.find("p").text(text);
          e.stopPropagation();
          const $gift = $(this);
          $gift.append($pop);
          $pop.show();
        });
        $(document).on("click", function () {
          $pop.hide();
        });
      });
      // 领取队伍人数达成奖励
      $(".schedule-box .xz").on("click", function () {
        if ($(this).hasClass("get")) {
          const giftId = ["T1", "T2", "T3"][$(this).parents(".item").index()];
          that.giftReceive(giftId);
        }
      });

      // 领取等级任务达成奖励
      $(".task-list li").on("click", ".btn-taskGet.get", function () {
        const giftId = ["T4", "T5", "T6", "T7"][$(this).parents("li").index()];
        that.giftReceive(giftId);
      });

      // 返回首页
      $(".btn-backHome").click(function () {
        const uid = $.tools.getParam("uid");
        const openEnum = { "true": ()=>open("page3"), "false": ()=>open("page4") };
        if (uid) {
          open("page5");
        } else {
          // 身份
          const { isLeader, leaderUin } = !uid
            ? that.data.user_info
            : that.data.user_info.userData;
          console.log(Boolean(isLeader), openEnum['true']);
          if (leaderUin) {
            openEnum[isLeader !== 0 ? "true" : "false"]();
          } else {
            open("page2");
          }
        }
      });

      // 返回首页
      $(".btn-close").click(function () {
        const popId = $(this).parents(".pop").attr("id");
        lay.close(popId);
      });
    };

    this.alert = function (msg, cbConfirm, cbCancel) {
      lay.open("common");
      $("#common .txt-box").html(msg);
      if (cbCancel && cbConfirm) {
        $("#common .btn-confirm").one("click", cbConfirm);
        $("#common .btn-close").one("click", cbCancel);
      } else if (cbConfirm) {
        $("#common .btn-confirm, #common .btn-close").one("click", cbConfirm);
      }
    };

    const versions = $.tools.browser.versions;
    if (versions.android || versions.ios || versions.ipad) {
      $(window).on("load orientationchange", handlerScreen);
    } else {
      $(window).on("load resize", handlerScreen);
    }

    function handlerScreen() {
      setTimeout(() => {
        const contbox = $(".contbox");
        const ww = $(window).width();
        const wh = $(window).height();
        const condition = ww / wh >= 375 / 667;
        const isMobile = versions.ios || versions.android || versions.ipad;
        if (condition) {
          const { versions } = $.tools.browser;
          const contHeight = 1650;
          if (versions.ios || versions.android || versions.ipad) {
            contbox.css({ height: contHeight });
          }
          let scale0 = wh / contHeight > 1 ? 1 : wh / contHeight;
          let scale = 1;
          if (!((versions.ios || versions.android) && ww / wh < 1)) {
            scale = scale0 > 1 ? 1 : scale0;
          }
          const scale1 = wh / 1334;
          const scaleCss = {
            transform:
              " translateY(-50%) scale(" +
              scale * (isMobile ? wh / 1550 : 1) +
              ")",
            top: "50%",
          };
          if (versions.ios || versions.android || versions.ipad) {
            if (wh <= 1334) {
              scaleCss.height = contHeight;
              const contH = ww / wh > 1 ? contHeight : 1334;
              $(".start_box_page").height(contH);
            } else {
              scaleCss.height = 1334;
              $(".start_box_page").height(1334);
            }
          } else {
            scaleCss.height = contHeight;
            $(".start_box_page").height(contHeight);
          }
          const scaleCss2 = {
            transform: "scale(" + scale1 + ")",
          };
          contbox.css(scaleCss);
          $(".scence_box, .page2 .result, .pop").css(scaleCss2);
        } else {
          const scale = 1;
          const scaleCss = {
            transform: " translateY(-50%) scale(" + scale + ")",
            top: "50%",
          };
          const scaleCss2 = {
            transform: "scale(" + scale + ")",
          };
          contbox.css(scaleCss);
          $(".page_bg").css(scaleCss2);
          $(".scence_box, .page2 .result, .pop").css(scaleCss2);
          $(".start_box_page").height(1334);
        }
      }, 550);
    }
  }

  window.app = new App();
  app.init();
})();
