<!DOCTYPE HTML>
<html>

<head>
	<meta charset="utf-8">
	<title>《天龙八部·归来》账号鉴定</title>
	<meta name="keywords" content="天龙八部归来，天龙八部端游，新天龙八部，经典天龙八部，怀旧天龙八部，福利，兄弟，黄金，宝石，账号鉴定，称号，" />
	<meta name="description" content="来看看你的天龙账号究竟什么水平，点击领取账号奖励和专属称号！" />
	<meta content="width=750,minimum-scale=0,maximum-scale=1,user-scalable=no" name="viewport" />
	<meta name="referrer" content="no-referrer-when-downgrade">
	<meta content="yes" name="apple-mobile-web-app-capable" />
	<meta content="black" name="apple-mobile-web-app-status-bar-style" />
	<meta content="telephone=no" name="format-detection" />
	<meta name="author" content="" />
	<meta name="robots" content="index, follow" />
	<meta name="googlebot" content="index, follow" />
	<link type="image/x-icon" href="//tlbbgl.changyou.com/favicon.ico" rel="icon" />
	<link type="image/x-icon" href="//tlbbgl.changyou.com/favicon.ico" rel="shortcut icon" />
<!--	<script class="external" src="//zhcdn01.xoyo.com/xassets/lib/vconsole/v3/vconsole.min.js"></script>-->
<!--	<script>-->
<!--		new VConsole();-->
<!--	</script>-->

	<script src="/act/all/cdn/jquery/3.4.1/jquery.min.js"></script>
	<script src="/act/tlbbgl/zhjd/20250508/m/js/tools.js"></script>
	<!--防屏蔽处理-->
	<script>
		$(function () {
			let validIndexArr = Array(20).fill(1).map((it, i) => '' + $.tools.fixNumer((i+1), 2));
			let invalidIndexArr = ['01', '05']
			validIndexArr = validIndexArr.filter(it=>(!invalidIndexArr.includes(it)))
			console.log(validIndexArr);
			var url = $.tools.getPath + 'index' + validIndexArr[$.tools.getRnd(0, validIndexArr.length - 1)] + '.shtml';
			console.log(url);
			window.location.href = url + window.location.search;
		});
	</script>
</head>

<body>
<!--#include virtual="/all/dma/dma_activity.html"-->
</body>

</html>
