@charset "utf-8";
/* CSS Document */

#certify {
    position: relative;
    width: 700px;
    height: 250px;
    margin: 0 auto;
}
#certify2 {
    position: relative;
    width: 690px;
    height: 250px;
    margin: 0 auto;
}
#certify3 {
    position: relative;
    width: 690px;
    height: 250px;
    margin: 0 auto;
}

/* 新的样式*/

.sect10 {
    position: relative;
}
.swiper-slide{
    width: 212px;
    height: 209px;
    margin: 3px 10px;
}
.slide_img{
    width: 212px;
    height: 209px;
}
.cil_desc{
    width: 158px;
    height: 74px;
    position: absolute;
    left: 26px;
    top: 74px;
    text-align: center;
}
.cil_desc .city{
    font-size: 22px;
    color: #541c00;
    font-weight: bold;
    margin-bottom: 5px;
}
.qun{
    color: #fff;
    font-size: 15px;
}
.sect10 .community-carousel .swiper-btn-prev {
    width: 26px;
    height: 26px;
    background: url(../img/council_prev.png) no-repeat;
    left: -10px;
    top: 94px;
    position: absolute;
    z-index: 10;
}

.sect10 .community-carousel .swiper-btn-next {
    width: 26px;
    height: 26px;
    background: url(../img/council_next.png) no-repeat;
    right: -10px;
    top: 94px;
    position: absolute;
    z-index: 10;
}
.swiper-wrapper{ width: 100%;height: 100%; margin: 0 auto;}
.container-carousel,.community-carousel,.swiper-container{width: 100%; height: 100%;}