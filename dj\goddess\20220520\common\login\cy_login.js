var TimerCount = function() {
    this.timer;
    this.num = 60;
    var _this = this;
    this.start = function() {
        _this.num = 60;
        $(".code_btn").hide().siblings('.code_tip').show();
        _this.timer = window.setInterval(this.btnShow, 1000);
    };
    this.clear = function() {
        _this.num = 0;
    };

    this.btnShow = function() {
        _this.num--;
        if (_this.num <= 0) {
            $(".code_btn").show().text("重新获取验证码").siblings('.code_tip').hide().text("重新获取 60S");
            window.clearInterval(_this.timer);
        } else {
            $(".code_btn").siblings('.code_tip').text("重新获取 " + _this.num + "S");
        }
    }
};
window.timerCount = new TimerCount();
$(function() {
    var Tips = function() {
        this.error = function(msg) {
            $(".error_tips").show().text(msg);
        };
        this.hide = function() {
            $(".error_tips").hide();
        }
    };
    window.tips = new Tips();
    //手机登录注册首页
    $('.login_index .form-first input.phone').on('input propertychange', function() {
        var val = $(this).val();
        if (/^1\d{10}$/.test(val)) {
            $(this).next("span").show();
            $(".login_index .form-first span.login_btn").hide();
            $(".login_index .form-first input.login_btn").show();
            $(".login_phone .phone").val(val);
            tips.hide();
        } else {
            $(this).next("span").hide();
            $(".login_index .form-first span.login_btn").show();
            $(".login_index .form-first input.login_btn").hide();
        }
    }).on("blur", function() {
        var val = $(this).val();
        if (!/^1\d{10}$/.test(val)) {
            if (val == "") {
                tips.error("请输入您的手机号");
            } else {
                tips.error("请输入正确的手机号");
            }
        }
    }).next("span").hide();

    //手机登录页
    $(".login_index .form-first input.login_btn").click(function() {
        tips.hide();
        $(".login_phone .phone").val($(".login_index .phone").val());
        $('.code_btn').click();
        $(".back_btn").show().off().on("click", function() {
            tips.hide();
            timerCount.clear();
            $(".login_index").show().find(".form-first .phone").focus();
            $(".login_phone,.back_btn").hide();
        });
    });
    //重新获取验证码倒计时
    $('.code_btn').click(function() {
        tips.hide();
        var phone = $(".login_phone .phone").val();
        $.post("/cyou/core/login/" + ja.options.app + "/" + ja.options.activity + "/" + ja.options.version + "/cynew/phone/verifycode", {
            telephone: phone
        }, function(ret) {
            if (ret.code == 10000) {
                $(".login_index").hide();
                $(".login_phone").show();
                $(".login_phone .pwd").val("").focus();
                timerCount.start();
            } else {
                tips.error(ret.message);
            }
        });
    });
    //手机短信登录
    $(".login_phone .login_btn").click(function() {
        tips.hide();
        var phone = $(".login_phone .phone").val();
        var code = $(".login_phone .pwd").val();
        if (phone == "") {
            tips.error("请输入您的手机号");
            return;
        } else if (!/^1\d{10}$/.test(phone)) {
            tips.error("请输入正确的手机号");
            return;
        }
        if (code == "") {
            tips.error("请输入您收到的短信随机码");
            return;
        } else if (!/^\d{6}$/.test(code)) {
            tips.error("请输入 6 位短信验证码");
            return;
        }
        var back = window.location.href;
        $.post("/cyou/core/login/" + ja.options.app + "/" + ja.options.activity + "/" + ja.options.version + "/cynew/phone/login", {
            telephone: phone,
            verifycode: code,
            callback: encodeURI(back)
        }, function(ret) {
            if (ret.code == 10000) {
                var locationUrl = '';
                if (ret.message == undefined || ret.message == null || ret.message == '') {
                    locationUrl = window.location.href;
                } else {
                    locationUrl = ret.message;
                }
                window.location.href = locationUrl.indexOf('?') == -1 ? locationUrl + "?v=" + 10000 * Math.random() : locationUrl + "&v=" + 10000 * Math.random();
            } else {
                tips.error(ret.message);
            }
        });
    });
    $(".phone").keydown(function(event) {
        if (event.keyCode == 13) {
            return false;
        }
    });
    $(".cnlogin_close").click(function(event) {
        $("#login_content").hide();
    });
});