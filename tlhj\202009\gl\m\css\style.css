@charset "utf-8";
body{background-color:#fff;}
.none{display:none;}
.pr{position:relative;}
.pa{position:absolute;}
.t{text-indent:-9999em; font-size: 0;}
#cy_bot{padding:20px!important;}
*{-webkit-appearance: none; -webkit-text-size-adjust:none; }
html,body{width:100%;height:100%; padding:0; margin: 0;}
html{-webkit-tap-highlight-color:rgba(0, 0, 0, 0);}
table {
	border-collapse: collapse;
	border-spacing: 0;
}

.com, .tab-i, .tit, .tit-s{background:url(../img/com.png) no-repeat;display:block;overflow:hidden;text-align:center;}
.btn{transition:filter 0.5s linear;}
.btn:hover{-webkit-filter: brightness(1.15);}
.xtl_logo_lk{top:20px;left:4px;z-index:101; width: 154px; height: 103px; background-position: -0px -0px;}
.xtl_logo_lk a{display:block; width:100%; height: 100%; z-index:999;}
.bar{width:750px;margin:0 auto;position:relative;}
.wrap{color:#fff;}
.head{height:796px;background:url(../img/bg-head.jpg) no-repeat center top; z-index:1;position:relative;}
.cont{height:auto;background:rgb(72, 51, 48) url(../img/bg-con.jpg) no-repeat center top; z-index:2;position:relative; padding-bottom:90px;}
.slogan{ background: url("../img/slogan.png"); width:498px; height: 263px; top: 62px; left:50%; margin-left: -245px; }

.main-wrap{min-height: 600px; position: relative;}
.main{display:none; opacity: 0; transition: all 0.5s; padding-top: 1px; background: url("../img/bg-main.png"); width:696px; margin: 6px auto; }
.main.show{opacity: 1; transition: all 0.3s;}
.main-con{width: 648px;  margin: 0 auto; padding-bottom: 40px; }

.tab-wrap{ width:100%; height: 88px; margin: 0 auto; }
.tab{width: 696px; height: 88px; margin: 0 auto; position: relative;}
.tab-wrap.fixed .tab{ top:0; left:50%; margin-left: -348px; position: fixed; z-index: 2;}
.tab-wrap.fixed::before{content:''; background-color: rgba(80,58,56,1); display: block; width:100%; height: 88px; position: fixed; z-index: 1; top:0; left:0;}
.tab .tab-i{width:174px; height: 88px; background-position-y: -216px; display: inline-block; float:left;}
.tab .tab-i:hover, .tab .tab-i.active{background-position-y: -125px; }
.tab .tab-i:nth-child(1){background-position-x:-0px;}
.tab .tab-i:nth-child(2){background-position-x:-174px; }
.tab .tab-i:nth-child(3){background-position-x:-348px; }
.tab .tab-i:nth-child(4){background-position-x:-522px; }

.tit{ width:555px; height: 39px; display: block; margin: 40px auto 0px;}
.main1 .tit{ background-position: 0 -320px;}
.main2 .tit{ background-position: 0 -376px;}
.main3 .tit{ background-position: 0 -431px;}
.main4 .tit{ background-position: 0 -486px;}

.tit-s{ height: 48px; background-position: 0 -550px; font-size: 30px; color: #702607; text-indent: 45px; line-height: 49px; text-align: left; margin-top: 54px;}
.tit-s2{background-position-y: -614px;}
.tit-s3{background-position-y: -744px;}
.tit-s4{background-position-y: -810px;}
.tit-s5{background-position-y: -680px;}


.art-con{margin: 15px 45px 15px 24px; font-size: 24px; color:#431c0c; line-height: 40px;}
.etrLink{line-height: 36px; border-bottom: 2px solid #ca9675; color:#b13805; display: inline-block;}
.etrLink:hover{color: #b13e23; border-color:#c2743b; }

.table thead td{background-color: #a34f2b; height: 40px; line-height: 40px; text-align: center; color:#f8eacd; font-size: 22px; width: 184px; border: 1px solid #c3b3a4;}
.table tbody td{background-color: #fbedd3; height: 40px; line-height: 40px; text-align: center; color:#7b371a; font-size: 20px; border: 1px solid #c3b3a4; }

.pop{position:relative;width:513px;height:288px;background:transparent no-repeat;color:#3f3e45; display:none;}

.ani .slogan{-webkit-animation:sloganAni .4s 0s linear both;}

@-webkit-keyframes fadeInUp{
	0%{opacity:0;
		-webkit-transform:translateY(20px)}
	100%{opacity:1;
		-webkit-transform:translateY(0)}
}
@-webkit-keyframes sloganAni{
	0%{-webkit-transform:scale(1.8);opacity:0;}
	60%{-webkit-transform:scale(1);opacity:1;}
	65%{-webkit-transform:translate(-4px,-4px);}
	70%{-webkit-transform:translate(0,0);}
	75%{-webkit-transform:translate(4px,4px);}
	80%{-webkit-transform:translate(0,0);}
	85%{-webkit-transform:translate(-4px,4px);}
	90%{-webkit-transform:translate(0,0);}
	95%{-webkit-transform:translate(4px,-4px);}
	100%{-webkit-transform:translate(0,0);opacity:1;}
}
/* 新添加 */
.disabled{pointer-events: none;}
