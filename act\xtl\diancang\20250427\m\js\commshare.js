//弹窗
var popIsShow = false;
var popDom = null;
function popShow(id) {
        popHide();
        var p = $('#'+id);
        popDom = p;
        if (p) {
            p.show().css({
                position: 'fixed',
                top: '50%',
                left: '50%',
                marginTop: -popDom.height() / 2 + 'px',
                marginLeft: -popDom.width() / 2 + 'px',
                zIndex: 998
            });
            p.attr('for', 'pop');
            popIsShow = true;
            if ($('[for="' + id + '"]').length >= 1) return;
            $('body').append('<div name="overlay" for=' + id + ' style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:997;background:rgba(0,0,0,0.8);"></div>');
        }
    }
function popHide() {
        $('[for="pop"]').hide().attr('style', '');
        $('[name="overlay"]').remove();
    }
window.addEventListener("resize", function () {
        if (!popIsShow)
            return;
        setTimeout(function () {
            popDom.css({
                marginTop: -popDom.height() / 2 + 'px',
                marginLeft: -popDom.width() / 2 + 'px',
                zIndex: 998
            });
        }, 400)
    });
//分享指示图片弹出
$('.invebtn').on('click',function(){
	$('.fxovlay').show();
});
$('.fxovlay').on('click',function(){
	$('.fxovlay').hide();
});
$('.wrapper').addClass('contAni');