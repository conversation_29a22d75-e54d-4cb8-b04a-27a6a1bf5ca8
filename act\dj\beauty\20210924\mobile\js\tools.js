$.extend($, {
    tools: {
        browser: {
            versions: function () {
                var u = window.navigator.userAgent;
                return {
                    u: u,
                    trident: u.indexOf('Trident') > -1, //IE内核
                    presto: u.indexOf('Presto') > -1, //opera内核
                    webKit: u.indexOf('AppleWebKit') > -1, //苹果、谷歌内核
                    gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') == -1, //火狐内核
                    mobile: !!u.match(/AppleWebKit.*Mobile.*/) || !!u.match(/Android/), //是否为移动终端
                    ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), //ios终端
                    android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1, //android终端或者uc浏览器
                    iPhone: u.indexOf('iPhone') > -1 || u.indexOf('Mac') > -1, //是否为iPhone或者安卓QQ浏览器
                    iPad: u.indexOf('iPad') > -1, //是否为iPad
                    webApp: u.indexOf('Safari') == -1,//是否为web应用程序，没有头部与底部
                    weixin: u.indexOf('MicroMessenger') > -1, //是否为微信浏览器
                    qq: (u.toLowerCase().indexOf('mqqbrowser') > -1 && u.toLowerCase().indexOf(" qq") > -1) ||
                        (!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) && u.toLowerCase().indexOf(" qq") > -1),  //QQ自带浏览器
                    qqInstalled: u.toLowerCase().indexOf('mqqbrowser') > -1 && u.toLowerCase().indexOf(" qq") < 0,  //QQ浏览器
                };
            }(),
            isQQWx: function () {
                var u = window.navigator.userAgent;
                return (u.indexOf('MicroMessenger') > -1 || u.indexOf('mqqbrowser') > -1 && u.indexOf(" qq") < 0 || u.indexOf(' qq') > -1 && u.indexOf('mqqbrowser') < 0)
            }()
        },
        //获取参数
        getParam: function (variable) {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == variable) {
                    return pair[1];
                }
            }
            return ('');
        },
        getHashParam: function (key) {
            var url = location.hash;
            var theRequest = new Object();
            if (url.indexOf("#") != -1) {
                var str = url.substr(1);
                strs = str.split("#");
                for (var i = 0; i < strs.length; i++) {
                    theRequest[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
                }
            }
            var value = theRequest[key];
            return value;
        },
        getRequest: function GetRequest() {
            var url = location.search; //获取url中"?"符后的字串
            var theRequest = new Object();
            if (url.indexOf("?") != -1) {
                var str = url.substr(1);
                strs = str.split("&");
                for (var i = 0; i < strs.length; i++) {
                    theRequest[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
                }
            }
            return theRequest;
        },
        objToQs: function (o) {
            var s = '';
            for (var it in o) {
                s += '&' + it + '=' + o[it]
            }
            return o ? s.substring(1, s.length) : ''
        },
        getUrl: function () {
            return window.location.protocol + '//' + window.location.host;
        }(),
        getFilename: function () {
            var pathname = window.location.pathname;
            var arr = pathname.split('\/');
            var filename = arr[arr.length - 1];
            return filename;
        }(),
        getPath: function () {
            var pathname = window.location.pathname;
            var arr = pathname.split('\/');
            var filename = arr[arr.length - 1];
            var url = window.location.protocol + '//' + window.location.host;
            return url + pathname.replace(filename, '');
        }(),
        getCurentTime: function () {
            var curentTime = new Date();
            var timeString = curentTime.getFullYear() + '-' + (curentTime.getMonth() + 1) + "-" + curentTime.getDate();
            timeString += ' ' + curentTime.getHours() + ':' + curentTime.getMinutes() + ":" + curentTime.getSeconds();
            return timeString;
        },
        stampToTime: function (stamp) {
            //13位数字的时间戳转为时间对象
            var d = new Date(stamp);
            return {
                d: d,
                YY: d.getFullYear(),
                MM: d.getMonth() + 1,
                DD: d.getDate(),
                hh: d.getHours(),
                mm: d.getMinutes(),
                ss: d.getSeconds(),
            };
        },
        formatNumberUs: function (n) {
            //将数字转化为美式计数表示：比如 12000 =》 12,000
            var b = parseInt(n).toString();
            var len = b.length;
            if (len <= 3) {
                return b;
            }
            var r = len % 3;
            return r > 0 ? b.slice(0, r) + "," + b.slice(r, len).match(/\d{3}/g).join(",") : b.slice(r, len).match(/\d{3}/g).join(",");
        },
        fixNumer: function (num, max) {
            var numStr = num.toString();
            var r = num;
            if (numStr.length < max) {
                var zero = '';
                for (var i = 0; i < max - numStr.length; i++) {
                    zero += '0';
                }
                r = zero + numStr;
            }
            return r;
        },
        getLen: function (str) {
            if (str == null) return 0;
            if (typeof str != "string") {
                str += "";
            }
            return str.replace(/[^\x00-\xff]/g, "01").length;
        },
        //处理超过长度的字符，截取maxlength的字符
        subString: function (str, n) {
            var that = this;
            var len = $.tools.getLen(str);
            if (len > n) {
                var newlen = Math.floor(n / 2);
                var stringLength = str.length;
                var newString = "";
                for (var i = newlen; i <= stringLength; i++) {
                    var tempString = str.substr(0, i);
                    if ($.tools.getLen(tempString) > n) {
                        return newString;
                        break;
                    } else {
                        newString = tempString;
                    }
                }
            } else {
                return str;
            }
        },
        isChinese: function (temp) {
            var re = /[^\u4e00-\u9fa5]/;
            if (re.test(temp)) return false;
            return true;
        },
        getRnd: function (num1, num2) {
            switch (arguments.length) {
                case 1:
                    return Math.floor(Math.random() * arguments[0] + 1);     //返回0-max的随机整数
                case 2:
                    var min = arguments[0], max = arguments[1];
                    if (arguments[0] - arguments[1] > 0) {
                        min = arguments[1];
                        max = arguments[0];
                    }
                    return Math.floor(Math.random() * (max - min + 1) + min);      //返回min-max的随机整数
                default:
                    return 0;
            }
        },
        getRndArrayItems: function (arr, count) {
            var shuffled = arr.slice(0), i = arr.length, min = i - count, temp, index;
            while (i-- > min) {
                index = Math.floor((i + 1) * Math.random());
                temp = shuffled[index];
                shuffled[index] = shuffled[i];
                shuffled[i] = temp;
            }
            return shuffled.slice(min);
        },
        // 格式化手机号，隐藏中间4位
        fixPhone: function (phone) {
            var head3 = phone.slice(0, 3);
            var end4 = phone.slice(-4);
            return head3 + '****' + end4;
        },
        //将base64转换为blob
        dataURLtoBlob: function(dataurl, fileName) {
            var arr = dataurl.split(','),
                mime = arr[0].match(/:(.*?);/)[1],
                bstr = atob(arr[1]),
                n = bstr.length,
                u8arr = new Uint8Array(n);
            while (n--) {
                u8arr[n] = bstr.charCodeAt(n);
            }
            return new Blob([u8arr], { type: mime }, fileName);
        },
        //将blob转换为file
        blobToFile: function(theBlob, fileName){
            theBlob.lastModifiedDate = new Date();
            theBlob.name = fileName;
            return theBlob;
        },

        //检测是否为手机号码
        isMobile: function (txt) {
            if (txt == null || txt == "") {
                return false;
            }
            else {
                var regex = /^((13[0-9])|(14[5,7,9])|(15[^4])|(16[0-9])|(18[0-9])|(19[0-9])|(17[0,1,3,5,6,7,8]))\d{8}$/;
                return regex.test(txt);
            }
        },

        // 检测身份证号
        isIdCard: function (idVal) {
            var _IDRe18 = /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
            var _IDre15 = /^([1-6][1-9]|50)\d{4}\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}$/
            // 校验身份证：
            return (_IDRe18.test(idVal) || _IDre15.test(idVal))
        },

    }
}, true);

(function (jQuery) {
    var special = jQuery.event.special,
        uid1 = 'D' + (+new Date()),
        uid2 = 'D' + (+new Date() + 1);

    special.scrollstart = {
        setup: function () {

            var timer,
                handler = function (evt) {

                    var _self = this,
                        _args = arguments;

                    if (timer) {
                        clearTimeout(timer);
                    } else {
                        evt.type = 'scrollstart';
                        jQuery.event.handle.apply(_self, _args);
                    }

                    timer = setTimeout(function () {
                        timer = null;
                    }, special.scrollstop.latency);

                };

            jQuery(this).bind('scroll', handler).data(uid1, handler);

        },
        teardown: function () {
            jQuery(this).unbind('scroll', jQuery(this).data(uid1));
        }
    };

    special.scrollstop = {
        latency: 300,
        setup: function () {

            var timer,
                handler = function (evt) {

                    var _self = this,
                        _args = arguments;

                    if (timer) {
                        clearTimeout(timer);
                    }

                    timer = setTimeout(function () {

                        timer = null;
                        evt.type = 'scrollstop';
                        jQuery.event.handle.apply(_self, _args);

                    }, special.scrollstop.latency);

                };

            jQuery(this).bind('scroll', handler).data(uid2, handler);

        },
        teardown: function () {
            jQuery(this).unbind('scroll', jQuery(this).data(uid2));
        }
    };

})($);

//长按事件触发
$.fn.longPress = function(fn) {
    var timeout = undefined;
    var $this = this;
    for(var i = 0; i < $this.length; i++) {
        (function(target) {
            var timeout;
            target.addEventListener('touchstart', function(event) {
                timeout = setTimeout(function() {
                    //fn.apply(target);
                    fn(event);
                }, 500);
            }, false);
            target.addEventListener('touchend', function(event) {
                clearTimeout(timeout);
            }, false);
        })($this[i]);
    }
}
