﻿@charset "utf-8";
/***public***/
*{margin: 0;padding: 0;}
address,caption,cite,code,dfn,em,strong,th,var{font-style:normal;font-weight:normal;}
body,html{-webkit-tap-highlight-color:transparent;-webkit-text-size-adjust:none; }
ol,ul,li{list-style:none;}
caption,th{text-align:left;}
h1,h2,h3,h4,h5,h6{font-size:100%;font-weight:normal;}
input[type="text"], input[type="password"], select { -webkit-appearance: none;appearance:none; outline: none;-webkit-tap-highlight-color: rgba(0,0,0,0); border-radius:0;box-sizing: border-box;}
a{text-decoration:none; outline:none;}
img{ width: 100%; display: block;}
.c:after{content:'\20';display:block;height:0;line-height:0;visibility:hidden;clear:both;}
.hh{display:block;text-indent:-999em;overflow:hidden;}
.hide{width:0;height:0;overflow:hidden;display:none;}
.pr{position: relative;}
.pa{ position: absolute;}
html{font-size:100px;}
body,html{width: 100%; height:100%;}
body{font: 14px/1.75 -apple-system, "Helvetica Neue", Helvetica, Arial, sans-serif; background:#000; font-size:0.24rem; color: #fff; position: relative; overflow-x: hidden;}
a{text-decoration:none;blr:expression(this.onFocus=this.blur());outline:none;}
html{-webkit-tap-highlight-color:rgba(0,0,0,0);}
.t{display: block;text-indent: -999em}
.g-fl {float: left; }
.g-fr {float: right; }
/***index ***/
.flex{
    display: flex;
    display: -webkit-flex;
    justify-content: space-between;
    flex-direction: row;
    flex-wrap: wrap;
}
.flex1{
    display: flex;
}

.sur{ background: url(../img/sur_m.png) no-repeat center; background-size: 9rem auto;}
.bg1{
    width: 6.4rem;
    height: 9.8rem;
    background: url(../img/bg1.jpg) no-repeat center;
    background-size: 100% auto;
    overflow: hidden;
    position: relative;
}
.bg2{
    width: 6.4rem;
    height: 18.96rem;
    background: url(../img/bg2.jpg) no-repeat center;
    background-size: 100% auto;
    overflow: hidden;
    position: relative;
}
.com_tent .bt_bg{
    width: 2.8rem;
    height: 4.17rem;
    background: url(../img/title_h.png) no-repeat center;
    background-size: 100% auto;
    margin: 1.2rem auto;
    animation: btnbox .8s .1s ease both;animation: btnbox .8s .1s ease both;
    -webkit-animation: btnbox .8s .1s ease both;animation: btnbox .8s .1s ease both;
    -moz-animation: btnbox .8s .1s ease both;animation: btnbox .8s .1s ease both;
}
@keyframes btnbox{
    0%{opacity:0;transform: translateY(30px);}
    100%{opacity:1;transform: translateY(-2px);}
}
@-webkit-keyframes btnbox{
    0%{opacity:0;transform: translateY(30px);}
    100%{opacity:1;transform: translateY(-2px);}
}
.logo_tl{
    width: 1.99rem;
    height: .93rem;
    background-position: -.11rem -.2rem;
    position: absolute;
    top: .2rem;
    left: 0.1rem;
}
.page_font{
    font-size: .18rem;
    color: #56427f;
    text-align: center;
    margin-top: 1.85rem;
}
.page_font span{
    width: 1.2rem;
    height: .25rem;
    line-height: .25rem;
    color: #ffffff;
    background: #56427f;
    display: inline-block;
    border-radius: .1rem;
}
.my_gb a{
    width: 2.6rem;
    height: .65rem;
    background-position: -.1rem -1.18rem;
    margin: 0.2rem auto;
}
.my_login{
    text-align: center;
    font-size: 16px;
    letter-spacing: 2px;
    position: relative;
    top: -10px;
}
.my_login a:link,
.my_login a:visited{
    color: #fff;
    /* text-decoration: underline; */
}
.com_content{
    padding: .5rem .3rem;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.nrUp_title .title_con{
    width: 2.01rem;
    height: .62rem;
    background-position: -.26rem -5.01rem;

}
.nrUp_title{
    padding-bottom: .14rem;
    border-bottom: 1px solid #ffd2ee;
}
.upData_sc{
    font-size: .2rem;
    color: #1b1b1b;
    margin-top: .1rem;
}
.upData_sc .jgIcon{
    width: .25rem;
    height: .25rem;
    background-position:-2.52rem -5.22rem;
    display: inline-block;
    vertical-align: middle;
}
.search{margin-top: .2rem}
.search label{
    color: #000000;
    font-size: .18rem;
    display: inline-block;
    line-height: .34rem;
}
.search select{
    width: 1.62rem;
    height: .32rem;
    background: #ffd7d0;
    color: #f36b78;
    border-radius: 3px;
    border: 0;
    padding-left: .1rem;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    background: url(../img/xia_la.jpg) #ffd7d0 right no-repeat;
    font-size: .18rem;
    vertical-align: middle;
}
.search a{
    width: .6rem;
    color: #ffffff;
    text-align: center;
    font-size: .18rem;
    height: .34rem;
    background: #f36b78;
    display: inline-block;
    border-radius: 3px;
    vertical-align: middle;
}
.search input{
    width: 1.28rem;
    height: .32rem;
    background: #ffd7d0;
    color: #f36b78;
    border-radius: 3px;
    border: 0;
    margin-left: .1rem;
    padding-left: .1rem;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    vertical-align: middle;
}
#serversT{
    width: 1.53rem;
}
.btn_input{
    margin-left: -.15rem;
}
.list_con{margin-top: .29rem; height: 14.7rem}
.list_con ul{
    display: flex;
    display: -webkit-flex;
    justify-content: space-between;
    flex-direction: row;
    flex-wrap: wrap;
}
.list_con ul li{
    width: 2.85rem;
    height: 7.3rem;
    background: url(../img/list_data.png) no-repeat center;
    background-size: 100% auto;
    color: #2e115b;
    padding: .2rem .21rem;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    font-size: .18rem;
}
.xin_dia i{
    width: .26rem;
    height: .21rem;
    display: inline-block;
    vertical-align: middle;
    background-position: -2.96rem -5.27rem;
    margin-right: .05rem;
}
.tu_list{
    width: 2.52rem;
    height: 1.55rem;
    background: #ffe8df;
    overflow: hidden;
    margin-top: .05rem;
    position: relative;
    cursor: pointer;
}
.bx_bg{
    width: .74rem;
    height: .73rem;
    background-position: -.27rem -5.9rem;
    display: none;
    position: absolute;
    top: 0;
}
.list_p{
    color: #1b1b1b;
    line-height: .23rem;
    margin-top: .12rem;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp:12;
    overflow: hidden;
    /* height: 2.4rem; */
}
.list_name{
    text-align: right;
    color: #1b1b1b;
    margin-top: .01rem;
    padding-right: .12rem;
}
.pages{text-align: center; margin-top: .2rem; color: #fa6f76;}
.pages a{
    color: #fa6f76;
}
.bg3{
    width: 6.4rem;
    height: 7.24rem;
    background: url(../img/bg3.jpg) no-repeat center;
    background-size: 100% auto;
    overflow: hidden;
}
.title_880{
    width: 6rem;
    height: .85rem;
    margin: 0 auto;
}
.page3_title{
    background-position:-1.3rem -2.26rem;
    margin: .9rem auto 0;
}
.login_btm{
    width: .87rem;
    height: .36rem;
    background-position: -3.47rem -5.18rem;
    margin: .13rem auto .2rem;
}
.login_box{
    text-align: center;
}
.hou_login{
    font-size: .18rem;
    color: #e22e57;
    margin-top: .1rem;
    margin-bottom: .1rem;
}
.hou_login a{color: #e22e57;}
.lott{
    width: 6.21rem;
    margin: 0 auto;
    position: relative;
}
.lott ul li{
    width: 1.44rem;
    height: 1.35rem;
    background: url(../img/hot_c.png) no-repeat center;
    background-size: 100% auto;
    float: left;
    margin-left: .09rem;
    margin-bottom: .1rem;
}
.lott ul li p{
    white-space: nowrap;
    font-size: .18rem;
    color: #ffffff;
    text-align: center;
    line-height: .36rem}
.lott ul li.play_sp{    width: 3rem;background: none}
.lott ul li.play_sp .play_lott{
    width: 3.07rem;
    height: 1.07rem;
    display: block;
    background-position: -2.62rem -.21rem;
    margin-top: .2rem;
}
.up_dao{
    width: 100%;
    height: 1rem;
    overflow: hidden;
}
.lott-cover {
    width: 1.44rem;
    height: 1.35rem;
    background: rgba(255,133,145,.5);
    display: none;
    position: absolute;
    z-index: 99;
}
.bg4{
    width: 6.4rem;
    height: 3.94rem;
    background: url(../img/bg4.jpg) no-repeat center;
    background-size: 100% auto;
    overflow: hidden;
}
.page4_title{
    height: .83rem;
    background-position:-1.2rem -3.13rem;
    margin-top: .48rem;
}
.mc_list{
    width: 6.21rem;
    margin: 0.2rem auto 0;
}
.mc_list ul li{
    width: 1.97rem;
    height: 1.97rem;
    background-size: 100% auto;
    overflow: hidden;
}
.mc_list ul li h2{
    font-size: .18rem;
    text-align: center;
    margin-top: .25rem;
    color: #cf4260;
}
.mc_list ul li h2 i{
    width: 4px;
    height:4px;
    border: 1px solid #e22e57;
    border-radius: .2rem;
    display: inline-block;
    vertical-align: middle;
    margin: 0 .05rem;
}
.bg5{
    width: 6.4rem;
    /* height: 10.19rem; */
    background: url(../img/bg5.jpg) no-repeat center;
    background-size: cover;
    overflow: hidden;
}
.page5_title{
    background-position: -1.2rem -3.99rem;
    margin-top: .5rem;
}
.hg_hui{
    width: 6.21rem;
    margin: 0 auto;
    font-size: .18rem;
    color: #5c5c5c;
}
.hg_hui .red{color: #f00;}
.zuoPN{
    width: 5.74rem;
}
.zuoP{
    width: 5.91rem;
}
.bang,.commonPop{
    width: 6.16rem;
    height: 4.33rem;
    background: url(../img/layer1.png) no-repeat center;
    background-size: 100% auto;
    text-align: center;
}
.layer_com{
    width: 5.75rem;
    margin-left: .22rem;
    padding: .6rem 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-align: center;
}
#pop1 .layer_com{width: 5.55rem;}
.layer_com h3{
    font-size: .35rem;
    color: #f36b78;
    text-align: center;
}
.layer_com>p{
    font-size: .18rem;
    color: #4f4b56;
    /*margin-top: -.1rem;*/
}
.jp_show{padding-top: 1rem;}
.sel_xz label{
    width: 2.2rem;
    display: inline-block;
    text-align: left;
    font-size: .2rem;
    color: #4f4b56;
}
.sel_xz select{
    width: 4.68rem;
    height: .36rem;
    border: 1px solid #f36b78;
    padding-left: .1rem;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    color: #8b867e;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    background: url(../img/xl_layer.jpg) #fffbfa right no-repeat;
    margin-bottom: 10px;
}
.subimt a{
    width: 2.05rem;
    height: .52rem;
    margin: .15rem auto 0;
    background-position: -2.76rem -1.35rem;
}
.inputLayer{
    margin-top: .05rem;
}
.btn-close{
    width: .46rem;
    height: .46rem;
    background-position: -1.38rem -5.85rem;
    position: absolute;
    right: .12rem;
    top: .05rem;
}
.pop{
    display: none;
}
.up_shang{
    width: 6.42rem;
    height: 9.3rem;
    background: url(../img/up-dataM.png) no-repeat center;
    background-size: 100% auto;
}
.font_sc{
    font-size: .2rem;
    color: #4f4b56;
}
.font_sc a{
    width: 1.04rem;
    height: .31rem;
    background: #f36b78;
    text-align: center;
    display: inline-block;
    position: relative;
    border-radius: .2rem;
    vertical-align: middle;
}
.font_sc a input{
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
}
.font_sc a.sc_input span{
    position: absolute;
    top: 0;
    left: 0;
    color: #ffffff;
    height: 100%;
    width: 100%;
    line-height: .31rem;
}
.sc_box{
    padding-left: .54rem;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    text-align: left;
}
.font_sc textarea{
    display: block;
    padding: .1rem;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    width: 90%;
    color: #858585;
    height: 2rem;
}
.p_bing{
    width: 5rem;
    font-size: .18rem;
    color: #4f4b56;
}
.bx_data{text-align: center; margin: .05rem auto}
.phone_box{margin-bottom: .1rem}
.phone_box label{font-size: .2rem; color: #4f4b56;}
.phone_box input{
    width: 3.15rem;
    height: .4rem;
    padding-left: .08rem;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border: 1px solid #f1cdd1;
}
.phone_box span.yzm_box{
    width: 3.15rem;
    height: .41rem;
    display: inline-block;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border: 1px solid #f1cdd1;
}
.phone_box span.yzm_box a{
    font-size: .18rem;
    vertical-align: top;
    display: inline-block;
    width: 1.15rem;
    height: .38rem;
    line-height: .38rem;
    text-align: center;
    color: #f36b78;
}
.phone_box span.yzm_box input{
    width: 1.89rem;
    height: .37rem;
    border: 0;
    vertical-align: top;
}
.mobile_yz{
    width: 4.5rem;
    margin: 0 auto;
    margin-top: .1rem;
}
.my_zx{
    width: 6.4rem;
    height: 5.6rem;
    background: url(../img/my_gr.png) no-repeat center;
    background-size: 100% auto;
}
.zy_renz{
    color: #4f4b56;
    padding: 0 .52rem;
    font-size: .18rem;
    margin-bottom: .1rem;
}
.zy_renz a{
    width: 1.22rem;
    height: .31rem;
    background: #f36b78;
    color: #ffffff;
    border-radius: .2rem;
}
.lay_mc_list{
    width: 4.52rem;
    margin: 0.2rem auto;
}
.subimt a.tj_sy{
    background-position: -4.9rem -1.35rem;
}
.zy_renz a.hui_gao{
    background: #868686;
}
.layer_com>p.xia_p{
    font-size: .24rem;
    margin-bottom: .05rem;
}
.subimt{margin-left: .3rem;}
.x_ping{
    width: .65rem;
    height: .65rem;
    margin: .2rem auto;
}
.bom_icon img {
    width: .64rem;
    height: .64rem;
    margin: .1rem auto .05rem;
}
.bom_icon p {
    font-size: .18rem;
    text-align: center;
    color: #cf4260;
}
.sm_duan{
    width: 4.41rem;
    margin: 0 auto;
    font-size: .18rem;
    color: #4f4b56;
    text-align: left;
}
.fr_span{
    width: 4rem;
}
.formSu{
    width: 3.85rem;
    margin: 0.1rem auto;
}
.formSu_in label{
    color: #4f4b56;
    vertical-align: middle;
}
.formSu_in input{
    width: 3rem;
    height: .4rem;
    border: 2px solid #ffe4e6;
    padding-left: .15rem;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.formSu_in textarea#city_tj{
    width: 3rem;
    height: 1rem;
    border: 2px solid #ffe4e6;
    padding-left: .15rem;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.formSu_in.city_tjBox label{
    vertical-align: top;
}
.formSu_in{margin-bottom: .1rem}
.tj_gc{
    width: 6.17rem;
    height: 2.26rem;
    background: url(../img/xx_cg.png) no-repeat center;
    background-size: 100% auto;
}
.layer_com.wan_s{
    padding: .85rem 0;
}
.pop-con .layer_com ul{
    display: flex;
    display: -webkit-flex;
    justify-content: space-between;
    flex-direction: row;
    flex-wrap: wrap;
}
.pop-con .layer_com ul li{
    background: url(../img/list_mc.png) no-repeat center;
    background-size: 100% auto;
}
.tu_list img{ height: 100%;}

.bx_data s img{
    width: .4rem;
    height: .34rem;
    display: inline-block;
    vertical-align: middle;
}
.font_sc s img{
    width: .4rem;
    height: .34rem;
    display: inline-block;
    vertical-align: middle;
}
.font_sc input{
    width: 3.21rem;
    height: .4rem;
    padding-left: .08rem;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border: 1px solid #f1cdd1;
    margin-bottom: .1rem;
}
.lay_mc_list ul li a {
    width: 1.03rem;
    height: .3rem;
    background: #f36b78;
    font-size: .16rem;
    color: #ffffff;
    border-radius: .3rem;
    line-height: .3rem;
    display: none;
    margin: 0 auto;
}
.lay_mc_list ul li em {
    font-size: .18rem;
    display: block;
}
.lay_mc_list ul li em p {
    color: #cf4260;
    margin-top: -.05rem;
}
.photo-clip-rotateLayer{
    margin: 0 auto;
}
.photo-clip-view{
    height: 255px !important;
}



.swiper_lb{
    width: 6rem;
    height: 4.6rem;
    background: url(../img/swiper_lb.png) no-repeat center;
    background-size: 100% auto;
}
.swiper_layer{
    width: 4.6rem;
    height: 2.84rem;
    margin-left: .65rem;
    margin-top: .81rem;
}
.swiper-container{height: 3rem;}
.swiper_layer img{width:100%;height: 100%;}
.sx_link{
    width: 100%;
    position: absolute;
    left: -.05rem;
    top: 2rem;
}
.sx_link a{
    width: .4rem;
    height: .4rem;
}
.sx_link a.pevr_s{
    background-position: -2.07rem -5.86rem;
}
.sx_link a.next_x{
    background-position: -2.63rem -5.85rem;
}
.fyq_box{
    text-align: center;
    color: #000;
    font-size: .18rem;
}
.pagination{
    text-align: center;
    margin-top: -.1rem;
}
.pagination span{
    width: .12rem;
    height: .12rem;
    background: #555;
    display: inline-block;
    border-radius: .22rem;
    margin: 0 0.05rem;
    border: 1px solid #fff;
    cursor: pointer;
}
.pagination span.swiper-active-switch {
    background: #ff959d;
}
.jpFont-zi{
    font-size: 0.18rem;
    color: #e22e57;
    text-align: center;
}
.jpFont-zi a{
    color: #e22e57;
}
.fr_width{
    width: 5.75rem;
}

.lott-list li:nth-last-of-type(1) .x_ping{
    width: auto;
    height: auto;
    margin-top: .4rem;
    max-width: 80%;
}
#name{
    width: 2rem;
    display: inline-block;
    text-overflow: ellipsis;
    overflow: hidden;
    vertical-align: middle;
}
.one_updata  s,.two_updata s{position: relative; display: inline-block; }
.one_updata .del,.two_updata .del{display: none;position: absolute; width: 0.2rem; height: 0.2rem; top: -0.08rem; right: -0.05rem; background: url(../img/delebtn.png) no-repeat; background-size: 100% 100%; cursor: pointer;}
.longmen{
    display: none;
    width: 0.8rem;
    position: absolute;
    top: 1rem;
    left: 0rem;
}

.lookPrize{color: #e22e57; text-decoration: underline;}