/*public*/
body { font: 12px/1.5 '\5FAE\8F6F\96C5\9ED1'; background: #000; overflow-x: hidden; min-width: 1200px; }
em, i { font-style: normal; }
.hh { overflow: hidden; text-indent: -999em; display: block; }
.pr { position: relative; }
.pa { position: absolute; }
.fl { float: left; }
img { display: block; }
.wrap {overflow-x: hidden; position: relative; }
.mid { width: 1200px; margin: 0 auto; position: relative; background: url("../img/bg2nr.jpg") top center no-repeat;height: 1950px;}
/*head*/
.none{display:none;}
.icon { background: url("../img/icon.png") no-repeat; display: block; }
.content-bg { background: url("../img/bg2.jpg") top center no-repeat; width: 100%; height: 1950px;position:relative;z-index:2;}
.yellow { color: #f7e8a8; }
.czbox{position:relative;}
.czbtn { width: 318px; height: 111px; margin: 0 auto; background-position:-130px 0;}
.cz-sm {text-align: center; padding-top:2px;color:#23313a;font-size:18px;}
/**content**/
.tit-num {color: #23313a; font-size: 30px; text-align: center;font-family:simsun;font-weight:bold;padding-top:20px;}
/*cont1*/
.cont1{height:926px;}
.time{color:#23313a;font-size:24px;text-align:center;padding:45px 0 5px;}
.addtxt{color:#fff;font-size:22px;text-align:center;}
.addtxt span{color:#cfe5c6;font-size:18px;display:block;}
.lb_box {height: 395px; position: relative;}
.lb_box:after{width:1251px;height:509px;content:'';position:absolute;background:url(../img/lb.png) no-repeat;top:-59px;left:-17px;z-index:1;}
.lb_cont {padding-left:9px;}
.fir_box { width: 354px; height: 100%;position: relative;float:left;z-index:2;}
.lb_cont .icon { display: none; }
.djlist { width: 100%; text-align: center;overflow: hidden;padding-top:109px;}
.djlist .dj-bg { width: 99px; height: 99px; margin: 0 auto;position: relative;background:url(../img/jl1.jpg) no-repeat center top;}
.djlist .dj-bg img { width: 47px; height: 47px;display:block;padding-top:17px;margin:0 auto;}
.djlist .dj-bg img.img2{padding-top:11px;}
.djlist p {font-size: 16px;color:#eeeff3;margin-top:-30px;position:relative;z-index:2;}
.djlist p.lh{line-height:18px;margin-top:-41px;}
.djlist .dj-li2 p{padding-top:8px;} 
.sec_box { width: 354px; height: 100%;padding: 0 59px 0 62px; position: relative; float:left;z-index:2;}
.thi_box { width: 354px; height: 100%;position: relative; float:left;z-index:2;}
.compe { width: 114px; height: 77px; background-position: 0 -92px; text-indent: -999em; top:50px; left:50%;margin-left:-205px;z-index:2;}
.tipi {text-align: center; color: #a94417; line-height:22px; top:101px; left: 0;font-size:18px;width:100%;}
.tit,.gzlist li span{background:url(../img/titbg.png) no-repeat;}
.tit{width:208px;height:30px;margin:0 auto;text-indent:-9999em;}
.gzlist{color:#23313a;font-size:18px;line-height:24px;margin:18px 0 0 45px;width:1147px;}
.gzlist li{position:relative;padding:0 0 14px 0;}
.gzlist strong{color:#851e1b;font-weight:normal;}
.gzlist li span{position:absolute;left:-39px;width:26px;top:-1px;height:26px;color:#d4efff;line-height:26px;text-align:center;background-position:-372px 0;}
.bgbox{margin:12px 0 0 4px;width:1098px;}
.bgbox table{width:100%;border:1px solid #944c23;}
.bgbox th{height:49px;background:#f6e8ba;}
.bgbox th,.bgbox td{text-align:center;height:50px;font-size:18px;border-right:1px solid #944c23;}
.bgbox td{border-bottom:1px solid #944c23;}
.morep{text-align:center;margin:11px 0 9px;}
.morep a{color:#e14d0b;text-decoration:underline;}
/*cont2*/
.c2-tit { width: 617px; height: 90px;margin: 0 auto;background-position:0 -72px;}
.cont2 .tit-num {font-size:20px;color:#23313a;font-family: '\5FAE\8F6F\96C5\9ED1';padding-top:20px;font-weight:normal;}
.addtime{text-align:center;font-size:24px;padding-top:3px;color:#23313a;line-height:30px;}
.c2-sm { line-height: 24px; color: #23313a; text-align: center;font-size:18px;}
.c2-sm span{color:#f2d76f;}
.pint-box {position: relative;width:990px;margin:24px auto 0;height:276px;}
.pint-box li { width: 250px; float: left; text-align: center;margin:0 40px;display:inline;background:url(../img/tgbg.png) no-repeat;}
.pt-bg {width: 100%; height: 142px; position: relative;}
.pt-bg p{color:#464a54;font-size:16px;line-height:18px;padding-top:97px;}
.djlist li { display: inline-block; vertical-align: top; width: 147px;height:129px;}
.pt-bg img { width: 72px; height: 72px; position: absolute; top: 50%; left: 50%; margin: -49px -36px; }
.pt-bg .pt-name { font-size: 14px; line-height: 18px; color: #526363; text-align: center; width: 100%; margin: 8px auto 0; }
.price { color: #eeeff3; font-size: 16px; line-height: 34px;margin-top:1px;}
.price b { font-size: 30px; color: #eeeff3; padding-left:10px; display: inline-block; }
.ypt-peo { width: 100%; height: 34px; line-height: 34px;font-size: 16px; color: #cacaca;}
.pt-peo { color: #ecedf1; line-height: 26px;font-size:16px;}
.my-pt-btn { width: 257px; height: 90px; background-position: 0 -193px; margin: 60px auto 0;}
.my-pt-btn.gray {background-position: 0 -326px;cursor: default;pointer-events: none;}
/**ani**/
.my-pt-btn { transition: all .2s linear; -webkit-transition: all .2s linear; }
.my-pt-btn:hover { transform: translateY(-3px); -webkit-transform: translateY(-3px); }
.my-pt-btn.gray:hover { transform: translateY(0); -webkit-transform: translateY(0); }
.my-pt-btn:hover, .copy-btn:hover { filter: brightness(1.1); -webkit-filter: brightness(1.1); }
.my-pt-btn.gray:hover { filter: brightness(1); -webkit-filter: brightness(1); }
.czbtn { animation: zoom .8s linear infinite alternate; -webkit-animation: zoom .8s linear infinite alternate; z-index: 30; }
@keyframes zoom {
	0% { transform: scale(1) }
	100% { transform: scale(1.04) }
}
@-webkit-keyframes zoom {
	0% { -webkit-transform: scale(1) }
	100% { -webkit-transform: scale(1.04) }
}
.fir_box, .pint-box .li1 { -webkit-transition-duration: .7s !important; transition-duration: .7s !important; }
.sec_box, .pint-box .li2 { -webkit-transition-duration: 1.4s !important; transition-duration: 1.4s !important; }
.thi_box, .pint-box .li3 { -webkit-transition-duration: 2s !important; transition-duration: 2s !important; }
.r-txt { -webkit-animation: lpAni 4s linear infinite; animation: lpAni 4s linear infinite; }
@-webkit-keyframes lpAni {
	0% { -webkit-transform: translateY(0); }
	50% { -webkit-transform: translateY(-6px); }
	100% { -webkit-transform: translateY(0); }
}
@keyframes lpAni {
	0% { transform: translateY(0); }
	50% { transform: translateY(-6px); }
	100% { transform: translateY(0); }
}
.s-dl i { -webkit-transform-origin: left top; -webkit-animation: trAni 10s linear infinite; transform-origin: left top; animation: trAni 10s linear infinite; }
@-webkit-keyframes trAni {
	0% { -webkit-transform: rotate(0deg); }
	10% { -webkit-transform: rotate(1deg); }
	20% { -webkit-transform: rotate(0.4deg); }
	30% { -webkit-transform: rotate(1deg); }
	40% { -webkit-transform: rotate(0); }
	50% { -webkit-transform: rotate(-.4deg); }
	60% { -webkit-transform: rotate(0); }
	70% { -webkit-transform: rotate(-1deg); }
	80% { -webkit-transform: rotate(-.4deg); }
	90% { -webkit-transform: rotate(.4deg); }
	100% { -webkit-transform: rotate(0); }
}
@keyframes trAni {
	0% { transform: rotate(0deg); }
	10% { transform: rotate(1deg); }
	20% { transform: rotate(0.4deg); }
	30% { transform: rotate(1deg); }
	40% { transform: rotate(0); }
	50% { transform: rotate(-.4deg); }
	60% { transform: rotate(0); }
	70% { transform: rotate(-1deg); }
	80% { transform: rotate(-.4deg); }
	90% { transform: rotate(.4deg); }
	100% { transform: rotate(0); }
}
@-webkit-keyframes flAni {
	from { -webkit-transform: translate3d(0, 0, 0); }
	to { -webkit-transform: translate3d(2px, 2px, 0); }
}
@keyframes flAni {
	from { transform: translate3d(0, 0, 0); }
	to { transform: translate3d(2px, 2px, 0); }
}
@-webkit-keyframes sloganAni {
	0% { -webkit-transform: scale(1.8); opacity: 0; }
	60% { -webkit-transform: scale(1); opacity: 1; }
	65% { -webkit-transform: translate(-4px, -4px); }
	70% { -webkit-transform: translate(0, 0); }
	75% { -webkit-transform: translate(4px, 4px); }
	80% { -webkit-transform: translate(0, 0); }
	85% { -webkit-transform: translate(-4px, 4px); }
	90% { -webkit-transform: translate(0, 0); }
	95% { -webkit-transform: translate(4px, -4px); }
	100% { -webkit-transform: translate(0, 0); opacity: 1; }
}
@keyframes sloganAni {
	0% { transform: scale(1.8); opacity: 0; }
	60% { transform: scale(1); opacity: 1; }
	65% { transform: translate(-4px, -4px); }
	70% { transform: translate(0, 0); }
	75% { transform: translate(4px, 4px); }
	80% { transform: translate(0, 0); }
	85% { transform: translate(-4px, 4px); }
	90% { transform: translate(0, 0); }
	95% { transform: translate(4px, -4px); }
	100% { transform: translate(0, 0); opacity: 1; }
}

/* .closed {
transform-origin: center top;
-webkit-transform-origin: center top;
animation: drop 1s linear infinite alternate;
-webkit-animation: drop 1s linear infinite alternate;
} */
@-webkit-keyframes drop {
0% { -webkit-transform: rotate3d(0, 0, 0, 0deg); }
100% { -webkit-transform: rotate3d(3, 5, 6, 6deg) }
}
@keyframes drop {
0% { transform: rotate3d(0, 0, 0, 0deg); }
100% { transform: rotate3d(3, 5, 6, 6deg) }
}
.pt-bg { -webkit-transition: .4s cubic-bezier(0.175, 0.885, 0.32, 1.275); transition: .4s cubic-bezier(0.175, 0.885, 0.32, 1.275); }
.pint-box .pt-bg:hover { -webkit-transform: translate(0, -6px); transform: translate(0, -6px); }

/* li1 */
.li1 { position: relative; }
.li1-pop,.li2-pop { width: 716px; height: 355px; background: url(../img/pop-pt-bg1.png) no-repeat; position: absolute; top: -332px; left: 50%; margin-left: -358px; z-index: 9;display:none;}
.li1-pop:before,.li2-pop:before{width:188px;height:60px;background-position:right top;top:38px;right:-99px;}
.li1-pop:after,.li2-pop:after{width:190px;height:63px;background-position:left bottom;bottom:-8px;left:-59px;}
.li1-pop .pt-name1 { font-size: 14px; line-height: 18px; color: #526363; text-align: center; width: 60%; margin: 54px auto 0; }
.li1-pop .pt-name { font-size: 14px; line-height: 18px; color: #526363; text-align: center; width: 100%; margin: 8px auto 0; }
.li1-pop .djlist,.li2-pop .djlist{margin-top:0;text-align:center;padding-top:164px;}
.li1-pop .djlist li,.li2-pop .djlist li{float: none;margin:0 38px 0;width:109px;height:109px;background:url(../img/djbg1.png) no-repeat center top;}
.li1-pop .djlist li .dj-bg,.li2-pop .djlist li .dj-bg{background:none;width:47px;height:64px;}
.li1-pop .djlist li .dj-bg{height:57px;}
.li1-pop .djlist li .dj-bg img{padding-top:10px;}
.li1-pop .djlist li p,.li2-pop .djlist li p{margin-top:8px;color:#eeeff3;font-size:20px;}
.li1-pop .djlist li p{line-height:20px;margin-top:5px;}
/* li3 */
.li3 { position: relative; }
.li3-pop { position: absolute; background: none; width: 518px; height: 632px; left: -536px; top: 50%; margin-top: -350px;z-index: 9; pointer-events: none;display:none; }
.pop-role { width: 558px; height: 632px; position: absolute;z-index: 2; }
.pcrole-tips { font-size: 18px; color: #eeeff3; line-height: 22px; text-align: center; width: 100%; position: absolute; left: 0; top: 660px; }
.pcrole-tips i { color: #e1cfa4;}
.pint-box .pop-role,.pint-box .li3-pop{width:603px;height:712px;}
.pint-box .li3-pop{margin-top:-408px;left:-509px;}
.maskShow::after { content: ' '; display: block; width: 100%; height: 100%; position: fixed; top: 0; left: 0; background-color: rgba(0, 0, 0, .5); pointer-events: none; z-index: 2; }
.kaji .content-bg{background: url(../img/kaji/bg2.jpg) top center no-repeat;}
.kaji .mid{background: url(../img/kaji/bg2nr.jpg) top center no-repeat;}
.kaji .time{color:#e1cfa4;padding:45px 0 15px;}
.kaji .icon{background: url(../img/kaji/icon.png) no-repeat;}
.kaji .czbtn{width:311px;height:82px;background-position:-137px 0;}
.kaji .cz-sm{color:#e1cfa4;padding-top:21px;}
.kaji .tit-num{color:#e1cfa4;}
.kaji .lb_box:after{background:url(../img/kaji/lb.png) no-repeat;}
.kaji .djlist .dj-bg{background: url(../img/kaji/jl1.jpg) no-repeat center top;}
.kaji .compe{background-position: 0 -92px;}
.kaji .tit,.kaji .gzlist li span{background: url(../img/kaji/titbg.png) no-repeat;}
.kaji .gzlist{color:#eeeff3;}
.kaji .gzlist strong{color:#e1cfa4;}
.kaji .gzlist li span{background-position: -372px 0;color:#eeeff3;}
.kaji .cont1{height:953px;}
.kaji .c2-tit{width:1001px;height:46px;background-position:0 -124px;}
.kaji .addtime{color:#e1cfa4;padding-top:20px;}
.kaji .c2-sm{color:#fafafa;}
.kaji .pint-box li{background: url(../img/kaji/tgbg.png) no-repeat;}
.kaji .price b{color:#e1cfa4;}
.kaji .ypt-peo{color:#e1cfa4;}
.kaji .my-pt-btn{width:252px;height:72px;background-position: 0 -193px;margin-top:70px;}
.kaji .my-pt-btn.gray{background-position: 0 -326px;}
.kaji .li1-pop,.kaji .li2-pop{background: url(../img/kaji/pop-pt-bg1.png) no-repeat;}
.kaji .li1-pop .djlist li,.kaji .li2-pop .djlist li{background: url(../img/kaji/djbg1.png) no-repeat center top;}