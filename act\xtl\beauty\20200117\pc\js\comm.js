$('.mzlist li').hover(function(){
	$(this).find('.addfada').show();
},function(){
	$(this).find('.addfada').hide();
});
//pop
function showDia(e, fun) {
	showDialog.show({
		id: e, //需要弹出的id，如果是弹出页面上的div，则该选项为必选
		bgcolor: "#000000",//弹出“遮罩”的颜色，格式为"#FF6600"，可选，默认为"#fff"
		opacity: 50 //弹出“遮罩”的透明度，格式为｛10-100｝，可选
	});
	$('body').css('overflow', 'hidden');
	fun && fun();
};
//礼包切换
var swiper1 = new Swiper('#swiper1', {
	effect: 'fade',
	fade: { crossFade: true }
});
$('.jlhd span').on('click', function () {
	$(this).addClass('on').siblings().removeClass('on');
	swiper1.slideTo($(this).index());
});
//活动规则 最后一行去掉样式
$('.wdlist li:last').css('border', 'none');
//活动规则切换
$('.tabhd li').on('click', function () {
	$(this).addClass('on').siblings().removeClass('on');
	$('.gzbox .dis_tab').removeClass('dis').eq($(this).index()).addClass('dis');
});
//弹窗
var popIsShow = false;
var popDom = null;
function popShow(id) {
	popHide();
	var p = $('#' + id);
	popDom = p;
	if (p) {
		p.show().css({
			position: 'fixed',
			top: '50%',
			left: '50%',
			marginTop: -popDom.height() / 2 + 'px',
			marginLeft: -popDom.width() / 2 + 'px',
			zIndex: 9998
		});
		p.attr('for', 'pop');
		popIsShow = true;
		$('.overlay').show();
	}
}
function popHide() {
	$('.overlay').hide();
	$('.pop').hide();
};
//直播PK浮层
$('.stepbox').hover(function () {
	$(this).next('.fcbox').show();
}, function () {
	$(this).next('.fcbox').hide();
});
//图片裁剪
var imageCropper = $('#imageCropper');
var options = {
	aspectRatio: 1 / 1, //图片裁剪比例
	//preview: '.img-preview',
	crop: function (e) {

	}
};
imageCropper.cropper(options);
$('.imageList input').live('change', function (event) {
	var i = $('.imageList input').index($(this));
	var eve = event || window.event;
	var file = event.currentTarget.files[0];
	var format = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
	var str = 'jpg,jpeg,png';
	if (str.indexOf(format) == -1) {
		alert('请选择jpeg，jpg，png格式的图片');
		return
	}
	var fileSizeBefter = Math.ceil(file.size / 1024);

	if (fileSizeBefter > 5120) {
		alert('请选择小于5M的图片');
		$('#imageList input').val("");
		return;
	}
	var reader = new FileReader();
	reader.onload = function () {
		showDia('cropperPop');
		$('.imageList input').val("");
		imageCropper.cropper('destroy').attr('src', reader.result).attr('len', i).cropper(options);
	};
	if (file) {
		reader.readAsDataURL(file);
	}
});

function cropperDate() {
	var imageSrc = imageCropper.cropper('getCroppedCanvas').toDataURL('image/jpeg');
	// 去掉base64编码中的前缀 data:image/png;base64,
	var tag = "base64,";
	baseStr = imageSrc.substring(imageSrc.indexOf(tag) + tag.length);
	// 去掉base64编码中的“=”号　　
	var eqTagIndex = baseStr.indexOf("=");
	baseStr = eqTagIndex != -1 ? baseStr.substring(0, eqTagIndex) : baseStr;
	// 计算文件流大小
	var strLen = baseStr.length;
	var fileSize = strLen - (strLen / 8) * 2
	if (fileSize / 1024 > 5120) {
		alert('图片过大请重新上传')
		return false;
	}

	example.uploadImg(imageSrc)

}
function cropperHide() {
	showDialog.hide();
};

// 删除图片
$('.delebtn').on('click', function () {
	$(this).hide()
	$(this).parent().find('img').removeAttr('src').hide()
	$(this).parent().find('input').val('')
	$(this).parent().find('a').show()
	$(this).parent().find('.mcbox').hide()
})

//侧导航
$('a[href*=#],area[href*=#]').click(function () {
	if (location.pathname.replace(/^\//, '') == this.pathname.replace(/^\//, '') && location.hostname == this.hostname) {
		var $target = $(this.hash);
		$target = $target.length && $target || $('[name=' + this.hash.slice(1) + ']');
		if ($target.length) {
			var targetOffset = $target.offset().top;
			$('html,body').animate({
				scrollTop: targetOffset
			}, 500);
			return false;
		}
	}
});
$(function () {
	var offsets = [$('#sec1').offset().top - 100, $('#sec2').offset().top - 100, $('#sec3').offset().top - 100, $('#sec4').offset().top - 100], len = offsets.length;
	$(window).bind('load scroll', function () {
		var t = $(window).scrollTop();
		navsett(t);
		if ($(window).scrollTop() + $(window).height() == $(document).height()) {
			$('.snav a').removeClass('on').eq(3).addClass('on');
		};
	});
	function navsett(t) {
		var fnav = $('.snav a');
		fnav.removeClass('on');
		if (t >= offsets[len - 1])
			fnav.eq(len - 1).addClass('on');
		else {
			for (var i = 0; i < len; i++) {
				if (t >= offsets[i] && t < offsets[i + 1]) fnav.eq(i).addClass('on');
			}
		}
	}
});
//视频
$('.vlist li').on('click', function () {
	// $('#pop-video-mp4').find('video').attr('src', $(this).attr('data-video'));
	// showDia("pop-video-mp4");
	// document.getElementById('videoTz').play();
	window.open($(this).attr('data-video'))
});
$('#pop-video-mp4 .gbbtn').click(function () {
	showDia('pop_inf');
	document.getElementById('videoTz').pause();
});
//支持她
$('.zcbtn').on('click', function () {
	var cpinex = parseInt($(this).prev().find('span').html()) + 1;
	$(this).prev().find('span').html(cpinex);
});
