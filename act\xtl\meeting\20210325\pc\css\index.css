@charset "utf-8";
@import url(http://tl.changyou.com/logo/xtl_logo.css);
body{
    font-family: '微软雅黑','宋体',sumsun,Helvetica,sans-serif;
    font-size: 14px;
    line-height: 24px;
    color: #545454;
    min-width: 1200px;
}
.p-a {
    position: absolute;
}
.p-r {
    position: relative;
}
.t-c {
    text-align: center;
}
.t-l {
    text-align: left;
}
.t-r {
    text-align: right;
}
.t-h {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.bg {
    background-size: 100% auto;
    background-position: top center;
    background-repeat: no-repeat;
}
.bgc {
    background-size: 100% auto;
    background-position: center center;
    background-repeat: no-repeat;
}
.t {
    text-indent: -999em;
    overflow: hidden;
}
.fl {
    float: left;
}
.fr {
    float: right;
}
a {
    cursor: pointer;
}
.ani {
    -webkit-transition: all .5s;
    -moz-transition: all .5s;
    -ms-transition: all .5s;
    -o-transition: all .5s;
    transition: all .5s;
}
.full-img img,
.full-img {
    display: block;
}
.full-img img {
    width: 100%;
}
.b {
    font-weight: bold;
}
.hide {
    display: none;
}
.btn-hv {
    transition: .1s all;
    -webkit-transition: .1s all;
}
.btn-hv:hover {
    filter: brightness(1.1);
    -webkit-filter: brightness(1.1);
}
.btn-hv:active {
    transform: scale(.95);
    -webkit-transform: scale(.95);
}
.btn-scale {
    animation: scaleBtn .6s ease-in-out infinite alternate;
    -webkit-animation: scaleBtn .6s ease-in-out infinite alternate;
}
@keyframes scaleBtn {
    to {
        transform: scale(1.1);
    }
}
@-webkit-keyframes scaleBtn {
    to {
        -webkit-transform: scale(1.1);
    }
}
.spr {
    display: block;
    background-image: url('../img/spr.png');
    background-repeat: no-repeat;
    background-size: 596px auto;
}
/* a{ text-decoration:none;color:#333; }
a:hover img{ border:none;} */
.zn_logo{
    width: 247px;
    /* height: 233px; */
    /* background: url(../img/zn_logo.png) no-repeat; */
}
.zn_logo img{
    width: 100px;
    position: absolute;
    left: 200px;
}
/* ---------header--------- */
.header{
    position: relative;
    width: 100%;
    height: 1178px;
    overflow-x: hidden;
    background: url(../img/header.jpg) no-repeat top center;
}
.wrap {
    position: relative;
    margin: 0 auto;
    width: 1200px;
    height: 100%;
}
.slogan {
    top: 160px;
    left: 120px;
    width: 436px;
    height: 593px;
}
.nav{
    width: 100%;
    height: 35px;
    margin: 0 auto;
    line-height: 35px;
    position: absolute;
    left: 0;
    top: 870px;
    font-size: 15px;
    text-align: center;
    color: #894125;
    z-index: 18;
}
.login,.quit{
    color: #5ba6ec;
}
.baoming_btn{
    top: 550px;
    left: 290px;
    width: 192px;
    height: 191px;
    background: url(../img/baoming_btn.png) no-repeat;
}
.times-tips {
    top: 910px;
    left: 0;
    width: 100%;
    font-size: 16px;
    color: #db830a;
}
.times-plank {
    bottom: 20px;
    left: 0;
    padding: 165px 92px 0 348px;
    box-sizing: border-box;
    width: 1201px;
    height: 238px;
    background-image: url(../img/times.png);
}
.times-plank li {
    float: left;
    width: 253px;
    line-height: normal;
    text-align: center;
    font-size: 22px;
    color: #db830a;
}

.main {
    position: relative;
    width: 100%;
    overflow: hidden;
    background: url(../img/content.jpg) no-repeat;
    background-size: 1920px auto;
    background-position: top center;
}
.part {
    width: 100%;
}
.part.p-1 {
    height: 648px;
}
.xtl_logo_lk a {
    top: 10px;
    width: 333px;
    height: 96px;
}
.xtl_logo_bg {
    top: 10px;
    width: 333px;
    height: 96px;
    background-image: url(../img/logo.png);
}
.tis {
    display: block;
    margin-left: -160px;
    width: 1520px;
    height: 167px;
    background-image: url(../img/tis.png);
    background-repeat: no-repeat;
}
.tis.ti-1 {
    background-position: 0 0;
}
.news-plank {
    margin: 11px 0 0 -56px;
    width: 1246px;
    height: 354px;
    background-image: url(../img/p1_kuang.png);
}
.news-swiper {
    margin: 20px 0 0 173px;
    width: 596px;
    height: 266px;
}
.news-img,
.news-img img {
    width: 100%;
    height: 100%;
}
.news-img img {
    object-fit: cover;
}
.news-list {
    margin: 74px 106px 0 0;
    width: 350px;
    height: 206px;
    line-height: normal;
    font-size: 18px;
    color: #9f722e;
}
.news-item {
    padding: 13px 10px 13px 0;
    border-bottom: 1px dashed #caab7c;
}
.news-item a {
    display: block;
    max-width: 290px;
    float: left;
    color: #9f722e;
}
.news-item a:hover {
    text-decoration: underline;
}
.news-item span {
    display: block;
    float: right;
    color: #9f722e;
}
.part.p-2 {
    height: 733px;
}
.tis.ti-2 {
    background-position: 0 -172px;
}


.stack{font-size:0;white-space:nowrap;position:relative;z-index:1;margin: 48px auto 0;width: 1008px;vertical-align:top;}
.stack-list li{display:inline-block;margin: 0 8px;vertical-align:top;position:relative;z-index:1;}
.stack-list li.acitve {margin: 0;}
.stack-tit{margin-top: 5px;width: 76px;height: 460px;text-indent:-999em;background:url(../img/stack_pg.png) no-repeat;float:left;position:relative;z-index:2;cursor:pointer;}
.stack-tit:after{content:'';width:90px;height:553px;background:url(../img/stack_hv.png) no-repeat -96px 0;position:absolute;top:6px;left:3px;display:none;}
.stack-tit2:after,.stack-tit4:after{background-position:0 0;}
.stack-tit1{background-position:-1px 0;}
.stack-tit2{background-position: -81px 0;}
.stack-tit3{background-position: -162px 0;}
.stack-tit4{background-position: -243px 0;}
.stack-pic{width:0;height:473px;overflow:hidden;position:relative;z-index:1;float:left;-webkit-transition:.2s cubic-bezier(0.165,0.84,0.44,1);transition:.2s cubic-bezier(0.165,0.84,0.44,1);}
.active .stack-tit{/* width:0; */}
.active .stack-pic{width: 648px;box-sizing: border-box;padding-right: 34px;}
.stack-pic {background:url(../img/stack.png) no-repeat;}
.stack .inset-icon {
    top: 420px;
    left: -151px;
    z-index: 99;
    width: 401px;
    height: 76px;
    background-image: url('../img/p2_inset.png');
    pointer-events: none;
}
.stack .prize-img {
    margin: 48px auto 0;
    width: 485px;
}

.part.p-3 {
    height: 735px;
}
.tis.ti-3 {
    background-position: 0 -344px;
}
.ch-container {
    margin: 0 0 0 -44px;
    padding: 80px 91px 0 164px;
    box-sizing: border-box;
    width: 1215px;
    height: 435px;
    background-image: url('../img/p3_kuang.png');
    font-size: 0;
}
.ch-container li {
    display: inline-block;
    position: relative;
    width: 284px;
    margin: 0px 16px;
}
.avatar-wrap {
    margin: 0 auto;
    width: 188px;
    height: 188px;
    background-image: url('../img/avatar.png');
    overflow: hidden;
}
.avatar-icon {
    margin: 10px auto 0;
    width: 168px;
    height: 168px;
    border-radius: 99px;
    overflow: hidden;
    object-fit: cover;
}
.ch-name {
    margin: 12px auto 0;
    width: 244px;
    height: 32px;
    line-height: 32px;
    background-image: url('../img/p3_name_bg.png');
    font-size: 22px;
    color: #d55634;
}
.ch-item.ch-2 .ch-name,
.ch-item.ch-3 .ch-name {
    width: 284px;
    background-image: url('../img/p3_name_bg2.png');
}
.bubble-wrap {
    display: none;
    top: -20px;
    left: 126px;
    padding: 6px 10px 0;
    box-sizing: border-box;
    width: 201px;
    height: 63px;
    background-image: url('../img/p3_bubble.png');
    line-height: 23px;
    font-size: 18px;
    color: #fcedd3;
}
.bubble-wrap.b-2 {
    width: 141px;
    background-image: url('../img/p3_bubble2.png');
}
.ch-container li:hover .bubble-wrap {
    display: block;
}
.part.p-4 {
    height: 490px;
}
.part.p-4.on {
    height: 992px;
}
.tis.ti-4 {
    background-position: 0 -516px;
}
.wkf-wrap {
    margin: 90px auto 0;
    width: 320px;
}
.part.p-4.on .wkf-wrap {
    display: none;
}
.city-container {
    display: none;
    margin: 55px auto 0;
    position: relative;
    width: 1201px;
    height: 724px;
    background-image: url('../img/p4_kuang.png');
}
.part.p-4.on .city-container {
    display: block;
}
.city-cut {
    width: 100%;
    height: 124px;
}
.city-cut li {
    width: 92px;
    height: 44px;
}
.city-cut li.active {
    width: 142px;
}
.city-cut li.city-1 {
    background-position: -504px 0;
}
.city-cut li.city-1.active {
    background-position: -315px 0;
}
.city-cut li.city-2 {
    background-position: -504px -49px;
}
.city-cut li.city-2.active {
    background-position: -315px -49px;
}
.city-cut li.city-3 {
    background-position: -504px -98px;
}
.city-cut li.city-3.active {
    background-position: -315px -98px;
}
.part.p-4 .btn-prev,
.part.p-4 .btn-next {
    top: 12px;
    width: 72px;
    height: 47px;
}
.part.p-4 .btn-prev {
    background-position: 0 -142px;
    left: 420px;
}
.part.p-4 .btn-next {
    background-position: -77px -142px;
    right: 420px;
}
.city-plank {
    width: 100%;
}
.city-item {
    display: none;
}
.city-top {
    height: 320px;
}
.city-video {
    float: left;
    margin: -6px 0 0 168px;
    width: 548px;
    height: 278px;
    background-image: url('../img/p4_video_bg.png');
}
.city-poster {
    margin: 6px auto 0;
    width: 536px;
    height: 266px;
}
.city-poster img {
    height: 100%;
    object-fit: cover;
}
.city-poster .video-shadow {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
    opacity: .7;
}
.btn-play {
    top: 95px;
    right: 0;
    left: 0;
    margin: 0 auto;
    width: 204px;
    height: 81px;
    background-image: url('../img/btn_play.png');
}
.city-msg {
    margin: 60px 0 0 24px;
    width: 330px;
}
.city-name {
    margin: 0 auto;
    width: 310px;
    height: 39px;
}
.city-name.c-1 {
    background-position: 0 0;
}
.city-name.c-2 {
    background-position: 0 -47px;
}
.city-name.c-3 {
    background-position: 0 -91px;
}
.city-msg .msg-text {
    margin-top: 8px;
    text-align: justify;
    line-height: 24px;
    font-size: 16px;
    color: #9f722e;
}
.city-list {
    margin: 10px auto 0;
    width: 944px;
    height: 202px;
    overflow-y: auto;
    white-space: nowrap;
    font-size: 0;
}
.city-list::-webkit-scrollbar {
    /*滚动条整体样式*/
    width : 0;  /*高宽分别对应横竖滚动条的尺寸*/
    height: 6px;
}
.city-list::-webkit-scrollbar-thumb {
    /*滚动条里面小方块*/
    border-radius: 10px;
    background: #d65535;
}
.city-list::-webkit-scrollbar-track {
    /*滚动条里面轨道*/
    border-radius: 10px;
    background: #f7d7af;
}
.city-list li {
    display: inline-block;
    margin-right: 30px;
}
.city-list li:last-child {
    margin-right: 0;
}
.city-list li a {
    display: block;
    border: 1px solid #caab7c;
}
.city-list li .city-link img {
    height: 100%;
    object-fit: cover;
}
.part.p-5 {
    height: 478px;
}
.tis.ti-5 {
    background-position: 0 -688px;
}
.scroll-center {
    display: block;
    top: 0;
    right: 0;
    left: 0;
    margin: 0 auto;
    width: 95% !important;
    height: 28px !important;
    background-image: url(../img/scrollbar.png);
    background-size: 57px auto;
    background-repeat: repeat;
}
.sc-left,
.sc-right {
    top: 0;
    width: 26px;
    height: 28px;
}
.sc-left {
    left: 0;
    background-position: -154px -142px;
}
.sc-right {
    right: 0;
    background-position: -185px -142px;
}
.sc-text {
    top: 0;
    left: 0;
    width: 100%;
    line-height: 28px;
    text-align: center;
    font-size: 16px;
    color: #fff;
}
.p5-swiper {
    margin: 17px auto 0;
    width: 1100px;
    height: 214px;
}
.qq-group {
    margin: 0 auto;
    width: 331px;
    height: 171px;
    background-image: url('../img/qq_group.png');
    overflow: hidden
}
.qq-group .title-text {
    margin: 44px auto 0;
    width: 240px;
    line-height: normal;
    font-weight: bold;
    line-height: 42px;
    font-size: 32px;
    color: #d74437;
}
.qq-group .msg-text {
    margin-top: 32px;
    font-size: 20px;
    color: #d74437;
}
.part.p-5 .swiper-scrollbar {
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 1010px;
    height: 28px;
    background-image: url('../img/drap_line.png');
    background-position: center center;
    background-repeat: repeat-x;
    background-color: transparent;
}
.part.p-5 .swiper-scrollbar div {
    width: 100%;
    height: 28px !important;
    background-color: transparent;
    cursor: pointer;
}
.part.p-6 {
    height: 731px;
}
.tis.ti-6 {
    background-position: 0 -860px;
}
.rule-list {
    margin: 30px auto 0;
    width: 1010px;
    line-height: 26px;
    font-size: 16px;
    color: #905803;
}
.page-inset {
    bottom: 0;
    left: 0;
    width: 100%;
    height: 311px;
    background-image: url('../img/page_inset.png');
    background-size: 1920px auto;
    background-repeat: no-repeat;
}

.pop {
    display: none;
}
/* 弹窗 */
#pop3 .dialog_box{margin: 17px 0 0 62px;width: 624px;}
.cyou_dialog {width:704px;height:500px;z-index:99999;position:relative;display:none;}
.cyou_dialog .bg { position:absolute; top:30px; left:0; width:704px; height:500px; z-index:-1; background:url(../img/bminfo.png) no-repeat; _background:0; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src='img/tc_bg1.png',sizingMethod='scale');}
.pop .btn-close{display:block;width: 115px;height: 58px;position:absolute;right: -60px;top: -36px;background-position: 0 -194px;cursor:pointer;_background:0;_filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src='img/close.png',sizingMethod='scale');text-indent:-9999em;}
.cyou_dialog2 {width: 740px;height: 327px;z-index:99999;position:relative;display:none;}
.cyou_dialog2 .bg {position:absolute;top:0;left:0;width: 100%;height: 100%;z-index:-1;background:url(../img/tc_bg1.png) no-repeat;_background:0;_filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src='img/tc_bg1.png',sizingMethod='scale');}
.pop-2 {width: 740px;height: 756px;background-image: url(../img/tc_bg2.png);}
.pop-3 {width: 740px;height: 587px;background-image: url(../img/tc_bg3.png);}
.dialog_box .pop-title-bm {margin: 54px auto 0;width: 373px;height: 58px;background-position: -315px -1032px;}
.dialog_box .pop-title-rule {margin: 60px auto 0;width: 310px;height: 58px;background-position: 0 -1032px;}


.dialog_box1 {width: 100%;height: 100%;margin:0px auto;box-sizing: border-box;text-align:center;padding:40px 0 20px;}
.dialog_box .pop-title-ct {margin: 6px 0 22px;font-size: 22px;color: #d74437;}
.dialog_box p { line-height:24px; font-family:'微软雅黑','宋体'; font-size:14px; color:#aa6b49; padding-bottom:10px;}
.xinxi_ul {position: relative;margin: 0px 0 0 25px;color:#AA6B49;}
.xinxi_ul .li_box {width: 321px; height:32px; margin-bottom:5px; clear:both; font-size:18px}
.xinxi_ul .li_box.btn { height:80px;}
.xinxi_ul .li_box * { vertical-align:middle;}
.xinxi_ul .tips-text {margin: 0 0 10px 4px;font-size: 20px;color: #8f8f8f;}

.xinxi_ul li input,
.xinxi_ul li textarea,
.xinxi_ul li select,
.li_box_r select,
.xinxi_ul textarea {
	display:inline;
	padding: 0 10px;
	box-sizing: border-box;
	height: 29px;
	line-height: 29px;
	font-family:'微软雅黑','宋体';
	font-size:16px;
	color:#aa6b49;
	border: 1px solid #9f722e;
	background: transparent;
}
.dialog_box .textarea-title {font-size: 22px;color: #9f722e;} 
.xinxi_ul .textarea-desc {width: 556px;height: 112px;border: 1px solid #9f722e;background-color: transparent;}
.xinxi_ul .li_box label { width:100px; height:28px; line-height:28px; text-align:right; margin-right:10px; display:block; float:left;font-size: 18px;position: relative;}
.xinxi_ul .li_box input.text1 { width:213px; height:30px; line-height:28px; border:1px solid #d5b479; background:rgba(255,255,255,0.5); padding:0 5px;}
.xinxi_ul .li_box .text2 { width:380px; height:44px; line-height:22px; border:1px solid #6F5206; background:#efddbc; padding:3px 5px;}
.xinxi_ul .li_box .text3 { width:100px; height:44px; line-height:22px; border:1px solid #6F5206; background:#efddbc; padding:3px 5px;}
/* .xinxi_ul .li_box select { width:60px; height:28px; border:1px solid #6F5206;} */
.xinxi_ul .li_box .radio { height:40px; border:1px solid #6F5206; background:rgba(255,255,255,0.5);}
.dialog_box1 h3 {line-height:32px;margin: 72px 0 24px;font-size: 30px;color: #d74437;}
.dialog_box h3 { height:32px; line-height:74px; margin:20px 0 10px; font-size:26px; color:#b79251;font-weight: bold;  text-align: center;}
.dialog_box1 p {line-height: 30px;font-size: 22px;color: #9f722e;}
.dialog_box a.btn { text-indent:0em; width: 328px; height: 76px; background:url(../img/tijiao_btn1.png) no-repeat; _background:0; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src='img/tijiao_btn1.png',sizingMethod='scale'); line-height:56px; font-family:'微软雅黑','宋体'; font-size:30px; color:#fff; text-align:center; text-indent:-999em; margin:0px auto; display:block;}
.dialog_box1 a.btn2 { text-indent:0em; width: 248px; height: 72px; background:url(../img/btn1.png) no-repeat; _background:0; _filter:progid:DXImageTransform.Microsoft.AlphaImageLoader(src='img/tijiao_btn2.png',sizingMethod='scale'); line-height:56px; font-family:'微软雅黑','宋体'; font-size:30px; color:#fff; text-align:center; text-indent:-999em; margin:20px auto; display:block;}
.dialog_box a.btn3{width: 199px;height: 48px;margin: 22px auto 0;display:block;background-position: -324px -194px;}


.xinxi_ul .xinxi_left{}
.xinxi_ul .xinxi_right{width: 320px; height: 320px;float: left;}

.rule{
    width: 655px;
    height: 300px;
    margin: -37px 0 0 25px;
    padding-top: 30px;
    color: #AA6B49;

}
.rule .title{
    font-size: 20px;
    color: #b79251;
    font-weight: bold;
    text-align: left;
    line-height: 62px;
}
.rule .des{
    height: 25px;
    font-size: 19px;
    color: #b79251;
    line-height: 30px;
}
.xinxi_ul .li_box_l{
    width: 124px;
    text-align: right;
    font-size: 22px;
    color: #9f722e;
    float: left;
}
.xinxi_ul .li_box_l label{
    display:inline-block;
    margin-bottom: 18px;
    width: 124px;
    height: 29px;
    line-height: 29px;
}
.xinxi_ul .li_box_r{
    position: relative;
    float: left;
}
.xinxi_ul .li_box_r li{
    width: 420px;
    height: 29px;
    margin: 0 0 18px;
    font-size: 0;
}
.sex{
    vertical-align: top;
    margin-left: 10px;
    text-align: right;
    float: left;
    font-size: 22px;
    color: #9f722e;
}
#info_game{
    width: 420px;
    height: 29px;
}
#info_name{
    width: 420px;
    height: 29px;
}
#info_bmcity{
    width: 420px;
    height: 30px;
}
#name{
    width: 89px;
    height: 30px;
}
#info_sex{
    width: 60px;
    height: 27px;
}
#info_phone{
    display: inline-block;
    width: 320px;
    height: 29px;
    vertical-align: top;
}
#info_yanzheng{
    display: inline-block;
    margin-left: -1px;
    line-height: 27px;
    width: 99px;
    height: 29px;
    color: #fff;
    background-image: url(../img/code_btn.jpg);
    background-size: auto 100%;
}
#info_code{
    width: 420px;
    height: 29px;
}
#info_province{
    display: inline-block;
    width: 155px;
}
#info_city{
    position: absolute;
    width: 420px;
    height: 29px;
}
.xinxi_right .li_box_l{
    width: 135px;
    height: 322px;
    text-align: right;
    font-size: 18px;
    color: #bc9a60;
    float: left;
}
.xinxi_right .li_box_r{
    width: 185px;
    height: 322px;
}
#info_nickName{
    width: 180px;
    height: 27px;
    margin: 0 0 25px;
    border: 1px solid #d5b479;
    font-size: 16px;
    overflow: hidden;
}
#info_nickName option{font-size: 8px;max-width: 180px;text-overflow:ellipsis;
    overflow: hidden;
    white-space: nowrap;
    }
.xinxi_ul .li_box_l label.comm_label{width: 135px;}
.err{text-align:center;height:27px;line-height: 30px;font-size: 18px;}
.select{position:relative;width: 420px;min-width:125px;height:30px;border:1px solid #d5b479;background:#efddbc;/* cursor:pointer; */float:left;margin-right:5px;font-size:14px;}
.select:after{content:"";position:absolute;top:50%;right:10px;margin-top:-2px;border-top:5px solid #666;border-right:5px solid transparent;border-left:5px solid transparent;}
.select .select_text{padding:0 20px 0 10px;height:26px;line-height:26px; overflow:hidden;}
.select_ul{display:none;position:absolute;top:34px;left:-1px;width:125px;min-width:125px;border:1px solid #d5b479;border-bottom-right-radius:3px;border-bottom-left-radius:3px;background:rgb(255,255,255); max-height:324px; overflow-y:auto;}
.select_ul li{line-height:26px;text-indent:10px; height:26px; margin-bottom:0; overflow:hidden;}
.select_ul li:hover{color:#fff;background:#6AA7EA;}
.select_ul li.cur{color:#fff;background:#195DA3;}

.pop-rule-list {margin: 10px 80px 0 80px;}
.pop-rule-list li {
    /* margin-bottom: 10px; */
    line-height: 30px;
    text-align: justify;
    font-size: 20px;
    color: #9f722e;
}
.pop-3 .btn-confirm {
    margin: 26px auto 0;
    width: 199px;
    height: 48px;
    background-position: -120px -194px;
}

/* 视频弹窗 */
#video_player{width:800px;height:450px;background:#000;position:relative;color:#f8f008;display:none;}
#video_player .video_placeholder{width:100%;height:100%;text-align:center;line-height:450px;color:#faf103;}
#video_player .close{width:37px;height:37px;display:block;position:absolute;background:url(http://i1.cy.com/ssqy/main/20161229/close.png) no-repeat;right:-40px;top:0;text-indent:-9999em;}
#video_player .close:hover{animation:rot360 .8s ease forwards;}

 