~(function () {
    /**
     * == 微信业务 ==
     **/
    function Wechat() {
    }
    Wechat.prototype = {
        // 微信初始化参数回去
        wxConfig: function() {
            var info;
            var _this = this
            if(!_this.appId) return console.error('微信初始化需要appId，请联系后台开发添加');
            $.ajax({
                url: '//tlbbgl-activity.changyou.com' + '/wechat/web/' + _this.appId + '/config',
                async: false,
                success: function (data) {
                    if (data.config) {
                        info = data.config;
                    } else {
                        console.error('微信初始化失败，config接口异常，' + data.msg);
                    }
                }
            });
            return info;
        },
        // 微信初始化
        wxInit: function(fun, opt) {
            var _this = this
            var _opt = {
                // appId: 'wx8fcf309675d53582',
                appId: 'wx73614e1184e96fbf',
                isWxDebug: false
            }
            $.extend(_opt, opt)
            _this.appId = _opt.appId
            _this.isWxDebug = _opt.isWxDebug
            if(_this.wxConfigInfo) {
                fun && fun();
            }else {
                $.getScript('https://res.wx.qq.com/open/js/jweixin-1.4.0.js', function() {
                    _this.wxConfigInfo = _this.wxConfig() || {};
                    var configInfo = _this.wxConfigInfo;
                    console.log('wxConfig', configInfo);
                    wx.config({
                        debug: _this.isWxDebug,     // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                        appId: configInfo.appId,           // 必填，公众号的唯一标识
                        timestamp: configInfo.timestamp,   // 必填，生成签名的时间戳
                        nonceStr: configInfo.nonceStr,     // 必填，生成签名的随机串
                        signature: configInfo.signature,   // 必填，签名，见附录1
                        jsApiList: [                        // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
                            'translateVoice',           // 识别音频并返回识别结果接口
                            'onMenuShareTimeline',      // 分享到朋友圈
                            'onMenuShareAppMessage',    // 分享给朋友
                            'onMenuShareQQ',            // 分享到QQ
                            'onMenuShareQZone',         // 分享到QQ空间
                            'onMenuShareWeibo',         // 分享到腾讯微博
                            'hideMenuItems',            // 批量隐藏功能按钮接口
                            'showMenuItems',            // 批量显示功能按钮接口
                            'hideAllNonBaseMenuItem'    // 隐藏所有非基础按钮接口
                        ]
                    });
                    fun && fun();
                })
            }
        },
        // 微信分享
        share: function (options) {
            var isWechat = window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i) == 'micromessenger';
            if (isWechat) {
                var _options = {
                    type: 'link',
                    shareList: [
                        'onMenuShareTimeline',      // 分享到朋友圈
                        'onMenuShareAppMessage',    // 分享给朋友
                        'onMenuShareWeibo',         // 分享到腾讯微博
                        'onMenuShareQQ',            // 分享到QQ
                        'onMenuShareQZone'          // 分型到QQ空间
                    ],
                    hideMenuList: [
                        // "menuItem:share:timeline",
                        // "menuItem:copyUrl",
                        // "menuItem:share:appMessage",
                        "menuItem:share:qq",
                        "menuItem:share:weiboApp",
                        "menuItem:favorite",
                        "menuItem:share:facebook",
                        "menuItem:share:QZone",
                        "menuItem:editTag",
                        "menuItem:delete",
                        "menuItem:originPage",
                        "menuItem:readMode",
                        "menuItem:openWithQQBrowser",
                        "menuItem:openWithSafari",
                        "menuItem:share:email",
                        "menuItem:share:brand",
                    ]
                };
                $.extend(_options, options);
                var list = _options.shareList,
                    hideList = _options.hideMenuList,       // 除去不分享渠道，如朋友圈('onMenuShareTimeline')
                    showList = _options.showMenuList;       // 显示要显示的

                delete _options['shareList'];
                delete _options['hideMenuList'];
                delete _options['showMenuList'];

                this.wxInit(function() {
                    wx.ready(function () {
                        for (var i in list) {
                            var shareType = list[i];
                            if (typeof wx[shareType] === 'function') {
                                wx[shareType](_options);
                            }
                        }
                        console.log('wx ready:');
                        console.log('shareList', list)
                        console.log('showMenuList', showList)
                        console.log('hideMenuList', hideList)
                        hideList && wx.hideMenuItems({ menuList: hideList });
                        showList && wx.showMenuItems({ menuList: showList });
                    })
                }, options)
            }
        }
    };
    $.wechat = new Wechat();
}());


$.extend($, {
    tools: {
        browser: {
            versions: function () {
                var u = window.navigator.userAgent;
                return {
                    u: u,
                    trident: u.indexOf('Trident') > -1, //IE内核
                    presto: u.indexOf('Presto') > -1, //opera内核
                    webKit: u.indexOf('AppleWebKit') > -1, //苹果、谷歌内核
                    gecko: u.indexOf('Gecko') > -1 && u.indexOf('KHTML') == -1, //火狐内核
                    mobile: !!u.match(/AppleWebKit.*Mobile.*/) || !!u.match(/Android/), //是否为移动终端
                    ios: !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), //ios终端
                    android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1, //android终端或者uc浏览器
                    iPhone: u.indexOf('iPhone') > -1 || u.indexOf('Mac') > -1, //是否为iPhone或者安卓QQ浏览器
                    iPad: (function isIpadFun() {
                        var ua = window.navigator.userAgent
                        var IsIPad = false
                        if (/ipad/i.test(ua)) {
                            IsIPad = true
                        }
                        // iPad from IOS13
                        var macApp = ua.match(/Macintosh/i) != null
                        if (macApp) {
                            // need to distinguish between Macbook and iPad
                            var canvas = document.createElement('canvas')
                            if (canvas != null) {
                                var context =
                                    canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
                                if (context) {
                                    var info = context.getExtension('WEBGL_debug_renderer_info')
                                    if (info) {
                                        var renderer = context.getParameter(info.UNMASKED_RENDERER_WEBGL)
                                        if (renderer.indexOf('Apple') != -1) IsIPad = true
                                    }
                                }
                            }
                        }
                        return IsIPad;
                    }()), //是否为iPad
                    webApp: u.indexOf('Safari') == -1,//是否为web应用程序，没有头部与底部
                    weixin: u.indexOf('MicroMessenger') > -1, //是否为微信浏览器
                    qq: (u.toLowerCase().indexOf('mqqbrowser') > -1 && u.toLowerCase().indexOf(" qq") > -1) ||
                        (!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) && u.toLowerCase().indexOf(" qq") > -1),  //QQ自带浏览器
                    qqInstalled: u.toLowerCase().indexOf('mqqbrowser') > -1 && u.toLowerCase().indexOf(" qq") < 0,  //QQ浏览器
                };
            }(),
            isQQWx: function () {
                var u = window.navigator.userAgent;
                return (u.indexOf('MicroMessenger') > -1 || u.indexOf('mqqbrowser') > -1 && u.indexOf(" qq") < 0 || u.indexOf(' qq') > -1 && u.indexOf('mqqbrowser') < 0)
            }()
        },
        //获取参数
        getParam: function (variable) {
            var query = window.location.search.substring(1);
            var vars = query.split("&");
            for (var i = 0; i < vars.length; i++) {
                var pair = vars[i].split("=");
                if (pair[0] == variable) {
                    return pair[1];
                }
            }
            return ('');
        },
        getHashParam: function (key) {
            var url = location.hash;
            var theRequest = new Object();
            if (url.indexOf("#") != -1) {
                var str = url.substr(1);
                strs = str.split("#");
                for (var i = 0; i < strs.length; i++) {
                    theRequest[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
                }
            }
            var value = theRequest[key];
            return value;
        },
        getRequest: function GetRequest() {
            var url = location.search; //获取url中"?"符后的字串
            var theRequest = new Object();
            if (url.indexOf("?") != -1) {
                var str = url.substr(1);
                strs = str.split("&");
                for (var i = 0; i < strs.length; i++) {
                    theRequest[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
                }
            }
            return theRequest;
        },
        objToQs: function (o) {
            var s = '';
            for (var it in o) {
                s += '&' + it + '=' + o[it]
            }
            return o ? s.substring(1, s.length) : ''
        },
        getUrl: function () {
            return window.location.protocol + '//' + window.location.host + window.location.pathname;
        }(),
        getFilename: function () {
            var pathname = window.location.pathname;
            var arr = pathname.split('\/');
            var filename = arr[arr.length - 1];
            return filename;
        }(),
        getPath: function () {
            var pathname = window.location.pathname;
            var arr = pathname.split('\/');
            var filename = arr[arr.length - 1];
            var url = window.location.protocol + '//' + window.location.host;
            return url + pathname.replace(filename, '');
        }(),
        replaceParam: function (url, param, value) {
            var urlObj = new URL(url);
            urlObj.searchParams.set(param, value);
            return urlObj.toString();
        },
        removeParam: function (url, param) {
            const urlObj = new URL(url);
            urlObj.searchParams.delete(param);
            return urlObj.toString();
        },
        getCurentTime: function () {
            var curentTime = new Date();
            var timeString = curentTime.getFullYear() + '-' + (curentTime.getMonth() + 1) + "-" + curentTime.getDate();
            timeString += ' ' + curentTime.getHours() + ':' + curentTime.getMinutes() + ":" + curentTime.getSeconds();
            return timeString;
        },
        stampToTime: function (stamp) {
            //13位数字的时间戳转为时间对象
            var d = new Date(stamp);
            return {
                d: d,
                YY: d.getFullYear(),
                MM: d.getMonth() + 1,
                DD: d.getDate(),
                hh: d.getHours(),
                mm: d.getMinutes(),
                ss: d.getSeconds(),
            };
        },
        formatNumberUs: function (n) {
            //将数字转化为美式计数表示：比如 12000 =》 12,000
            var b = parseInt(n).toString();
            var len = b.length;
            if (len <= 3) {
                return b;
            }
            var r = len % 3;
            return r > 0 ? b.slice(0, r) + "," + b.slice(r, len).match(/\d{3}/g).join(",") : b.slice(r, len).match(/\d{3}/g).join(",");
        },
        fixNumer: function (num, max) {
            var numStr = num.toString();
            var r = num;
            if (numStr.length < max) {
                var zero = '';
                for (var i = 0; i < max - numStr.length; i++) {
                    zero += '0';
                }
                r = zero + numStr;
            }
            return r;
        },
        getLen: function (str) {
            if (str == null) return 0;
            if (typeof str != "string") {
                str += "";
            }
            return str.replace(/[^\x00-\xff]/g, "01").length;
        },
        //处理超过长度的字符，截取maxlength的字符
        subString: function (str, n, fix) {
            fix = !fix ? '' : fix
            var len = $.tools.getLen(str);
            if (len > n) {
                var newlen = Math.floor(n / 2);
                var stringLength = str.length;
                var newString = "";
                for (var i = newlen; i <= stringLength; i++) {
                    var tempString = str.substr(0, i);
                    if ($.tools.getLen(tempString) > n) {
                        return newString + fix;
                    } else {
                        newString = tempString;
                    }
                }
            } else {
                return str;
            }
        },
        isChinese: function (temp) {
            var re = /[^\u4e00-\u9fa5]/;
            if (re.test(temp)) return false;
            return true;
        },
        getRnd: function (num1, num2) {
            switch (arguments.length) {
                case 1:
                    return Math.floor(Math.random() * arguments[0] + 1);     //返回0-max的随机整数
                case 2:
                    var min = arguments[0], max = arguments[1];
                    if (arguments[0] - arguments[1] > 0) {
                        min = arguments[1];
                        max = arguments[0];
                    }
                    return Math.floor(Math.random() * (max - min + 1) + min);      //返回min-max的随机整数
                default:
                    return 0;
            }
        },
        getRndArrayItems: function (arr, count) {
            var shuffled = arr.slice(0), i = arr.length, min = i - count, temp, index;
            while (i-- > min) {
                index = Math.floor((i + 1) * Math.random());
                temp = shuffled[index];
                shuffled[index] = shuffled[i];
                shuffled[i] = temp;
            }
            return shuffled.slice(min);
        },
        // 格式化手机号，隐藏中间4位
        fixPhone: function (phone) {
            var head3 = phone.slice(0, 3);
            var end4 = phone.slice(-4);
            return head3 + '****' + end4;
        },

        //检测是否为手机号码
        isMobile: function (txt) {
            if (txt == null || txt == "") {
                return false;
            } else {
                var regex = /^((13[0-9])|(14[5,7,9])|(15[^4])|(16[0-9])|(18[0-9])|(19[0-9])|(17[0,1,3,5,6,7,8]))\d{8}$/;
                return regex.test(txt);
            }
        },

        // 检测身份证号
        isIdCard: function (idVal) {
            var _IDRe18 = /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/
            var _IDre15 = /^([1-6][1-9]|50)\d{4}\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}$/
            // 校验身份证：
            return (_IDRe18.test(idVal) || _IDre15.test(idVal))
        },

        setStorage: function (key, value) {
            try {
                localStorage.setItem(key, value);
            } catch (e) {
                console.log(e);
            }
        },
        getStorage: function (key) {
            try {
                return localStorage.getItem(key);
            } catch (e) {
                console.log(e);
            }
        },
        removeStorage: function (key) {
            try {
                localStorage.removeItem(key);
            } catch (e) {
                console.log(e);
            }
        },


        /**
         * @desc 函数节流
         * @param func 函数
         * @param wait 延迟执行毫秒数
         * @param type 1 表时间戳版(立即执行)，2 表定时器版(非立即执行)
         */
        throttle: function (func, wait, type) {
            if (type === 2) {
                var timeout;
            } else {
                var previous = 0;
            }
            return function () {
                var context = this;
                var args = arguments;
                if (type === 2) {
                    if (!timeout) {
                        timeout = setTimeout(function () {
                            timeout = null;
                            func.apply(context, args)
                        }, wait)
                    }
                } else {
                    var now = Date.now();
                    if (now - previous > wait) {
                        func.apply(context, args);
                        previous = now;
                    }
                }
            }
        },
        /**
         * @desc 函数防抖
         * @param func 函数
         * @param wait 延迟执行毫秒数
         * @param immediate true 表立即执行，false 表非立即执行
         */
        debounce: function (func, wait, immediate) {
            var timeout;
            return function () {
                var context = this;
                var args = arguments;
                if (timeout) clearTimeout(timeout);
                if (immediate) {
                    var callNow = !timeout;
                    timeout = setTimeout(function () {
                        timeout = null;
                    }, wait)
                    if (callNow) func.apply(context, args)
                } else {
                    timeout = setTimeout(function () {
                        func.apply(context, args)
                    }, wait);
                }
            }
        },

        encodeBase64URLSafeString: function (str) {
            // 首先进行Base64编码
            let base64 = btoa(str);
            // 将'+'替换为'-'，'/'替换为'_'
            base64 = base64.replace(/\+/g, '-').replace(/\//g, '_');
            // 移除末尾的'='
            base64 = base64.replace(/=+$/, '');
            return base64;
        },
        decodeBase64URLSafeString: function (str) {
            // 将'-'替换为'+'，'_'替换为'/'
            str = str.replace(/-/g, '+').replace(/_/g, '/');
            // 根据原始Base64字符串的长度，在末尾添加相应数量的'='
            let padding = str.length % 4;
            if (padding > 0) {
                str += '='.repeat(4 - padding);
            }
            // 进行Base64解码
            return atob(str);
        }
    }
}, true);

(function (jQuery) {
    var special = jQuery.event.special,
        uid1 = 'D' + (+new Date()),
        uid2 = 'D' + (+new Date() + 1);

    special.scrollstart = {
        setup: function () {

            var timer,
                handler = function (evt) {

                    var _self = this,
                        _args = arguments;

                    if (timer) {
                        clearTimeout(timer);
                    } else {
                        evt.type = 'scrollstart';
                        jQuery.event.handle.apply(_self, _args);
                    }

                    timer = setTimeout(function () {
                        timer = null;
                    }, special.scrollstop.latency);

                };

            jQuery(this).bind('scroll', handler).data(uid1, handler);

        },
        teardown: function () {
            jQuery(this).unbind('scroll', jQuery(this).data(uid1));
        }
    };

    special.scrollstop = {
        latency: 300,
        setup: function () {

            var timer,
                handler = function (evt) {

                    var _self = this,
                        _args = arguments;

                    if (timer) {
                        clearTimeout(timer);
                    }

                    timer = setTimeout(function () {

                        timer = null;
                        evt.type = 'scrollstop';
                        jQuery.event.handle.apply(_self, _args);

                    }, special.scrollstop.latency);

                };

            jQuery(this).bind('scroll', handler).data(uid2, handler);

        },
        teardown: function () {
            jQuery(this).unbind('scroll', jQuery(this).data(uid2));
        }
    };

})($);
