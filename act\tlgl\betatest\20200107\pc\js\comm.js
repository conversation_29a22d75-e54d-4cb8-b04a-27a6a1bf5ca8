var commPath='/changyou/tlgl/backtogether/',
    isClick = true,
    yyData = { //预约参数配置
        game: "tlgl",
        actvKey: "tlgl20200114",
        userId: "",//用户标志
        phone: "",//手机号
        numCode: "",//验证码
        deviceType: "",//手机型号
        cdkType: "",//礼包码是否相同
        col01: "",//其他参数1(最大32字符)
        col02: "betatest-pc",//其他参数2(最大32字符)
        col03: "",//其他参数3(最大32字符)
    };
    //适配页面
    var windowWidth = $(window).width(),_s;
    function elementFit(){
        _s = windowWidth / 1920;
        $('.page,.flodiv').css("zoom", _s);
    };
$(window).on('resize',function(){
	windowWidth = $(window).width();
	onResize();
});
function onResize(){
	elementFit();
}
elementFit();
//弹窗
var popIsShow = false;
var popDom = null;
function popShow(id) {
            popHide();
            var p = $('#'+id);
            popDom = p;
            if (p) {
                p.show().css({
                    position: 'fixed',
                    top: '50%',
                    left: '50%',
                    marginTop: -popDom.height() / 2 + 'px',
                    marginLeft: -popDom.width() / 2 + 'px',
                    zIndex: 9998
                });
                p.attr('for', 'pop');
                popIsShow = true;
                $('.overlay').show();
            }
        }
        function popHide() {
            $('.overlay').hide();
            $('.pop').hide();
        };
//分享
var share = new Cyoushare('target', {
	offset: ['10px', '10px']
	, icon: 'small'
	, title: '天龙八部归来'
	, fontSize: '14px'
	, fontFamily: '微软雅黑'
	, notShareTo: 's_renren,s_kaixin,s_qzone'
	, url: '' // 分享的地址
	, content: '天龙八部归来'
	, pic: ''//分享图片
	, showTitle: '天龙八部归来' 	
});

  
 //侧导航
 $(function () {
    var offsets = [$('#section0').offset().top*_s, $('#section1').offset().top*_s, $('#section2').offset().top*_s, $('#section3').offset().top*_s, $('#section4').offset().top*_s], len = offsets.length,lenIndex;
    $(window).bind('load scroll', function () {
      var t = $(window).scrollTop();
      navsett(t);
      if ($(window).scrollTop() + $(window).height() == $(document).height()) {
             $('.s_con a').removeClass('on').eq(4).addClass('on');  
        }
    });
    $('.s_con a').on('click',function(){
        lenIndex=$(this).index();
        $('html,body').animate({
            scrollTop: offsets[lenIndex]+55
        }, 500);
    });
   function navsett(t) {
     var fnav = $('.s_con a');
     fnav.removeClass('on');
     if (t >= offsets[len - 1])
       fnav.eq(len - 1).addClass('on');
     else {
       for (var i = 0; i < len; i++) {
         if (t >= offsets[i] && t < offsets[i+1]) fnav.eq(i).addClass('on');
       }
     }
   }
 });


 //实物收货地址
 function __changeUserName(of){
   var username=$('#'+of).val();
   if(username=='' || !isMobilePhone(username)) {
 	  $('.tj_tip p').show();
	  $('.tj_tip p').html('请输入正确的手机号码!');
 	return;
   }
 };
 function checkMobilePhone(telphone) {
 	if(telphone=='' || !isMobilePhone(telphone)) {
 	$('.tj_tip p').show();
	$('.tj_tip p').html('请输入正确的手机号码!');
   }else{
 	$('.tj_tip p').hide();
	$('.tj_tip p').html('您还有必填项未填写!');
   }
 };
 if($('#mobile').val()=='' || !isMobilePhone($('#mobile').val())) {            
 	  $('.tj_tip p').html('手机号码不正确!');
 	  ckh_result = false;
   };
 function isMobilePhone(value) {
 	if(value.search(/^(\+\d{2,3})?\d{11}$/) == -1)
 	return false;
 	else
 	return true;
 };
 function __changeCom(comname){
 	if (comname == "") {
 		$('.tj_tip p').show();
		$('.tj_tip p').html('您还有必填项未填写!');
 	}else {
 		$('.tj_tip p').hide();
 	}
 };
 function check() {
 	$('.tj_tip p').hide();
 	var ckh_result = true;     
   if($('#mobile').val()=='' || !isMobilePhone($('#mobile').val())) {            
 	  $('.tj_tip p').show();
	   $('.tj_tip p').html('请输入正确的手机号码!');
 	  ckh_result = false;
   }
   if ($('#usename').val() == '') {
 	$('.tj_tip p').show();
	$('.tj_tip p').html('您还有必填项未填写!');
 	ckh_result = false;
   }
   if ($('#address_input').val() == '') {
   	$('.tj_tip p').show();
	$('.tj_tip p').html('您还有必填项未填写!');
   	ckh_result = false;
   }
   return ckh_result;
 };
 
 //视频
$('.v_btn').on('click',function(){
    $('#pop_video').find('video').attr('src',$(this).attr('data-video'));
    popShow('pop_video');
    document.getElementById('videoTz').play();
});
$('#pop_video .close').click(function(){
	popHide();
    document.getElementById('videoTz').pause();
});
//背景视频
var canPlayVideo = document.createElement('video').canPlayType, isMob = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    if(!isMob &&canPlayVideo) {
		var video = document.getElementById('videoBg');
		video.style.display="none";
		video.src='/act/tlgl/betatest/20200107/pc/video/video.mp4';
//      video.loop=1;
        video.addEventListener("canplaythrough", function(){
            var sTime = 4;
			video.style.display="block";
            video.play();
            setInterval(function(){
                if( video.currentTime >= 8 ){
                    video.currentTime = sTime;
                }
            },40);
        });
    };
$(window).on('load scroll',function (){
        var sTop = $(window).scrollTop(),wh = $(window).height();
        $('.mid').each(function() {
            if(sTop >= $(this).offset().top - $(window).height()/1.1) {
                $(this).addClass('animate');
            }
        });
});
