<!DOCTYPE HTML>
<html>
<head>
    <meta charset="utf-8">
    <meta name="keywords" content="天龙,新天龙,天龙八部,新天龙八部,天龙八部3,tl,tlbb,tl3,xtl,网络游戏,网游,武侠,浪漫武侠,武侠游戏,金庸,<PERSON><PERSON>,天龙八部OL,萧峰,段誉,虚竹,王语嫣,天龙八部钟汉良版,天龙八部黄日华版,8周年,八周年,周年庆,全民代言,纵情四海" />
    <meta name="description" content="《新天龙八部》第十三门派桃花岛11月1日上线！高爆强控，单兵之王，箫中剑+离人偶，双武器作战方式颠覆江湖！许嵩全新桃花岛主题曲发布！" />
    <title>江湖同归-《天龙八部·归来》官方网站</title>
    <meta name="author" content="Design:CP; Web Layout:CP;" />
    <meta name="applicable-device" content="mobile">
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no">
    <meta content="yes" name="apple-mobile-web-app-capable" />
    <meta content="black" name="apple-mobile-web-app-status-bar-style" />
    <meta content="telephone=no" name="format-detection" />
    <link type="image/x-icon" href="//xtl.changyou.com/favicon.ico" rel="icon"/>
    <link type="image/x-icon" href="//xtl.changyou.com/favicon.ico" rel="shortcut icon"/>
    <link type="text/css" rel="stylesheet" href="/act/all/css/reset.css" />
    <link type="text/css" rel="stylesheet" href="/act/xtl/betatest/20200107/m/css/share.css" />
    <script>
      //移动端版本兼容
      var phoneWidth = parseInt(window.screen.width);
      var phoneScale = phoneWidth/750;
    
      var ua = navigator.userAgent;
      if (/Android (\d+\.\d+)/.test(ua)){
        var version = parseFloat(RegExp.$1);
        // andriod 2.3
        if(version>2.3){
          document.write('<meta name="viewport" content="width=750, minimum-scale = '+phoneScale+', maximum-scale = '+phoneScale+', target-densitydpi=device-dpi">');
          // andriod 2.3以上
        }else{
          document.write('<meta name="viewport" content="width=750, target-densitydpi=device-dpi">');
        }
        // 其他系统
      } else {
        document.write('<meta name="viewport" content="width=750, user-scalable=no, target-densitydpi=device-dpi">');
      }
    </script>
    <script type="text/javascript">
      var deviceWidth = document.documentElement.clientWidth;
      if (deviceWidth > 750) {
        deviceWidth = 750
      };
      document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
    </script>
</head>
<body>
<div class="rulebox">
	<a href="javascript:;" class="btn banckbtn">返回</a>
	<h2 class="tctit com">参与预约</h2>
	<p class="tc_tip">登录天龙八部·归来不删档测试预约官方网站，填写手机号和验证码，<br>报名不删档测试预约。</p>
	<div class="rulbx">
		<img src="/act/xtl/betatest/20200107/m/img/share/rule1.jpg" width="462" height="238" alt="活动规则">
	</div>
	<h2 class="tctit tctit1 com">邀请好友</h2>
	<p class="tc_tip">您可以通过点击<span>【邀请好友】</span>按钮，进入移动端分享页。<br>将不删档的消息传递给您的好友，邀请他们来一起参与。</p>
	<div class="rulbx">
		<img src="/act/xtl/betatest/20200107/m/img/share/rule2.jpg" width="462" height="238" alt="活动规则">
	</div>
	<h2 class="tctit tctit2 com">抽取奖励</h2>
	<p class="tc_tip">每当您邀请的好友到达<span>5人</span>时，您将获得1次抽奖的机<br>会，您最多可获得<span>100次</span>抽奖机会。</p>
	<div class="rulbx">
		<img src="/act/xtl/betatest/20200107/m/img/share/rule3.jpg" width="462" height="238" alt="活动规则">
	</div>
</div>
<!--#include virtual="/act/all/nav/cy_public_js_dark.html"-->
<!--#include virtual="/act/all/dma/dma_static.html"-->
<script type="text/javascript" src="/act/all/js/jquery-1.8.3.min.js"></script>
<script>
	//返回按钮判断
    var getParam = function (name) {
        var search = document.location.search;
        //alert(search);
        var pattern = new RegExp("[?&]" + name + "\=([^&]+)", "g");
        var matcher = pattern.exec(search);
        var items = null;
        if (null != matcher) {
            try {
                items = decodeURIComponent(decodeURIComponent(matcher[1]));
            } catch (e) {
                try {
                    items = decodeURIComponent(matcher[1]);
                } catch (e) {
                    items = matcher[1];
                }
            }
        }
        return items;
    };
    var adtagID=getParam('id')
    if(adtagID){
        switch(adtagID){
            case "0":
                $('.banckbtn').attr('href','share.shtml');
                break;
            case "1":
                $('.banckbtn').attr('href','beshared.shtml');
                break;
        }
    };
    if(!adtagID){
    	adtagID = 'n';
    };
</script>
</body>
</html>
