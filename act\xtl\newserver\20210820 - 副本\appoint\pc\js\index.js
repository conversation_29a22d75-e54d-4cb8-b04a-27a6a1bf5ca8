// 弹窗
function pop(msg) {
	popHide();
	$('#pop_common_txt').html(msg);
	popShow('pop_common');
}
var sign = 1,
	countdown = 60, //倒计时
	xh = null; // 倒计时的循环
$(function() {
	var isLongmen = location.href.indexOf('/longmen/') !== -1,
		isMobile = ja.glob.isMobile,
		isWechat = ja.glob.isWechat,
		oldtime = '',
		yyTime = true, // 默认在预约阶段中
		isReserve = undefined,
		isVipGift = false,
		isBackGift = false,
		isBackUser = false,
		isStopAccount = false, //封号 停权 默认为false
		isClick = true,
		vipLevel = 0,
		flag = false, //检测手机号
		initCode = undefined, // 预约错误码
		typeName = isLongmen ? 'longmen' : 'public',
		arrNew = [5000, 10000, 20000],
		arrOld = [500, 1500, 3000],
		commonPath = '/xtl/newserver/********/common/',
		httpPath = '/changyou/xtl/reserve',
		shareUrl = location.origin + '/xtl/newserver/********/' + typeName + '/return/m/share.shtml?CYSTID=share',
		keyAppoint = {},
		keyReturning = {},
		$btnReserve = $('.yybtn'), //预约按钮
		$btnGetBackGift = $('.getbackbtn'), //一键领取老友回归礼包按钮
		$btnGetGift = $('.getvipbtn'), //一键领取等级礼包按钮
		$btnSubmit = $('.tjbtn'), //提交预约按钮
		$btnYZM = $('#yz_btn'); //获取验证码按钮

	// 链接赋值/龙门
	(function() {
		if (isLongmen) {
			ja.utils.longmen({
				color: 'white',
				top: isMobile ? 100 : 170,
				left: isMobile ? 0 : 420
			});
			alert('亲爱的玩家您好，本次活动为测试，奖励仅在测试服可用，感谢您的支持！');
		}
	})();

	// ja 初始化
	(function() {
		$.get(commonPath + 'key.json', function(resKey) {
			$.get(commonPath + 'share.json', function(resShare) {
                console.log(resKey);
				keyAppoint = resKey['appoint'][typeName];
				keyReturning = resKey['return'][typeName];
				var json = resKey.appoint[typeName];
				shareObj = resShare.appoint;
				shareObj.link = ja.utils.url_add_key(location.href, 'CYSTID', 'share');
				ja.config({
					app: json.APP,
					activity: json.ACTIVITY,
					version: json.VERSIONCODE,
					platform: json.PLAT,
					type: 1,
					isWxLogin: true,
					isWxInit: true,
					wxShare: shareObj,
					ready: function(glob) {
						glob.code === 1 ? loginAfter() : loginBefore();
						getCount();
						isWechat && isWxBind();
					}
				});
			})
		});
	})();
	/**
	 *  ================== 函数定义 ===================
	 **/

	// 页面点击事件统一
	function $click(fun, actName) {
		switch (ja.glob.code) {
			case 0:
				break;
			case 1:
				if (!isStopAccount) {
					if (actName && initCode) {
						var errMsg = '';
						if (initCode == 1105) {
							errMsg = '已结束';
							yyTime = false;
						}
						pop(actName + errMsg);
					}
					fun && typeof fun === 'function' && fun();
				} else {
					// 停权
					popShow('pop_power');
				}
				break;
			case 2:
				isWechat ? popup('#pop-loginBox') : ja.login();
				break;
			case 1106:
				pop('活动未开始');
				break;
			case 1103:
				pop('活动已结束');
				break;
			case 1102:
				pop('活动无效');
				break;
			default:
		}
	}

	// 登录前
	function loginBefore() {
		$('.login .login_box').show();
		$('.login .logout_box').hide();
	}

	// 登录后
	function loginAfter() {
		$('.login .login_box').hide();
		$('.login .logout_box').show();
		popHide();
		$('#userName').html(ja.glob.userInfo.openid);
		getVipInfo();
		isBack();
		reserveInfo();
		// reserveDateRange(function (info) {

		// });
	}

	// 加载组件
	function getComponent(path, fun) {
		// 加载js
		function popMycenter() {
			$.getScript(path + 'index.js');
			fun && fun();
		}
		// 加载css
		if ($('#pop-mycenter').length === 0) {
			$('<div />').load(path + 'index.html', function() {
				popMycenter();
			}).appendTo($('body'));
		} else {
			popMycenter();
		}
	}
	// 函数防抖
	function debounce(func, wait) {
		var timeout;
		return function() {
			var context = this;
			var args = arguments;
			if (timeout) clearTimeout(timeout);
			var callNow = !timeout;
			timeout = setTimeout(function() {
				timeout = null;
			}, wait)
			if (callNow) func.apply(context, args)
		}
	}

	/**
	 *  ================== 接口请求 ===================
	 **/
	// 预约时间段查询 错误提示根据code返回
	// function reserveDateRange(fun) {
	//     ja.ajax({
	//         type: 'get',
	//         url: '/changyou/landing/init',
	//         headers: {
	//             ACTIVITY: 'newservertopic'
	//         },
	//         success: function (res) {
	//             fun && fun(res);
	//             if (res.code !== 10000) {
	//                 initCode = res.code;
	//             }
	//         }
	//     })
	// }

	// 预约人数
	function getCount() {
		ja.get(httpPath + '/reserveCount', function(res) {
			if (res.code === 10000) {
				var info = res.data,
					newNum = info.reserveCount,
					oldNum = info.backCount;
                
				// reserveSwitchCode 1105 已结束
				if (info.reserveSwitchCode !== 10000) {
					initCode = info.reserveSwitchCode;
				}
				$('.number_new').html(newNum);
				$('.number_old').html(oldNum);
				//已预约人数状态显示
				for (var i = 0; i < arrNew.length; i++) {
					if (newNum >= arrNew[i]) {
						$('#yylist1 li').eq(i).addClass('ydc');
					}
				};
				//老友已预约人数状态显示
				for (var i = 0; i < arrOld.length; i++) {
					if (oldNum >= arrOld[i]) {
						$('#yylist2 li').eq(i).addClass('ydc');
					}
				};
			}else {
                console.log(res.message);
            }
		})
	}

	// 当前账号是否预约过
	function reserveInfo() {
		ja.post(httpPath + '/reserveInfo', function(res) {
			if (res.code === 10000 && res.data) {
				// 0没有预约 1已经预约
				isReserve = res.data.reserveStatus === 1;
				if (isReserve) {
					$btnReserve.addClass('yyuyue disabled');
				}
			} else if (res.code == 3103) {
				isStopAccount == true
			}else {
                console.log(res.message);
            }
		})
	}

	function preventclick(msc) {
		var getTime = new Date().getTime();
		if (oldtime == '') {
			oldtime = getTime;
			return true;
		} else {
			var newtime = getTime;
			if (newtime - oldtime > msc) {
				oldtime = getTime;
				return true;
			} else {
				return false;
			}
		}
	}

	// Vip 等级查询
	function getVipInfo() {
		ja.post('/changyou/xtl/vip/vipLevel', function(res) {
			if (res.code === 10000 && res.data) {
				vipLevel = res.data.vipLevel || 0;
				$('.vipGrade').html(vipLevel + '级');
				if (vipLevel < 4) {
					$btnGetGift.addClass('dis_btn disabled');
				} else {
					// 1 已领取 0 没有领取
					if (res.data.vipGiftStatus == 1) {
						isVipGift = true;
						$btnGetGift.addClass('ylq disabled');
					}
				}
			} else if (res.code === 3103) { // 停权
				isStopAccount = true;
			}
		})
	}

	// 查询微信是否绑定
	function isWxBind() {
		function autoLogin() {
			ja.user.wxAutoLogin(function(res) {
				if (res.code === 10000) {
					ja.user.getUserInfo(function(res) {
						if (res.code === 10000) {
							loginAfter();
							hideMask('.pop')
						}
					})
				} else {
					alert(res.message);
				}
			})
		}

		function jumpUrl() {
			location.href = ja.glob.urlWxBind;
		}

		ja.user.wxIsBind(function(res) {
			if (res.code === 10000 && res.data) {
				$('#unBind').hide();
				$('#isBind').html(res.data + '<br />已绑定').show();
				$('#isBind').click(autoLogin);
				$('#isBind').parent().click(autoLogin);
			} else {
				$('.wxicon').show();
				$('#unBind,.wxicon').click(jumpUrl);
			}
		})
	}

	// 预约请求
	function reserve(param) {
		ja.ajax({
			type: 'post',
			async: false,
			url: httpPath + '/reserve',
			data: param,
			success: function(res) {
				$btnSubmit.removeClass('disabled');
				isClick = true;
				if (res.code == 10000) {
					isReserve = true;
					$btnReserve.addClass('dis_btn');
					reserveInfo();
					getCount();
					if (vipLevel >= 4 && !isVipGift && isBackUser  && !isBackGift) {
						getVipGift(function() {});
						getBackGift(function() {});
						if(isVipGift && isBackGift){
							$('.sus_tip').removeClass('sus_tip1');
							$('.sus_tip').html(
								'您是回归老友且VIP等级≥4级，<br>回归礼包及VIP4礼包已直接发放。<br>所有礼包可在新服开启后 ，<br>前往<span>大理（172，122）龚彩云</span>处领取。<br>江湖梦远，幸胜友归。');
							popShow('pop_sus');
							$('.yybtn').addClass('yyuyue');
						}
					}else if(vipLevel >= 4 && !isVipGift && (!isBackUser || (isBackUser && isBackGift))){
						getVipGift(function() {})
						if(isVipGift){
							$('.sus_tip').removeClass('sus_tip1');
							$('.sus_tip').html(
								'您的VIP等级≥4级，VIP4礼包已直接发放。<br>VIP4礼包及预约奖励可在新服开启后 ，<br>前往<span>大理（172，122）龚彩云</span>处领取');
							popShow('pop_sus');
							$('.yybtn').addClass('yyuyue');
						}
					}else if(isBackUser  && !isBackGift && (vipLevel < 4 || (vipLevel >= 4 && isVipGift))){
						getBackGift(function() {})
						if(isBackGift){
							$('.sus_tip').removeClass('sus_tip1');
							$('.sus_tip').html(
								'您是回归老友，回归礼包已直接发放。<br>回归礼包及预约奖励可在新服开启后 ，<br>前往<span>大理（172，122）龚彩云</span>处领取。<br>江湖梦远，幸胜友归。');
							popShow('pop_sus');
							$('.yybtn').addClass('yyuyue');
						}
					} else {
						$('.sus_tip').addClass('sus_tip1');
						$('.sus_tip').html('预约奖励可在新服开启后 ，<br>前往<span>大理（172，122）龚彩云</span>处领取');
						popShow('pop_sus');
						$('.yybtn').addClass('yyuyue');
					}
				} else if (res.code == 1024) {
					popShow('pop_fail');
				} else if (res.code == 3002) {
                    alert('验证码错误');
				} else {
					errorTip(res);
				}
				// setTimeout(function() {
				// 	isClick = true;
				// }, 500)
			}
		})
	}

	// 当前账号是否是回归玩家
	function isBack() {
		$click(function() {
			if (keyAppoint && isClick) {
				isClick = false;
				ja.ajax({
					type: 'get',
					url: '/changyou/xtl/backFlowG/isBack',
					headers: keyAppoint,
					success: function(res) {
						isClick = true;
						if (res.code === 10000 && res.data) {
							// 1是 0否
							var isFlag = Number(res.data.split('_')[0]) === 1;
							if (isFlag) {
								//是
								isBackUser = true;
								//是否已领取老友礼包
								if(Number(res.data.split('_')[1]) === 0){
									$btnGetBackGift.addClass('ylq disabled');
									isBackGift = true;
								}else{
									isBackGift = false;
								}
							} else {
								//不是 
								isBackUser = false;
								$btnGetBackGift.addClass('dis_btn disabled');								
							}
						} else {
							errorTip(res);
						}
					}
				});
			}
		})
	}

	// 验证码
	function getCode() {
		isClick = false;
		var data = {
			phone: $('#phone').val()
		}
		$btnYZM.addClass('dis_btn');
		ja.ajax({
			type: 'post',
			url: '/changyou/core/zhpt/sendcode',
			headers: keyAppoint,
			data: data,
			success: function(res) {
				if (res.code == 10000 && res.data) {
					alert('验证码发送成功')
					settime($btnYZM)
				} else {
					$btnYZM.removeClass('dis_btn');
					errorTip(res);
				}
				setTimeout(function() {
					isClick = true;
				}, 500);
			}
		})
	}

	// 倒计时
	function settime(obj) {
		if (countdown == 0) {
			clearInterval(xh)
			obj.removeClass('dis_btn').html("发送验证码");
			countdown = 60;
			return;
		} else {
			obj.addClass('dis_btn').html("" + countdown + "s后再试");
			countdown--;
		}
		xh = setTimeout(function() {
			settime(obj)
		}, 1000)
	}
	/**
	 *  ================== 按钮绑定 ===================
	 **/

	// 登录
	$('#login').click($click);

	// 登出
	$('#logout').click(ja.logout);

	// 个人中心
	$("#centerBtn").on('click', function() {
		$click(function() {
			getComponent(commonPath + 'myCenter/', function() {
				setTimeout(function() {
					popup('#pop-mycenter');
				}, 100)
			});
		});
	});

	// 账号登录
	$('.login_account').click(ja.login)
	// 立即预约
	$btnReserve.on('click', function() {
		if ($(this).hasClass('yyuyue')) return;
		$('input').val('');
		clearInterval(xh);
		$btnYZM.removeClass('dis_btn').html('发送验证码')
		countdown = 60;
		$click(function() {
			if (yyTime) { // 可以进行预约
				// 账号未停权弹窗绑定的弹窗
				popShow('pop_yz');
				$('#phone').focus();
			}
		}, '预约活动')
	});

	// 回归老友
	$('.hgbtn').on('click', function() {
		$click(function() {
			if(isBackUser){
				$('#tishi_fir').removeClass('fri_str1');
				$('#tishi_fir').html('亲爱的少侠，您<span>是回归老友</span>,<br>欢迎归来。');
				popShow('pop_fir');
			}else{
				$('#tishi_fir').removeClass('fri_str1');
				$('#tishi_fir').html('亲爱的少侠，很遗憾<br>您<span>不是回归老友。');
				popShow('pop_fir');
			}
		})
	});

	//老友礼包领取（事件）
	$btnGetBackGift.click(function() {
		if ($(this).hasClass('disabled')) return;
		$click(function() {
			if (isBackUser && !isBackGift) {
				getBackGift(function() {
					// 领取成功后的提示
					$('#tishi_fir').addClass('fri_str1');
					$('#tishi_fir').html('老友回归礼包领取成功!');
					popShow('pop_fir');
				})
			}
		});
	});

	// vip领取（事件）
	$btnGetGift.click(function() {
		if ($(this).hasClass('disabled')) return;
		$click(function() {
			if (vipLevel >= 4 && !isVipGift) {
				getVipGift(function() {
					// 领取成功后的提示
					$('#tishi_fir').addClass('fri_str1');
					$('#tishi_fir').html('VIP4等级礼包领取成功!');
					popShow('pop_fir');
				})
			}
		});
	});

	// 领取 老友礼（方法）
	function getBackGift(fun) {
		if (isClick) {
			isClick = false;
			ja.ajax({
				type: 'post',
				async: false,
				url: '/changyou/xtl/backFlowG/backReward',
				success: function(res) {
					isClick = true;
					if (res.code === 10000) {
						isBackGift = true;
						$btnGetBackGift.addClass('ylq disabled');
						fun && fun();
					} else {
						errorTip(res);
					}
				}
			})
		}
	}

	// 领取 vip礼（方法）
	function getVipGift(fun) {
		if (isClick) {
			isClick = false;
			ja.ajax({
				type: 'post',
				async: false,
				url: '/changyou/xtl/vip/receiveGift',
				success: function(res) {
                    console.log(res);
					isClick = true;
					if (res.code === 10000) {
						isVipGift = true;
						$btnGetGift.addClass('ylq disabled');
						fun && fun();
					} else {
						errorTip(res);
					}
				}
			})
		}
	}

	// 错误提示
	function errorTip(res) {
		if (res.code == 1019 || res.code == 1012) { //用户登录状态监测失败
			ja.login();
		} else if (res.code == 0) {
			alert('网络繁忙，请稍后再试')
		} else {
			alert(res.message)
		}
	}

	// 预约提交
	$btnSubmit.click(function() {
		if ($(this).hasClass('dis_btn')) return;
		var reg = new RegExp(/^\d{6}$/),
			yzmMass = '';
		$('#yzm_input').focus();
		if (vailPhone()) {
			//验证码验证
			if ($('#yzm_input').val() == '') {
				yzmMass = "验证码不能为空!";
				$('#tishi_yz').html(yzmMass);
				popShow('pop_yztishi');
			} else if (!reg.test($('#yzm_input').val())) {
				yzmMass = "验证码错误!";
				$('#yzm_input').val('');
				$('#tishi_yz').html(yzmMass);
				popShow('pop_yztishi');
			} else {
				$btnSubmit.addClass('disabled');
				if (isClick && preventclick(700)) {
					isClick = false;
					reserve({
						phone: $('#phone').val(),
						code: $('#yzm_input').val()
					})
				}
			};
		};
	});

	// 判断手机号是否合法
	function checkPhone(num) {
		if (!(/^1(3|4|5|6|7|8|9)\d{9}$/.test(num))) {
			return false;
		}
		return true;
	}

	// 验证手机号
	function vailPhone(){
		var messageErr = '', //错误信息提示
			phone = $('#phone').val();
		if (phone == '') {
			messageErr = "手机号码不能为空！";
			flag = false;
		} else if (!checkPhone(phone)) {
			messageErr = "请输入有效的手机号码！";
			flag = false;
		} else {
			flag = true;
		}
		if (!flag) {
			$('#phone').val('');
			$('#tishi_yz').html(messageErr);
			popShow('pop_yztishi');
		}
		return flag;
	};

	// 外包提供代码部分 功能性
	window.addEventListener("resize", function() {
		if (!popIsShow)
			return;
		setTimeout(function() {
			popDom.css({
				marginTop: -popDom.height() / 2 + 'px',
				marginLeft: -popDom.width() / 2 + 'px',
				zIndex: 998
			});
		}, 400)
	});

	// 验证手机号码
	$btnYZM.on('click', function() {
		if (vailPhone()) {
			if (!$(this).hasClass('dis_btn')) {
				if (preventclick(800) && isClick) {
					getCode();
				}
			}
		}
	});

	// 验证信息关闭
	$('#yz_close').on('click', function() {
		if (yyTime) {
			popHide();
			popShow('pop_yz');
		}
	});
});
// pop(弹窗)
var popIsShow = false;
var popDom = null;

function popShow(id) {
	popHide();
	var p = $('#' + id);
	popDom = p;
	if (p) {
		p.show().css({
			position: 'fixed',
			top: '50%',
			left: '50%',
			marginTop: -popDom.height() / 2 + 'px',
			marginLeft: -popDom.width() / 2 + 'px',
			zIndex: 998
		});
		p.attr('for', 'pop');
		popIsShow = true;
		if ($('[for="' + id + '"]').length >= 1) return;
		$('body').append('<div name="overlay" id="Overlay" for=' + id +
			' style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:997;background:rgba(0,0,0,0.8);"></div>');
	}
}

function popHide(ele) {
	$('[for="pop"]').hide().attr('style', '');
	$('[name="overlay"]').remove();

}
