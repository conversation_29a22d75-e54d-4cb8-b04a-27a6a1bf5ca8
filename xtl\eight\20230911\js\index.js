//横屏提示
((function () {
    function orient() {
        if (window.orientation == 0 || window.orientation == 180) {
            $('.orientLayer').css('display','none');
            $('.wrap').show()
            orientation = 'portrait';
            return false;
        } else if (window.orientation == 90 || window.orientation == -90) {
            $('.orientLayer').css('display','flex');
            $('.wrap').hide()
            orientation = 'landscape';
            return false;
        }
    }
    orient();
    $(window).bind('orientationchange', function (e) {
        orient();
    });
}()));

function getRnd(num1, num2) {
    switch (arguments.length) {
        case 1:
            return Math.floor(Math.random() * arguments[0] + 1);     //返回0-max的随机整数
        case 2:
            var min = arguments[0], max = arguments[1];
            if (arguments[0] - arguments[1] > 0) {
                min = arguments[1];
                max = arguments[0];
            }
            return Math.floor(Math.random() * (max - min + 1) + min);      //返回min-max的随机整数
        default:
            return 0;
    }
}

// 多个Deferred延迟对象可以采用$.when来实现promise.All即同步发起异步请求，等全部的异步完成后再执行
// $.when(
//   _this.ajax({url: url})
// ).done(function (res){
//     _this.config = res;
// })
function App() {
    var that = this
    that.data = {
        nowpage: 'index',
        game_status: 0, // 0-未开始， 1-进行中
        attr_info: [
            {type: 1, name: '血上限', max: 11050, min: 7981},
            {type: 2, name: '冰攻击', max: 177, min: 130},
            {type: 2, name: '火攻击', max: 177, min: 130},
            {type: 2, name: '玄攻击', max: 177, min: 130},
            {type: 2, name: '毒攻击', max: 177, min: 130},
            {type: 3, name: '外功攻击', max: 1950, min: 1420},
            {type: 3, name: '内功攻击', max: 1950, min: 1420},
            {type: 4, name: '外功防御', max: 1941, min: 1413},
            {type: 4, name: '内功防御', max: 1941, min: 1413},
            {type: 5, name: '会心', max: 9, min: 7},
            {type: 5, name: '会心防御', max: 9, min: 7},
            {type: 6, name: '力量', max: 101, min: 73},
            {type: 6, name: '灵气', max: 101, min: 73},
            {type: 7, name: '体质', max: 100, min: 73},
            {type: 7, name: '定力', max: 100, min: 73},
            {type: 8, name: '身法', max: 54, min: 40},
            {type: 9, name: '命中', max: 1084, min: 784},
            {type: 10, name: '闪避', max: 361, min: 261},
            {type: 11, name: '所有属性', max: 18, min: 14},
        ]
    }

    $('.list_attr .item_attr').each(function (i, it){
        var attr_name = $(it).children('.attr_name')
        var attr_value = $(it).children('.attr_value')
        attr_name.empty();
        attr_value.empty();
    })

    function Game(){
        var max_attr = 7;
        var st = null
        var game_status = 0; // 游戏是否开始

        // 从数组中随机取n条数据
        function randomSample(arr, n) {
            if (n > arr.length) n = arr.length;
            var randomIndexes = [];
            while (randomIndexes.length < n) {
                var randomIndex = Math.floor(Math.random() * arr.length);
                if (!randomIndexes.includes(randomIndex)) {
                    randomIndexes.push(randomIndex);
                }
            }
            var result = randomIndexes.map(function(index) {
                return arr[index];
            });
            return result;
        }

        // 随机出现所有属性中随机4-7个
        function rndAttr(n){
            var arr = randomSample(that.data.attr_info, n)
            arr.forEach((it)=>{
                it.curVal = getRnd(it.min, it.max)
            })
            return arr;
        }

        function render(arr){
            var elWraps = $('.con_index, #popMakeSuccess, .page_main')
            elWraps.each((idx, wrap)=>{
                var elItems = $(wrap).find('.list_attr .item_attr')
                elItems.each((i, el)=>{
                    var attr_name = ''
                    var attr_value = ''
                    if(i < arr.length){
                        var it = arr[i]
                        attr_name = it.name;
                        attr_value = it.curVal
                        elItems.eq(i).show();
                    } else{
                        elItems.eq(i).hide();
                    }
                    var children = $(el).children()
                    children.eq(0).text(attr_name);
                    children.eq(1).text(attr_value);
                });
            })
        }

        function start(){
            if(game_status != 1){
                game_status = 1;
                st = setInterval(function (){
                    var n = getRnd(4, 7);  // 随机获取4-7
                    var arr = rndAttr(n);
                    render(arr);
                }, 100)
            }
        }

        // 按规则随机得到指定值
        function stop(){
            var n = getRnd(4, 7);  // 随机获取4-7
            var arr = rndAttr(n);
            // 如果有出现属性相同的就复制相同值
            arr.forEach((it, i)=>{

            })
            game_status = 0
            if(st) {
                clearInterval(st);
                st = null;
            }
            console.log('stop')
            render(arr);
        }

        this.start = start;
        this.stop = stop;
        this.getStatus = function (){
            return game_status
        };
    }
    var game = new Game();
    bindEvent()
    function bindEvent(){
        lay.open('loginBox')

        $(".qr-box").qrcode({
            text: location.origin + location.pathname.replace('index', 'share'),
            width: 240,
            height: 240,
            colorDark: "#000000",
            colorLight: "#ffffff"
        });

        $('.btn_start').click(function (){
            if(game.getStatus() != 1){
                game.start();
            } else{
                setTimeout(function (){
                    game.stop();
                    lay.open('popMakeSuccess');
                }, 1500)
            }
        })

        $('.btn_back').click(function (){
            showPage('index')
        })

        $('.btn_share').click(function (){
            lay.closeAll();
            showPage('main')
        })

        $('.btn_rule').click(function (){
            lay.open('popRule')
        })

        $('.pop').on('click', '.pop_close', function (){
            var popId = $(this).parents('.pop').attr('id')
            lay.close(popId);
        })
    }
    function createPoster(cb){
        var $poster = $('#poster')
        var width = $poster.outerWidth();
        var height = $poster.outerHeight();
        html2canvas($poster.get(0), {
            useCORS: true,
            allowTaint: false,
            backgroundColor: null,
            width: width,
            height: height,
            y: $poster.offset().top,
            scrollY: 0,
            scrollX: 0,
        }).then(function (canvas) {
            var url = canvas.toDataURL("image/png");
            console.log(url);
            setTimeout(function () {
                $('#saveImg').show().removeClass('disabled');
                const img = $('<img />').attr('src', url).addClass('poster-save-img')
                $($poster).find('.poster-save-img').remove();
                $($poster).append(img);
                if(cb) cb();
            }, 100)
        }).catch((e)=>{
            console.log('海报生成失败')
            console.log(e.toString());
        })
    }

    /** 显示页面
     * @param pageId String 可选值 'index' | 'rule'
     */
    function showPage(pageId) {
        if (['index', 'main'].indexOf(pageId) === -1) {
            pageId = 'index'
        }
        $('.page_' + pageId).addClass('show').siblings().removeClass('show');
        that.data.nowpage = pageId;
        if(pageId === 'main') {
            createPoster(function () {

            });
        }
    }
    this.showPage = showPage
    this.createPoster = createPoster
}
$(function () {
    window.vm = new App();
})
