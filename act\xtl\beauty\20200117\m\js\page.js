$(function () {
    var isLongmen = location.href.indexOf('longmen') != -1;
    ja.config({
        /* config配置项详见 表 3.2 */
        app: "xtl", // 必填，应用标识
        activity: 'goddess', // 必填，活动标识
        version: isLongmen ? '2020011701' : '20200117', // 必填，活动版本标识
        platform: 'wechat', // 必填，登陆平台标识
        type: 1,
        // isWxLogin: true,
        // isWxInit: true,
        ready: function (glob) { // 非必填，初始化完成回调
            /*
            * 通过判断 glob.code 值，判断是否登录成功
            *       0 初始化未完成；
            *       1 已登录；
            *       2 未登录；
            *       1106 活动未开始；
            *       1103 活动已结束；
            *       1102 活动无效;
            * 返回全局对象 glob,详见 表 3.3
            */

            ja.glob.isWechat && ja.wx.init(function () {
                ja.wx.share({
                    link: location.origin + '/xtl/beauty/' + (isLongmen ? 'longmen' : '20200117') + '/m/share.shtml?sn=' + ja.utils.getQueryString('sn'),
                    title: '我正在参加天龙全服女神大赛，快来投我一票吧！',
                    desc: '《新天龙八部》全服女神大赛正火热进行中！参与即得稀有时装！',
                    imgUrl: 'http://i0.cy.com/tlpt/beauty/share.jpg',
                })
            })

            getTick()

            if (ja.utils.getQueryString('sn') == null) {
                // alert('请点击分享链接进入')
                return
            }
            getInfo()

            actvInfo(function () {
                // 投一票
                $('#vote')[0].addEventListener('touchstart', function () {
                    if (ja.glob.code == 0) {
                        alert('网络繁忙，请稍后再试')
                        return
                    } else if (ja.glob.code == 1106) {
                        alert('活动未开始')
                        return
                    } else if (ja.glob.code == 1103) {
                        alert('活动已结束')
                        return
                    } else if (ja.glob.code == 1102) {
                        alert('活动无效')
                        return
                    }
                    vote()

                });

                $('#shareTo').on('touchstart', function () {
                    if (ja.glob.code == 0) {
                        alert('网络繁忙，请稍后再试')
                        return
                    } else if (ja.glob.code == 1106) {
                        alert('活动未开始')
                        return
                    } else if (ja.glob.code == 1103) {
                        alert('活动已结束')
                        return
                    } else if (ja.glob.code == 1102) {
                        alert('活动无效')
                        return
                    }
                    showDia('share')
                })
            })

        }
    })

    if (isLongmen) {
        ja.utils.longmen({
            top: 200,
            left: 666
        });
    }

    function getTick() {
        ja.ajax({
            type: 'POST',
            url: '/vote/wxSelectTicketCount',
            success: function (res) {
                if (res.code === 10000) {
                    $('#myTick').text(res.data.count)
                } else if (commonErrPop(res)) {
                    //alert(res.message);
                }
            }
        })
    }
    var clickFlag = false;
    function vote(a) {
        if (clickFlag) return;
        clickFlag = true;
        ja.ajax({
            type: 'POST',
            url: '/vote/wxVote',
            data: {
                userId: ja.utils.getQueryString('sn')
            },
            success: function (res) {
                clickFlag = false;
                if (res.code === 10000) {
                    var num = Number($('#myTick').text());
                    if (num <= 0) return;
                    num--;
                    $('#myTick').text(num);
                    var count = Number($('#ticket').text());
                    count++;
                    $('#ticket').text(count);

                    $('#vote').append('<span class="sp" title="+1"></span>')
                    setTimeout(function () {
                        $('#vote').find('span').eq(0).remove()
                    }, 2000)
                } else if (commonErrPop(res)) {
                    //alert(res.message);
                }
            }
        })
    }

    function getInfo() {
        ja.ajax({
            type: 'get',
            url: '/goddess/queryGoddess',
            data: {
                sn: ja.utils.getQueryString('sn')
            },
            success: function (res) {
                if (res.code === 10000) {
                    //回调
                    $('#name').text(res.data.xtlGoddessUser.roleName)
                    $('#headImg').attr('src', res.data.xtlGoddessUser.image.split('@@@@@')[0])
                    $('#zone').text(res.data.xtlGoddessUser.server.split('_')[0])
                    $('#server').text(res.data.xtlGoddessUser.server.split('_')[1])
                    $('#xuanyan').text(Base64.decode(res.data.xtlGoddessUser.declaration))
                    $('#ticket').text(res.data.xtlGoddessUser.voteNum)
                } else if (commonErrPop(res)) {
                    //alert(res.message);
                }
            }
        })
    }

    function commonErrPop(data) {
        var flag = false;
        if (ja.glob.code == 0) {
            alert('网络繁忙，请稍后再试')
            return
        } else if (ja.glob.code == 1106) {
            alert('活动未开始')
            return
        } else if (ja.glob.code == 1103) {
            alert('活动已结束')
            return
        } else if (ja.glob.code == 1102) {
            alert('活动无效')
            return
        } else if (data.code == '1202' || data.code == '1209' || data.code == '1207' || data.code == '1007') { //1202请求参数缺失或者为空  1209操作频繁
            alert('系统繁忙，请稍后重试！');
            return;
        } else if (data.code == 3304) {
            alert(res.message)
            return;
        } else if (data.code == 5074) {
            alert('少侠，今日已完成投票，请明日再来');
            return;
        } else {
            flag = true;
        }
        return flag;
    }

    function actvInfo(cb) {
        ja.ajax({
            type: 'GET',
            url: '/goddess/actvInfo',
            async: true,
            success: function (res) {
                if (res.code == 10000) {
                    var date = new Date();
                    if (res.data.applyEndTime < date.getTime() || res.data.applyStartTime > date.getTime()) {
                        alert('当前不在投票时间段内！');
                        $('#vote')[0].addEventListener('touchstart', function () {
                            alert('当前不在投票时间段内！');
                        });
                        $('#shareTo').on('touchstart', function () {
                            alert('投当前不在投票时间段内');
                        })
                    } else {
                        cb && cb()
                    }
                } else if (commonErrPop(res)) {
                    alert(res.message);
                }
            }
        })
    }
})
