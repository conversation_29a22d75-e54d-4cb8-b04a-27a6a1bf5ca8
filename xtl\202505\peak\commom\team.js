var list = '';
for (var id in teamList) {
	var info = teamList[id];
	list += '<a data-id="' + id + '" ><span>' + info.teamName + '</span></a>';
}
$('.teamscrollbox').html(list);
function findTeam(ID) {
	var info = teamList[ID] || undefined;
	if (!info) return info;

	var list = '<div class="popclosebtn close">×</div>' +
		'<div class="teambox">' +
		'<p>战队名称：' + info.teamName + '</p>' +
		'<p>所在服务器：' + info.server + '</p>' +
		'</div>' +
		'<ul>';
	info.players.forEach(function (item, index) {
		list += '<li>' +
			'<span class="com touxian">' + (item.type ? '队长' : '队员') + '</span>' +
			'<div class="detailbox">' +
			'<div>' +
			'<p>' + item.name + '</p>' +
			'<em>' + item.level + '级</em>' +
			'<i>' + item.occ + '</i>' +
			'</div>' +
			'<span>装备评分<br>' + item.gs + '</span>' +
			'</div>' +
			'</li>';
	})
	return list + '</ul>';
}
