if (!ja.glob.isWechat) {
    popTip('请使用微信参与本次活动');
}
var inviteCode = ja.utils.getUrlParams().inviteCode, allPages = '', order = ja.utils.getUrlParams().order;
var nickName = 'nickName', headImgUrl = 'headImgUrl';
ja.config({
    app: 'tlgl', // 必填，应用标识
    activity: 'backtogether', // 必填，活动标识
    version: '20200108', // 必填，活动版本标识
    platform: 'changyou', // 必填，登陆平台标识
    isWxLogin: true,
    type: 1,
    ready: function (glob) {
        nickName = ja.glob.wechatInfo.nickname;
        headImgUrl = ja.glob.wechatInfo.headimgurl;
        wxShare(inviteCode, nickName, headImgUrl, order, 'index')
    }
});

$('#MainBg').addClass('sharebg' + order)
//分享指示图片弹出
$('.invebtn').on('click', function () {
    $shareClick(function(){
        if (!ja.glob.isWechat) {
            popTip('请使用微信参与本次活动');
            return;
        } else {
            wxShare(inviteCode, nickName, headImgUrl, order, 'beshared', function () {
                wxShare('inviteCode', 'nickName', 'headImgUrl', 'order', 'index')
                $('.fxovlay').fadeOut();
            });
            $('.fxovlay').fadeIn();
        }
    })
    
});
//分享提示关闭
$('.fxovlay').on('click', function () {
    $('.fxovlay').hide();
});
var nowPageNum = 1;
$('#goIndex').click(function(){
    window.location.href = location.origin + "/tlgl/betatest/20200107/m/index.shtml"
})














