var lb_mod=$('#lb_mod').offset().top,sTop;
//点击VIP4领奖按钮跳到VIP4领奖区域
$('.xlbtn').on('click',function(){
	$('html,body').animate({scrollTop:lb_mod});
});
$(window).on('load scroll',function(){
	$('.wrap').addClass('active');
	sTop = $(window).scrollTop();
	if(sTop>=lb_mod){
		$('.xlbtn').hide();
	}else{
		$('.xlbtn').show();
	};
});
$('.backbtn').on('click',function(){
	$('html,body').animate({scrollTop: 0},'slow');
});
//预约礼包浮层
$('.yylist .picbox').hover(function(){
	$(this).find('.fcbox').fadeIn('fast');
},function(){
	$(this).find('.fcbox').fadeOut('fast');
});
//pop(弹窗)
var popIsShow = false;
var popDom = null;
function popShow(id) {
	popHide();
	var p = $('#' + id);
	popDom = p;
	if (p) {
		p.show().css({
			position: 'fixed',
			top: '50%',
			left: '50%',
			marginTop: -popDom.height() / 2 + 'px',
			marginLeft: -popDom.width() / 2 + 'px',
			zIndex: 998
		});
		p.attr('for', 'pop');
		popIsShow = true;
		if ($('[for="' + id + '"]').length >= 1) return;
		$('body').append('<div name="overlay" for=' + id +
			' style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:997;background:rgba(0,0,0,0.8);"></div>');
	}
}
function popHide() {
	$('[for="pop"]').hide().attr('style', '');
	$('[name="overlay"]').remove();
}
window.addEventListener("resize", function() {
	if (!popIsShow)
		return;
	setTimeout(function() {
		popDom.css({
			marginTop: -popDom.height() / 2 + 'px',
			marginLeft: -popDom.width() / 2 + 'px',
			zIndex: 998
		});
	}, 400)
});
var logined=false,power,booked,vip_is,lb_get,back_player;//logined 是否登录  power是否停权 booked 是否预约 vip_is 是否>=vip4   lb_get  是否预约过且已领取礼包  //back_player 回归老友
//注销
$('#logout').on('click',function(){
	$('.login .login_box').show();
	$('.login .logout_box').hide();
	logined=false;//未登录
});
//登录
$('#login').on('click',function(){
	$('.login .login_box').hide();
	$('.login .logout_box').show();
	logined=true;// true 已登录
	power=true;// true 账号未停权
	booked=false;//true 已预约
	vip_is=true;//true 是>=vip4
	lb_get=false;//true 预约过且已领取礼包
	back_player=true;//true 是回归老友
	//预约按钮状态显示
	if(booked){
		$('.yybtn').addClass('yyuyue');
	};
	//一键领取状态显示
	if(!vip_is){
		$('.getbtn').addClass('dis_btn');
	}else{
		if(lb_get){
			$('.getbtn').addClass('ylq');
		}
	};
	//个人中心弹窗回归老友是/否显示
	if(back_player){
		$('#backPlayer').html('是');
	}else{
		$('#backPlayer').html('否');
	}
});
//预约人数状态显示
var yyuyueNum=5000;//已预约人数
var yyuyueNumArr=[5000,10000,20000];//预约人数数组
var yyuyueNumOld=1600;//老友已预约人数
var yyuyueNumOldArr=[500,1500,3000];//老友预约人数数组
$('.yynum').eq(0).html('<span>已预约</span>'+yyuyueNum);
$('.yynum').eq(1).html('<span>老友已预约</span>'+yyuyueNumOld);
//已预约人数状态显示
for(var i=0;i<yyuyueNumArr.length;i++){
	if(yyuyueNum>=yyuyueNumArr[i]){
		$('#yylist1 li').eq(i).addClass('ydc');
	}
};
//老友已预约人数状态显示
for(var i=0;i<yyuyueNumOldArr.length;i++){
	if(yyuyueNumOld>=yyuyueNumOldArr[i]){
		$('#yylist2 li').eq(i).addClass('ydc');
	}
};
//立即预约
$('.yybtn').on('click',function(){
	if(logined){//是否已登录
		if(booked){//账号是否已预约
			$('#tishi_fir').removeClass('fri_str1');
			$('#tishi_fir').html('亲爱的少侠，<br>您已经预约过此活动啦!');
			popShow('pop_fir');
		}else{
			if(power){
				//账号未停权弹窗绑定的弹窗
				popShow('pop_yz');
				$('#phone').focus();
			}else{
				popShow('pop_power');
			}
		}
	}else{
		$('#tishi_fir').addClass('fri_str1');
		$('#tishi_fir').html('亲爱的少侠，请先登录。');
		popShow('pop_fir');
	}
});
//预约提交
var message='';//错误信息提示
var bdphone=false;//该手机号码已经被绑定
var flag=false;
//验证手机号码
$('#yz_btn').on('click',function(){
	vailPhone();
});
//验证信息关闭
$('#yz_close').on('click',function(){
	popHide();
	popShow('pop_yz');
});
//验证成功
var yzflag;
var reg = new RegExp(/^\d{6}$/);
var yzmMass='';
$('.tjbtn').on('click',function(){
	vailPhone();//手机号码验证
	//验证码验证
	if($('#yzm_input').val()==''){
		yzflag=false;
		yzmMass="验证码不能为空!";
	}else if(!reg.test($('#yzm_input').val())){
		yzmMass="验证码错误!";
	}else{
		yzflag=true;
	};
	if(flag){
		if(yzflag){
			if(vip_is){
				//验证成功礼包发放
				$('.sus_tip').removeClass('sus_tip1');
				$('.sus_tip').html('您已获得祈年机会，快去祈年拿豪礼吧！<br>监测到您的VIP等级≥4级，VIP4礼包已直接发放。<br>VIP4礼包及预约奖励可在新服开启后 ，<br>前往<span>大理（172，122）龚彩云</span>处领取');
				popShow('pop_sus');
				$('.yybtn').addClass('yyuyue');
				booked=true;
			}else{
				$('.sus_tip').addClass('sus_tip1');
				$('.sus_tip').html('您已获得祈年机会，快去祈年拿豪礼吧！<br>预约奖励可在新服开启后 ，<br>前往<span>大理（172，122）龚彩云</span>处领取');
				popShow('pop_sus');
				$('.yybtn').addClass('yyuyue');
				booked=true;
			}
		}else{
			//验证码错误或者为空
			$('#yzm_input').val('');
			$('#yzm_input').focus();
			$('#tishi_yz').html(yzmMass);
			popShow('pop_yztishi');
		}
	}else{
		//手机号码为空或者错误
		$('#phone').val('');
		$('#tishi_yz').html('请填写正确的手机号码!');
		popShow('pop_yztishi');
	}
});


//验证手机号
function vailPhone(){
	var phone = $('#phone').val();
    var myreg = /^(((13[0-9]{1})|(14[0-9]{1})|(17[0]{1})|(15[0-3]{1})|(15[5-9]{1})|(18[0-9]{1}))+\d{8})$/;       
    if(phone == ''){
        message = "手机号码不能为空！";
    }else if(phone.length !=11){
        message = "请输入有效的手机号码！";
    }else if(!myreg.test(phone)){
        message = "请输入有效的手机号码！";
    }else if(bdphone){
        message = "该手机号码已经被绑定！";
    }else{
        flag = true;
    }
    if(!flag){
		$('#phone').val('');
        $('#tishi_yz').html(message);
		popShow('pop_yztishi');
    }
    return flag;
};
//一键领取
$('.getbtn').on('click',function(){
	if(logined){
		//已登录
		if(vip_is){
			if(!lb_get){
				$('#tishi_fir').addClass('fri_str1');
				$('#tishi_fir').html('礼包领取成功!');
				popShow('pop_fir');
				$('.getbtn').addClass('ylq');
				lb_get=true;
			}else{
				return;
			}
		}else{return;}
	}else{
		$('#tishi_fir').addClass('fri_str1');
		$('#tishi_fir').html('亲爱的少侠，请先登录。');
		popShow('pop_fir');
	}
});
//回归老友
$('.hgbtn').on('click',function(){
	if(logined){
		if(back_player){
			//是
			$('#tishi_fir').removeClass('fri_str1');
			$('#tishi_fir').html('亲爱的少侠，您<span>是回归老友</span>,<br>欢迎归来。');
			popShow('pop_fir');
		}else{
			//不是
			$('#tishi_fir').removeClass('fri_str1');
			$('#tishi_fir').html('亲爱的少侠，很遗憾<br>您<span>不是回归老友。');
			popShow('pop_fir');
		}
	}else{
		$('#tishi_fir').addClass('fri_str1');
		$('#tishi_fir').html('亲爱的少侠，请先登录。');
		popShow('pop_fir');
	}
});
//个人中心切换
$('.tabhd li').on('click',function(){
	$(this).addClass('on').siblings().removeClass('on');
	$('.tab_dis').removeClass('dis').eq($(this).index()).addClass('dis');
	popShow('pop_center');
});
//一键复制
var clipboard = new Clipboard('.copybtn');
clipboard.on('success', function(e) {
    console.log(e);
	$('#tishi_fir').addClass('fri_str1');
	$('#tishi_fir').html('复制成功!');
	popShow('pop_fir');
});
clipboard.on('error', function(e) {
    $('#tishi_fir').addClass('fri_str1');
    $('#tishi_fir').html('复制失败,请手动复制!');
    popShow('pop_fir');
});