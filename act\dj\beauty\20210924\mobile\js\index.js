var conf = SITE_CONFIG;
var g = {
    console: true,
    showPassword: false,
	data: {
		isLogin: false,
        page: 1,
        size: 4,
        filterData: []
	},

    // 通用请求
    ajax: function (o, cb) {
        var that = this;
        var t = {
            method: o.method || 'post',
            url: o.sp ? o.url : (conf.domain + o.url),
            data: o.data || {},
            dataType: o.dataType || 'json',
            contentType: o.contentType || 'application/x-www-form-urlencoded'
        };
        var load = null;
        (o.load) && ( load = lay.load(1,{content:'加载中，请稍后...', shade: 0.5}));
        // console.log(t);
        $.ajax({
            type: t.method,
            url: t.url,
            data: t.data,
            dataType: t.dataType,
            contentType: t.contentType,
            // xhrFields: {withCredentials: true},
            crossDomain: true,
            cache: false,
            success: function (d) {
                if(that.console || o.console) { console.log('ajax: ' + o.url + ' | ' + JSON.stringify(t.data) + ' | ' + (new Date()).valueOf(), d); }
                if(o.load){
                    setTimeout(function () {
                        lay.close(load);
                        (cb) && cb(d);
                    }, t.time ? t.time : 500);
                } else{
                    (cb) && cb(d);
                }


            },
            error: function (xhr, type) {
                var d = {
                    code: -505,
                    msg: '系统错误',
                    message: '系统错误',
                    data: xhr.responseText
                };
                if(o.console) console.log('ajax: ' + o.url + ' | ' + JSON.stringify(t.data) + ' | ' + (new Date()).valueOf(), d);
                if(o.load){
                    setTimeout(function () {
                        lay.close(load);
                        (cb) && cb(d);
                    }, t.time ? t.time : 500);
                } else{
                    (cb) && cb(d);
                }
            }
        });
    },

    // 微博分享&qq空间分享
    getShareArgs: function(o){
        var opt = $.extend(true, {
            title: '江湖风月鉴古今，《剑网3》十一周年同人嘉年华故事大赛正式开启',
            desc: '江湖风月鉴古今，《剑网3》十一周年同人嘉年华故事大赛正式开启，诚邀各位大侠参与！十一周年同人嘉年华定制特效挂件、海量现金通宝、全套官方小说等奖励等你来拿！',
            url: window.location.href,
            summary: '活动时间：5月14日-7月15日',
            icon: $.tools.getPath + 'assets/images/icon-share.png'
        }, o)
        opt = $.extend(true, opt, o)
        console.log(opt);
        switch (opt.type) {
            case 'QQ':
                var component = '';
                component += 'url=' + (encodeURIComponent(opt.url));
                component += '&title=' + encodeURIComponent(opt.title);
                component += '&desc=' + encodeURIComponent(opt.desc);
                component += '&summary='+encodeURIComponent(opt.summary);
                // component += '&site=';
                component += '&pics='+encodeURIComponent(opt.icon);
                return component;

            case 'WEIBO':
                var _component = '';
                _component += 'url=' + (encodeURIComponent(window.location.href));
                _component += '&title=' + encodeURIComponent(opt.title);
                _component += '&appkey=';
                _component += '&pic=' + opt.icon;
                _component += '&searchPic=true#_loginLayer_1556245391157';
                return _component;
        }
        return opt;
    },

	swiperInit: function(){
    	var mySwiper = new Swiper('.swiper-container', {
            loop: true, //可选选项，开启循环
            prevButton:'.swiper-prev',
            nextButton:'.swiper-next',
            observer:true,
            observeParents:true
        });
        this.swiper = mySwiper
	},

    // 下拉渲染
    renderSelect: function(){
        var that = this;
        var el = $('.selectbox select');
        var serverIdEl = $('#serverId');
        var serverNameEl = $('#serverName');
        var serverNameSelect = serverNameEl.val()
        el.empty();
        var data = SERVER_DATA;
        var options = '<option value="按服务器查找">按服务器查找</option>'
        data.forEach(function (it, idx) {
            options += '<option value="'+ it.serverName + '" data-serverid="'+ it.serverId +'" '+ (it.serverName == serverNameSelect ? 'selected' : '') +' >'+ it.serverName +'</option>'
        });
        el.append(options);

        el.on('change', function () {
            var selectedEl = el.find('option:selected')
            var selectedServerId = selectedEl.data('serverid')
            var selectedServerName = selectedEl.val()
            serverIdEl.val(selectedServerId)
            serverNameEl.val(selectedServerName)
            that.getRankData();
        })
    },
    // 拿到原始排行榜数据
    getRankData: function(){
        var that = this;
        $('.pamlist-tip').show().html('加载中...');
        that.ajax({url: conf.apis.rank, load: false, time: 1200}, function (res) {
            if(res.code == 0){
                $('.pamlist-tip').hide();
                that.data.rankData = res.data; // 全部的数据
                that.data.filterData = that.getFilterData();  // 全部的符合筛选条件的数据
                var total = that.data.filterData.length;
                that.renderRank();

                // 上一页
                $('.leftbtn').off().on('click', function () {
                    if(that.data.page <= 1){
                        return;
                    }
                    var page = that.data.page - 1;
                    var load = lay.load(1, {content:'加载中...', shade: 0.8})
                    that.data.page = page
                    that.data.filterData = that.getFilterData();  // 全部的符合筛选条件的数据
                    setTimeout(function () {
                        lay.close(load);
                        that.renderRank();
                    }, $.tools.getRnd(300, 500))

                })
                $('.rightbtn').off().on('click', function () {
                    if(that.data.page >= total){
                        return;
                    }
                    var page = that.data.page + 1
                    var load = lay.load(1, {content:'加载中...', shade: 0.8})
                    that.data.page = page
                    that.data.filterData = that.getFilterData();  // 全部的符合筛选条件的数据
                    setTimeout(function () {
                        lay.close(load);
                        that.renderRank();
                    }, $.tools.getRnd(300, 500))
                })
            }
            else{
                $('.pamlist').empty();
                $('.pamlist-tip').show().html(res.msg + '&nbsp; <a href="javascript:;" class="btn-retry">请稍后重试！</a>');
                $('.btn-retry').one('click', function () {
                    that.getRankData()
                })
            }
        })
    },
    getFilterData: function(){
        var that = this;
        var data = that.data.rankData
        var serverName = $('#serverName').val()
        var rankId = $('#rankId').val()
        var ret = data.filter(function (it, idx) {
            return !rankId ? ( (!serverName || serverName=='按服务器查找') || serverName == it.serverName ) : ( it.id == rankId )
        })
        return ret
    },
    renderRank: function(){
        var that = this;
        var ul = $('.pamlist')
        ul.empty();
        var filterData = that.data.filterData
        var total = filterData.length

        var size = that.data.size
        var pageTotal = Math.ceil(total / size)
        var page = that.data.page;

        console.log(page, total, filterData);
        if(total == 0){
            $('.pamlist-tip').show().html('没有数据，请更换筛选条件！');
            return;
        }
        if(pageTotal >= page){
            $('.pamlist-tip').hide()
            ul.removeClass('pamlist-loading pamlist-nodata')
            var list = [] = that.data.filterData.slice( ((page-1)<0 ? 0 : (page-1))*size, page*size )
            var html = ''
            list.forEach(function (it) {
                var slides = ''
                var photos = it.photo;
                if(photos){
                    photos.forEach(function (photo) {
                        photo = photo.substring(0, 2) == '//' || photo.substring(0, 4) == 'http' ? photo : ('//' + photo);
                        slides += '<div class="swiper-slide"><img src="'+ photo +'" onerror="this.οnerrοr=\'\';this.src=\'/act/dj/beauty/20210924/pc/img/img-loading.png\'"></div>'
                    })
                }
                html += '<li>\n' +
                    '\t\t\t\t\t\t<div class="nrbox">\n' +
                    '\t\t\t\t\t\t\t<div class="swibox">\n' +
                    '\t\t\t\t\t\t\t\t<div class="swiper-container">\n' +
                    '\t\t\t\t\t\t\t\t\t<div class="swiper-wrapper">\n' +
                    slides +
                    '\t\t\t\t\t\t\t\t\t</div>\n' +
                    '\t\t\t\t\t\t\t\t<div class="swiper-prev"></div>\n' +
                    '\t\t\t\t\t\t\t\t<div class="swiper-next"></div>\n' +
                    '\t\t\t\t\t\t\t\t</div>\n' +
                    '\t\t\t\t\t\t\t</div>\n' +
                    '\t\t\t\t\t\t\t<p>序列号<span>'+ it.id +'</span></p>\n' +
                    '\t\t\t\t\t\t\t<p>服务器<span>'+ it.serverName +'</span></p>\n' +
                    '\t\t\t\t\t\t\t<p>角色名<span>'+ it.roleName +'</span></p>\n' +
                    '\t\t\t\t\t\t\t<p>职&nbsp;&nbsp;&nbsp;业<span>'+ it.profession +'</span></p>\n' +
                    '\t<p>票&nbsp;&nbsp;&nbsp;数<strong class="voteCount" data-uid="'+ it.uid +'" data-count="'+it.voteCount+'">'+ it.voteCount +'</strong></p>\n' +
                    '\t<a href="javascript:;" class="btn tpbtn" title="为她投票" data-uid="'+ it.uid +'">为她投票</a>\n' +
                    '\t\t\t\t\t\t</div>\n' +
                    '\t\t\t\t\t</li>'
            });
            ul.html(html);
            that.swiperInit();
        }
        // 没有数据直接渲染为数据为空的状态
        else{
            $('.pamlist-tip').show().html('没有数据，请更换筛选条件！');
        }
    },

    // 获取验证码
    getImgCode: function(admin_change){
        var that = this;
        $('#code').val('');
        that.ajax({url: conf.apis.imageVerify, method: 'get'}, function (res) {
            if(res.code === 0){
                var img = 'data:image/jpeg;base64,' + res.data.img
                $('.img_code').attr('src', img)

                that.data.verifySign = res.data.verifySign;
                var verifyCode = res.data.verifyCode
                if(that.showPassword){
                    console.log(verifyCode);
                    $('#code').val(verifyCode)
                }
            } else{
                if(admin_change) {
                    lay.msg(res.msg)
                }
            }
        })
    },

    // 登陆弹窗
    popLogin: function(){
        var that = this;
        that.getImgCode();
        lay.open('popLogin');
        if(that.showPassword) {
            var pass = 'a123456'
            $('#account').val('***********')
            $('#psw').remove()
            $('.input_area').html('<input id="psw" value="' + pass + '" \/>')
        }
    },

    // 判断是否已登录
    checkLogin: function(from, uid){
        var that = this;
        if(!that.data.isLogin){
            console.log(uid);
            $('#popLogin').attr('data-from', from).attr('data-uid', uid)
            that.popLogin();
            return false
        }
        return true;
    },

    login: function(cb){
        var that = this;

        var cn = $('#account').val();
        var pass = $('#psw').val();
        var vCode = $('#code').val();
        var verifySign = that.data.verifySign;

        var data = {
            cn: cn,
            pass: pass,
            vCode: vCode,
            verifySign: verifySign
        }
        that.ajax({url: conf.apis.login, data: data}, function (res) {
            if(res.code == 0){
                that.data.isLogin = true;
                that.setStorage('token', res.data.token)
            }
            cb(res)
        })
    },

    // 退出登录
    logout: function(){
        var that = this;
        that.clearStorage();
        that.get_config();
    },

    setStorage: function(key, val){
        if(window.localStorage){
            localStorage.setItem(key, val)
        }
    },
    getStorage: function(key){
        if(window.localStorage){
            return localStorage.getItem(key)
        }
    },
    clearStorage: function(){
        if(window.localStorage) {
            localStorage.clear();
        }
    },

	vote: function(uid){
        var that = this
        var elVoteCount = $('.voteCount[data-uid="'+ uid + '"]')
        var voteCountOrigin = elVoteCount.data('count')
        that.ajax({url: conf.apis.vote, data: {fuid: uid, verifyToken: that.getStorage('token')}, load: true, time: 50}, function (res) {
            if(res.code == 0){
                var voteCount = res.data.userInfo.voteCount;
                that.alert('投票成功!当日剩余'+ voteCount +'票。')
                var voteCountNew = Number(voteCountOrigin)+1
                elVoteCount.data('count', voteCountNew).text(voteCountNew)
                that.get_config();
            } else{
                that.alert(res.msg);
            }
        })
	},
	get_config: function(){
    	var that = this;
    	that.ajax({url: conf.apis.init, data:{verifyToken: that.getStorage('token')}}, function (res) {
            if(res.code == 0){
                that.data.isLogin = true
                that.data.userInfo = res.data.userInfo
                that.data.userSign = res.data.userSign
            }
            else {
            	that.data.isLogin = false
                that.clearStorage();
			}
			that.renderConfig();
        })
	},

    renderConfig: function(){
    	var that = this;
    	// 已登录
        if(that.data.isLogin){
            $('.logined').show().siblings().hide();
            var userInfo = that.data.userInfo
            var userSign = that.data.userSign
            if(userInfo){
            	$('.logined span').text(userInfo.cn + '，')
			}

            // 默认分享文案
            $('.bmtips').html('参与寻找刀剑女神活动，赢iPhone13 pro max和海量专属礼包！每日投满10票还能获得女神助力礼包哦！赶紧分享给好友，一起得好礼吧！');

            // 已登陆，未报名
			if(!userSign){
                // 显示报名按钮，并且显示报名按钮
                $('.bmbtn').addClass('show').siblings().removeClass('show');
                // 未报名但是已投票
                $('.centerbtn').addClass('show');

                // 未报名
                $('#status').html('您还未报名，请先报名参加！');
                // 剩余票数
                $('#remainVote').html('当天还剩<strong>'+ userInfo.voteCount +'</strong>票可投')
                // 未参加活动，不显示参赛情况信息
                $('#partyInfo').hide();
            }
            // 已登陆，已报名
			else{
                $('.centerbtn').addClass('show').siblings().removeClass('show');
                // 未报名但已投票，分享引导文案
                if(!userSign && userInfo.isVote == 1){
                    $('.bmtips').html('您已成功投票，即刻分享给好友，一起为心中的女神助力吧~每日投满10票还能获得女神助力礼包哦！');
                }

                // 剩余票数
                $('#remainVote').html('当天还剩<strong>'+ userInfo.voteCount +'</strong>票可投')

                // 参赛情况： 只有审核通过才显示
                if(userSign.status == 0){ $('#partyInfo').show().html('<em>参赛情况：</em><p>您当前获得了<strong>'+ userSign.voteCount +'</strong>票</p>'); }
                else{ $('#partyInfo').hide(); }

                switch (userSign.status) {
                    case 0: {
                        // 审核通过

                        // 报名情况
                        $('#status').html('您已通过报名审核，您的报名序列号为<strong>'+ userSign.id +'</strong>，您当前获得了<strong>'+ userSign.voteCount +'</strong>票。')

                        // 分享引导文案
                        $('.bmtips').html('您已成功通过报名审核，即刻分享给好友，为你助力吧~每日投满10票还能获得女神助力礼包哦！');
                        break;
                    }
                    case 2: {
                        // 审核失败
                        $('#status').html('对不起，审核失败')
                         break;
                    }
                    default: {
                        // 待审核
                        $('#status').html('已经报名成功，请等待官方审核，您的报名序列号为<strong>'+ userSign.id +'</strong>。')
                    }
                }

			}
 		}
 		// 未登录
 		else{
            $('.login').show().siblings().hide();
            $('.bmbtn').addClass('show').siblings().removeClass('show');
		}
	},

    alert: function(msg){
        lay.open('popAlert')
        $('#popAlert .comtip').html(msg)
    },

    getTimeStatus: function(){
        var that = this;
        var now = new Date()
        var vote = that.data.dates.vote;
        var step1 = that.data.dates.step1;
        var step2 = that.data.dates.step2;
        if(now > new Date(vote[0]) && now < new Date(vote[1])){
            return 'vote'; // 已过报名时间，处于投票期
        }
        else if(now > new Date(step1[0]) && now < new Date(step1[1])){
            return 'result';  //  已过投票时间，还在结果等待期，11/5号投票结束-11.18日期间
        }
        else if(now > new Date(step2[0]) && now < new Date(step2[1])){
            return 'over';  // 投票&海选结束，
        }
        else if(now > new Date(step2[1])){
            // 活动全部结束
            return 'allover';
        }
        else {
            return 'sign';  // 处于报名时间
        }
    },

	init: function () {
        var that = this;

        that.data.dates = conf.dates;
        var timeStatus = that.getTimeStatus();
        that.data.timeStatus = timeStatus;
        if(that.data.timeStatus == 'result'){
            that.alert(that.data.dates.msg[1])
        }
        if(that.data.timeStatus == 'over'){
            that.alert(that.data.dates.msg[2])
        }
        if(that.data.timeStatus == 'allover'){
            that.alert(that.data.dates.msg[3])
        }

        that.swiperInit();
        that.renderSelect();
        that.getRankData();
        that.bind();
        that.get_config();
    },

	bind: function () {
    	var that = this;
        $('.pamlist').on('mouseover', '.swibox', function(){
            $(this).parent().parent().css('z-index','2');
            $(this).siblings('.fcbox').show();
        }).on('mouseout', '.swibox', function(){
            $(this).parent().parent().css('z-index','1');
            $(this).siblings('.fcbox').hide();
        });

        $('.rulebtn').click(function () {
            lay.open('popRule')
        })

        // 更换图形验证码
        $('#btn_change_code').click(function () {
			that.getImgCode(true);
        })

		// 我要报名
        $('.bmbtn').click(function () {
            if(that.data.timeStatus == 'vote'){
                that.alert(that.data.dates.msg[0])
                return;
            }
            if(that.data.timeStatus == 'result'){
                that.alert(that.data.dates.msg[1])
                return;
            }
            if(that.data.timeStatus == 'over'){
                that.alert(that.data.dates.msg[2])
                return;
            }
            if(that.data.timeStatus == 'allover'){
                that.alert(that.data.dates.msg[3])
                return;
            }

            if(that.checkLogin('sub', '')){
                location.href = 'sub.shtml'
            }
        })

        // 投票
        $('.pamlist').on('click', '.tpbtn', function () {
            var $this = $(this)
            var uid = $this.data('uid')
            if(that.data.timeStatus == 'result'){
                that.alert(that.data.dates.msg[1])
                return;
            }
            if(that.data.timeStatus == 'over'){
                that.alert(that.data.dates.msg[2])
                return;
            }
            if(that.data.timeStatus == 'allover'){
                that.alert(that.data.dates.msg[3])
                return;
            }
            if(that.checkLogin('vote', uid)){
                var uid = $(this).data('uid')
                that.vote(uid);
            }
        })

		// 登陆 链接按钮
        $('.login a').click(function () {
            if(that.checkLogin('', '')){ }
        })

		// 登陆提交
		$('.dlbtn').click(function () {
			var $this = $(this)
			if(!$this.hasClass('clicked')){
                var cn = $('#account').val();
                var pass = $('#psw').val();
                var vCode = $('#code').val();

                console.log(cn, pass, vCode);
                // 数据校验
                if(cn == '') { lay.msg('请输入用户名！'); $('.dlbtn').removeClass('clicked'); return; }
                if(pass == '') { lay.msg('请输入密码！'); $('.dlbtn').removeClass('clicked'); return; }
                if(vCode == '') { lay.msg('请输入验证码！'); $('.dlbtn').removeClass('clicked'); return; }

                that.login(function (res) {
                    console.log(res);
                    $this.removeClass('clicked')
					if(res.code == 0){
                        lay.close('popLogin');
						// 根据当前弹窗的 data-from 属性来判断是否需要进行下一步操作
						var from = $('#popLogin').data('from')
						if(from == 'sub' && !res.data.userSign){
                            lay.msg('登陆成功！');
                            $('#account').val('');
                            $('#psw').val('');
                            $('#code').val('');
                            // return;
							location.href = 'sub.shtml';
                            // window.open("sub.shtml", "_blank");
                            return;
						}
						if(from == 'vote'){
							var voteId = $('#popLogin').data('uid')
							// 发起投票
							that.vote(voteId);
                            that.get_config();
                            return;
						}
                        that.get_config();
                        lay.msg('登陆成功！');
                        $('#account').val('');
                        $('#psw').val('');
                        $('#code').val('');
					}
					else
					if(res.code == 20002) {
                        lay.msg(res.msg);
                       	that.getImgCode();
					} else{
                        lay.msg(res.msg);
					}
                })
			} else{
                $this.addClass('clicked')
			}
        })

		// 退出登录
		$('.logined a').click(function () {
			that.logout();
        })

        // 按回测搜索
        $('#rankId').on('keyup', function (e) {
            var keyCode = e.keyCode
            if(keyCode == 13){
                that.data.page = 1;
                that.getRankData()
            }
        })
        $('#rankId').on('change', function () {
            that.data.page = 1;
            that.getRankData()
        })
        // 点击搜索按钮搜索
        $('.searbtn').click(function () {
            that.data.page = 1;
            that.getRankData()
        })
        // 个人中心
        $('.centerbtn').click(function () {
            if(that.data.timeStatus == 'over'){
                that.alert(that.data.dates.msg[2])
                return;
            }
            if(that.data.timeStatus == 'allover'){
                that.alert(that.data.dates.msg[3])
                return;
            }
            lay.open('popCenter')
        })

		$('.pop').on('click', '.close', function () {
			var popId = $(this).parents('.pop').attr('id')
			lay.close(popId)
            $('#account').val('')
            $('#psw').val('')
            $('#code').val('')
        })


        $('.share, .sharebox').on('click', ' a.btn', function () {
            lay.close('popCenter')
            // 拉起分享引导层弹窗
            lay.open('popShareGuide')
        })
        $('#popShareGuide').click(function () {
            lay.closeAll();
        })
    }
}

$(function () {
	g.init();
})
