$(function(){
    isLogin(0);
    checkMessage();
});

function checkMessage(){
    var message = getParameters()['message'];
    if ("ok" != message &&  typeof message != 'undefined' && "" != trim(message) ) {
        alert(decodeURI(message));
        return;
    }
    /*else{
        var flag = getParameters()['flag'];
        var cn = getParameters()['cn'];
        //登陆成功
        if(flag==1){
            afterLogin(cn);
            var url =window.location.href;
            window.location.href = url.split('?')[0];
            //afterLogin(cn);
        }
    }*/
}

function isLogin(flag){
    $.ajax({
        type:"post", url:"/servlet/IsLoginAction.ncdo", async:false,
        data:{'id':$('#activityId').val()},
        success:function (data, textStatus) {
            data=trim(data);
            console.log(data);
            eval("outPut="+data);
            if('0'!=outPut.result){
                if(1==flag){
                    getLoginPop();
                }
            }
            else{
                if(1==flag){
                    hideMask($("#loginPop"));
                }
                document.getElementById('loginIframe').src="";
            }
            afterLocation(outPut.cn);
        },
        error:function () {
        }
    });
}

$('#toLogin').click(function(){
    isLogin(1);
    return;
});

function getLoginPop(){
    var basePath =document.getElementById('basePath').value;
    var referer =document.getElementById('referer').value;
    var value = parseInt(Math.random()*10000);
    var back = encodeURIComponent(referer+"-activityId-"+document.getElementById('activityId').value);
    document.getElementById('loginIframe').src="http://auth.changyou.com/interfaceLogin?project=activity&s="+basePath+"servlet/AuthLoginAction.ncdo?back="+back+"&v="+value;
    popup($("#loginPop"));
    return;
}

function getParameters() {
    var vars = [], hash;
    var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
    for(var i = 0; i < hashes.length; i++)
    {
        hash = hashes[i].split('=');
        vars.push(hash[0]);
        vars[hash[0]] = hash[1];
    }
    return vars;
}

/*登出检查*/
function processLogout(back){
    if (XMLHttpReq.readyState == 4) { // 判断对象状态
        if (XMLHttpReq.status == 200) { // 信息已经成功返回，开始处理信息
            location.reload();
        }
    }
}

//登录点关闭，清楚记录
$('#closeLogin').click(function(){
    //clearLoginRecord();
});

/*登出，非跨域的AJAX登出*/
function dealLoginComOut(){
    $.ajax({
        url:"/core/login/logoutManager.jsp",
        type:"post",
        data:{},
        success:function (data, textStatus) {
            var currURL = location.href;
            location.href="http://member.changyou.com/logout?b="+currURL;
        },error:function(){
            alert("系统繁忙，请稍后再试！");
        }
    });
}

/*登出检查*/
function processLogout(back){
    if (XMLHttpReq.readyState == 4) { // 判断对象状态
        if (XMLHttpReq.status == 200) { // 信息已经成功返回，开始处理信息
            location.reload();
        }
    }
}

function reloadCheckCode(flag){
    var cC="";
    if(flag==1){
        cC=document.getElementById("checkcodeImg");
    }else{
        cC=document.getElementById("checkcodeImg"+flag);
    }
    var value = parseInt(Math.random()*10000);
    cC.src="/core/login/imgcode.jsp?random_mark="+value;
}

/*
* 判断是否非空
* 输入：要检查的内容
* 输出：true 非空 false 空
*/
function isNullLogin(info){
    if(info==null || trimLogin(info)==""){
        return true;
    }

    return false;
}

/**
 * 删除左右两端的空格
 * 输入：字符串
 * 输出：去空格的字符串
 */
function trimLogin(str) {
    return str.replace(/(^\s*)|(\s*$)/g, "");
}


/**
 * 删除左右两端的空格
 * 输入：字符串
 * 输出：去空格的字符串
 */
function trim(str) {
    return str.replace(/(^\s*)|(\s*$)/g, "");
}