$(function () {
    var isInit = true,
        isLongmen = location.href.indexOf('/longmen/') !== -1,//是否为龙门
        isKaji = location.href.indexOf('/kaji/') !== -1,//是否为卡级服
        arrActList = ['appoint', 'precharge', 'return'],
        config = {},
        ajaxHeader = {},
        actName = arrActList[sign - 1],
        typeName = '',
        $content = $('.pop-mycenter .tab-center-cont'),
        $btnTab = $('.tab-center-nav a'),
        $btnShare = $('#centerShare'),
        shareUrl = '',
        linkUrl = '',
        arrAppoint = [5000, 10000, 20000],
        arrPrecharge = [12000, 30000, 60000],
        addressStatus = 1, // 默认填写收货地址
        isClick = true,
        logIdAdd = '',
        oldtime = '',
        commonPath = '/xtl/newserver/20210820/common/';
    
    // 判断当前卡级服or常规服
    if(isKaji){
        typeName = isLongmen ? 'longmen' : 'kaji';
    }else {
        typeName = isLongmen ? 'longmen' : 'public';
    }
    // 分享地址
    shareUrl = location.origin + '/xtl/newserver/20210820/' + typeName + (isLongmen ? '/longmen' : '') + '/return/m/share.shtml?CYSTID=share';
    linkUrl = location.origin + '/xtl/newserver/20210820/' + typeName + (isLongmen ? '/longmen' : '') + '/' + actName + '/pc/index.shtml';

    var giftList3 = {
        BACK: '燕归故里礼包',
        TROOPS: [
            '结伴共游*旧念',
            '结伴共游*寻情',
            '结伴共游*新趣',
            '结伴共游*同归',
        ]
    };

    if (isLongmen) {
        var isAppoint = location.href.indexOf('/appoint/') !== -1;
        $('.tab-center-nav a:eq(1)').hide();
        if(!isAppoint){
            $('.tab-center-nav a:eq(0)').hide();
        }
    };
    // 初始化
    var keyUrl = isKaji ? 'key_kaji.json' : 'key.json';
    $.get(commonPath + keyUrl, function(json) {
        config = json;
        ajaxHeader = config[arrActList[1]][typeName];
        getRoleInfo();
        switchTab(sign - 1);
    });

    // 弹窗
    function pop(info) {
        hideMask($('.pop'));
        if(info) {
            // string 为 id，否则走通用
            if(typeof info === 'string') {
                popup($('#' + info))
            }else {
                $('.pop_common_title').html(info.title || '提示');
                $('.pop_common_dec').html(info.msg);
                popup($('#PopCommon'));
            }
        }
    }

    // 函数防抖
    function debounce(func,wait) {
        var timeout;
        return function () {
            var context = this;
            var args = arguments;
            if (timeout) clearTimeout(timeout);
            var callNow = !timeout;
            timeout = setTimeout(function(){
                timeout = null;
            },wait)
            if (callNow) func.apply(context, args)
        }
    }

    // tab切换
    function switchTab(index) {
        $btnTab.removeClass('curr');
        $btnTab.eq(index).addClass('curr');
        $content.hide();
        $content.eq(index).show();
        $content.animate({ scrollTop: '0px' }, 100);
        ajaxHeader = config[arrActList[index]][typeName];
        switch (index) {
            case 1:         // 新服预充值
                getChargeCount();
                groupInfo();
                break;
            case 2:         // 江湖同归
                myLotCnt();
                teamInfo();
                break;
            default:        // 新服预约
                if(!isKaji)getVipInfo();
                reserveInfo(); //该账号是否预约
        }
    }

    // 是否是回归玩家
    function getRoleInfo() {
        ja.ajax({
            type: 'get',
            url: '/changyou/xtl/backFlowG/isBack',
            headers: ajaxHeader,
            success: function(res) {
                if (res.code === 10000 && res.data) {
                    var isBack = res.data.split('_')[0] == 1;
                    $('.is_back').text(isBack ? '是' : '否')
                    if(isBack){
                        var isGetBackLb = res.data.split('_')[1] == 0;
                        $('.is_right_1_5').text(isGetBackLb ? '√' : '×');
                    }
                }
            }
        });
    }

    // 当前账号是否预约过
    function reserveInfo() {
        ja.ajax({
            type: 'post',
            url: '/changyou/xtl/reserve/reserveInfo',
            headers: ajaxHeader,
            success: function (res) {
                if (res.code === 10000 && res.data) {
                    // 0没有预约 1已经预约
                    if(res.data.reserveStatus == '1'){
                        getPeopleNum();
                    }
                }
            }
        });

    }

    // 获取人数
    function getPeopleNum() {
        ja.ajax({
            type: 'get',
            url: '/changyou/xtl/reserve/reserveCount',
            headers: ajaxHeader,
            success: function(res) {
                if (res.code === 10000 && res.data) {
                    var num = res.data.reserveCount;
                    $('.is_right_1_1').text(num >= arrAppoint[0] ? '√' : '×');
                    $('.is_right_1_2').text(num >= arrAppoint[1] ? '√' : '×');
                    $('.is_right_1_3').text(num >= arrAppoint[2] ? '√' : '×');
                }
            }
        });
    }

    // 查询vip 等级
    function getVipInfo() {
        ja.ajax({
            type: 'post',
            url: '/changyou/xtl/vip/vipLevel',
            headers: ajaxHeader,
            success: function(res) {
                if (res.code === 10000 && res.data) {
                    $('.vip_num').text(res.data.vipLevel + '级');
                    $('.is_right_1_4').text(res.data.vipGiftStatus === 1 ? '√' : '×');
                }
            }
        });
    }

    // 获取预充值额度
    function getChargeCount() {
        ja.ajax({
            type: 'post',
            url: '/changyou/xtl/precharge/chargeCount',
            headers: ajaxHeader,
            success: function(res) {
                if (res.code === 10000 && res.data) {
                    var num = res.data.chargecount || 0;
                    $('.charge_count').text(num);
                    $('.is_right_2_1').text(num >= arrPrecharge[0] ? '√' : '×');
                    $('.is_right_2_2').text(num >= arrPrecharge[1] ? '√' : '×');
                    $('.is_right_2_3').text(num >= arrPrecharge[2] ? '√' : '×');
                }
            }
        });
    }

    // 查询拼团次数
    function groupInfo() {
        ja.ajax({
            type: 'get',
            url: '/changyou/xtl/precharge/myHasCnt',
            headers: ajaxHeader,
            success: function (res) {
                if(res.code === 10000) {
                    $('.number_group').html(res.data.hasCnt || 0);
                }
            }
        })
    }
    // 查询是否填写收货地址
    function myLotCnt() {
        ja.ajax({
            type: 'get',
            url: '/changyou/xtl/backFlowG/myLotCnt',
            headers: ajaxHeader,
            success:function(res){
                if (res.code == 10000) {
                    var data= res.data;
                    addressStatus = data.addressStatus;
                } else if (commonErrPop(res)) {
                    // alert(res.message)
                }
            }
        })
    }
    // “江湖同归”礼包
    function getGiftList(oldNum) {
        ja.ajax({
            type: 'get',
            url: '/changyou/xtl/backFlowG/prizeInfo',
            headers: ajaxHeader,
            success: function(res) {
                console.log(res);
                if (res.code === 10000 && res.data) {
                    var htmlStr = '';
                    if (res.data.length !== 0) {
                        res.data.forEach(function (item) {
                            htmlStr += '<tr>' +
                                '           <td>' + item.remark.replace("#",'') +'</td>' +
                                '           <td>' + ja.utils.formatDate(item.createTime) + '</td>' +
                                '        </tr>';
                            // 查询是否中过实物奖
                            if(item.prizeType.indexOf('LOT_R')!=-1){
                                if(item.prizeType.split('#')[1] && addressStatus == 0){ //中实物奖没填写 （展示填写按钮） 
                                    $('#pop-mycenter .fillAddress')[0].style.display='block'
                                    logIdAdd = item.prizeType.split('#')[1];
                                } 
                            } 
                        })
                    } else {
                        htmlStr = '<tr><td colspan="2">还没有礼包哦~</td></tr>'
                    }
                    $('.gift_list_body_3').html(htmlStr);
                }
            }
        });
    }

    // 获取小队成员列表
    function teamInfo() {
        ja.ajax({
            type: 'get',
            url: '/changyou/xtl/backFlowG/troopsMember2',
            headers: ajaxHeader,
            success: function(res) {
                if (res.code === 10000 && res.data) {
                    var htmlStr,
                        oldNum = 0;
                    if (res.data.length !== 0) {
                        var info = res.data[0];
                        $('.team_num').text(info.troopsSn);
                        $('.team_code').text(info.verifyCode);
                        $('.btn_one_copy').show();
                        res.data.forEach(function (item, index) {
                            htmlStr += '<tr>\n' +
                                '            <td style="word-break: break-all;">' + item.cn + '</td>\n' +
                                '            <td>' + (index === 0 ? "队长" : "队员") + '</td>\n' +
                                '            <td><a class="viewInfo"\
                                data-cn="' +  item.cn +'" \
                                data-rolename="' +  item.roleName +'" \
                                data-rolelevel="' +  item.roleLevel +'" \
                                data-contact="' +  item.contact +'" \
                                data-backflag="'+ item.backFlag +'" \
                                 href="javascript:void(0);">查看信息</a></td>\n' +
                                '       </tr>';
                            // if (item.backFlag) oldNum++;
                        });
                        // getGiftList(oldNum);
                        copy();
                    } else {
                        $('.team_num').text('无');
                        $('.team_code').text('无');
                        $('.btn_one_copy').hide();
                        htmlStr = '<tr><td colspan="4">还没有小队哦~</td></tr>'
                        //getGiftList(oldNum);
                    }
                    getGiftList(oldNum);
                    $('.team_info_body').html(htmlStr);
                }
            }
        });
    }

    // 复制
    function copy() {
        if (ja.glob.isWechat) {
            $btnShare.text('一键分享');
            $btnShare.on('click', function () {
                wxShareConfig($('#foo').text())
            })
        } else {
            if(isInit) {
                isInit = false;
                $.getScript(commonPath + '/myCenter/clipBordCopy.min.js', function () {
                    var code = $('#foo').text();
                    $btnShare.on('click', function () {
                        clipBordCopy(ja.glob.isMobile
                            ? ja.utils.url_add_key(shareUrl, 'teamCode', code)
                            : code);
                        alert('复制成功，快去粘贴发送给好友吧');
                    });
                })
            }
        }
    }

    // 分享
    function wxShareConfig(a) {
        hideMask($('.pop'));
        addOverlay('#000', 0.8);
        $('<img />').addClass('share_img').attr('src', '/act/xtl/newserver/20210820/return/m/img/sharetips.png').css({
            position: 'fixed',
            top: '10px',
            right: '0px',
            zIndex: 999
        }).appendTo($('body'));

        function hideShareImg() {
            removeOverlay();
            $('.share_img').fadeOut().remove();
            $('body').off()
        }
        
        var keyUrl = isKaji ? 'share_kaji.json' : 'share.json';
        $.get(commonPath + keyUrl, function (res) {
            var code = $('#foo').text();
            ja.share($.extend(res.share, {
                link: ja.utils.url_add_key(shareUrl, 'teamCode', code),
                success: function () {
                    hideShareImg();
                    ja.share($.extend(res[actName], { link: linkUrl }));
                }
            }));
        });

        // $(document).off()
        // $(document).on('touchstart', '#Overlay', hideShareImg);
        $('body').on('touchstart', '#Overlay', hideShareImg);
    }

     // 保存收货地址
     function saveAddr(obj){
         console.log('发起请求');
        isClick = false;
        ja.ajax({
            url:'/changyou/xtl/backFlowG/saveAddr',
            type:'post',
            data:obj,
            headers: ajaxHeader,
            async: false,
            success:function(res){
                isClick = true;
                if(res.code == 10000){
                    popup('#pop-address-suc')
                    $('.fillAddress').hide()
                } else if(commonErrPop(res)){
                    alert(res.message)
                }
            },
            error:function(){
                isClick = true;
            }
        })
    }
    // 错误提示
    function commonErrPop(data) {
        if (data.code == '1202' || data.code == '1209' || data.code == '1207' || data.code == '1007') { //1202请求参数缺失或者为空  1209操作频繁
            alert("系统繁忙，请稍后重试！");
            return;
        } else if (data.code == '1019' || data.code == '1012') { //登录状态检测失败
            $("#login").click();
            return;
        } else {
            flag = true;
        }
        return flag;
    }
    // 检测非汉字
    function checkIsChEnNum(str) {
        var pattern = /^[A-Za-z0-9\u4e00-\u9fa5]+$/gi;
        if (pattern.test(str))
            return true;
        else
            return false;
    }
     // 检测手机号
     function checkPhone(str) {
        if (!(/^1(3|4|5|6|7|8|9)\d{9}$/.test(str))) {
            return false;
        }
        return true;
    }
    // tab切换
    $btnTab.click(function () {
        switchTab($(this).index());
    });

    // 编辑收获地址
    $('.btn_write').off();
    $('.btn_write').click(function() {
        $('.input_addr_people').val('');
        $('.input_addr_phone').val('');
        $('.input_addr_dec').val('');
        pop('PopAddr')
    });

    // 弹窗关闭
    $('.p-close').click(function() {
        $btnTab.off();
        $btnShare.off();
        hideMask($('.pop'));
    });
    $("body").on("click",'.viewInfo',function(){
        var obj = {
            cn: $(this).data('cn'),
            contact:(($(this).data('contact')=='undefined'||$(this).data('contact')== 'no')?"未填写信息":$(this).data('contact')),
            roleLevel:($(this).data('rolelevel')=='undefined'||$(this).data('rolelevel')=='no')?"无":$(this).data('rolelevel'),
            roleName:$(this).data('rolename'),
            backFlag:$(this).data('backflag')
        }
        var area = serv = role = '无';
        if(obj.roleName && obj.roleName != 'no' && obj.roleName.split('_').length>1){
            area = obj.roleName.split('_')[0] || '';
            serv = obj.roleName.split('_')[1] || '';
            role = obj.roleName.split('_')[2] || '';
        }
        $("#popinf .inflist li span").eq(0).text(obj.cn);
        $("#popinf .inflist li span").eq(1).text(area);
        $("#popinf .inflist li span").eq(2).text(serv);
        $("#popinf .inflist li span").eq(3).text(role);
        $("#popinf .inflist li span").eq(4).text(obj.roleLevel);
        $("#popinf .inflist li span").eq(5).text(obj.contact);
        $("#popinf .inflist li span").eq(6).text('是否为老友：'+(obj.backFlag ? '是' : '否'));
        popup($("#popinf"));
    });
    /* $('#popinf .close').live("click",function(){
        popup($("#pop-mycenter"))
    }) */
    // 填写收货地址
    $('.center_btn_addr_submi').click(function(){
        
        var realName = $('.people_center').val().replace(/\s*/g, ""),
            phone = $('.phone_center').val(),
            address = $('.dec_center').val().replace(/\s*/g, "");
            if (realName == '') {
                $('#pop-address-err-center p').html('还未填写收货人信息呢~ <br />包裹无法准确到达您的手中呢！');
                popup('#pop-address-err-center');
            } else if (!checkIsChEnNum(realName)) {
                $('#pop-address-err-center p').html('请输入正确的姓名！');
                popup('#pop-address-err-center');
            } else if (!phone) {
                $('#pop-address-err-center p').html('还未填写您的联系方式哦~ <br />包裹无法准确到达您的手中呢！');
                popup('#pop-address-err-center');
            } else if (!checkPhone(phone)) {
                $('#pop-address-err-center p').html('请输入正确的手机号码！');
                popup('#pop-address-err-center');
            } else if (!address) {
                $('#pop-address-err-center p').html('还未填写收货地址哦~ <br />包裹无法准确到达您的手中呢！')
                popup('#pop-address-err-center');
            } else if (!checkIsChEnNum(address)) {
                $('#pop-address-err-center p').html('请输入正确的地址！');
                popup('#pop-address-err-center');
            } else {
            var obj={
                rname:realName,
                phone:phone,
                address:address,
                logId:logIdAdd
            }
            console.log('通过第一层验证');
            if(isClick && preventclick(1000)){
                console.log("通过第二层验证");
                saveAddr(obj)
            }
        }
    })
    // 频繁点击
    function preventclick(msc) {
        var getTime = new Date().getTime();
        if (oldtime == '') {
            oldtime = getTime;
            return true;
        } else {
            var newtime = getTime;
            if (newtime - oldtime > msc) {
                oldtime = getTime;
                return true;
            } else {
                return false;
            }
        }
    }
    // 提交失败 展示收货地址
    $('.pop-address-center .close').click(function(){
        hideMask();
        setTimeout(function(){
            popup('#PopAddrCenter')
        })
    })
});
