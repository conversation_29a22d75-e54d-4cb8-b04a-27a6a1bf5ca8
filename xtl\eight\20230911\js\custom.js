//横屏提示
function orient() {
    if (window.orientation == 0 || window.orientation == 180) {
        $('.orientLayer').css('display','none');
        $('.wrap').show()
        orientation = 'portrait';
        return false;
    } else if (window.orientation == 90 || window.orientation == -90) {
        $('.orientLayer').css('display','flex');
        $('.wrap').hide()
        orientation = 'landscape';
        return false;
    }
}
orient();
$(window).bind('orientationchange', function (e) {
    orient();
});

//弹窗
function Popshow (e) {
    Pophide ();
    $(e).css('display','flex');
}
function Pophide () {
    $('.pop_main').css('display','none');
}
// 多个Deferred延迟对象可以采用$.when来实现promise.All即同步发起异步请求，等全部的异步完成后再执行
// $.when(
//   _this.ajax({url: url})
// ).done(function (res){
//     _this.config = res;
// })
function App() {
    var that = this
    that.data = {
        nowpage: 'custom',
    }
    // 预加载
    that.loadAssets = new LoadAssets({
        path: '/act/xtl/eight/202030911/m/img/',
        source: [
            'bg.jpg',
            'bg2.jpg',
            'bg_con.png',
            'btn_back.png',
            'gift.png'
        ],
        start: function () {
            console.log('loading start')
        },
        loading: function (r) {
            // console.log(r)
        },
        complete: function (r) {
            console.log(r);
            hidePageLoading();
            bindEvent();
        }
    });
    /*** 资源预加载
     * @param o {source, path, complete} source--图片文件名 path--图片路径 complete--加载完成回调
     * @constructor
     */
    function LoadAssets(o) {
        var that = this;
        var path = o.path,
            source = o.source,
            start = o.start || function () {
            },
            loading = o.loading || function () {
            },
            complete = o.complete || function () {
            };
        start.apply(that)
        var count = 0,
            count_success = 0,
            count_fail = 0,
            total = source.length;
        for (var i = 0; i < total; i++) {
            var url = path + source[i];

            function xhrSend(url) {
                var xhr = new XMLHttpRequest();
                xhr.open('get', url);
                xhr.onload = function () {
                    var status = xhr.status
                    ++count
                    if (status === 200) {
                        ++count_success
                    } else {
                        ++count_fail
                    }
                    var ret = {
                        total: total,
                        success: count_success,
                        fail: count_fail,
                        count: count,
                        percent: (count / total * 100).toFixed(0) + '%'
                    }
                    loading.apply(that, [ret])
                    if (count == total) {
                        complete.apply(that, [ret]);
                    }
                };
                xhr.onerror = function () {
                    ++count
                    ++count_fail
                    var ret = {
                        total: total,
                        success: count_success,
                        fail: count_fail,
                        count: count,
                        percent: (count / total * 100).toFixed(0) + '%'
                    }
                    loading.apply(that, [ret])
                };
                xhr.send();
            }

            xhrSend(url)
        }
        return {
            success: function () {
                return count_success
            },
            fail: function () {
                return count_fail
            },
            total: function () {
                return total
            }
        }
    };

    function hidePageLoading(){
        // showPage('index')
        showPage('main')
    }
    function bindEvent(){
        $('.btn_back').click(function (){
            showPage('index')
        })
    }


    /** 显示页面
     * @param pageId String 可选值 'index' | 'rule'
     */
    function showPage(pageId) {
        if (['index', 'main'].indexOf(pageId) === -1) {
            pageId = 'index'
        }
        $('.page_' + pageId).addClass('show').siblings().removeClass('show');
        that.data.nowpage = pageId;
    }
    this.showPage = showPage
}
$(function () {
    window.vm = new App();
})
