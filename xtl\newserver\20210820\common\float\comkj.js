$('a[href*=#],area[href*=#]').click(function() {
	if(location.pathname.replace(/^\//, '') == this.pathname.replace(/^\//, '') && location.hostname == this.hostname) {
		var $target = $(this.hash);
		$target = $target.length && $target || $('[name=' + this.hash.slice(1) + ']');
		if($target.length) {
			var targetOffset = $target.offset().top;
			$('html,body').animate({
				scrollTop: targetOffset
			}, 500);
			return false;
		}
	}
});
$(function() {
	var fnav=null;
	var floatKey = location.href;
	var offsets = [];
	if (floatKey.indexOf('appoint') != -1) {
		fnav = $('.float_appoint li');
		offsets = [$('#seccont1').offset().top , $('#seccont2').offset().top ];
	}else if (floatKey.indexOf('billboard') != -1) {
		fnav = $('.float_return li');
		offsets = [$('#seccont1').offset().top , $('#seccont2').offset().top , $('#seccont3').offset().top ];
	}else if (floatKey.indexOf('precharge') != -1) {
		fnav = $('.float_precharge li');
		offsets = [$('#seccont1').offset().top , $('#seccont2').offset().top];
	};
	var	len = offsets.length;
	$(window).bind('load scroll resize', function() {
		var w = $(window).width();
		var t = $(window).scrollTop();
		if(t > $('.head').height()) {
            if(ja.glob.isMobile){
                if($(".navs").css("display") === "block" || $(".navs").css("display") === "inline"){
                    $('.flobox').hide();
	                
                }else{
                    $('.flobox').show();
                }
            }else{
                $('.flobox').show();
            }
		} else {
            $('.flobox').hide();
		};
		navsett(t);
		if($(window).scrollTop() + $(window).height() == $(document).height()) {
			fnav.removeClass('on').eq(fnav.length-1).addClass('on');
		}
	});

	function navsett(t) {
		fnav.removeClass('on');
		if(t >= offsets[len - 1])
			fnav.eq(len - 1).addClass('on');
		else {
			for(var i = 0; i < len; i++) {
				if(t >= offsets[i] && t < offsets[i + 1]) fnav.eq(i).addClass('on');
			}
		}
	}
});
$('.flobox .openbtn').on('click',function(){
	if($('.flobox').hasClass('sq')){
		$('.flobox').removeClass('sq');
		$('.openbtn').attr('title','收起');
	}else{
		$('.flobox').addClass('sq');
		$('.openbtn').attr('title','展开');
	};
});