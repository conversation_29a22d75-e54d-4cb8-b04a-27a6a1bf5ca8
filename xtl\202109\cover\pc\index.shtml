<!DOCTYPE HTML>
<html>
	<head>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
		<meta name="keywords" content="新天龙八部,天龙八部,武侠浪漫美学,网络游戏,浪漫武侠,武侠游戏,金庸,萧峰,段誉,虚竹,王语嫣,xtl,xtlbb,tlbb,天龙八部钟汉良版,天龙八部黄日华版" />
		<meta name="description" content="畅游2013年浪漫武侠网游巨作《新天龙八部》，以极致惊艳的唯美光影，对情节与角色的全面重铸，开创全新形态的浪漫江湖！" />
		<meta name="author" content="Design:CP; Web Layout:CP;" />
		<title>从刃出鞘 风云再起-《新天龙八部》官方网站</title>
		<link type="image/x-icon" href="http://xtl.changyou.com/favicon.ico" rel="icon" />
		<link type="image/x-icon" href="http://xtl.changyou.com/favicon.ico" rel="shortcut icon" />
		<link type="text/css" rel="stylesheet" href="/all/css/reset.css" />
		<link type="text/css" rel="stylesheet" href="css/style.css" />
	</head>
	<script>
		(function() {
			if (/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)) {
				var a = document.referrer,
					b = {
						"baidu.com": "seo_baidu",
						"sogou.com": "seo_sogou",
						"sm.cn": "seo_sm",
						"so.com": "seo_360",
						"bing.com": "seo_bing",
						"google.com": "seo_google"
					},
					c;
				for (c in b) {
					if (-1 != a.indexOf(c)) {
						a = b[c];
						if (window.sessionStorage) {
							sessionStorage.setItem("channel", a)
						} else {
							var d = d || 0,
								b = "";
							0 != d && (b = new Date, b.setTime(b.getTime() + 1000 * d), b = "; expires=" + b.toGMTString());
							document.cookie = "channel=" + escape(a) + b + "; path=/"
						}
						break
					}
				}
				window.location.href = "../m/index.shtml"
			};
		})();
	</script>
	<body>
		<h1 class="none">从刃出鞘 风云再起</h1>
		<div class="wrap">
			<div class="xtl_logo_lk">
				<a href="http://tl.changyou.com/index.shtml" target="_blank" title="新天龙八部"></a>
			</div>
			<div class="xtl_logo_bg"></div>
			<a href="javascript:;" class="pa btn yuyuebtn" title="新门派预约">新门派预约</a>
			<div class="bar">
				<div class="head">
					<div class="slog pa">从刃出鞘 风云再起</div>
				</div>
				<div class="cont">
					<a href="/xtl/newserver/20210820/public/appoint/pc/index.shtml" target="_blank" title="新服预约" class="btn pa xfyuyue">新服预约</a>
					<a href="/xtl/newserver/20210820/kaji/appoint/pc/index.shtml" target="_blank" title="卡级服预约" class="btn pa kjfyuyue">卡级服预约</a>
				</div>
			</div>
		</div>
		<script src="/all/js/jquery-1.8.3.min.js"></script>
		<!--#include virtual="/all/nav/xtl_white.html"-->
		<!--#include virtual="/all/dma/dma_static.html"-->
	</body>
</html>
