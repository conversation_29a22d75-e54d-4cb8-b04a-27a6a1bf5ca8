~(function () {
    function App() {
        const that = this
        let mock = false
        let mockUserInfo = false
        let mockToken = false
        let mockTokens = [
            'a9c7ePovYDughB1VMgJgzXcd2bt0lfAckyDxDv4KQcaFXkUHsbHIPVrb2JtxdwqQCtrCD3vgb_Fj6_J1KzHbWBUFhQ6DL7Yp-7JduIUxHzBlaGPdYpdMVmaHrdV-3So_e8cyaM7KW2xUDZLLmlqU1w34C_sF5V-Wb_OF79hAJ9ezIkDLVp2gICOkEObA2Q',
            '10b790xDzGC3de7q6JFCwZ38fuMbchjdRF6untQPqcd21LwQYORoxZfybjUZDyW0wBnzOk2gIoG_BX-JVqR1sax-IZ0bmWyjkd0q2GMJHlKBLYeoDQhuApbSmLWdORjq039xNerujfqtDePsE7Tf2BXZZH99cIPLL2gsYwUR9Xw_vye5xpL5er6gUPMkZA'
        ]
        let mockVerifyToken = mockTokens[0]
        let mockUserInfoDataLimit = function () {
            return {"code": -20003, "msg": "您的账号受限，暂时无法登录参与活动", "data": []}
        }
        let mockUserInfoData = {
            "msg": "操作成功",
            "code": 200,
            "data": {
                "userId": 4,
                "isReserve": 1,
                "cnMaster": null,
                "uin": null,
                "cn": "qs***<EMAIL>",
                "token": "9dcPicZVTJAOT/Dq6y68DA==",
                "phonenumber": null,
                "ticketNum": 3,
                "readCartoon": 1,
                "isRole": 1,
                "phone": false,
                "ok_sign": 2,
                "today_is_sign": false,
                "invite_my_count": 0,
                "award_list": ["000001", "000002", "000003", "000004", "000005", "000006", "000007", "0", "00000"],
                "invite_list": [{"id": 100, "num": 1, "code": "000002", "status": "0"}, {
                    "id": 101,
                    "num": 3,
                    "code": "000003",
                    "status": "0"
                }, {"id": 102, "num": 5, "code": "000006,000007", "status": "0"}],
                "map_list": [{"code": "00000", "isSign": true}, {"code": "00000", "isSign": false}, {
                    "code": "00000",
                    "isSign": false
                }, {"code": "000007", "isSign": false}, {"code": "00000", "isSign": false}, {
                    "code": "00000",
                    "isSign": false
                }, {"code": "000005,000006", "isSign": false}, {"code": "00000", "isSign": false}, {
                    "code": "00000",
                    "isSign": false
                }, {"code": "00000", "isSign": false}, {"code": "00000", "isSign": false}, {
                    "code": "00000",
                    "isSign": false
                }, {"code": "00000", "isSign": false}, {"code": "00000", "isSign": false}, {
                    "code": "00000",
                    "isSign": false
                }, {"code": "00000", "isSign": false}, {"code": "00000", "isSign": false}, {
                    "code": "00000",
                    "isSign": false
                }, {"code": "00000", "isSign": false}, {"code": "00000", "isSign": false}, {
                    "code": "00000",
                    "isSign": false
                }, {"code": "00000", "isSign": false}, {"code": "00000", "isSign": false}, {
                    "code": "00000",
                    "isSign": false
                }, {"code": "00000", "isSign": false}, {"code": "00000", "isSign": false}, {
                    "code": "00000",
                    "isSign": false
                }, {"code": "00000", "isSign": false}, {"code": "00000", "isSign": false}, {
                    "code": "00000",
                    "isSign": false
                }]
            }
        }


        const audioUrls = {
            1: '1.mp3',
            2: '2.mp3',
            3: '3-4.mp3',
            4: '3-4.mp3',
            5: '5.mp3',
            6: '6-7.mp3',
            7: '6-7.mp3',
            8: '8.mp3',
            9: '9.mp3',
            10: '10-20.mp3',
            11: '11.mp3',
            12: '12.mp3',
            13: '13.mp3',
            14: '14.mp3',
            15: '15.mp3',
            16: '16.mp3',
            17: '17.mp3',
            18: '18.mp3',
            19: '19-22-24-27.mp3',
            22: '19-22-24-27.mp3',
            24: '19-22-24-27.mp3',
            27: '19-22-24-27.mp3',
            21: '21-25.mp3',
            25: '21-25.mp3',
            26: '26.mp3',
            28: '28.mp3',
            29: '29.mp3',
            30: '30.mp3'

        }
        const audioPath = '/act/tlbbgl/rlr/20250507/m/audio/'

        this.data = {
            scenceIndex: 0,
            start_time: 'x月x日', // 新服开启时间
            activity_status: 1, // -1-未开始 0-活动结束  1-正常
            bindPhoneLink: '',
            user_info: null, // 登录信息，可用于判断是否为空
            lottInitIndex: 0,
            lott_map: {
                '0': {name: '定制黄金5g *1', img: 1, isReal: 1},
                '1': {name: '高级珍兽还童天书 *3', img: 2, isReal: 0},
                '2': {name: '时装-仙侣情缘(7天）*1', img: 3, isReal: 0},
                '3': {name: '金蚕丝 *3', img: 4, isReal: 0},
                '4': {name: '谢谢参与', img: 5, isReal: 0},
                '5': {name: '魂冰珠（1级）*3', img: 6, isReal: 0},
                '6': {name: '虹耀石 *1', img: 7, isReal: 0},
                '7': {name: '坐骑-天马（7天）*1', img: 8, isReal: 0}
            },
        }
        this.mapAudio = new Audio()


        this.ajax = function (l, d, opt) {
            let loading
            return new Promise((resolve, reject) => {
                const env = 3  // 1-内网IP接口  2-内部服务器接口  3-畅游测试接口  4-畅游正式接口
                const domainMap = {
                    1: 'https://tianlongguilai.xing-peng.com',
                    2: 'https://tianlongguilai.xing-peng.com',
                    3: 'https://api-tlgl-rlr.changyou.com',
                    4: 'https://api-tlbbglact.changyou.com'
                }
                const domain = domainMap[env]
                const activeName = 'rlr'
                const path = `/changyou/${activeName}/api/`
                const url = domain + path + l

                const data = d || {}
                const headers = {}
                let verifyToken = $.tools.getStorage('token_rlr')
                if (l !== 'login') {
                    if (mockToken) verifyToken = mockVerifyToken
                    headers.token = verifyToken
                }

                if (opt && opt.loading) loading = lay.load()

                let rq_status = 'loading'
                const rq = $.ajax({
                    url: url,
                    type: opt?.method ? opt?.method : 'POST',
                    headers: headers,
                    data: data,
                    dataType: 'json',
                    success: function (res) {
                        if (opt && opt.loading) lay.close(loading)
                        if (mockUserInfo && (l === 'login/init' || l === 'login')) {
                            res = mockUserInfoData
                        }
                        if (res.code === 601) {
                            res = {
                                code: 601,
                                msg: '登录态失效，请重新登录'
                            }
                        }
                        rq_status = 'finish'
                        resolve(res)
                    },
                    error: function (err) {
                        if (opt && opt.loading) lay.close(loading)
                        console.log('post err res', err);
                        lay.msg('接口请求繁忙，请稍后重试')
                        rq_status = 'finish'
                        if (mockUserInfo && (l === 'login/init' || l === 'login')) {
                            resolve(mockUserInfoData)
                        } else {
                            reject(err)
                        }
                    }
                })

                // if (l === 'login/init') {
                //     setTimeout(() => {
                //         // 请求超时取消请求
                //         rq.abort()
                //         rq_status = 'finish'
                //         const res = {
                //             code: 200,
                //             msg: '请求超时了'
                //         }
                //         resolve(res)
                //     }, 1000)
                // }
            })
        }

        this.loginPop = function () {
            if ($('.page1 .btn-login').hasClass('timeover')) return that.alert('当前不在活动时间内')
            const src = 'https://auth.changyou.com/interfaceLogin?project=tlglactivity&s=' + encodeURIComponent(window.location.href) + '&_=' + new Date().getTime();
            console.log('登录组件链接：', src)
            $.tools.setStorage('URL_BEFORE_LOGIN', window.location.href)
            $('#popLogin .login-box').html(`<iframe src="${src}" frameborder="0" width="100%" height="100%" scrolling="no"></iframe>`);
            that.openPop('popLogin')
        }

        this.login = function () {
            const ticket = $.tools.getStorage('ticket')
            if (!ticket) return Promise.resolve({code: -1, msg: '没有ticket'})
            // 登录
            const urlBeforeLogin = $.tools.getStorage('URL_BEFORE_LOGIN')
            if (!urlBeforeLogin) return Promise.resolve({code: -1, msg: '没有传登录前的url'})
            const s = encodeURIComponent(urlBeforeLogin)
            console.log('login接口传参：', s)
            return new Promise(resolve => {
                const data = {ticket, s}
                const CYSCID = $.tools.getParam('CYSCID')
                if (CYSCID) data.CYSCID = CYSCID
                that.ajax('login', data, {method: 'get'})
                    .then(res => {
                        $.tools.removeStorage('ticket')

                        if (res.code === -20003) {
                            that.alert('您的账号已停权')
                            $('#common .btn-confirm, #common .btn-close').one('click', function () {
                                that.logout()
                            })
                        }

                        if (res.code === 200) {
                            const token = res.data.token
                            $.tools.setStorage('token_rlr', token)
                            that.data.user_info = res.data
                            // 重新拉取初始话数据
                            // _checkLogin().then(res => {
                            //     resolve(res)
                            // })

                            resolve(res)
                        } else {
                            resolve(res)
                        }
                    })
                    .catch(res => {
                        resolve(res)
                    })
            })
        }

        function _checkLogin() {
            const uid = $.tools.getParam('uid')
            const data = {}
            if (uid) data.code = uid
            const CYSCID = $.tools.getParam('CYSCID')
            if (CYSCID) data.CYSCID = CYSCID
            const url = 'login/init'
            return that.ajax(url, data, {method: 'GET'})
        }

        this.checkLogin = function () {
            let verifyToken = $.tools.getStorage('token_rlr');
            // 缓存是否有token
            //  -无 缓存token，则表示还未登录 =>
            if (!verifyToken) {
                if ($.tools.getParam('ticket')) {
                    $.tools.setStorage('ticket', $.tools.getParam('ticket'))
                    const href = $.tools.removeParam(location.href, 'ticket')
                    return window.location.href = $.tools.removeParam(href, 'state')
                }
                //       判断缓存中是否有 ticket 参数，有则
                let ticket = $.tools.getStorage('ticket')
                if (ticket) {
                    //       -如果有，则调login接口
                    //          -接口正常code=0,拿到token，并缓存
                    //          -接口异常，则登录失败返回错误信息
                    return this.login()
                } else {
                    //       -如果无ticket参数，返回未登录
                    return Promise.resolve({code: -1, msg: '未登录'})
                }
            }
                //  -有 缓存token，调初始话接口，拉取登录信息 =>
                //       -接口正常code=0，则登录成功，返回登录信息
            //       -接口异常，登录失败，返回错误信息
            else {
                return new Promise(resolve => {
                    _checkLogin().then(res => {
                        if (res.code === -20003) {
                            that.alert('您的账号已停权')
                            $('#common .btn-confirm, #common .btn-close').one('click', function () {
                                that.logout()
                            })
                        }
                        if (res.code === -21003) {
                            lay.msg('分享参数错误')
                            setTimeout(function () {
                                location.href = $.tools.removeParam(location.href, 'uid')
                            }, 1500)
                            return
                        }
                        resolve(res)
                    }).catch(res => {
                        console.log('checkLogin catch---:', res);
                        if (mockUserInfo) res = mockData()
                        console.log('checkLogin catch mock res：', res);
                        if (res.responseJSON) res = res.responseJSON
                        resolve(res)
                    })
                })
            }
        }

        this.init = function () {
            const ticket = $.tools.getParam('ticket')
            if (ticket) {
                $.tools.setStorage('ticket', ticket)
                const href = $.tools.removeParam(location.href, 'ticket')
                return window.location.href = $.tools.removeParam(href, 'state')
            }

            if ($.tools.browser.versions.weixin) {
                console.log('微信内，初始化微信分享sdk')
                that.shareToWeiXin = new ShareToWx({
                    shareUrl: location.href.split('#')[0],
                    shareTitle: '',
                    shareDesc: '',
                    shareImg: '',
                    debug: false,
                    callback: function () {
                        //alert('');
                    },
                    ...getWxShareData()
                });

                console.log(that.shareToWeiXin);
            }

            this.playerLoadIcon = new ImgPlayer({
                el: document.querySelector('.loading_icon'),
                autoplay: true,
                loop: true,
                source: function () {
                    var imgs = []
                    for (var i = 0; i < 20; i = i + 1) {
                        imgs.push('/act/tlbbgl/rlr/20250507/m/img/ani/loading/machexulz_000' + $.tools.fixNumer(i, 2) + '.png')
                    }
                    return imgs
                }(),
                ready: function () {
                    that.playerLoadIcon.play()
                }
            })

            //默认打开页面
            open('loading');

            pageLoading(function () {
                pageInit()
                setTimeout(() => {
                    // that.open('page1')
                }, 300)
            })
        }

        function pageInit() {
            console.log('pageInit---');

            $(window).on('scroll', function () {
                var scrollTop = $(this).scrollTop();
                if (scrollTop > $('.part5').offset().top - $('.part5').height()) {
                    $('.icon_scroll_top').fadeOut();
                } else {
                    $('.icon_scroll_top').fadeIn();
                }
            });

            document.addEventListener('touchstart', function (event) {
                if (event.touches.length > 1) {
                    console.log('禁止双指放大')
                    event.preventDefault();
                }
            }, {passive: false});

            document.addEventListener('touchmove', function (event) {
                if (event.touches.length > 1) {
                    console.log('禁止双指放大')
                    event.preventDefault();
                }
            }, {passive: false});

            $('.foot_menu').removeClass('show')

            $('.start_time').text(that.data.start_time)

            $('.btn-logout').hide()

            bindEvent()

            setTimeout(() => {
                that.playerLoadIcon.pause()
                that.loading_index = lay.load()
                that.checkLogin().then(res => {
                    // 判断ticket参数，如果存在，以及获取登录态信息
                    if (res.code === -40001) {
                        // 客态分享链接uid不是队长
                        if ($.tools.getParam('uid')) {
                            lay.msg(res.msg)
                            setTimeout(() => {
                                window.location.href = $.tools.removeParam(location.href, 'uid')
                            }, 1500)
                            return
                        }
                    }

                    if (res.code === -90001) {
                        lay.close(that.loading_index)
                        // that.alert('当前不在活动时间内')
                        console.log('当前不在活动时间内')
                        $('.page1 .btn_start').addClass('timeover')
                        that.data.activity_status = -1
                    }

                    if (res.code !== 200) {
                        that.login().then(res => {
                            lay.close(that.loading_index)

                            if (res.code !== 200) {
                                lay.close(that.loading_index)
                                console.log('此时需要用户点击登录按钮')
                            } else {
                                that.refreshUserInfo()
                            }
                        })
                    }


                    that.enterScence(true)

                    if (res.code === 200) {
                        if (!that.data.user_info && res.data) that.data.user_info = res.data
                        handlerLoginSuccess()
                    }

                }).catch(e => {
                    lay.close(that.loading_index)
                    console.log(e);
                })
            }, 10)
        }

        // 登录成功回调
        function handlerLoginSuccess() {
            console.log('handlerLoginSuccess')
            lay.close(that.loading_index)

            renderPage()

            if ($.tools.browser.versions.weixin) {
                setWxShare()
            }
        }

        // 渲染页面数据
        function renderPage() {
            console.log('start renderPage')
            const {
                cn,
                isReserve,
                invite_my_count,
                invite_list,
                isRole,
                ok_sign,
                today_is_sign,
                ticketNum,
                readCartoon
            } = that.data.user_info
            $('.btn-logout').show()
            $('.J_username').text(cn)
            $('#unlogin').hide()
            $('#login').show()
            // 如果已经登录，
            //   ---没有看过动画，直接进入场景动画页面
            // if(readCartoon !== 1) {
            //     startSence()
            // }
            // //   ---已看过动画，直接进入预约主页
            // else {
            //     showMainPage()
            // }
            that.enterScence()
            // =====渲染预约按钮状态
            $('.btn_reserve')[isReserve === 1 ? 'addClass' : 'removeClass']('reserved')
            // =====渲染集结进度
            $('.pro_step .pro_prize').removeClass('get got')
            // ->集结进度领取按钮状态：0不可领取,1可领取,2已领取
            const statusClassMap = {
                0: '',
                1: 'get',
                2: 'got'
            }
            const proStepClassnameMap = {
                1: 'pro_step1',
                3: 'pro_step2',
                5: 'pro_step3'
            }
            invite_list.forEach(function (item) {
                const {num, status, code} = item
                const pro_prize_class = proStepClassnameMap[num]
                const status_class = statusClassMap[status]
                $(`.${pro_prize_class}`).addClass(status_class)
            })
            // ->集结人数进度条百分比
            let step_class = ''
            if (invite_my_count >= 1) step_class = 'step1'
            if (invite_my_count >= 3) step_class = 'step2'
            if (invite_my_count >= 5) step_class = 'step3'
            $('.part2 .pro').addClass(step_class)
            // =====创角按钮状态
            $('.btn_get').removeClass('got')
            $('.btn_get')[isRole === 1 ? 'addClass' : 'removeClass']('got')
            // =====地图点亮数量 和 地图点亮状态
            const elMap = $('.map_wrap')
            elMap.removeClass().addClass('map_wrap')
            const mapCount = ok_sign;
            $('.map_pro .cur_num').text(mapCount)
            elMap.addClass('day' + mapCount)
            // -> 签到按钮：今日是否已签到 是否已签到达30日
            if (mapCount >= 30) {
                $('.btn_sign').removeClass('signed').addClass('signed_all')
            } else if (today_is_sign) {
                $('.btn_sign').removeClass('signed_all').addClass('signed')
            }
            // =====抽奖次数渲染
            $('.lott_count em').text(ticketNum)
            $('.btn_lott')[ticketNum > 0 ? 'removeClass' : 'addClass']('disabled')
        }

        this.refreshUserInfo = function () {
            this.checkLogin().then(res => {
                if (res.code === 0) {
                    handlerLoginSuccess()
                }
            })
        }

        function pageLoading(cb) {
            var loaderInit = function (config) {
                var a = {
                    path: '/act/tlbbgl/rlr/20250507/m/img/',
                    source: [],
                    delay: 100,
                    mode: 1,
                    handle: function (count, len) {
                    },
                    complete: function () {
                    }
                };
                for (var k in config)
                    a[k] = config[k];
                var count = 0,
                    len = a.source.length,
                    array = [],
                    intv,
                    time = 0;
                for (var i = 0; i < len; i++) {
                    array.push(new Image());
                    array[i].loaded = false;
                    array[i].onload = function () {
                        this.loaded = true;
                    };
                    array[i].onerror = function () {
                        this.loaded = true;
                    };
                    array[i].src = a.path + a.source[i];
                }
                intv = setInterval(function () {
                    time += 20;
                    for (var i = 0; i < array.length; i++) {
                        if (array[i].loaded && time >= a.delay) {
                            count++;
                            array.splice(i, 1);
                            a.handle(count, len);
                            if (a.mode !== 1) {
                                time = 0;
                                break;
                            }
                        }
                    }
                    if (count === len) {
                        clearInterval(intv);
                        a.complete();
                    }
                }, 20);
            };
            var imagePath = "/act/tlbbgl/rlr/20250507/m/img/";
            loaderInit({
                path: imagePath,
                source: [
                    'pop-common.png',
                    'pop-home.png',
                    'pop-home.png',
                    'ani/loading/machexulz_00000.png',
                    'ani/loading/machexulz_00001.png',
                    'ani/loading/machexulz_00002.png',
                    'ani/loading/machexulz_00003.png',
                    'ani/loading/machexulz_00004.png',
                    'ani/loading/machexulz_00005.png',
                    'ani/loading/machexulz_00006.png',
                    'ani/loading/machexulz_00007.png',
                    'ani/loading/machexulz_00008.png',
                    'ani/loading/machexulz_00009.png',
                    'ani/loading/machexulz_00010.png',
                    'ani/loading/machexulz_00011.png',
                    'ani/loading/machexulz_00012.png',
                    'ani/loading/machexulz_00013.png',
                    'ani/loading/machexulz_00014.png'
                ],
                delay: 100,
                mode: 2,
                handle: function (count, max) {
                    // loadClick = false;
                    var per = Math.floor(count / max * 100) + '%';
                    $('.loading_inner').width(per);
                    $('.loading_percent').text(per);
                },
                complete: function () {
                    // console.log('imgs load complete');
                    cb()
                }
            });
        }

        function lotteryStart(e, config) {
            var opt = $.extend({
                    index: 0,//初始序号
                    css: 'active',//高亮样式名
                    complete: function () {
                        //to do
                    }
                }, config),
                item = $('[data-lottery]'),
                parent = item.parent(),
                speedS = 40,
                speedE = 360,
                loop = 6,
                past = 0,
                state = function (s) {
                    if (s) parent.attr('data-state', s);
                    return parent.attr('data-state');
                },
                git = function (e) {
                    for (var i = 0; i < item.length; i++) {
                        if (item.eq(i).attr('data-lottery') == e)
                            return item.eq(i);
                    }
                },
                run = function () {
                    item.removeClass(opt.css);
                    git(opt.index).addClass(opt.css);
                    if (past == loop && e == opt.index) {
                        state('结束');
                        opt.complete(opt.index);
                    } else {
                        setTimeout(run, speedS + Math.pow(past / loop, 2) * (speedE - speedS));
                    }
                    opt.index++;
                    if (opt.index >= item.length) {
                        past++;
                        opt.index = 0;
                    }
                };
            if (state() == '运行') return;
            state('运行');
            run();
        };

        function pageScroll() {
            const handler = function () {
                const scrollY = $('#page3').scrollTop()
                let navIndex = 0
                const contentY =  $('.page3_cont').offset().top
                if (scrollY >= 0 && scrollY < $("#part2").offset().top - contentY - 200) {
                    navIndex = 0
                } else if (scrollY >= ($("#part2").offset().top - contentY) - 200 && scrollY < $("#part3").offset().top - contentY - 80) {
                    navIndex = 1
                } else if (scrollY >= $("#part3").offset().top - contentY - 80 && scrollY < $("#part4").offset().top - contentY - 130) {
                    navIndex = 2
                } else if (scrollY >= $("#part4").offset().top - contentY - 130 && scrollY < $("#part5").offset().top - contentY - 300) {
                    navIndex = 3
                } else if (scrollY >= $("#part5").offset().top - contentY - 300) {
                    navIndex = 4
                }
                $('.foot_menu a').eq(navIndex).addClass('on').siblings().removeClass('on')
            }

            if (!that.isBindScroll) {
                $('#page3').on('scroll', handler)
                $(window).on('load', handler)

                $('.foot_menu a').click(function () {
                    const index = $(this).index()
                    console.log(`#part${index + 1}`, $(`#part${index + 1}`).offset().top)
                    $('#page3').animate({
                        scrollTop: $(`#part${index + 1}`).offset().top - $('.page3_cont').offset().top
                    })
                })
            }

            that.isBindScroll = true
        }

        function tsPlayer() {
            if (that.ts_player) return that.ts_player

            var videos = [
                '/act/tlbbgl/rlr/20250507/m/videos/video.ts'
            ]

            var pt = $('.scence_box'),
                elm = document.createElement('canvas');
            pt.append(elm)
            // var timer = 0
            const timeSteps = [
                {
                    timer: [7.500, 7.800], callback: function () {
                        $('.video_continue').fadeOut();
                        setTimeout(function () {
                            $('.video_continue1').show()
                        }, 300)
                    }, eventIsEmit: false
                },
                {
                    timer: [8.500, 8.800], callback: function () {
                        $('.video_continue').fadeOut();
                        setTimeout(function () {
                            $('.video_continue2').show()
                        }, 500)

                    }, eventIsEmit: false
                },
                {
                    timer: [15.300, 15.600], callback: function () {
                        $('.video_continue').fadeOut();
                        setTimeout(function () {
                            $('.video_continue3').show()
                        }, 300)
                    }, eventIsEmit: false
                },
                {
                    timer: [24.000, 24.2300], callback: function () {
                        $('.video_continue').fadeOut();
                        setTimeout(function () {
                            $('.video_continue4').show()
                        }, 300)
                    }, eventIsEmit: false
                },
                {
                    timer: [32.900, 33.200], callback: function () {
                        $('.video_continue').fadeOut();
                        setTimeout(function () {
                            $('.video_continue5').show()
                        }, 300)

                    }, eventIsEmit: false
                },
            ]
            var player = new JSMpeg.Player(videos[0], {
                canvas: elm,
                autoplay: false,
                loop: false,
                throttled: false,
                audio: false,
                chunkSize: 1 * 1024 * 1024,
                decodeFirstFrame: true,
                onPlay: function () {
                    playBgm().play()
                    $('.video_continue').hide();
                    player.volume = 0
                    //开始播放事件回调
                    if(window.st) return

                    window.st = setInterval(function () {
                        // timer = timer + 100
                        const timer = player.currentTime
                        // console.log(timer, 'player1 currentTime:', player.currentTime)
                        timeSteps.forEach(function (it) {
                            if (timer >= it.timer[0] && timer < it.timer[1]) {
                                if (!it.eventIsEmit) {
                                    player.pause()
                                    it.callback()
                                }
                                it.eventIsEmit = true
                            }
                        })
                    }, 100)
                },
                onEnded: function () {
                    //播放完成事件回调
                    window.clearInterval(window.st)
                    window.st = null
                    timer = 0
                    timeSteps.forEach(it => it.eventIsEmit = false)
                    playBgm().pause()
                    finishCartoon()
                },
                onPause: function () {
                    //暂停时的回调
                    window.clearInterval(window.st)
                    window.st = null
                },
                onVideoDecode: function () {
                    //–在每个解码和渲染的视频帧之后调用的回调
                }
            });

            player.replay = function () {
                timeSteps.forEach(it => it.eventIsEmit = false)
                if (window.st) {
                    window.clearInterval(window.st)
                    window.st = null
                }
                player.currentTime = 0
                player.play()
                playBgm().replay()
            }

            return player
        }

        function playBgm() {
            if (!that.bgm) {
                const src = '/act/tlbbgl/rlr/20250507/m/audio/3-4.mp3'
                that.bgm = new Audio(src)
                that.bgm.loop = true
                that.bgm.play().catch(error => {
                    console.error('自动播放失败:', error);
                    $('.btn_muted').addClass('muted')
                    that.bgm.muted = true
                })
            }

            function _play() {
                that.bgm.play()
            }

            function _replay() {
                that.bgm.play()
                that.bgm.currentTime = 0
            }

            function _pause() {
                that.bgm.pause()
            }

            function toggleMuted() {
                that.bgm.muted = !that.bgm.muted
                if (that.bgm.muted) that.bgm.pause()
                else that.bgm.play()
                $('.btn_muted')[that.bgm.muted ? 'addClass' : 'removeClass']('muted')
            }

            return {
                toggleMuted,
                pause: _pause,
                play: _play,
                replay: _replay
            }
        }

        // 进入场景动画，如果已选择过跳过场景，则直接进入主页
        function startSence() {
            that.ts_player = tsPlayer()
            open('page2')
            that.ts_player.currentTime = 0
            that.ts_player.play()
        }

        // 跳过场景动画
        function skipCartoon() {
            const currentTime = that.ts_player.currentTime
            const timeSteps = [0, 15.0, 23.0, 31.0, 50]
            for(let index = 0; index < timeSteps.length; index++) {
                if(index === timeSteps.length - 1) {
                    playBgm().pause()
                    return finishCartoon()
                } else {
                    if(currentTime >= timeSteps[index] && currentTime < timeSteps[index + 1]) {
                        app.ts_player.currentTime = (timeSteps[index + 1])
                        app.ts_player.play()
                        $('.video_continue').hide();
                        return
                    }
                }
            }
        }

        /**
         * 记录完成或跳过动画请求
         */
        function finishCartoon() {
            showMainPage()
            // 记录完成动画到接口
            if (that.data.user_info.readCartoon !== 1) {
                that.ajax('login/cartoon', {}, {method: 'GET'}).then(res => {
                    if (res.code === 200) {
                        that.data.user_info.readCartoon = 1
                    }
                })
            }
        }

        // 进入主页
        function showMainPage() {
            pageScroll()
            open('page3')
            $('.foot_menu').addClass('show')
        }

        // 重看动画
        function review() {
            startSence()
        }

        // 点击预约按钮
        function reserve() {
            const {isReserve, phonenumber} = that.data.user_info
            const sid = $.tools.getParam('sid')
            if (sid) {
                if (isReserve === 1) return
                if (!phonenumber) {
                    // 先查询是否绑定过手机
                    that.ajax('login/phone', {}, {method: 'GET'}).then(res => {
                        if (res.code === 200 && res.data) {
                            that.data.user_info.phonenumber = true
                            doReserve()
                        } else {
                            that.openPop('popPromote')
                        }
                    })
                }
            } else {
                doReserve()
            }
        }

        // 预约请求
        function doReserve() {
            const code = $.tools.getParam('sid')
            that.ajax('login/reserve', {code}, {method: 'GET'}).then(res => {
                if (res.code === 200) {
                    that.data.user_info.isReserve = 1
                    if (code) {
                        if (res.data) {
                            that.alert('预约助力成功')
                        } else {
                            that.alert('预约成功！助力失败')
                        }
                    } else {
                        that.alert('预约成功!')
                    }
                    return renderPage()
                }
                that.alert(res.msg)
            })
        }

        // 点击领取集结奖励
        function clickTakeTeamPrize() {
            const {invite_list, invite_my_count} = that.data.user_info
            const $this = $(this).parents('.pro_step')
            const index = $this.index()
            const {id, status} = invite_list[index]
            const prizeInfoList = [
                {name: '高级珍兽还童天书*3、乾坤壶*2', type: '邀请1人', count: 1},
                {name: '千淬神玉*3、忘无石*3', type: '邀请3人', count: 3},
                {name: '红宝石(3级)*1、灵兽精魄*3', type: '邀请4人', count: 5}
            ]
            const {type, name, count} = prizeInfoList[index]
            if (invite_my_count < count) return console.error('邀请人数未达到邀请人数：' + count)
            if (status === '1') return console.error(`您已领取过${type}奖励`)

            that.ajax('login/invite', {id}, {method: 'GET'}).then(res => {
                if (res.code === 200) {
                    renderPage()
                    return that.alert(`恭喜获得${type}奖励：${name}`)
                } else {
                    that.alert(res.msg)
                }
            })

        }

        // 点击领取创角奖励
        function clickRolePrize() {
            const {isRole} = that.data.user_info
            if (isRole === 1) return
            // 如果还未领取过创角奖励，则查询创角
            that.ajax('login/role', {}, {method: 'GET'}).then(res => {
                if (res.code === 200) {
                    if (res.data) {
                        // 已创角，表示可领奖
                        that.alert('恭喜获得创角奖励！')
                        that.data.user_info.isRole = 1
                        renderPage()
                    } else {
                        that.alert('您未在指定时间内创角！')
                    }
                    return
                }

                that.alert(res.msg)
            })
        }

        // 点击签到
        function clickSign() {
            const {ok_sign, today_is_sign} = that.data.user_info
            if (today_is_sign || (ok_sign >= 30)) return
            // 播放今日语音
            if (!$('.btn_music').hasClass('muted')) {
                const audioUrl = audioPath + audioUrls[ok_sign + 1]
                that.mapAudio.src = audioUrl
                that.mapAudio.play()
            }

            that.ajax('sign/make', {}, {method: 'POST'}).then(res => {
                if (res.code === 200) {
                    that.data.user_info.today_is_sign = true
                    that.data.user_info.ok_sign++
                    that.data.user_info.ticketNum++

                    // 累计签到1天奖励	 天罡强化精华*3 、 虹耀石*1
                    // 累计签到3天奖励	 神亦石*2 、 千淬神玉*1
                    // 累计签到7天奖励	 3级棉布*1 、 金蚕丝*5
                    // 累计签到15天奖励 回天神石*2 、 金蚕丝*5
                    // 累计签到25天奖励 称号：签王之王（永久） 、 3级秘银*1
                    const {ok_sign} = that.data.user_info
                    const signPrizeMap = {
                        1: '天罡强化精华*3、虹耀石*1',
                        3: '神亦石*2、千淬神玉*1',
                        7: '3级棉布*1、金蚕丝*5',
                        15: '回天神石*2、金蚕丝*5',
                        25: '称号：签王之王（永久）、3级秘银*1'
                    }
                    const signPrizeName = signPrizeMap[ok_sign]
                    that.alert('签到成功，恭喜获得抽奖次数+1' + (signPrizeName ? '，' + signPrizeName : ''))
                    renderPage()
                    return
                }
                that.alert(res.msg)
            })
        }

        // 抽奖
        function lott() {
            const {ticketNum, lottInitIndex} = that.data.user_info
            if (ticketNum <= 0) return that.alert('抽奖次数不足')
            that.ajax('ticket/make', {}, {method: 'POST'}).then(res => {
                if (res.code === 200) {
                    that.data.user_info.ticketNum--
                    renderPage()
                    // todo:
                    // 1、判断是否登录
                    // 2、判断是否有抽奖次数
                    // 3、拉取抽奖接口，映射抽奖奖励的PrizeCode跟页面奖励的序号
                    const {award_id: sortNum, phone} = res.data
                    lotteryStart(sortNum, {
                        index: lottInitIndex, // 初始
                        complete: function (e) {
                            const lott = that.data.lott_map[e]
                            that.data.lottInitIndex = e
                            // 未中奖
                            if (e === 4) {
                                return that.alert(lott.name)
                            }
                            if (!lott.isReal || (lott.isReal && phone)) {
                                that.alert('恭喜获得' + lott.name)
                            } else if (lott.isReal && !phone) {
                                $('.real_prize_name').text(lott.name)
                                that.openPop('writePhone')
                            }
                        }
                    });
                    renderPage()
                    return
                }
                that.alert(res.msg)
            })

        }

        // 保存手机号
        function savePhone() {
            // 校验手机号格式
            const phone = $('#phone').val()
            if (!phone) return lay.msg('请填写手机号~')
            if (!$.tools.isMobile(phone)) return lay.msg('手机号格式错误~')
            that.ajax('login/platform/setPhone', {phone}, {method: 'GET'}).then(res => {
                if (res.code === 200) {
                    that.data.user_info.phone = phone
                    return that.alert('手机号填写成功~官方将在活动结束后与您联系~')
                }
                lay.msg(res.msg)
            })
        }

        // 抽奖
        this.lott = $.tools.throttle(lott, 1000, true)

        // 个人中心
        this.enterScence = function (loading) {
            const {user_info, activity_status} = this.data
            // 活动是否结束
            if (activity_status !== 1) {
                open('page1')
                return that.alert('当前不在活动时间内')
            }
            // 未登录
            if (!user_info) {
                if (!loading) this.loginPop()
                else {
                    open('page1')
                }
                return
            }
            // 未完成场景
            if (user_info.readCartoon !== 1) {
                return startSence()
            }
            // 已登录，且已完成场景，直接跳过场景页进入主页
            showMainPage()
        }
        function userCenter() {
            const categoryMap = {
                'invite': '预约奖励',
                'sign': '签到奖励',
                'chou': '抽奖奖励',
                'other': '邀请奖励',
                'role': '创角奖励'
            }
            const ul = $('.gift-list')
            ul.empty()
            that.ajax('login/log', {}, {method: 'GET'}).then(res => {
                if (res.code === 200) {
                    that.openPop('userCenter')
                    // res.data.isReal = true
                    // res.data.phone = '17700558796'
                    const {log_list: list, isReal, phone} = res.data

                    // 判断是否有实物奖励, 且填写过收货手机号
                    const elPhoneBox = $('.btn_write_box')
                    if (isReal) {
                        elPhoneBox.show();
                        if (phone) $('.btn_write_box').html('您的收货手机为：' + phone)
                    } else {
                        elPhoneBox.hide()
                    }

                    if (list.length === 0) return ul.addClass('no-data')
                    ul.removeClass('no-data')
                    const html = list.map(item => {
                        const {category, content} = item
                        return `
                                <li>
                                    <div class="col1">${categoryMap[category]}</div>
                                    <div class="col2">${content}</div>
                                </li>`
                    }).join('\n')
                    ul.html(html)
                    return
                }
                that.alert(res.msg)
            })
        }

        this.rule = function () {
            that.openPop('rule')
        }

        // 回顾
        this.review = review

        // 退出登录
        function logout() {
            $.tools.removeStorage('LOGIN_INFO')
            $.tools.removeStorage('token_rlr')
            const b = encodeURIComponent(location.href)
            location.href = `https://auth.changyou.com/logout?b=${b} `
        }

        this.logout = logout

        // 跳转到指定页面
        function open(page) {
            if (page !== 'page3') $('.foot_menu').removeClass('show')
            $('.page').hide();
            $('#' + page).show();
        }

        this.open = open

        function copyMyLink() {
            lay.closeAll()
            if (clipBordCopy('你的兄弟，送你3级红宝石，和”黄金“点击领取-《天龙八部·归来》' + getShareLink())) {
                lay.msg('复制成功')
            } else {
                lay.msg("复制失败");
            }
        }

        function isLogin() {
            return that.data.user_info
        }

        // 获得分享链接
        function getShareLink() {
            return that.data.user_info?.userId ? $.tools.replaceParam(location.href, 'sid', that.data.user_info.userId) : location.href
        }

        // 获得微信分享参数
        function getWxShareData() {
            return {
                shareTitle: '《天龙八部·归来》兄弟重逢',
                shareDesc: '你的兄弟，送你3级红宝石，和”黄金“点击领取-《天龙八部·归来》',
                shareUrl: getShareLink(),
                shareImg: 'https://i0.cy.com/tlbbgl/pic/2025/0409/icon-100.png',
            }
        }

        // 设置微信分享参数
        function setWxShare() {
            const wxShareParams = getWxShareData()
            for (let key in wxShareParams) {
                that.shareToWeiXin[key] = wxShareParams[key]
            }
            console.log('----设置后的微信分享内容：')
            console.log(that.shareToWeiXin);
            that.shareToWeiXin.init()
        }

        //集结小队战友
        this.share = function () {
            setWxShare()
            that.openPop('shareWX')

            // if ($.tools.browser.versions.weixin) {
            //     setWxShare()
            //     that.openPop('shareWX')
            // } else {
            //     $('.ipt-copy').val(getShareLink())
            //     that.openPop('share');
            // }
        }

        function bindEvent() {

            document.addEventListener('visibilitychange', function() {
                if (document.visibilityState === 'hidden') {
                    // 页面变得不可见，例如用户切换到了另一个标签页
                    if (that.bgm?.src && !that.bgm.paused && !that.bgm.muted) {
                        that.bgm.needReset = true;
                        that.bgm.muted = true
                    }
                    if (that.mapAudio?.src && !that.mapAudio.paused && !that.mapAudio.muted) {
                        that.mapAudio.needReset = true;
                        that.mapAudio.muted = true
                    }
                } else {
                    // 页面变得可见，例如用户返回了当前标签页
                    if(that.bgm?.needReset) {
                        that.bgm.muted = false
                        that.bgm.needReset = false;
                    }
                    if(that.mapAudio?.needReset) {
                        that.mapAudio.muted = false
                        that.mapAudio.needReset = false;
                    }

                }
            });

            $('.btn_go_bind').click(function () {
                if (that.data.bindPhoneLink) location.href = that.data.bindPhoneLink
                else lay.msg('请先配置绑定手机号的链接地址~')
            })

            $('.btn_agree').click(function () {
                $(this).toggleClass('checked')
            })

            $('.btn_next').click(function () {
                that.data.scenceIndex++
                const {scenceIndex} = that.data
                if (scenceIndex <= 3) {
                    $('.scence_box').eq(scenceIndex).addClass('show').siblings().removeClass('show')
                }
            })

            // 退出登录
            $('.btn-logout').click(function () {
                // location.reload()
            })

            //复制我的邀请链接
            $('.pop-share input').click(function () {
                copyMyLink()
            })

            //分享（微信内关闭
            $('.pop-shareWX').on('click', function () {
                lay.closeAll()
            });

            $('.btn-rule').click(function () {
                that.openPop('rule')
            })

            // 跳过动画
            $('.btn_skip').click(skipCartoon)

            // 背景音乐静音
            $('.btn_muted').click(function () {
                playBgm().toggleMuted()
            })

            // 预约
            $('.btn_reserve').click($.tools.throttle(reserve, 1000, true))

            // 不填手机号，直接继续预约
            $('.btn_continue_reserve').click($.tools.throttle(doReserve, 1000, false))

            // 点击领取集结奖励
            $(".pro_icon").click($.tools.throttle(clickTakeTeamPrize, 1000, false))

            // 点击领取创角奖励
            $('.btn_get').click($.tools.throttle(clickRolePrize, 1000, false))

            // 点击签到
            $('.btn_sign').click($.tools.throttle(clickSign, 1000, false))

            // 签到的静音按钮切换
            $('.btn_music').click(function () {
                const {mapAudio} = that
                $('.btn_music').toggleClass('muted')
                if ($('.btn_music').hasClass('muted')) {
                    mapAudio.muted = true
                } else {
                    mapAudio.muted = false
                }
            })

            // 个人中心
            $('.btn_usercenter').click($.tools.throttle(userCenter, 1000, true))

            // 个人中心-填写收货手机号
            $('.btn_write_link').click(function () {
                that.openPop('writePhone')
            })

            // 填写收货手机号-确认
            $('.btn_confirm').click($.tools.throttle(savePhone, 1000, true))
            $('.btn_cancel').click(function () {
                const popId = $(this).parents('.pop').attr('id')
                lay.close(popId)
            })

            // 通用弹窗里的点击关闭按钮
            $('#common .btn-confirm').click(function () {
                lay.close('common')
            })
            // 关闭弹窗
            $('.btn-close').click(function () {
                const popId = $(this).parents('.pop').attr('id')
                lay.close(popId)
            })
        }

        this.openPop = function (id) {
            lay.closeAll()
            lay.open(id)
        }

        this.alert = function (msg, cb) {
            this.openPop('common')
            $('#common .txt-box').html(msg)
            if (cb) {
                $('#common .btn-confirm, #common .btn-close').one('click', cb)
            }
        }

        this.open = open


        $(window).on('load resize', handlerScreen)
        function handlerScreen() {
            console.log('screen')
            const contbox = $('.page')
            // $('.contbox').height() > $(window).height()
            const ww = $(window).width()
            const wh = $(window).height()
            if (ww/wh >= 375/550) {
                const contHeight = contbox.height()
                const scale = wh / Math.min(contHeight, 1650)
                console.log(scale);
                const scaleCss = {
                    'transform': 'scale(' + scale + ')'
                }
                contbox.css(scaleCss);
                $('.foot_menu').css(scaleCss)
                $('.pop').css(scaleCss)
            } else {
                const scaleCss = {
                    'transform': 'scale(' + 1 + ')'
                }
                contbox.css(scaleCss);
                $('.foot_menu').css(scaleCss)
                $('.pop').css(scaleCss)
            }
        }

    }

    window.app = new App()
    app.init()
}());


