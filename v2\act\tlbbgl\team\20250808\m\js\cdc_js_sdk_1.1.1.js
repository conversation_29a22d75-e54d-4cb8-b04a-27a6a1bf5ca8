(function(window,document){

  var url = document.location;
  var domain = url.hostname;
  var domain_sub = domain.substr(domain.indexOf('.') + 1);
  var stat_url = 'https://mon.changyou.com:8443/httpsdk/cyou_click.html'; // 初始化采集地址
  var site_id = '-1'; // 站点ID
  var track_id = -1; // 监测ID
  var sdk_version = '1.1.1';
  var user_id = '';
  var query_str = url.search;
  var is_posted = false;

  var last_url = document.referrer;
  var method = 'normal';

  var first_visit = 0;
  var is_mobi=0;


  function returnBase(number, base){
    var convert = ['0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z'];
    if (number < base) var output = convert[number];
    else {
      var MSD = '' + Math.floor(number / base);
      var LSD = number - MSD*base;
      if (MSD >= base) var output = returnBase(MSD,base) + convert[LSD];
      else var output = convert[MSD] + convert[LSD];
    }
    return output;
  }
  function getIntegerBits(val,start,end){
    var base16 = returnBase(val,16);
    var quadArray = [];
    var quadString = '';
    var i = 0;
    for(i=0;i<base16.length;i++){
      quadArray.push(base16.substring(i,i+1));
    }
    for(i=Math.floor(start/4);i<=Math.floor(end/4);i++){
      if(!quadArray[i] || quadArray[i] == '') quadString += '0';
      else quadString += quadArray[i];
    }
    return quadString;
  }
  function rand(max){
    return Math.floor(Math.random() * max);
  }
  function createUUID() {
    var dg = new Date(1582, 10, 15, 0, 0, 0, 0);
    var dc = new Date();
    var t = dc.getTime() - dg.getTime();
    var h = '';
    var tl = getIntegerBits(t,0,31);
    var tm = getIntegerBits(t,32,47);
    var thv = getIntegerBits(t,48,59) + '1'; // version 1, security version is 2
    var csar = getIntegerBits(rand(4095),0,7);
    var csl = getIntegerBits(rand(4095),0,7);
    var n = getIntegerBits(rand(8191),0,7) +
                    getIntegerBits(rand(8191),8,15) +
                    getIntegerBits(rand(8191),0,7) +
                    getIntegerBits(rand(8191),8,15) +
                    getIntegerBits(rand(8191),0,15); // this last number is two octets long
    return tl + h + tm + h + thv + h + csar + csl + h + n;
  }
  function getAccessId(){
    var access_id = getCookie('accessid');
    if(access_id == null || access_id == '') {
      first_visit = 1;
      access_id = createUUID();
      setCookie('accessid', access_id, null);
      var now = new Date();
      setCookie('accessid_settime',now.getTime(), null);
    }else {
      first_visit = 0;
    }
    return access_id;
  }


  function getLastDomain() {
    var a = '';
    if (last_url && last_url != '') {
      if (last_url.indexOf('?') > 0) {
        a = last_url.substring(0, last_url.indexOf('?'));
      } else {
        a = last_url;
      }
    }
    return a;
  }
  function cyou_random() {
    var a = Math.round(Math.random() * 3364721474);
    return a;
  }
  function getCookie(a) {
    if (document.cookie.length > 0) {
      c_start = document.cookie.indexOf(a + '=');
      if (c_start != -1) {
        c_start = c_start + a.length + 1;
        c_end = document.cookie.indexOf(';', c_start);
        if (c_end == -1) {
          c_end = document.cookie.length;
        }
        return unescape(document.cookie.substring(c_start, c_end));
      }
    }
    return '';
  }
  function setCookie(b, c, a) {
    var d = new Date();
    d.setDate(d.getDate() + a);
    document.cookie = b + '=' + escape(c) + ((a == null) ? '': '; expires=' + d.toGMTString()) + '; path=/; domain=' + domain_sub;
  }

  (function() {
    var Client = function() {
      var engine = { ie: 0, webkit: 0, gecko: 0, opera: 0, khtml: 0 };
      var browser = { se360: 0, se: 0, maxthon: 0, qq: 0, tt: 0, theworld: 0, cometbrowser: 0, greenbrowser: 0, ie: 0, chrome: 0, netscape: 0, firefox: 0, opera: 0, safari: 0, konq: 0,  Edg: 0 };
      var ua = navigator.userAgent.toLowerCase();
      for (var type in engine) {
        if (typeof type === 'string') {
          var regexp = 'gecko' === type ? /rv:([\w.]+)/ : RegExp(type + '[ \\/]([\\w.]+)');
          if (regexp.test(ua)) {
            engine.version = window.opera ? window.opera.version() : RegExp.$1;
            engine[type] = parseFloat(engine.version);
            engine.type = type;
            break;
          }
        }
      }
      for (var type in browser) {
        if (typeof type === 'string') {
          var regexp = null;
          switch(type) {
            case 'se360': regexp = /360se(?:[ \/]([\w.]+))?/; break;
            case 'se': regexp = /se ([\w.]+)/; break;
            case 'qq': regexp = /qqbrowser\/([\w.]+)/; break;
            case 'tt': regexp = /tencenttraveler ([\w.]+)/; break;
            case 'safari': regexp = /version\/([\w.]+)/; break;
            case 'konq': regexp = /konqueror\/([\w.]+)/; break;
            case 'netscape': regexp = /navigator\/([\w.]+)/; break;
            default: regexp = RegExp(type + '(?:[ \\/]([\\w.]+))?');
          }
          if (regexp.test(ua)) {
            browser.version = window.opera ? window.opera.version() : RegExp.$1 ? RegExp.$1 : 'unknown';
            browser[type] = parseFloat(browser.version);
            browser.type = type;
            break;
          }
        }
      }
      return { engine: engine, browser: browser };
    }();
    window.Client = Client;
  })();
  function cyou_getBrowser() {
    var clientBrowser = 'Unknown';
    if(Client.browser.type == 'ie') {
      clientBrowser = 'IE '+Client.browser.version;
    } else {
      clientBrowser = Client.browser.type;
    }
    return clientBrowser;
  }
  function cyou_getOS() {
    var sUserAgent = navigator.userAgent;
    var isWin = (navigator.platform == 'Win32') || (navigator.platform == 'Windows');
    var isMobi = cyou_isMobi();

    var isUnix = (navigator.platform == 'X11' && !isWin && !isMac);
    var isLinux = (String(navigator.platform).indexOf('Linux') > -1);
    var isWin95 = isWin98 = isWinNT4 = isWin2K = isWinME = isWinXP = isWin2K3 = isVista = false;

    if(isWin){
      isWin95 = sUserAgent.indexOf('Win95') > -1 || sUserAgent.indexOf('Windows 95') >-1;
      isWin98 = sUserAgent.indexOf('Win98') > -1 || sUserAgent.indexOf('Windows 98') >-1;
      isWinME = sUserAgent.indexOf('Win 9x 4.90') > -1 || sUserAgent.indexOf('Windows ME') >-1;
      isWin2K = sUserAgent.indexOf('Windows NT 5.0') > -1 || sUserAgent.indexOf('Windows 2000') >-1;
      isWinXP = sUserAgent.indexOf('Windows NT 5.1') > -1 || sUserAgent.indexOf('Windows XP') >-1;
      isWin2K3 = sUserAgent.indexOf('Windows NT 5.2') > -1 || sUserAgent.indexOf('Windows 2003') >-1;
      isVista = sUserAgent.indexOf('Windows NT 6.0') > -1 || sUserAgent.indexOf('Windows Vista') >-1;
      if(!isWin2K3&&!isVista){
        isWinNT4 = (sUserAgent.indexOf('WinNT') > -1
                                || sUserAgent.indexOf('Windows NT') >-1
                                || sUserAgent.indexOf('WinNT4.0') >-1
                                || (sUserAgent.indexOf('Windows NT 4.0') >-1))
                                && (!isWinME&&!isWin2K&&!isWinXP);
      }
    }
    switch(true) {
      case isMobi:
        return cyou_getPhoneOS();
      case isUnix:
        return 'Unix';
      case isLinux:
        return 'Linux';
      case isWin95:
        return 'Windows 95';
      case isWin98:
        return 'Windows 98';
      case isWinME:
        return 'Windows ME';
      case isWin2K:
        return 'Windows 2000';
      case isWinXP:
        return 'Windows XP';
      case isWin2K3:
        return 'Windows 2003';
      case isVista:
        return 'Windows Vista';
      case isWinNT4:
        return 'Windows NT';
      default:
        return 'Unknown';
    }
  }

  function cyou_getUserlanguage() {
    var ul = '';
    if (navigator.language) {
      ul = navigator.language.toLowerCase();
    } else if (navigator.browserLanguage) {
      ul = navigator.browserLanguage.toLowerCase();
    }
    return typeof(ul) != 'undefined' ? ul: 'Unknown';
  }

  function cyou_getScreenHeight() {
    var sr = '';
    if (self.screen) {
      sr = screen.height;
    } else if (self.java) {
      sr = java.awt.Toolkit.getDefaultToolkit().getScreenSize().height;
    }
    return typeof(sr) != 'undefined' ? sr: 'Unknown';
  }
  function cyou_getScreenWidth() {
    var sr = '';
    if (self.screen) {
      sr = screen.width;
    } else if (self.java) {
      sr = java.awt.Toolkit.getDefaultToolkit().getScreenSize().width;
    }
    return typeof(sr) != 'undefined' ? sr: 'Unknown';
  }

  function cyou_getColorDepth() {
    var sc = '';
    if (self.screen) {
      sc = screen.colorDepth + '-bit';
    }
    return typeof(sc) != 'undefined' ? sc: 'Unknown';
  }


  let pageX = null,pageY = null;
  function cyou_trackId(trackid,clk = true) {
    if(clk){
      return `&et=clk&trackid=${trackid}&x=${pageX}&y=${pageY}`;
    }else {
      return `&et=pv&trackid=${trackid}`;
    }
  }
  window.addEventListener('click', function(e){
    const { scrollTop, scrollLeft } = document.documentElement;
    const { top, left } = e.target.getBoundingClientRect();
    pageX =  (left + scrollLeft).toFixed(0);
    pageY = (top + scrollTop).toFixed(0);
  }, false);

  function send(b) {
    img = new Image(1, 1);
    img.id = 'cyou_ad_track';
    var a = cyou_random();
    img.src = b + '&cyou_rdm=' + a;
    img.onload = function() {
      is_posted = true;
      return;
    };
  }

  function getBaseUrl(){
    last_domain = getLastDomain();
    if (!is_posted) {

      if (last_domain && last_domain != '') {
        setCookie('last_domain', last_domain, null);
      }
    }
    var current_domain = document.URL.replace(query_str, '');
    if(cyou_isMobi()){
      is_mobi=1;
    }
    return stat_url
                    + '?deviceid=' + getAccessId()
                    + '&first_visit=' + first_visit
                    + '&sdk=' + sdk_version
                    + '&page=' + current_domain
                    + '&refer=' + last_url
                    + '&refer_domain=' + getLastDomain()
                    + '&is_mobi=' +is_mobi
                    + '&browser=' + cyou_getBrowser()
                    + '&os=' + cyou_getOS()
                    + '&lang=' + cyou_getUserlanguage()
                    + '&s_height=' + cyou_getScreenHeight()
                    + '&s_width=' + cyou_getScreenWidth()
                    + '&color_depth=' + cyou_getColorDepth()
                    + '&siteid=' + getCookie('site_id');
  }


  function post_info(trackid = null) {
    var access_msg = getBaseUrl() + cyou_trackId(trackid);
    console.log('access_msg:::',access_msg);
    send(access_msg);
  }

  function cyou_getPhoneOS(){
    var sUserAgent = navigator.userAgent;
    isIpod = sUserAgent.indexOf('Ipod') > -1;
    isIphone = sUserAgent.indexOf('iphone') > -1;
    isAndroid = sUserAgent.indexOf('Android') > -1;
    isSymbian = sUserAgent.indexOf('symbian') > -1;
    isWince = sUserAgent.indexOf('win ce') > -1;
    isJ2me = sUserAgent.indexOf('j2me') > -1;
    switch(true) {
      case isIpod:
        return 'ios';
      case isIphone:
        return 'ios';
      case isAndroid:
        return 'android';
      case isSymbian:
        return 'symbian';
      case isWince:
        return 'wince';
      case isJ2me:
        return 'j2me';
      default:
        return 'Unknown';
    }
  }

  function cyou_isMobi(){
    var isMobi = navigator.userAgent.toLowerCase().match(/(ipod|iphone|android|coolpad|mmp|smartphone|midp|wap|xoom|symbian|j2me|blackberry|win ce)/i) != null;
    return isMobi;
  }
  /**
       * 四舍五入
       * @param number 数值
       * @param decimal 要舍去的位数
       */
  function rounded(number, decimal) {
    return parseFloat(number.toFixed(decimal));
  }

  /**
           * 得到当前时间戳，单位毫秒
           * Date.now() 会受系统程序执行阻塞的影响
           * performance.now() 的时间是以恒定速率递增的，不受系统时间的影响（系统时间可被人为或软件调整）
           */
  function getNowTimestamp() {
    return performance.now();
  }


  function sendPerformance (data) {
    // 如果传了数据就使用该数据，否则读取性能参数，并格式化为字符串
    var str = getBaseUrl() + '&load_time=' + data.loadTime + cyou_trackId(track_id,false);
    send(str);
  }
  function sendBeacon(str){
    navigator.sendBeacon(stat_url, str);
  }
  function sendBehavior(data){
    var str = paramifyBehavior(data);
    sendBeacon(str);
  }
  function paramifyBehavior (obj) {
    /*行为参数*/ //todo
    obj.deviceid = getAccessId();
    obj.referer = location.href; // 来源地址
    return JSON.stringify(obj);
  }


  var WW = window.innerWidth;
  var WH = window.innerHeight;
  /*
          * 非首屏首次可见时触发事件
          * */
  var _IntersectionObserver = /** @class */ (function (params) {
    function _IntersectionObserver(params) {
      this.observer = new IntersectionObserver((changes, observer) => {
        changes.forEach((change) => {
          // 目标元素与视窗或祖先元素相交// 处理曝光、懒加载等逻辑
          if (change.isIntersecting) {
            params.intersection.intersectionObserverFun()
            // todo 添加触发事件
            observer.unobserve(change.target);
          }
        });
      },{});
      if(params.intersection && params.intersection.isOutScreen){
        let selecter = Array.from(document.querySelectorAll(params.intersection.selecter)).filter(item =>{
          return this.isOutScreen(item);
        });
        selecter.forEach((item) =>{
          this.observer && this.observer.observe(item);
        });
      }
    }

    /*
    * 判断非首屏元素
    * */
    _IntersectionObserver.prototype.isOutScreen= function (node) {
      var _a = node.getBoundingClientRect(), left = _a.left, top = _a.top;
      return WH < top || WW < left;
    };

    return _IntersectionObserver;
  }());

  var PerformanceMonitor = /** @class */ (function () {
    function PerformanceMonitor(params) {
      this.params = params;
      this.isNeedHideEvent = true;
      this.beginStayTime = getNowTimestamp();
    }
    /**
       * 从 performance.timing 读取的性能参数，有些值是 0
       * @param timing
       */
    PerformanceMonitor.prototype.setTimingDefaultValue = function (timing) {
      if (timing.redirectStart === 0)
        timing.redirectStart = timing.navigationStart;
      if (timing.redirectEnd === 0)
        timing.redirectEnd = timing.navigationStart;
      if (timing.loadEventStart === 0)
        timing.loadEventStart = timing.domComplete;
      if (timing.loadEventEnd === 0)
        timing.loadEventEnd = timing.loadEventStart;
    };
    /**
     * 读取 timing 对象，兼容新版和旧版
     */
    PerformanceMonitor.prototype.getTiming = function () {
      // 在 iOS 设备中，若 SDK 涉及跨域，那就需要声明 timing-allow-origin 首部，否则 PerformanceResourceTiming 中的大部分属性都是 0
      var timing = (performance.getEntriesByType('navigation')[0] || performance.timing);
      var now = 0;
      if (!timing) {
        return { now: now };
      }
      var navigationStart;
      if (timing.startTime === undefined) {
        navigationStart = timing.navigationStart;
        var cloneTiming = {};
        // 不能直接将 timing 传递进去，因为 timing 的属性都是只读的
        for (var key in timing) {
          cloneTiming[key] = timing[key];
        }
        // 消除为 0 的性能参数
        this.setTimingDefaultValue(cloneTiming);

        now = new Date().getTime() - navigationStart;
        return { timing: cloneTiming, navigationStart: navigationStart, now: rounded(now) };
      }
      navigationStart = timing.startTime;
      now = getNowTimestamp() - navigationStart;
      return { timing: timing, navigationStart: navigationStart, now: rounded(now) };
    };

    /**
     * 请求时间统计
     * https://github.com/addyosmani/timing.js
     */
    PerformanceMonitor.prototype.getTimes = function () {
      // 出于对浏览器兼容性的考虑，仍然引入即将淘汰的 performance.timing
      var currentTiming = this.getTiming();
      var timing = currentTiming.timing;
      var api = {};
      if (!timing) {
        return null;
      }
      var navigationStart = currentTiming.navigationStart;
      /**
       * http://javascript.ruanyifeng.com/bom/performance.html
       * 页面加载总时间，有可能为0，未触发load事件
       * 这几乎代表了用户等待页面可用的时间
       * loadEventEnd（加载结束）-navigationStart（导航开始）
       */
      api.loadTime = timing.loadEventEnd - navigationStart;
      /**
       * 执行 onload 回调函数的时间
       * 是否太多不必要的操作都放到 onload 回调函数里执行了，考虑过延迟加载、按需加载的策略么？
       */
      api.loadEventTime = timing.loadEventEnd - timing.loadEventStart;
      api.now = getNowTimestamp();
      // 全部取整
      for (var keyName in api) {
        api[keyName] = rounded(api[keyName]);
      }
      return api;
    };
    /**
     * 注册 load 和页面隐藏事件
     */
    PerformanceMonitor.prototype.registerLoadAndHideEvent = function () {
      var _this = this;
      // 发送性能数据
      var sendPerformance = function () {
        var data = _this.getTimes();
        if (_this.isNeedHideEvent && data) {
          _this.params._this.sendPerformance(data);
          _this.isNeedHideEvent = false;
        }
      };
      // 计算行为数据
      var caculateBehavior = function () {
        var behavior = {};
        behavior.duration = rounded(getNowTimestamp() - _this.beginStayTime); // 页面停留时长
        return behavior;
      };
      // 发送用户行为数据
      var sendBehavior = function () {
        var behavior = caculateBehavior();
        _this.params._this.sendBehavior(behavior);
        localStorage.removeItem('behavior_data'); // 移除行为缓存
      };
      /**
       * 发送缓存的行为数据
       * 例如停留时长需要在 pagehide 或 beforeunload 两个事件中发送
       * 但如果两个事件都不支持，那么这个数据就是空的
       */
      var sendExistBehavior = function () {
        var exist = localStorage.getItem('behavior_data');
        if (!exist) {
          return;
        }
        // 直接发送，不需要再次封装数据
        _this.params._this.sendBeacon(exist)
        localStorage.removeItem('behavior_data'); // 移除行为缓存
      };
      /**
       * 在 load 事件中，上报性能参数
       * 该事件不可取消，也不会冒泡
       */
      window.addEventListener('load', function () {
        // 发送缓存的行为数据
        // sendExistBehavior();
        // 加定时器是避免在上报性能参数时，loadEventEnd 为 0，因为事件还没执行完毕
        setTimeout(function () {
          sendPerformance();

          if(_this.params.intersection && _this.params.intersection.selecter){
            new _IntersectionObserver(_this.params);
          }

        }, 0);
        // 通过定时器缓存数据
        clearInterval(_this.params._this.behaviorDataTimer)
        _this.params._this.behaviorDataTimer = setInterval(function () {
          var behavior = caculateBehavior();
          localStorage.setItem('behavior_data', _this.params._this.paramifyBehavior(behavior));
        }, 1000);
      });
      /**
       * iOS 设备不支持 beforeunload 事件，需要使用 pagehide 事件
       * 在页面卸载之前，推送性能信息
       */
      var isIOS = !!navigator.userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
      var eventName = isIOS ? 'pagehide' : 'beforeunload';
      window.addEventListener(eventName, function () {
        sendPerformance();
        sendBehavior();
      }, false);
    };
    return PerformanceMonitor;
  }());
  /**
   * 默认属性
   */
  var behaviorDataTimer = null;
  var defaults = {
    /*intersection:{
      selecter:'.page-footer',
      isOutScreen: true,
      intersectionObserverFun(){
        var url = getBaseUrl() +'&footer_show=true'+ cyou_trackId(track_id,false);
        send(url)
      }
    },*/
    _this:{
      sendPerformance,sendBehavior,paramifyBehavior,behaviorDataTimer,sendBeacon
    }
  };



  var timer = null;
  function trackClick(trackid) {
    timer && clearTimeout(timer);
    timer = setTimeout(function(){
      post_info(trackid);
    },30);

  }

  function initUrl (url = stat_url){
    stat_url = url;
  }

  function initSiteId (siteId = site_id){
    site_id = siteId;
    setCookie('site_id', site_id, null);
  }

  function setUserId (userId = user_id){
    user_id = userId;
  }
  function setTrackId (trackId = track_id){
    track_id = trackId;
  }
  function pageview() {
    post_info();
  }


  function initFun(params){
    // 启动性能监控
    var pe = new PerformanceMonitor(params);
    pe.registerLoadAndHideEvent(); // 注册 load 和页面隐藏事件

  }
  initFun(defaults);

  window.cdc = {
    getCookie: getCookie,
    setCookie: setCookie,
    trackClick: trackClick,
    initUrl: initUrl, // 传入接收地址
    initSiteId: initSiteId, // 初始化站点ID
    setUserId: setUserId, // 设置用户ID
    setTrackId: setTrackId
  };

})(window,document);

