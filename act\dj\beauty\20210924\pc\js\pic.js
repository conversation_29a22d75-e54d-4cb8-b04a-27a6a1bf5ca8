var conf = SITE_CONFIG;

//裁剪
var Cropper = window.Cropper;
var cropper;
var image = $('.img-container img')[0];
var i;//当前点击的第几个input
var imgObj={};//上传图片对象
var allflage=false;

//提交照片是否可用
function openSub(){
	if($('.piclist li.yypic').length == 3){
		allflage=true;
	}else{
		allflage=false;
	};
	if(allflage){
		$('.tjbtn').addClass('canuse');
	}else{
		$('.tjbtn').removeClass('canuse');
	};
};
//将图片存在图片对象中
function cReatimageSrc(){
    var imgSrc = cropper.getCroppedCanvas({ width: 386, height: 304 }).toDataURL('image/png');
    var item = $('.piclist li').eq(i).addClass('yypic').find('.mrbox')
    item.show().find('img').attr('src',imgSrc);
    imgObj[i]=imgSrc;
};


var g = {
    console: true,
    showPassword: false,
    data: {
        isLogin: false,
        uploadedImages: [
            'djyx-activity-pic-1251125656.cos.ap-beijing.myqcloud.com/2021092816328376922xvTqy9E.png',
            'djyx-activity-pic-1251125656.cos.ap-beijing.myqcloud.com/2021092816328376922xvTqy9E.png',
            'djyx-activity-pic-1251125656.cos.ap-beijing.myqcloud.com/2021092816328376922xvTqy9E.png'],
        uploadRequestCount: 0,
        uploadSuccessCount: 0,
        userInfo: null,
        unserSign: null,
    },

    // 通用请求
    ajax: function (o, cb) {
        var that = this;
        var t = {
            method: o.method || 'post',
            url: o.sp ? o.url : (conf.domain + o.url),
            data: o.data || {},
            dataType: o.dataType || 'json',
            contentType: (o.contentType == false ? false : (o.contentType || 'application/x-www-form-urlencoded')),
            processData: (o.processData == false ? false : true)
        };
        var load = null;
        (o.load) && ( load = lay.load(1,{content:'加载中，请稍后...', shade: 0.5}));
        console.log(t);

        var ajaxOpions = {
            type: t.method,
            url: t.url,
            data: t.data,
            dataType: t.dataType,
            processData: t.processData,
            contentType: t.contentType,
            // xhrFields: {withCredentials: true},
            crossDomain: true,
            cache: false,
            success: function (d) {
                if(that.console || o.console) { console.log('ajax: ' + o.url + ' | ' + JSON.stringify(t.data) + ' | ' + (new Date()).valueOf(), d); }
                if(o.load){
                    setTimeout(function () {
                        lay.close(load);
                        (cb) && cb(d);
                    }, t.time ? t.time : 500);
                } else{
                    (cb) && cb(d);
                }
            },
            error: function (xhr, type) {
                var d = {
                    code: -505,
                    msg: '系统错误',
                    message: '系统错误'
                };
                if(o.console) console.log('ajax: ' + o.url + ' | ' + JSON.stringify(t.data) + ' | ' + (new Date()).valueOf(), d);
                if(o.load){
                    setTimeout(function () {
                        lay.close(load);
                        (cb) && cb(d);
                    }, t.time ? t.time : 500);
                } else{
                    (cb) && cb(d);
                }
            }
        }
        $.ajax(ajaxOpions);
    },

    checkLogin: function(){
        var that = this;
        if(!that.data.isLogin){
            that.popLogin();
            return false
        }
        return true;
    },
    login: function(cb){
        var that = this;

        var cn = $('#account').val();
        var pass = $('#psw').val();
        var vCode = $('#code').val();
        var verifySign = that.data.verifySign;

        var data = {
            cn: cn,
            pass: pass,
            vCode: vCode,
            verifySign: verifySign
        }
        that.ajax({url: conf.apis.login, data: data}, function (res) {
            if(res.code == 0){
                that.data.isLogin = true;
                that.setStorage('token', res.data.token)
                console.log('login success')
            }
            cb(res)
        })
    },
    // 退出登录
    logout: function(){
        var that = this;
        that.clearStorage();
        that.get_config();
    },

    setStorage: function(key, val){
        if(window.localStorage){
            localStorage.setItem(key, val)
        }
    },
    getStorage: function(key){
        if(window.localStorage){
            return localStorage.getItem(key)
        }
    },
    clearStorage: function(){
        if(window.localStorage) {
            localStorage.clear();
        }
    },

    getImgCode: function(admin_change){
        var that = this;
        $('#code').val('');
        that.ajax({url: conf.apis.imageVerify, method: 'get'}, function (res) {
            if(res.code === 0){
                var img = 'data:image/jpeg;base64,' + res.data.img
                $('.img_code').attr('src', img)
                that.data.verifySign = res.data.verifySign;
                var verifyCode = res.data.verifyCode
                if(that.showPassword){
                    console.log(verifyCode);
                    $('#code').val(verifyCode)
                }
            } else{
                if(admin_change) {
                    lay.msg(res.msg)
                }
            }
        })
    },

    popLogin: function(){
        var that = this;
        that.getImgCode();
        lay.open('popLogin');

        if(that.showPassword) {
            var pass = 'a123456'
            $('#account').val('***********')
            $('#psw').remove()
            $('.input_area').html('<input id="psw" value="' + pass + '" \/>')
        }
    },

    alert: function(msg, hideClose){
        lay.open('popAlert')
        $('#popAlert .comtip').html(msg)
        if(hideClose){ $('#popAlert .close').hide(); }
        else{ $('#popAlert .close').show(); }
    },

    renderSelect: function(){
        var that = this;
        var el = $('#serverSelect');
        var serverIdEl = $('#serverId');
        var serverNameEl = $('#serverName');
        var serverNameSelect = serverNameEl.val()
        el.empty();
        var data = SERVER_DATA;
        var options = '<option value="">请选择所在服务器</option>'
        data.forEach(function (it, idx) {
            options += '<option value="'+ it.serverName + '" data-serverid="'+ it.serverId +'" '+ (it.serverName == serverNameSelect ? 'selected' : '') +' >'+ it.serverName +'</option>'
        });
        el.append(options);

        el.on('change', function () {
            var selectedEl = el.find('option:selected')
            var selectedServerId = selectedEl.data('serverid')
            var selectedServerName = selectedEl.val()
            serverIdEl.val(selectedServerId)
            serverNameEl.val(selectedServerName)
        })
    },

    get_config: function(){
        var that = this;
        that.ajax({url: conf.apis.init, data:{verifyToken: that.getStorage('token')}}, function (res) {
            if(res.code == 0){
                that.data.isLogin = true
                that.data.userInfo = res.data.userInfo
                that.data.userSign = res.data.userSign
            }
            else {
                that.data.isLogin = false
            }
            if(that.checkLogin()) that.renderConfig();;

        })
    },

    renderConfig: function(type){
        var that = this;
        if(that.data.userSign){
            if(type!= 'signUp'){
                that.alert('<div>您已报过名，资料不可再次提交！可前往<a href="index.shtml">个人中心</a>查看审核进度！</div>', true)
            } else{
                that.alert('<div>您已成功报名！可前往<a href="index.shtml">个人中心</a>查看审核进度！</div>', true)
            }
        }
    },

    // 提交三个照片
    uploadImages: function(){
        var that = this;
        if(!$('.tjbtn').hasClass('disabled')){
            // 发起上传
            $('.tip-con').empty();
            that.uploadImg(0, function () {
                $('.tjbtn').removeClass('disabled');
                $('.img-upload-tips').fadeOut();
                that.data.uploadRequestCount = 0;
            })
        }
        $('.tjbtn').addClass('disabled');
    },

    uploadImg: function(index, cb){
        var that = this;
        $('.img-upload-tips').show();
        var base64Data = imgObj[index]
        var imgName = '图片' + index + '.png';
        var blob = $.tools.dataURLtoBlob(base64Data, imgName);
        var formData = new FormData()
        formData.append('image', blob, imgName)
        formData.append('verifyToken', that.getStorage('token'))

        that.ajax({
            url: conf.apis.upload,
            data: formData,
            processData: false, // jQuery不要去处理发送的数据
            contentType: false, // jQuery不要去设置Content-Type请求头
        }, function (res) {
            that.data.uploadRequestCount++;
            // 登录超时
            if(res.code == -111){
                that.alert(res.msg)
                $('#popAlert .close').one('click', function () {
                    that.popLogin();
                })
                cb()
                return;
            }
            // 上传次数超限制
            if(res.code == 10008){
                that.alert(res.msg)
                cb()
                return;
            }
            // 30005
            if(res.code == 30005){
                that.alert('<div>您已报过名，资料不可再次提交！可前往<a href="index.shtml">个人中心</a>查看审核进度！</div>')
                cb()
                return;
            }
            var tipHtml = '<div class="tip-line">第' + (index*1+1) + '个：'
            if (res.code == 0) {
                that.data.uploadedImages[index] = res.data.imageUrl
                that.data.uploadSuccessCount++
                tipHtml += '成功；'
            }
            else {
                that.data.uploadedImages[index] = ''
                tipHtml += '失败；'+ res.msg + '，请重新上传！'
            }
            tipHtml += '</div>'
             $('.tip-con').append(tipHtml)

            if(that.data.uploadRequestCount == 3){
                // 图片上传完毕
                $('.tip-con').append('<div class="tip-line">所有图片上传完毕！</div>');
                setTimeout(function () {
                    cb()
                }, (that.data.uploadSuccessCount < that.data.uploadRequestCount ? 3000 : 2000) )
            }
            else{
                that.uploadImg(index + 1, cb);
            }
        })
    },

    // 提交报名数据
    signUp: function(data){
        var that = this;
        data.verifyToken = that.getStorage('token')
        that.ajax({url: conf.apis.signUp, data: data}, function (res) {
            if(res.code == 0) {
                that.data.userSign = res.data
                that.renderConfig('signUp');
            } else{
                that.alert(res.msg);
            }
        })

    },

    bind: function () {
        var that = this;

        $('.piclist input').live('change', function(event) {
            if(!that.checkTimeStatus()){ return; }
            if([0,1,2].indexOf(i) !== -1){
                cReatimageSrc();
            };
            i = $('.piclist input').index($(this));
            var eve = event || window.event;
            var file = eve.currentTarget.files[0];
            var reader = new FileReader();
            reader.onload = function() {
                $('.piclist li').eq(i).removeClass('yypic')
                $('.img-container img').attr('src', reader.result);
                if (cropper) { cropper.destroy(); };
                cropper = new Cropper(image, {
                    aspectRatio: 386 / 304,
                    preview: '.img-preview' + i
                });
                $('.piclist input').val('');
                $('.piclist li').eq(i).addClass('yypic');
                $('.admin-tip').hide();
                openSub();
            };
            if (file) {
                reader.readAsDataURL(file);
            }
        });

        $('.plus').on('click',function(){
            if(!that.checkTimeStatus()){ return; }
            if(cropper){ cropper.zoom(0.1); };
        });

        $('.minus').on('click',function(){
            if(!that.checkTimeStatus()){ return; }
            if(cropper){ cropper.zoom(-0.1); };
        });

        //删除图片
        $('.piclist li em').on('click',function(){
            if(!that.checkTimeStatus()){ return; }
            var index = $(this).parent().index()
            // 更新已上传数量计数以及已保存的上传后返回的图片链接
            that.data.uploadRequestCount--
            that.data.uploadSuccessCount--
            that.data.uploadedImages[index] = ""
            $(this).parent().removeClass('yypic');
            $(this).siblings('.mrbox img').attr('src','');
            $(this).siblings('.mrbox').hide();
            $('.admin-tip').show();
            openSub();
            if (index == i) {
                if (cropper) { cropper.destroy(); };
                i = null;
                $('.img-container img').attr('src','/act/dj/beauty/20210924/pc/img/ex.png');
            };
        });

        //提交照片
        $('.tjbtn').on('click',function(){
            if(!that.checkTimeStatus()){ return; }
            if($(this).hasClass('canuse')){
                cReatimageSrc();
                that.uploadImages(imgObj);
            }else {
                that.alert('请添加所有照片后，再点击确认上传哦~');
            };
        });

        // 报名提交
        $('.bigbmbtn').click(function () {
            if(!that.checkTimeStatus()){ return; }
            if(that.checkLogin()){
                if(that.data.userSign){
                    // 已报名
                    that.alert('<div>您已报过名，资料不可再次提交！可前往<a href="index.shtml">个人中心</a>查看审核进度！</div>', true)
                    return;
                }
                else{
                    var data = {
                        serverId: $('#serverId').val(),
                        serverName: $('#serverName').val(),
                        roleName: $('#roleName').val(),
                        phone: $('#phone').val(),
                        profession: $('#profession').val(),
                        address: $('#address').val(),
                        email: $('#weixin').val(),  // 微信号
                        douyin: $('#douyin').val(),
                        desc: $('#desc').val(),
                    }
                    // 数据校验
                    if(data.serverName == '' || data.serverName == '请选择所在服务器'){
                        that.alert('请选择所在服务器！')
                        return;
                    }
                    if(data.roleName == '' ){
                        that.alert('请填写角色名！')
                        return;
                    }
                    if(data.phone == '' ){
                        that.alert('请填写手机号！')
                        return;
                    }
                    if(!$.tools.isMobile(data.phone)){
                        that.alert('手机号格式不正确！')
                        return;
                    }
                    if(data.profession == '' ){
                        that.alert('请填写游戏角色职业！')
                        return;
                    }
                    if(data.desc.length > 20 ){
                        that.alert('一句话宣言字符不能超过20个字！')
                        return;
                    }
                    // 判断图片是否已上传
                    // that.data.uploadRequestCount = 3
                    if(that.data.uploadSuccessCount < 3){
                        that.alert('请提交照片后再点击报名参加哦~')
                        return;
                    }
                    data.photo = that.data.uploadedImages.filter(function (it) { return it != "";}).join("|")
                    that.signUp(data);
                }
            }
        });

        // 更换图形验证码
        $('#btn_change_code').click(function () {
            that.getImgCode(true);
        })

        // 登陆提交
        $('.dlbtn').click(function () {
            if(!that.checkTimeStatus()){ return; }
            var $this = $(this)
            if(!$this.hasClass('clicked')){
                var cn = $('#account').val();
                var pass = $('#psw').val();
                var vCode = $('#code').val();

                // 数据校验
                if(cn == '') { lay.msg('请输入用户名！'); $('.dlbtn').removeClass('clicked'); return; }
                if(pass == '') { lay.msg('请输入密码！'); $('.dlbtn').removeClass('clicked'); return; }
                if(vCode == '') { lay.msg('请输入验证码！'); $('.dlbtn').removeClass('clicked'); return; }

                that.login(function (res) {
                    $this.removeClass('clicked')
                    if(res.code == 0){
                        lay.close('popLogin');
                        lay.msg('登陆成功！');
                        that.get_config();
                    }
                    // 验证码过期或错误
                    else if(res.code == 20002) {
                        lay.msg(res.msg);
                        that.getImgCode();
                    } else{
                        lay.msg(res.msg);
                    }
                })
            } else{
                $this.addClass('clicked')
            }
        });

        // 弹窗关闭按钮
        $('.pop').on('click', '.close', function () {
            var popId = $(this).parents('.pop').attr('id')
            lay.close(popId)
            $('#account').val('')
            $('#psw').val('')
            $('#code').val('')
        })
    },

    getTimeStatus: function(){
        var that = this;
        var now = new Date()
        var vote = that.data.dates.vote;
        var step1 = that.data.dates.step1;
        var step2 = that.data.dates.step2;
        if(now > new Date(vote[0]) && now < new Date(vote[1])){
            return 'vote'; // 已过报名时间，处于投票期
        }
        else if(now > new Date(step1[0]) && now < new Date(step1[1])){
            return 'result';  //  已过投票时间，还在结果等待期，11/5号投票结束-11.18日期间
        }
        else if(now > new Date(step2[0]) && now < new Date(step2[1])){
            return 'over';  // 投票&海选结束，
        }
        else if(now > new Date(step2[1])){
            // 活动全部结束
            return 'allover';
        }
        else {
            return 'sign';  // 处于报名时间
        }
    },
    checkTimeStatus: function(){
        var that = this;
        if(that.data.timeStatus == 'vote'){
            that.alert(that.data.dates.msg[0], true)
            return false;
        }
        if(that.data.timeStatus == 'result'){
            that.alert(that.data.dates.msg[1], true)
            return false;
        }
        if(that.data.timeStatus == 'over'){
            that.alert(that.data.dates.msg[2], true)
            return false;
        }
        if(that.data.timeStatus == 'allover'){
            that.alert(that.data.dates.msg[2], true)
            return false;
        }
        return true;
    },

    init: function () {
        var that = this;
        that.data.dates = conf.dates;
        var timeStatus = that.getTimeStatus();
        that.data.timeStatus = timeStatus;
        that.checkTimeStatus();
        that.renderSelect();
        that.bind();
        that.get_config();
    },

}
$(function () {
    g.init();
})

