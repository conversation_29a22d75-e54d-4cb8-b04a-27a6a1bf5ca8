var pro = ["内测服务器", "绝世双线", "周年庆专区", "鸿运大区", "纵横双线", "人气专区", "唯美双线", "超级电信", "超级双线", "唯美电信", "超级网通", "至尊电信", "网通全国一区", "电信全国一区", "无双网通", "唯美网通", "华中电信一区", "网通专区", "网通二区", "西南电信一区", "华南电信一区", "南部电信二区", "东部电信", "华北网通二区", "东北网通一区", "华北网通一区", "华东电信一区", "南部电信", "西北电信一区", "中西部电信"];
var city = [
    ["龙腾天下", "龙啸苍穹"],
    ["愿得一人心", "乘风破浪", "山河无恙", "洛城飞雪", "碧海潮生", "潇潇雨幕", "箫中剑", "桃花影落"],
    ["江山风华", "山河万象", "鹤舞九霄", "瑞鹤千秋", "十里桃花", "三生三世"],
    ["元夕踏灯", "华灯如昼", "鸿运当头", "鸿运连年"],
    ["清歌遥望月", "惜君青玉裳", "愿君共白首", "以梦为马", "逐梦江湖", "师门逆徒", "宁为我道", "铜锣湾", "守望江湖", "一梦十年", "不见不散", "无法无天", "天下为棋"],
    ["天下会武", "金戌迎瑞"],
    ["三世情缘", "醉沙场", "冰雪奇缘", "王者", "天命", "绝杀", "至尊", "千秋殿"],
    ["倾国倾城", "天下第一"],
    ["天下", "天龙"],
    ["仙人指路", "狂战天下"],
    ["独步江湖"],
    ["天外江湖", "唯我独尊"],
    ["刀光剑影"],
    ["九天惊雷", "仙侣情缘", "听香水榭", "天若有情", "琴音小筑"],
    ["独孤求败"],
    ["武林至尊"],
    ["逐鹿中原", "乔峰", "庐山飞瀑", "洞庭秋月"],
    ["百泉书院", "三生石", "问情崖", "太极剑法", "杜康"],
    ["锦绣中华"],
    ["忘忧谷", "乐山大佛", "剑门蜀道", "蜀南竹海"],
    ["东坡书院", "罗浮山", "桂林山水"],
    ["烟雨轩"],
    ["神龙摆尾", "九阴真经", "烧刀子", "云雾茶"],
    ["龙腾九天"],
    ["水晶湖", "凤凰山"],
    ["北戴河", "什刹海", "八达岭", "紫禁之巅"],
    ["上海滩", "双龙洞", "雨花台", "武夷山", "太湖仙岛"],
    ["乾坤大挪移"],
    ["昆仑山"],
    ["一阳指"]
];

$(function(){
    //初始化省份下拉列表
    for(var i=0;i<pro.length;i++){
        $("#province").append($("<option></option>").val(i+1).html(pro[i]));
    }
    //为省份下拉列表绑定change事件
    $("#province").change(function(){
        // alert($('#province option:selected').html());
        var index=$(this).val()-1;//获取当前省的下标
        $("#city").prop("length",1);//清空原有的数据
        if(index == -1){
            return;
        }
        for(var i=0;i<city[index].length;i++){//重新为市赋值
            $("#city").append($("<option>").val(i+1).html(city[index][i]));
        }
    });
})