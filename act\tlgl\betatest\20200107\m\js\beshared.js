if (!ja.glob.isWechat) {
    popTip('请使用微信参与本次活动');
}
var getUrlParams = ja.utils.getUrlParams(),
    inviteCode = getUrlParams.inviteCode,
    nickName = getUrlParams.nickName,
    headImgUrl = getUrlParams.headImgUrl,
    mpList = ['逍遥', '峨眉', '明教', '武当', '天山', '丐帮'];//门派信息

if (headImgUrl && headImgUrl != '' && headImgUrl != 'undefined') {
    $('#HeadImgUrl').attr('src', getUrlParams.headImgUrl);
} else {
    $('#HeadImgUrl').attr('src', '/act/xtl/competition/20191115/m/img/wxDefaultImg.png');
}
if (nickName && nickName != '' && nickName != "undefined" && nickName != "nickName") {
    $('#NickName').html(getUrlParams.nickName);
}
if (getUrlParams.order) {
    $('.friinf').html('《天龙八部•归来》初心首测即将开启，我准备加入XX'+mpList[getUrlParams.order]+'门派，请响应我的号召，与我同归江湖！');
    $('#HelpBg').addClass('sharebg' + getUrlParams.order);
}
var commPath = '/wechat/tlgl/backtogether/'; //重新修改请求地址

function $helpClick(fun) {
    var code = Number(ja.glob.code);
    if (code == 0) {
        popTip('系统繁忙，请稍后重试！');
    } else if (code == 1106) {
        popTip('响应号召活动未开始'); //活动未开始
    } else if (code == 1103) {
        popTip('响应失败，响应号召活动已结束'); //活动已结束
    } else if (code == 1102) {
        popTip('活动无效');
    } else {
        fun && fun();
    }
}
ja.config({
    app: 'tlgl', // 必填，应用标识
    activity: 'backtogether', // 必填，活动标识
    version: '20200108', // 必填，活动版本标识
    platform: 'changyou', // 必填，登陆平台标识
    isWxLogin: true,
    type: 1,
    ready: function (glob) {
        $("#Call").on("click", function () {
            $helpClick(function () {
                if (isClick) {
                    isClick = false;
                    if (!ja.glob.isWechat) {
                        popTip('请使用微信响应号召')
                        return;
                    }
                    ja.ajax({
                        type: 'get',
                        url: commPath + 'help?invite_code=' + inviteCode + '',
                        headers: {
                            PLAT: 'wechat'
                        },
                        success: function (res) {
                            if (res.code == 10000) {
                                popShow('pop_sus'); //应援成功
                            } else if (res.code == 5072) {
                                popShow('pop_norepeat');//请勿重复应援
                            } else if (res.code == 5074) {
                                popShow('pop_norespond');  //无法为该好友应援
                            } else if (res.code == 1106) {
                                popTip('响应号召活动未开始'); //活动未开始
                            } else if (res.code == 1103) {
                                popTip('响应失败，响应号召活动已结束'); //活动已结束
                            } else if (res.code == 1102) {
                                popTip('活动无效');
                            } else if(res.code==0){
                                popTip('系统繁忙，请稍后重试！');
                            }else {
                                popTip(res.message);
                            }
                            isClick = true;
                        }
                    })
                }
            })

        })
        
        wxShare(inviteCode, nickName, headImgUrl, getUrlParams.order, 'beshared');
    }
});
// 我要参与
$('#Ijoin').bind('click', function () {
    window.location.href = location.origin + "/tlgl/betatest/20200107/m/index.shtml"
})


















