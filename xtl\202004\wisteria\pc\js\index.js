jQuery.extend(jQuery.easing, {
    def: 'easeOutExpo',
    easeInExpo: function (x, t, b, c, d) {
        return (t == 0) ? b : c * Math.pow(2, 10 * (t / d - 1)) + b;
    },
    easeOutExpo: function (x, t, b, c, d) {
        return (t == d) ? b + c : c * (-Math.pow(2, -10 * t / d) + 1) + b;
    },
    easeInOutExpo: function (x, t, b, c, d) {
        if (t == 0) return b;
        if (t == d) return b + c;
        if ((t /= d / 2) < 1) return c / 2 * Math.pow(2, 10 * (t - 1)) + b;
        return c / 2 * (-Math.pow(2, -10 * --t) + 2) + b;
    }
});
var _modh = $(window).height() - 55;
$('.page,.part').height(_modh);
$('.part').css('top',_modh);
$('.show').css('top',0);
wheel = {
    //h: 850,
    h: _modh,
    mods: $('.part'),
    fnav: $('.float-con a'),
    se: 0,
    loaded: [false, false, false, false],
    turn: false,
    len: $('.part-n').length,
    roll: function (e) {
        if (wheel.turn || e == wheel.se) return;
        wheel.turn = true;
         if(e==0){
             $('.float').hide();
             wheel.fnav.removeClass('show');
         }else{
             $('.float').show();
             wheel.fnav.eq(e-1).addClass('show').siblings().removeClass('show')
         }
        if(e==4){
            setTimeout(function(){
				$('html,body').css('overflow','visible');
                $('#cyou_bottom').show();
				$('.arrow').hide();
            },1000);
        }else{
			$('html,body').css('overflow','hidden');
            $('#cyou_bottom').hide();
			$('.arrow').show();
        }
        wheel.mods.eq(e).css({ top: (e < wheel.se ? -1 : 1) * wheel.h, zIndex: 3 }).stop(true, true).animate({ top: 0 }, 300, 'easeInOutExpo', function () {
            $(this).css({ zIndex: 2 });
            wheel.mods.eq(wheel.se).css({ zIndex: 1 });
            $(this).addClass('show');
            wheel.mods.eq(wheel.se).removeClass('show');
            wheel.se = e;
            wheel.turn = false;
        });
    },
    init: function () {
        var flag = localStorage.getItem("flag");
        if(flag == "true"){
            localStorage.removeItem("flag");
            wheel.roll(1);
        }
        $('.page').bind('mousewheel DOMMouseScroll', function (event, delta) {
            var sc = $(window).scrollTop();
            var max = document.body.clientHeight - $(window).height();
            if (!wheel.turn && sc >= max && delta == -1 || !wheel.turn && sc <= 0 && delta == 1) {
                var n = 0;
                if (delta > 0 && wheel.se > 0) { n = wheel.se - 1; wheel.roll(n); }
                if (delta < 0 && wheel.se < wheel.len) { n = wheel.se + 1; wheel.roll(n); }
            }
        });
    }
};
//鼠标滚动
$('.arrow').on('click',function(){
    wheel.se<wheel.len&&wheel.roll(wheel.se+1);
});
//侧导航
$(document).ready(wheel.init);
$('[date-page]').on('click',function(){
    var index = $(this).index();
    console.log(index)
    var pa = $(this).attr('date-page')-1;
    wheel.roll(eval(pa));
});
//返回首页
$('.back-btn').on('click',function(){
    wheel.roll(eval(0));
});