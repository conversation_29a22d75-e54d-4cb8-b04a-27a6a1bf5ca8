<!-- 账号绑定 -->
<div class="pop bang" id="pop1">
    <a href="javascript:;" class="btn-close t sur"></a>
    <div class="pop-con">
        <div class="layer_com">
            <h3>账号绑定</h3>
            <p>小提示：账号一旦绑定，将无法更换哦~</p>
            <div class="inputLayer">
                <form action="">
                    <div class="sel_xz">
                        <label>请选择您要绑定的角色</label>
                        <select class="js_select" v-model="role">
                            <option v-for="item in roleList" :value="item.value">{{ item.name }}</option>
                        </select>
                    </div>
                    <div class="subimt">
                        <a href="javascript:;" class="sur t tj_a" id="bang_js" @click="putRole">提交</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 上传认证 -->
<div class="new_pop">
    <div class="pop up_shang" id="pop2">
        <a href="javascript:;" class="btn-close t sur"></a>
        <div class="pop-con">
            <div class="layer_com">
                <h3 v-show="!isOnlyB">海誓山盟</h3>
                <div class="sc_box">
                    <div class="upBx_gb bk_bjuP" v-show="!statusAB || statusAB[0].btn">
                        <div class="font_sc">告白对象：<input type="text" id="name_dx" maxlength="8" v-model="formProfess.target"></div>
                        <div class="font_sc one_updata">上传游戏截图/照片：
                            <a href="javascript:;" class="sc_input" @click="imgStatus=1">
                                <input type="file" class="img_file" value="选择视频" placeholder="选择视频" accept="image/*">
                                <span>上传图片</span>
                            </a>
                            <div class="img_game clearfix">
                                <div class="item" v-for="item in imgGame">
                                    <img :src="item" alt="imgGame">
                                </div>
                            </div>
                        </div>
                        <div class="font_sc">
                            告白内容：<textarea id="text_wb" rows="10" cols="30" maxlength="150" placeholder="不超过150字"
                                           style="resize: none;height: 50px" v-model="formProfess.declaration"></textarea>
                        </div>
                    </div>
                    <div class="upBx_bx bk_bj" v-show="!statusAB || statusAB[1].btn">
                        <p v-if="isOnlyB && statusAB[0]" class="status_personal">{{ statusAB[0].msg }}</p>
                        <h3>奔现认证<span v-show="!isOnlyB">（选填）</span></h3>
                        <div class="p_bing">上传与游戏内另一半奔现认证证明，如结婚照、合影等 皆可，即可参与奔现认证赢取最终定情金簪与线下婚礼 等终极奖励（注：奔现认证证明图片不做展示用）</div>
                        <div class="font_sc bx_data two_updata">
                            <a href="javascript:;" class="sc_input" @click="imgStatus=2">
                                <input type="file" class="img_file" value="选择视频" placeholder="选择视频" accept="image/*">
                                <span>上传图片</span>
                            </a>
                            <ul class="img_face">
                                <li v-for="item in imgFace">
                                    <img :src="item" alt="imgFace">
                                </li>
                            </ul>
                        </div>
                        <div class="mobile_yz">
                            <div class="phone_box">
                                <label>手机号：</label>
                                <input type="text" id="phone" class="input_phone" v-model="formProfess.phone" placeholder="填写联系方式（手机号）" maxlength="11">
                            </div>
                            <div class="phone_box">
                                <label>验证码：</label>
                                <span class="yzm_box">
                                    <input type="text" placeholder="验证码" id="yam_code" v-model="formProfess.code" class="input_phone_code" maxlength="6">
                                    <a href="javascript:;" class="btn_phone_code" @click="getCode">获取验证码</a>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="subimt">
                    <a href="javascript:;" class="sur t tj_sy" id="shiyan" @click="putProfess">提交誓言</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 个人中心 -->
<div class="pop my_zx" id="pop3">
    <a href="javascript:;" class="btn-close t sur"></a>
    <div class="pop-con">
        <div class="layer_com">
            <h3>个人中心</h3>
            <div v-if="roleInfo">
                <div v-for="item in statusAB" v-if="item" class="status_personal" :class="{'include_btn':item.btn}">
                    {{ item.msg }}
                    <a href="javascript:;" v-if="item.btn" @click="dialog(2)">{{ item.btn }}</a>
                </div>
            </div>
            <div class="mc_list lay_mc_list">
                <ul class="c">
                    <li>
                        <h2><i class="sur_y sur"></i>实物奖励<i class="sur_y sur"></i></h2>
                        <em class="shiwu">
                            <p v-for="item in giftOwn.entity">{{ item.name }}</p>
                        </em>
                        <a href="javascript:;" @click="address">完善信息</a>
                    </li>
                    <li>
                        <h2><i class="sur_y sur"></i>虚拟奖励<i class="sur_y sur"></i></h2>
                        <em class="xuni">
                            <p v-for="item in giftOwn.vertual">{{ item.name }}</p>
                        </em>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!--图片裁剪框1 start-->
<div class="yc-upload-wrap" style="display: none" v-show="isClip">
    <div style="padding-right:0;padding-left:36px;">
        <div style="min-height:1px;line-height:160px;text-align:center;position:relative;" ontouchstart="">
            <div class="cover-wrap">
                <div class="clip_all">
                    <div id="clipArea"></div>
                    <div class="one_but">
                        <button id="clipBtn">保存截图</button>
                        <button class="btn_close" @click="isClip=false">取消</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--<div class="yc-upload-wrap" style="display: none" v-show="isClip">-->
<!--    <div class="yc-upload-box">-->
<!--        <div class="container">-->
<!--            <div class="row">-->
<!--                <div class="col-md-12 col-sm-12 col-xs-12" style="padding:0;">-->
<!--                    <div class="ycupload-mainbox">-->
<!--                        <div class="ycupload-line"></div>-->
<!--                        <div style="height:30px;"></div>-->
<!--                        <div style="min-height:1px;">-->
<!--                            <div class="container">-->
<!--                                <div class="row">-->
<!--                                    <div class="col-md-12 col-sm-12 col-xs-12"-->
<!--                                         style="padding-right:0;padding-left:36px;">-->
<!--                                        &lt;!&ndash;<a href="javascript:void(0);" class="cover-uploadBtn">-->
<!--                                        <img src="img/yc_uploadimg_06.png">-->
<!--                                        <div id="clipArea"></div>-->
<!--                                        <input type="file" id="file">-->
<!--                                        <button id="clipBtn">截取</button>-->
<!--                                    </a>-->
<!--                                    <div id="view"></div>&ndash;&gt;-->
<!--                                        <div style="min-height:1px;line-height:160px;text-align:center;position:relative;"-->
<!--                                             ontouchstart="">-->
<!--                                            <div class="cover-wrap"-->
<!--                                                 style="display:none;position:fixed;left:0;top:0;width:100%;height:100%;background: rgba(0, 0, 0, 0.4);z-index: 10000000;text-align:center;">-->
<!--                                                <div class=""-->
<!--                                                     style="width:100%;height:7.5rem;margin:100px auto;background-color:#FFFFFF;overflow: hidden;border-radius:4px;">-->
<!--                                                    <div id="clipArea" style="margin:10px;height: 6.3rem;"></div>-->
<!--                                                    <div class="one_but"-->
<!--                                                         style="height:56px;line-height:36px;text-align: center;padding-top:8px; ">-->
<!--                                                        <button id="clipBtn"-->
<!--                                                                style="width:120px;height: 36px;border-radius: 4px;background-color:#ff8a00;color: #FFFFFF;font-size: 14px;text-align: center;line-height: 36px;outline: none;border: none">保存截图</button>-->
<!--                                                        <button class="quxiao"-->
<!--                                                                style="width:120px;height: 36px;border-radius: 4px;background-color:#ff8a00;color: #FFFFFF;font-size: 14px;text-align: center;line-height: 36px;outline: none;border: none">取消</button>-->
<!--                                                    </div>-->
<!--                                                    <div class="two_but"-->
<!--                                                         style="height:56px;line-height:36px;text-align: center;padding-top:8px; display: none">-->
<!--                                                        <button id="clipBtn1"-->
<!--                                                                style="width:120px;height: 36px;border-radius: 4px;background-color:#ff8a00;color: #FFFFFF;font-size: 14px;text-align: center;line-height: 36px;outline: none;border: none">保存截图</button>-->
<!--                                                        <button class="quxiao"-->
<!--                                                                style="width:120px;height: 36px;border-radius: 4px;background-color:#ff8a00;color: #FFFFFF;font-size: 14px;text-align: center;line-height: 36px;outline: none;border: none">取消</button>-->
<!--                                                    </div>-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                            <div id="view" style="width:214px;height:160.5px;"-->
<!--                                                 title="请上传 428*321 的封面图片"></div>-->
<!--                                            <div style="height:10px;"></div>-->

<!--                                        </div>-->


<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->

<!--                        </div>-->
<!--                        <div style="height:25px;"></div>-->
<!--                    </div>-->

<!--                </div>-->
<!--            </div>-->
<!--        </div>-->

<!--    </div>-->
<!--</div>-->




<!--认证失败-->
<div class="pop tj_gc" id="pop4">
    <a href="javascript:;" class="btn-close t sur"></a>
    <div class="pop_4">
        <ul v-if="roleInfo">
            <li v-for="item in statusAB" v-if="item.btn" class="status_personal" :class="{'include_btn':item.btn}">
                {{ item.msg }}
                <a href="javascript:;" v-if="item.btn" @click="dialog(2)">{{ item.btn }}</a>
            </li>
        </ul>
    </div>
</div>

<!--提示小弹窗-->
<div class="pop tj_gc" id="pop5">
    <div class="inner">
        <a href="javascript:;" class="btn-close t sur"></a>
        <div class="pop_5" v-if="msg">
            <h4 class="title" v-text="msg.title"></h4>
            <p class="content" v-text="msg.content"></p>
        </div>
    </div>
</div>

<!-- 快递地址填写 -->
<div class="pop my_zx" id="pop6">
    <a href="javascript:;" class="btn-close t sur"></a>
    <div class="pop-con">
        <div class="layer_com">
            <h3>恭喜您抽中<span class="shiwu_box" v-text="gift"></span>！</h3>
            <div class="sm_duan">
                <p class="c">1、请务必填写真实信息，<span style="color: #f04f53">提交后不可修改</span></p>
                <p class="c"><span class="g-fl">2、</span><span class="g-fl fr_span">因个人原因导致奖品无法签收，而被快递退回的奖品将视为自动放弃获奖资格。</span></p>
            </div>
            <div class="formSu">
                <form action="">
                    <div class="formSu_in">
                        <label>姓名：</label><input type="text" id="name_tj" maxlength="5" v-model="formAddress.name">
                    </div>
                    <div class="formSu_in">
                        <label>电话：</label><input type="text" id="phone_tj" maxlength="11" v-model="formAddress.phone">
                    </div>
                    <div class="formSu_in city_tjBox">
                        <label>地址：</label><input type="text" id="city_tj" v-model="formAddress.addr">
                    </div>
                    <div class="subimt">
                        <a href="javascript:;" class="sur t cg_link" @click="putAddress">完成</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- swiper 轮播 -->
<div class="pop swiper_lb" id="pop7">
    <a href="javascript:;" class="btn-close t sur"></a>
    <div class="pop-con">
        <div class="swiper_layer">
            <div class="swiper-container">
                <div class="swiper-wrapper" id="weyi_sw"></div>
            </div>
            <div class="pagination"></div>
        </div>
        <div class="sx_link c">
            <a href="javascript:;" class="pevr_s t sur g-fl">上一页</a>
            <a href="javascript:;" class="next_x t sur g-fr">下一页</a>
        </div>
    </div>
</div>


