var isLongmen = location.href.indexOf('/longmen/') !== -1;
var shareUrl = isLongmen ? 'longmen' : '20200317';

var th = {
    serverTime: String, // 服务器时间
    thGift: undefined,  //周年庆礼包是否可领取
    clickFlag: false, //防抖标识
    /**
     * 1 正常 2 已结束 -1 未开始
     */
    phase_1: String, // 阶段标记
    phase_2: String, // 阶段标记
    isBaned: undefined, // 是否是封停账号
    isGrade: undefined, // 是否等级不足
    shareParam: {
        index: {
            link: location.origin + '/xtl/cloth/' + shareUrl + '/m/index.shtml',
            title: '你的好友邀你一起制作华裳',
            desc: '《新天龙八部》周年制衣活动，点击链接，进入活动页面，周年庆时装技术革新，永久水袖时装免费拿！',
            imgUrl: 'http://i0.cy.com/tlpt/cloth/20200317/indexSmall.jpg'
        },
        share: {
            // link: location.origin + '/xtl/cloth/' + shareUrl + '/m/share.shtml' + ja.utils.setUrlParams({ infoData: a, clothOrder: b }),
            title: ['你的好友赠予你一支毛笔', '你的好友赠予你一盘颜料', '你的好友赠予你一把水袖金粉', '你的好友赠予你一匹布料'],
            desc: '《新天龙八部》周年制衣活动，点击链接，进入活动页面，周年庆时装技术革新，永久水袖时装免费拿！',
            imgUrl: [
                'http://i0.cy.com/tlpt/cloth/20200317/maobiSamll.jpg',
                'http://i0.cy.com/tlpt/cloth/20200317/yanLiaoSamll.jpg',
                'http://i0.cy.com/tlpt/cloth/20200317/jinFenSamll.jpg',
                'http://i0.cy.com/tlpt/cloth/20200317/buLiaoSmall.jpg']
        }
    },
    giftName: ["惟妙惟肖礼盒", "玉剪巧织礼盒", "仗剑江湖", "周年礼包"],
    init: function () {

    },
    /**
    * 通用错误码处理
    * @param {*} data 返回的对象
    */
    commonErrPop: function (data) {
        var flag = false;
        th.clickFlag = false;
        if (data.code == '1202' || data.code == '1209' || data.code == '1207' || data.code == '1007') { //1202请求参数缺失或者为空  1209操作频繁
            th.tips('系统繁忙，请稍后重试！');
            return;
        } else if (data.code == '1019' || data.code == '1012') { // 登录状态检测失败
            $('#login').click();
            return;
        } else if (data.code == '3103') { // 账号封停
            popShow('forbidden')
            return;
        } else if (data.code == '2131') { // 等级不足
            popShow('lowGrade')
            return;
        } else {
            flag = true;
        }
        return flag;
    },
    /**
    * 通用提示
    */
    tips: function (msg) {
        $('#tips p').html(msg);
        popShow('tips')
    },
    /**
    * 个人中心 传null true情况是因为个人中心会刷新绘图相关的参数 为了避免影响 只有在第一次进入页面才会初始化这些参数
     * @param {*} taskid 时装编号
     * @param {*} taskid 时装编号
    */
    getUserinfo: function (a, b) {
        ja.ajax({
            type: 'GET',
            url: commPath + '/mybackpack',
            async: false,
            success: function (res) {
                th.isBaned = res.code == '3103';
                th.isGrade = res.code == '2131';
                if (res.code == 10000) {
                    //服务器时间
                    var data = res.data
                    var time = new Date(data.timestamp);
                    th.serverTime = time.getFullYear() + '-' + (time.getMonth() + 1) + '-' + time.getDate();

                    th.phase_1 = data.STAGE_1_STATUS
                    th.phase_2 = data.STAGE_2_STATUS

                    th.methodForPack(data.mybackpack)
                    b = b ? undefined : true;
                    th.methodForUserinfo(data.userinfo, b)

                    a && a(th.serverTime)
                } else if (th.commonErrPop(res)) {
                    //alert(res.message);
                }
            }
        })
    },
    /**
     * 解锁图纸
     * @param {*} taskid 时装编号
     */
    getDrawings: function (a) {
        console.log(parseInt($(this).attr('flag')));

        ja.ajax({
            type: 'POST',
            url: commPath + '/task/get',
            async: false,
            data: {
                taskid: parseInt($(a).attr('flag'))
            },
            success: function (res) {
                if (res.code == 10000) {
                    underwayDraw = parseInt($(a).attr('flag'));
                    th.methodForUserinfo(res.data.userinfo)
                    $('.swiperbox .swiper-slide').eq(underwayDraw - 1).find('img').attr('src', '/act/xtl/cloth/20200317/m/img/pic' + (underwayDraw - 1) + '_on.png');
                    $('.swiperbox .swiper-slide').eq(underwayDraw - 1).find('.suobtn').hide();
                    $('#com_txt p').html('恭喜少侠可以开始使用活动道具绘制图纸啦!');
                    popShow('pop_com');
                    $('#step_box').css({ 'display': 'none' });
                    underwayStep = 1;
                    $('.cailist li').eq(underwayStep - 1).addClass('cur');

                    th.clickFlag = false;
                } else if (th.commonErrPop(res)) {
                    switch (res.code) {
                        case 1208:
                            alert('请选择正确的图纸进行解锁！');
                            break;
                        case 2110:
                            alert('制作完成才可以解锁下一个哦!');
                            break;
                        case 5082:
                            alert('解锁失败，请稍后再试！');
                            break;
                        case 5083:
                            alert('您已经领取过这张图纸了！');
                            break;
                        case 5085:
                            alert('您已绘制完成这张图纸了！');
                            break;
                        default:
                            alert(res.message)
                    }
                }
            },
            error: function () {
                th.clickFlag = false;
            }
        })
    },
    /**
     * 材料制作(勾线/上色/水袖/制衣/)(获得Q奖励)
     * @param {*} taskid  1,2,3,4,代表4个时装
     * @param {*} fashionStatus  2勾线3上色4水粉5制衣
     * @param {*} cb  回调函数
     */
    toStep: function (taskid, fashionStatus, cb, cb2) {
        fashionStatus = Number(fashionStatus)
        fashionStatus++;
        ja.ajax({
            type: 'POST',
            url: commPath + '/task/step',
            async: false,
            data: {
                taskid: taskid,
                fashionStatus: fashionStatus
            },
            success: function (res) {
                res.code != 10000 && cb2()
                if (res.code == 10000) {
                    th.methodForUserinfo(res.data.userinfo);
                    cloth_flage = false;
                    cb && cb()
                } else if (th.commonErrPop(res)) {
                    switch (res.code) {
                        case 1001:
                            th.tips('您所需要消耗的道具不足，<br>去看看好友是否有多余可以使用吧！')
                            break;
                        case 1208:
                            th.tips('请选择正确的步骤进行操作！')
                            break;
                        case 2110:
                            th.tips('少侠还未完成上一步的绘制，先完成上一步吧！')
                            break;
                        case 5081:
                            th.tips('您还没有进行到这一步呢！')
                            break;
                        case 5082:
                            th.tips('操作失败，请稍候刷新页面重试！')
                            break;
                        case 5085:
                            th.tips('您已经完成过此操作了，请勿进行重复操作！')
                            break;
                        case 5086:
                            th.tips('操作失败，请稍候刷新页面重试！')
                            break;
                        default:
                            alert(res.message)
                    }

                }
            },
            error: function () {
                cb2()
            }
        })
    },
    /**
     * 分享材料
     * @param {*} a  1234毛笔/颜料/水粉/布料
     */
    share: function (a) {
        ja.ajax({
            type: 'POST',
            url: commPath + '/exchange/share',
            async: false,
            data: {
                itemid: a
            },
            success: function (res) {
                if (res.code == 10000) {
                    var id = Number(a)
                    id--;

                    // var arr = $('.djlist').children().eq(id).find('p').text().split('×');
                    // arr[1] = parseInt(arr[1]) - 1;
                    // if (arr[1] < 0) return;
                    // arr[1] >= 2 ? ($('.djlist').children().eq(id).find('p').next().show()) : ($('.djlist').children().eq(id).find('p').next().hide());
                    // $('.djlist').children().eq(id).find('p').text(arr.join('×'))

                    // var num1 = parseInt($('.cailist li').eq(id).find('em').text());
                    // if (num1 - 1 < 0) return
                    // $('.cailist li').eq(id).find('em').text(num1 - 1);

                    dialogUp('dia_share');
                    th.shareHtml(res.data.orderid, id)
                } else if (th.commonErrPop(res)) {
                    if (res.code == 1001) {
                        alert('您的道具数量不足')
                    } else if (res.code == 3301) {
                        alert('分享失败，请稍后刷新页面重试~')
                    }
                }
            }
        })
    },
    /**
     * 处理userinfo对象 图纸相关
     * @param {*} a userinfo对象
     * @param {*} b 初始化时传递一次即可  刷新绘图相关参数
     */
    methodForUserinfo: function (a, b) {
        // 周年庆礼包领取状态a
        th.thGift = a.anniversaryStatus;

        // 渲染抽奖码
        if (a.lotcodeStr != '') {
            var code = a.lotcodeStr.split('#'), tr = '';
            $('#clothCode').text(code[code.length - 1]); // 渲染制衣成功弹窗里的抽奖码
            $.each(code, function (i, v) {
                tr += '<tr><td>' + v + '</td></tr>'
            })
            $('#hasCode tbody').html(tr);
            $('#hasCode').next().hide()
        } else {
            $('#hasCode').next().show()
        }

        // 图纸绘制进度
        var fashionStatus = a.fashionStatus.substr(0, 4).split(''), lastCom;
        unlockedDraws = 0;

        $.each(fashionStatus, function (i, v) {
            // 数组下标 i 对应1-4图纸
            //0 未解锁;1已解锁;2已勾线;3.已上色4.已水粉 5.已制衣
            if (v != 0) {
                unlocked[i] = 1;
                lastCom = i;
                if (v == 4 || v == 5) { // 已水粉  已制衣都是完成了图纸绘制\\
                    unlockedDraws++;
                    // 渲染个人中心 我的材料 我的图纸
                    var li = $('.mypiclist').children().eq(i);
                    li.find('span').hide()
                    var url = li.find('img').prop('src');
                    if (url.indexOf('on') == -1) {
                        url.indexOf('.png')
                        li.find('img').prop('src', url.slice(0, url.indexOf('.png')) + '_on.png')
                    }
                    // 渲染制衣坊图纸
                    if (location.href.indexOf('cloth') !== -1) {
                        if (v == 4) {
                            $('#plist').children().eq(i).removeClass('dis_btn')
                        } else if (v == 5) {
                            $('.whislist').children().eq(i).removeClass('wjsli')
                            $('#plist').children().eq(i).removeClass('dis_btn')
                            $('#plist').children().eq(i).addClass('btn-huih')
                        }
                    }
                } else {
                    if (typeof b == 'undefined') {
                        return
                    }
                    switch (v) {
                        case '1':
                            underwayStep = Number(v)
                            underwayDraw = ++i
                            isDrawFlag = true;
                            break;
                        case '2':
                            underwayStep = Number(v)
                            underwayDraw = ++i
                            isDrawFlag = true;
                            break;
                        case '3':
                            underwayStep = Number(v)
                            underwayDraw = ++i
                            isDrawFlag = true;
                            break;
                    }
                }
            }
        })
        // 在toStep中调用则不刷新绘图工具相关
        if (typeof b == 'undefined') {
            return false;
        }
        //读取数据用于解锁图纸的页面显示
        for (var i = 0; i < unlocked.length; i++) {
            if (unlocked[i] == 1) {
                $('.swiperbox .swiper-slide').eq(i).find('img').attr('src', '/act/xtl/cloth/20200317/m/img/pic' + i + '_on.png');
                $('.swiperbox .swiper-slide').eq(i).find('.suobtn').hide();
            };
        };

        //读取数据 步骤显示
        if (underwayDraw != 0) {
            if (underwayStep != 0) {
                $('.cailist li').eq(underwayStep - 1).addClass('cur');
            }
        };
        $('#step_box').removeClass().addClass('stepbox cloth' + (underwayDraw - 1) + '_' + (underwayStep - 1));//图纸框显示
        if (underwayDraw == 0 && unlockedDraws != 0) {
            $('#step_box').removeClass().addClass('stepbox cloth' + lastCom + '_4');//图纸框显示
        }
    },
    /**
     * 处理mybackpack对象 材料相关
     * @param {*} a mybackpack对象
     */
    methodForPack: function (a) {
        $.each(a, function (i, v) {
            var box = $('.djlist')
            // 1 毛笔 2 颜料 3 金粉 4 布料
            switch (v.itemid) {
                case 1:
                    box.children().eq(0).find('p').text('毛笔×' + v.itemnum);
                    v.itemnum >= 2 ? (box.children().eq(0).find('p').next().show()) : (box.children().eq(0).find('p').next().hide());
                    props[0] = v.itemnum;
                    break;
                case 2:
                    box.children().eq(1).find('p').text('颜料×' + v.itemnum);
                    v.itemnum >= 2 ? (box.children().eq(1).find('p').next().show()) : (box.children().eq(1).find('p').next().hide());
                    props[1] = v.itemnum;
                    break;
                case 3:
                    box.children().eq(2).find('p').text('水袖金粉×' + v.itemnum);
                    v.itemnum >= 2 ? (box.children().eq(2).find('p').next().show()) : (box.children().eq(2).find('p').next().hide());
                    props[2] = v.itemnum;
                    break;
                case 4:
                    box.children().eq(3).find('p').text('布料×' + v.itemnum);
                    v.itemnum >= 2 ? (box.children().eq(3).find('p').next().show()) : (box.children().eq(3).find('p').next().hide());
                    // 渲染制衣坊 布料按钮
                    $('#plist li.buliao').find('p').html('布料×' + v.itemnum)
                    if (v.itemnum < 1) {
                        $('#plist li.buliao').removeClass('cur').addClass('btn-huih')
                    } else {
                        $('#plist li.buliao').removeClass('btn-huih').addClass('cur')
                    }
                    break;
            }
        })
        //读取毛笔 颜料 水袖金粉道具的数据
        for (var i = 0; i < props.length; i++) {
            $('.cailist li').eq(i).find('em').html(props[i]);
            if (props[i] == 0) {
                $('.cailist li').eq(i).addClass('dis_btn');
            } else {
                $('.cailist li').eq(i).removeClass('dis_btn');
            }
        };
    },
    /**
     * 处理阶段二 不在阶段二内返回false
     */
    methodForPhase_2: function () {
        if (th.phase_2 == '1') {
            return true;
        } else if (th.phase_2 == '2') {
            th.tips('此阶段已结束！');
            return false;
        } else if (th.phase_2 == '-1') {
            th.tips('此阶段未开始！');
            return false;
        }
    },
    /**
     * 首页分享
     */
    indexShare: function () {
        ja.wx.share({
            link: th.shareParam.index.link,
            title: th.shareParam.index.title,
            desc: th.shareParam.index.desc,
            imgUrl: th.shareParam.index.imgUrl,
            hideMenuList: ['menuItem:share:timeline'],
            success: function () {
                dialogClose()
                ja.ajax({
                    type: 'get',
                    url: commPath + '/pageLog',
                    data: {
                        itemid: 0
                    },
                    success: function (res) {

                    }
                })
            }
        })
    },
    /**
     * 分享材料的分享设置
     * @param {*} a 订单id
     * @param {*} b 道具类型
     */
    shareHtml: function (a, b) {
        ja.wx.share({
            link: location.origin + '/xtl/cloth/' + shareUrl + '/m/share.shtml' + ja.utils.setUrlParams({ orderId: a, clothOrder: b }),
            title: th.shareParam.share.title[b],
            desc: th.shareParam.share.desc,
            imgUrl: th.shareParam.share.imgUrl[b],
            hideMenuList: ['menuItem:share:timeline'],
            success: function () {
                if (location.href.indexOf('share') != -1) {
                    ja.ajax({
                        type: 'get',
                        url: commPath + '/pageLog',
                        data: {
                            itemid: Number(b) + 1
                        },
                        success: function (res) {

                        }
                    })
                } else {
                    dialogClose()
                    th.indexShare()
                }
            }
        })
    },
    /**
     * 我的奖励
     */
    getAwards: function () {
        var gift = '';
        ja.ajax({
            type: 'get',
            url: commPath + '/sectsPrizeInfo',
            async: false,
            success: function (res) {
                if (res.code === 10000) {
                    var data = res.data;
                    if (!data.length) { $('#hasGift').next().show(); return; }
                    var awards = {}, tr = '';
                    // 整理礼包
                    $.each(data, function (i, v) {
                        if (!awards[v.PRIZE_TYPE]) {
                            var name = v.PRIZE_TYPE.split('_'), age = '';
                            if (name.length != 1) {
                                age = name[0]
                                name = name[1]
                            } else {
                                name = name[0]
                            }
                            switch (name) {
                                case "picture#":
                                    name = th.giftName[0]
                                    break;
                                case "fashion#":
                                    name = th.giftName[1]
                                    break;
                                case "age#":
                                    name = (th.giftName[2] + th.toChinesNum(age) + th.giftName[3]);
                                    gift = name;
                                    break;
                            }

                            awards[v.PRIZE_TYPE] = {
                                name: name,
                                total: 1
                            }
                        } else {
                            awards[v.PRIZE_TYPE].total = awards[v.PRIZE_TYPE].total + 1
                        }
                    })
                    // 渲染礼包
                    $.each(awards, function (i, v) {
                        tr += '<tr><td>' + v.name + '</td><td>' + (isLongmen ? (v.total / 2) : v.total) + '</td></tr>'
                    })
                    $('#hasGift tbody').html(tr);
                    $('#hasGift').next().hide();
                } else if (th.commonErrPop(res)) {
                    alert(res.message);
                }
            }
        })
        return gift;
    },
    /**
     * 转化中文数字
     * @param {String} num 需要转化的数字要字符串格式的数字
     */
    toChinesNum: function (num) {
        var changeNum = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'],
            newNum = '',
            arr = num.split('');
        arr[0] = Number(arr[0]) - 1;//转化为下标
        if (arr.length > 1) {
            arr[1] = Number(arr[1]) - 1;//转化为下标
            newNum = (!arr[0] ? changeNum[9] : (changeNum[arr[0]] + changeNum[9])) + changeNum[arr[1]];
        } else {
            newNum = changeNum[arr[0]];
        }
        return newNum;
    },
    /**
     * 用于在绘制步骤结束后减少道具
     * @param {String} type 类型  空为绘制类操作 true 为制衣
     */
    ReduceProp: function (type) {
        if (type) {
            var arr = $('#plist li.buliao').find('p').text().split('×');
            arr[1] = parseInt(arr[1]) - 1;
            if (arr[1] < 0) {
                // return;
            } else if (arr[1] == 0) {
                $('#plist li.buliao').find('p').text(arr.join('×'));
                $('#plist li.buliao').removeClass('cur').addClass('btn-huih')
            } else {
                $('#plist li.buliao').find('p').text(arr.join('×'));
                $('#plist li.buliao').removeClass('btn-huih')
            }

            var arr1 = $('.djlist').children().eq(3).find('p').text().split('×');
            arr1[1] = parseInt(arr1[1]) - 1;
            if (arr1[1] < 0) return;
            arr1[1] >= 2 ? ($('.djlist').children().eq(3).find('p').next().show()) : ($('.djlist').children().eq(3).find('p').next().hide());
            $('.djlist').children().eq(3).find('p').text(arr1.join('×'))
        } else {
            var index = underwayStep - 1;
            var num1 = parseInt($('.cailist li').eq(index).find('em').text()) - 1;
            if (num1 < 0) {
                // return;
            } else if (num1 == 0) {
                $('.cailist li').eq(index).addClass('dis_btn');
                $('.cailist li').eq(index).find('em').text(num1);
            } else {
                $('.cailist li').eq(index).removeClass('dis_btn');
                $('.cailist li').eq(index).find('em').text(num1);
            }

            var arr = $('.djlist').children().eq(index).find('p').text().split('×');
            arr[1] = parseInt(arr[1]) - 1;
            if (arr[1] < 0) return;
            arr[1] >= 2 ? ($('.djlist').children().eq(index).find('p').next().show()) : ($('.djlist').children().eq(index).find('p').next().hide());
            $('.djlist').children().eq(index).find('p').text(arr.join('×'))
        }
    }
}
