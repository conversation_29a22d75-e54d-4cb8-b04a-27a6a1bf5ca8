/* jQuery photoClip v1.8.0 | Relying on the plug-in [iscroll-zoom.js] [hammer.js] [lrz.all.bundle.js] */
!function(t,o){"use strict";"function"==typeof define&&define.amd?define(["jquery","iscroll-zoom","hammer","lrz"],o):"object"==typeof exports?module.exports=o(require("jquery"),require("iscroll-zoom"),require("hammer"),require("lrz")):(t.bjj=t.bjj||{},t.bjj.PhotoClip=o(t.jQuery,t.IScroll,t.Hammer,t.lrz))}(this,function(t,o,n,e){"use strict";function i(o,n){if(!window.FileReader)return void alert("您的浏览器不支持 HTML5 的 FileReader API， 因此无法初始化图片裁剪插件，请更换最新的浏览器！");var e=t.extend({},s,n),i=r(o,e);this.destroy=i.destroy}function r(i,r){function s(){N=!0,to.append(this),w.call(this,G,function(){J=this.naturalWidth,K=this.naturalHeight}),w(_,function(){u()}),D.call(this,this.src)}function l(){var t={zoom:!0,scrollX:!0,scrollY:!0,freeScroll:!0,mouseWheel:!0,wheelAction:"zoom"};io=new o($[0],t)}function u(){uo=0,fo=0,po=0,to.css({width:J,height:K}),S(to,uo,fo,po),T(J,K),io.zoom(io.options.zoomStart),f(J,K);var t=.5*(X-J*io.options.zoomStart),o=.5*(Y-K*io.options.zoomStart);io.scrollTo(t,o)}function f(t,o){_.css({width:t,height:o}),$.append(_),io.refresh()}function p(){var t=!!navigator.userAgent.match(/mobile/i);if(t){eo=new n(_[0]),eo.add(new n.Rotate);var o,e;eo.on("rotatemove",function(t){lo||(o=t.rotation,o>180?o-=360:-180>o&&(o+=360),e=o>0?1:0>o?-1:0)}),eo.on("rotateend",function(t){lo||Math.abs(o)>30&&(1==e?d(t.center):-1==e&&h(t.center))})}else _.on("dblclick",function(t){d({x:t.clientX,y:t.clientY})})}function d(t){m(90,t)}function h(t){m(-90,t)}function m(t,o){if(!lo){lo=!0;var n;n=o?y(_,o.x,o.y):x(_,$,.5*X,.5*Y);var e,i,r=k(po,n),a=r.x,s=r.y,c=0,l=0,u=0,p=0,d=po+t;90==d||-270==d?(c=a+s,l=s-a,d>po?(u=K-a-s,p=a-s):po>d&&(u=K-s-(J-a),p=a+s-K),e=K,i=J):180==d||-180==d?(c=2*a,l=2*s,d>po?(u=J-a-(K-s),p=K-(a+s)):po>d&&(u=J-(a+s),p=K-s-(J-a)),e=J,i=K):270==d||-90==d?(c=a-s,l=a+s,d>po?(u=a+s-J,p=J-a-(K-s)):po>d&&(u=s-a,p=J-a-s),e=K,i=J):(0==d||360==d||-360==d)&&(c=0,l=0,d>po?(u=a-s,p=a+s-J):po>d&&(u=a+s-K,p=s-a),e=J,i=K),0==po?(uo=0,fo=0):90==po||-270==po?(uo-=a+s,fo-=s-a):180==po||-180==po?(uo-=2*a,fo-=2*s):(270==po||-90==po)&&(uo-=a-s,fo-=a+s),uo=uo.toFixed(2)-0,fo=fo.toFixed(2)-0,S(to,uo,fo,po,a,s),F(to,uo,fo,d,200,function(){lo=!1,po=d%360,uo+=c+u,fo+=l+p,uo=uo.toFixed(2)-0,fo=fo.toFixed(2)-0,S(to,uo,fo,po),io.scrollTo(io.x-u*io.scale,io.y-p*io.scale),T(e,i),io.scale<io.options.zoomMin&&io.zoom(io.options.zoomMin),f(e,i)})}}function g(){no=document.createElement("canvas")}function v(){if(!N)return void alert("亲，当前没有图片可以裁剪!");var t=x(_,$),o=io.scale,n=no.getContext("2d");n.clearRect(0,0,no.width,no.height),n.save(),Z&&Q?(no.width=Z,no.height=Q,n.scale(Z/X*o,Q/Y*o)):(no.width=X/o,no.height=Y/o),n.translate(uo-t.x/o,fo-t.y/o),n.rotate(po*Math.PI/180),n.drawImage(G[0],0,0),n.restore();var e=no.toDataURL(H,1);oo.css("background-image","url("+e+")"),U.call(G[0],e)}function b(){w(V,function(){ro=V.width(),ao=V.height()})}function x(t,o,n,e){n=n||0,e=e||0;var i,r;return w(t,function(){i=t.offset()}),w(o,function(){r=o.offset()}),{x:r.left-i.left+n,y:r.top-i.top+e}}function y(t,o,n){o=o||0,n=n||0;var e;return w(t,function(){e=t.offset()}),{x:o+co.scrollLeft()-e.left,y:n+co.scrollTop()-e.top}}function w(o,n){var e=t();t.each(o,function(o,n){for(var i,r=t(n),a=r.parents().andSelf().filter(":hidden"),o=0;o<a.length&&r.is(":hidden");o++)i=a.eq(o),"none"==i.css("display")&&(e=e.add(i.show()))}),"function"==typeof n&&n.call(this),e.hide()}function k(t,o){var n=io.scale,e={};return 0==t?(e.x=o.x/n,e.y=o.y/n):90==t||-270==t?(e.x=o.y/n,e.y=K-o.x/n):180==t||-180==t?(e.x=J-o.x/n,e.y=K-o.y/n):(270==t||-90==t)&&(e.x=J-o.y/n,e.y=o.x/n),e}function z(t,o,n,e){var i=t/n,r=o/e;return i>r?i:r}function T(t,o){io.options.zoomMin=z(X,Y,t,o),io.options.zoomMax=Math.max(1,io.options.zoomMin),io.options.zoomStart=Math.min(io.options.zoomMax,z(ro,ao,t,o))}function M(){G&&G.length&&(G.remove(),delete G[0])}function j(o){M(),G=t("<img>").css({"user-select":"none","pointer-events":"none"}),G.load(s),G.attr("src",o)}function S(t,o,n,e,i,r){i=i||0,r=r||0;var a={};a[c+"transform"]="translateZ(0) translate("+o+"px,"+n+"px) rotate("+e+"deg)",a[c+"transform-origin"]=i+"px "+r+"px",t.css(a)}function F(t,o,n,e,i,r){t.css(c+"transform"),t.css(c+"transition",c+"transform "+i+"ms"),t.one(a,function(){t.css(c+"transition",""),r.call(this)}),t.css(c+"transform","translateZ(0) translate("+o+"px,"+n+"px) rotate("+e+"deg)")}function L(t){return"[object Array]"===Object.prototype.toString.call(t)}function q(){V=t(i).css({"user-select":"none",overflow:"hidden"}),"static"==V.css("position")&&V.css("position","relative"),$=t("<div class='photo-clip-view'>").css({position:"absolute",left:"50%",top:"50%",width:X,height:Y,"margin-left":-X/2,"margin-top":-Y/2}).appendTo(V),_=t("<div class='photo-clip-moveLayer'>").appendTo($),to=t("<div class='photo-clip-rotateLayer'>").appendTo(_);{var o=t("<div class='photo-clip-mask'>").css({position:"absolute",left:0,top:0,width:"100%",height:"100%","pointer-events":"none"}).appendTo(V);t("<div class='photo-clip-mask-left'>").css({position:"absolute",left:0,right:"50%",top:"50%",bottom:"50%",width:"auto",height:Y,"margin-right":X/2,"margin-top":-Y/2,"margin-bottom":-Y/2,"background-color":"rgba(0,0,0,.5)"}).appendTo(o),t("<div class='photo-clip-mask-right'>").css({position:"absolute",left:"50%",right:0,top:"50%",bottom:"50%","margin-left":X/2,"margin-top":-Y/2,"margin-bottom":-Y/2,"background-color":"rgba(0,0,0,.5)"}).appendTo(o),t("<div class='photo-clip-mask-top'>").css({position:"absolute",left:0,right:0,top:0,bottom:"50%","margin-bottom":Y/2,"background-color":"rgba(0,0,0,.5)"}).appendTo(o),t("<div class='photo-clip-mask-bottom'>").css({position:"absolute",left:0,right:0,top:"50%",bottom:0,"margin-top":Y/2,"background-color":"rgba(0,0,0,.5)"}).appendTo(o),t("<div class='photo-clip-area'>").css({border:"1px dashed #ddd",position:"absolute",left:"50%",top:"50%",width:X,height:Y,"margin-left":-X/2-1,"margin-top":-Y/2-1}).appendTo(o)}oo=t(I),oo.length&&oo.css({"background-color":"#666","background-repeat":"no-repeat","background-position":"center","background-size":"contain"})}function R(){B.off("change"),B=null,eo?(eo.off("rotatemove"),eo.off("rotateend"),eo=null):_.off("dblclick"),io.destroy(),io=null,V.empty(),V=null,$=null,_=null,to=null,oo.css({"background-color":"","background-repeat":"","background-position":"","background-size":""}),oo=null}var C=r.size,A=r.outputSize,E=r.file,I=r.view,P=r.ok,H="image/jpeg",W=r.loadStart,D=r.loadComplete,O=r.loadError,U=r.clipFinish;L(C)||(C=[260,260]),L(A)||(A=[0,0]);var X=C[0]||260,Y=C[1]||260,Z=Math.max(A[0],0),Q=Math.max(A[1],0),B=t(E);if(B.length){var G,J,K,N;B.attr("accept","image/*"),B.on("change",function(){if(this.files.length){var t=this.files[0];if(!/image\/\w+/.test(t.type))return alert("图片格式不正确，请选择正确格式的图片文件！"),!1;var o=new FileReader;o.onprogress=function(t){console.log((t.loaded/t.total*100).toFixed()+"%")},o.onload=function(){e(t).then(function(t){j(t.base64)}).catch(function(t){alert("图片处理失败"),O.call(this,t)})},o.onerror=function(t){alert("图片加载失败"),O.call(this,t)},o.readAsDataURL(t),W.call(o,t)}}),B.click(function(){this.value=""});var V,$,_,to,oo,no,eo,io,ro,ao;q(),l(),p(),g();var so=t(P);so.length&&so.click(function(){v()});var co=t(window);b(),co.resize(b);var lo,uo,fo,po;return{destroy:R}}}var a,s={size:[260,260],outputSize:[0,0],file:"",view:"",ok:"",loadStart:function(){},loadComplete:function(){},loadError:function(){},clipFinish:function(){}},c="";return function(){var t,o={Webkit:"webkit",Moz:"",O:"o"},n=document.documentElement,e=function(o){return t?t+o:o.toLowerCase()};for(var i in o)if(void 0!==n.style[i+"TransitionProperty"]){c="-"+i.toLowerCase()+"-",t=o[i];break}a=e("TransitionEnd")}(),i});