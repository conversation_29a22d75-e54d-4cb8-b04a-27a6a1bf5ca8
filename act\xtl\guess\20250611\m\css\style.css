@charset "utf-8";

* {
  text-decoration: none;
  margin: 0;
  padding: 0;
  list-style: none;
  border: 0;
  -webkit-text-size-adjust: none;
}

body,
button,
input,
select,
textarea {
  font: 12px/1.5 tahoma, arial, sans-serif
}

.pa {
  position: absolute
}

.pr,
.part {
  position: relative
}

.hide {
  width: 0;
  height: 0;
  overflow: hidden;
  display: block;
  visibility: hidden
}

body,
html {
  width: 100%;
  background-color: #ece1d3;
}

a {
  text-decoration: none;
  blr: expression(this.onFocus=this.blur());
  outline: none
}

html {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0)
}

.c:before,
.c:after {
  content: "";
  display: table
}

.c:after {
  clear: both
}

.c {
  zoom: 1
}

a {
  text-decoration: none;
  overflow: visible
}

a:hover {
  text-decoration: none
}

body {
  font-size: 22px;
  color: #000;
  text-align: center
}

.wrap {
  width: 100%;
  overflow: hidden;
  position: relative;
  color: #d0e3ff;
}

.btn {
  transition: filter 0.5s linear;
  background: url(../img/com.png) no-repeat;
  display: block;
  text-indent: -9999em;
  overflow: hidden;
  background-size: 500px auto;
}

.btn:not(.gray):hover {
  -webkit-filter: brightness(1.15);
}

.head {
  position: relative;
  width: 100%;
  height: 938px;
  background: url(../img/head.jpg) no-repeat;
  background-size: 100% 100%;
}

.logo {
  width: 165px;
  height: 76px;
  background: url(../img/logo.png) no-repeat;
  display: block;
  text-indent: -9999em;
  position: absolute;
  top: 24px;
  left: 14px;
  z-index: 2;
}
.logo2 {
  pointer-events: none;
  width: 129px;
  height: 86px;
  background: url(../img/logo2.png) no-repeat;
  display: block;
  text-indent: -9999em;
  position: absolute;
  top: 18px;
  left: 190px;
  z-index: 1;
}

.arr {
  position: absolute;
  z-index: 3;
  width: 235px;
  height: 66px;
  left: 50%;
  margin-left: -117px;
  text-align: center;
  top: 846px;
  text-indent: -9999em;
  background: url(../img/arr.png) no-repeat;
  animation: flow 2s linear infinite both;
  -webkit-animation: flow 2s linear infinite both;
}
@keyframes flow {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(10px);
  }

  100% {
    transform: translateY(0px);
  }
}

@-webkit-keyframes flow {
  0% {
    -webkit-transform: translateY(0px);
  }

  50% {
    -webkit-transform: translateY(10px);
  }

  100% {
    -webkit-transform: translateY(-0px);
  }
}

.login_cn {
  position: absolute;
  padding: 0 30px;
  right: 10px;
  /* width: 100%; */
  text-align: center;
  font-size: 22px;
  line-height: 36px;
  top: 30px;
  text-shadow: 0 0 10px #fff;
  background-image: linear-gradient(to right, transparent, rgba(96, 41, 58, 1), transparent); 
}

.login_cn,
.login_cn a {
  color: #fff1d7;
}

.cont {
  height: auto;
  width: 100%;
  background: url(../img/cont.jpg) no-repeat center top;
  position: relative;
  background-size: 100% 100%;
  margin-top: -1px;
  padding-bottom: 200px;
}

.cont1 {
  height: 7361px;
  background: url(../img/cont1.jpg) no-repeat center top;
  background-size: 100% 100%;
}

.cont2 {
  height: 6361px;
  background: url(../img/cont2.jpg) no-repeat center top;
  background-size: 100% 100%;
}

.cont3 {
  height: 5561px;
  background: url(../img/cont3.jpg) no-repeat center top;
  background-size: 100% 100%;
}

.tit {
  margin: 0 auto;
  width: 750px;
  height: 129px;
  background: url(../img/tit.png) no-repeat;
  text-indent: -9999em;
  overflow: hidden;
  background-size: 750px auto;
}
.tit.t0{
  height: 129px;
  background-position: 0 0;
}


.jcb {
  z-index: 1;
  position: relative;
  text-align: center;
  color: #ffefcc;
  font-size: 22px;
  margin-top: 25px;
  line-height: 34px;
  height: 44px;
}
.jcb.jcbbox{
  width: 100%;
  height: 44px;
  background: url(../img/jcbbox.png) center no-repeat;
  background-size: 386px 44px;
}
.jcb span {
  color: #ffefcc;
  font-size: 34px;
}

.recordbtn {
  z-index: 3;
  position: absolute;
  color: #ffde98;
  font-size: 22px;
  right: 30px;
  top: 50px;
  line-height: 28px;
  text-indent: -99999rem;
  width: 102px;
  height: 37px;
  background: url(../img/recordbtn.png) no-repeat;
  background-size: 100% 100%;
}

.dhlist {
  margin-top: 43px;
  display: flex;
  justify-content: space-evenly;
  flex-wrap: wrap;
}

.dhlist li {
  width: 213px;
  height: 382px;
  background: url(../img/libg.png) no-repeat;
  padding-top: 26px;
  background-size: 100% 100%;
  box-sizing: border-box;
}
.dhlist li span{
  color: #ffefcc;
}
.dhlist li em{
  list-style: none;
}

.dhlist div,
.getlist div {
  width: 120px;
  height: 120px;
  margin: 30px auto 0;
  text-align: center;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.getlist div {
  width: 98px;
  height: 97px;
  background: url(../img/djbox1.png) no-repeat;
  background-size: 100% 100%;
  margin-top: 10px;
}

.dhlist div img,
.getlist div img {
  width: 64px;
  height: 64px;

}

.djname {
  color: #d6ae82;
  font-size: 18px;
  text-align: center;
  line-height: 20px;
  padding: 0 5px;
  height: 55px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dhbtn {
  width: 197px;
  height: 55px;
  margin: 0 auto;
  background-position: 0 0;
}

.infp {
  color: #ffefcc;
  font-size: 20px;
  text-align: center;
  line-height: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tit.t1 {
  margin-top: 30px;
  background-position: 0 -150px;
}

.tit.t2 {
  width: 542px;
  height: 44px;
  background-position: 0 -450px;
  margin-top: 30px;
}

.textbox {
  margin: 12px auto 0;
  text-align: center;
}

.textbox .jcb {
  margin-top: -7px;
}

.stepsbox {
  height: 176px;
  margin-top: 12px;
  position: relative;
}

.steps {
  position: relative;
  width: 100%;
}

.steps li {
  float: left;
  position: relative;
  height: 176px;
}

.steps li.li0 {
  width: 110px;
}

.steps li.li1 {
  width: 177px;
}

.steps li.li2 {
  width: 177px;
}

.steps li.li3 {
  width: 286px;
}

.steps li .numdiv {
  position: absolute;
  top: 0;
  right: -88px;
  width: 175px;
  height: 176px;
  background: url(../img/stepicon.png) no-repeat;
  padding-top: 65px;
  z-index: 2;
  background-size: 500px auto;
  background-position: 0 0;
  box-sizing: border-box;
}
.steps li.li3 .numdiv {
  right: 20px;
}
.steps li .numdiv p{
  line-height: 26px;
  text-align: center;
  font-size: 22px;
  color: #6c422b;
  background-image: -webkit-linear-gradient(top,#9f4e18, #6c422b);
  -webkit-background-clip: text;
  -webkit-text-fill-color:transparent;
  font-weight: bold;
}
.steps li.cur .numdiv {
  background-position: -325px 0;
}
.steps li.cur .numdiv p{
  color: #d58544;
  background-image: -webkit-linear-gradient(top,#d58544, #6c422b);

}
.jindu,
.jindu p {
  background: url(../img/jindu.png) no-repeat;
  background-size: 750px auto;
}

.jindu {
  width: 100%;
  position: absolute;
  left: 0;
  height: 23px;
  top: 75px;
  z-index: 1;
}

.jindu p {
  position: absolute;
  top: 7px;
  left: 37px;
  height: 100%;
  background-position: -37px -51px;
}



.jindu.jindu0 p {
  width: 105px;
}

.jindu.jindu1 p {
  width: 284px;
}

.jindu.jindu2 p {
  width: 466px;
}

.jindu.jindu3 p {
  width: 750px;
}

/* .jindu p::after {
  content: '';
  position: absolute;
  width: 239px;
  height: 30px;
  background: url(/act/xtl/guess/20240716/pc/img/active.png) no-repeat top right;
  top: 0;
  right: 0px;
  z-index: 2;
  background-size: 239px auto;
}

.jindu.jindu0 p::after {
  width: 100px;
}

.jindu.jindu3 p::after {
  right: 15px;
} */



.getlist {
  width: 100%;
  display: flex;
  justify-content: space-around;
}

.getlist li {
  margin: -37px 4px 0;
  width: 148px;
  height: 327px;
  background: url(../img/divbg.png) no-repeat center top;
  padding-top: 70px;
  background-size: 148px auto;
  box-sizing: border-box;
}

.getlist li.long {
  height: 477px;
  background: url(../img/long.png) no-repeat center top;
  background-size: 148px auto;
}
.getlist li.long2 {
  height: 477px;
  background: url(../img/long2.png) no-repeat center top;
  background-size: 148px auto;
}
.getlist dl {
  text-align: center;
  font-size: 0;
}

.getlist dd {
  display: inline-block;
  vertical-align: top;
  width: 100%;
}

.getlist .djname {
  height: 70px;
}
.getlist .djname2 {
  height: 52px;
}
.getlist .djname3 {
  height: 88px;
}
.getlist .djname.djnamelong {
  line-height: 22px;
}

.getlist.getlistbot .djname.djnamelong {
  line-height: 22px;
}

.getbtn {
  margin-left: -3px;
  width: 153px;
  height: 55px;
  background-position: right top;
}

.getbtn.ylq {
  background-position: right -77px;
}

.stepsboxbot {
  margin-top: 20px;
}

.stepsboxbot .steps li.li0 {
  width: 163px;
}

.stepsboxbot .steps li.li1 {
  width: 213px;
}

.stepsboxbot .steps li.li2 {
  width: 374px;
}

.stepsboxbot .steps li.li2 .numdiv {
  right: 74px;
}

.stepsboxbot .jindu.jindu0 p {
  width: 194px;
}

.stepsboxbot .jindu.jindu1 p {
  width: 377px;
}

.stepsboxbot .jindu.jindu2 p {
  width: 750px;
}

.stepsboxbot .jindu.jindu0 p::after {
  width: 150px;
}

.stepsboxbot .jindu.jindu2 p::after {
  right: 15px;
}



.tit.t3 {
  margin-top: 22px;
  width: 542px;
  height: 44px;
  background-position: 0 -515px;
}

.jclist {
  position: relative;
  width: 750px;
  height: 172px;
  background: url(../img/jclist-bg.png) no-repeat center top;
  background-size: 750px auto;
  margin-top: 25px;
}

.jclist li {
  position: absolute;
  background: url(../img/jclist.png) no-repeat;
  text-indent: -9999em;
  width: 164px;
  height: 75px;
  cursor: pointer;
  overflow: hidden;
  background-size: 400px auto;
}

.jclist li.li0 {
  background-position: -25px 0;
  left: 49px;
  top: 3px;
}

.jclist li.li1 {
  background-position: -25px -100px;
  left: 212px;
  top: 3px;
}

.jclist li.li2 {
  background-position: -25px -200px;
  left: 375px;
  top: 3px;
}

.jclist li.li3 {
  background-position: -25px -300px;
  left: 537px;
  top: 3px;
}

.jclist li.li4 {
  background-position: -25px -400px;
  left: 129px;
  top: 100px;
}

.jclist li.li5 {
  background-position: -25px -500px;
  left: 291px;
  top: 100px;
}

.jclist li.li6 {
  background-position: -25px -600px;
  left: 454px;
  top: 100px;
}

.jclist li.li0.cur {
  background-position: -236px 0;
}

.jclist li.li1.cur {
  background-position: -236px -100px;
}

.jclist li.li2.cur {
  background-position: -236px -200px;
}

.jclist li.li3.cur {
  background-position: -236px -300px;
}

.jclist li.li4.cur {
  background-position: -236px -400px;
}

.jclist li.li5.cur {
  background-position: -236px -500px;
}

.jclist li.li6.cur {
  background-position: -236px -600px;
}

.distab {
  display: none;
}

.distab.dis {
  display: block;
}

.matchbk {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  padding-top: 4px;
}

.saiqubox {
  margin: 28px 30px 0;
  width: 279px;
  height: 415px;
  background: url(../img/saiqubox.png) center no-repeat;
  background-size: 100% 100%;
}

.saiqubox1 {
  background: url(../img/saiqubox1.png) no-repeat;
  background-size: 100% 100%;
}

.saiqubox2 {
  background: url(../img/saiqubox2.png) no-repeat;
  background-size: 100% 100%;
}

.saiqubox3 {
  background: url(../img/saiqubox3.png) no-repeat;
  background-size: 100% 100%;
}

.saiqubox4 {
  background: url(../img/saiqubox4.png) no-repeat;
  background-size: 100% 100%;
}

.saiqubox5 {
  background: url(../img/saiqubox5.png) no-repeat;
  background-size: 100% 100%;
}

.saiqubox6 {
  background: url(../img/saiqubox6.png) no-repeat;
  background-size: 100% 100%;
}

.saiqubox7 {
  background: url(../img/saiqubox7.png) no-repeat;
  background-size: 100% 100%;
}

.saiqubox ul {
  padding-top: 57px;
}

.saiqubox li,
.saiqubox li.yixuan::before,
.saiqubox li.win::after {
  background: url(../img/sqli.png) no-repeat;
  background-size: 300px auto;
}



.ttsai .saiqubox li,
.ttsai .saiqubox li.yixuan::before,
.ttsai .saiqubox li.win::after {
  background: url(../img/sqli2.png) no-repeat;
  background-size: 300px auto;
}
.saiqubox li {
  margin: 10px auto 0;
  color: #ddb689;
  font-size: 22px;
  text-align: center;
  width: 226px;
  height: 50px;
  padding: 3px 6px 0;
  line-height: 45px;
  cursor: pointer;
  position: relative;
  white-space: nowrap;
  text-overflow: ellipsis;
  box-sizing: border-box;
  background-position: 0 -60px;
}


.saiqubox li.yixuan{
  color: #fef398;
  background-position: -0 -120px;
}
.saiqubox li.yixuan::before {
  content: '';
  z-index: 2;
  width: 58px;
	height: 25px;
  position: absolute;
  right: 0;
  top: -5px;
  background-position:  -242px -63px;
}


.saiqubox li.win {
  color: #793a0a;
  background-position:0 0;
}

.saiqubox li.win::after {
  width: 58px;
  height: 25px;
  position: absolute;
  content: '';
  top: -5px;
  left: 0;
  background-position:-242px -8px;
  z-index: 3;
}



.matchbktop .saiqubox li.win::after {
  right: 14px;
}

.tjbtn {
  margin: 14px auto 0;
  width: 197px;
  height: 55px;
  background-position: 0 -161px;
}


.tjbtn.committed {
  background-position: 0 -250px;
}

.tip {
  text-align: center;
  color: #ffde98;
  font-size: 24px;
  line-height: 24px;
  margin-top: 42px;
}

.tip.mt {
  font-size: 22px;
  margin-top: 30px;
}

.tit.t4 {
  margin: 30px auto 0;
  width: 374px;
  height: 53px;
  background-position: 0 -668px;
}

.ttsai {
  width: 100%;
  padding-top: 24px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.ttsai.pt {
  padding-top: 0px;
}

.ttsai .saiqubox {
  background: none;
  width: 263px;
  height: auto;
  margin: 0 38px;
}

.ttsai .saiqubox ul {
  padding-top: 0;
  background: url(../img/ttsai.png) no-repeat center right;
  background-size: 60px auto;
}

.ttsai .saiqubox li {
  width: 215px;
  height: 48px;
  background-position: 0 -50px;
  margin: 9px 0 0 0;
}
.ttsai .saiqubox li.yixuan{
  background-position: 0 -100px;
}
.ttsai .saiqubox li.yixuan::before{
	width: 58px;
	height: 25px;
  background-position: -242px -63px;
}

.ttsai .saiqubox li.win{
  width: 218px;
  height: 56px;
  background-position: 0 0;
}
.ttsai .saiqubox li.win::after{
  width: 58px;
	height: 25px;
  background-position: -242px -7px;
}
.tit.t5 {
  margin-top: 40px;
  width: 374px;
  height: 53px;
  background-position: 0 -749px;
}

.tit.t5.mt {
  margin-top: 27px;
}

.jjjcbox {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  width: 100%;
  padding-top: 30px;
}

.jjjclist {
  margin: 0 9px 22px;
  width: 323px;
  height: 366px;
  background: url(../img/jjjclist.png) no-repeat;
  background-size: 100% 100%;
}

.bifen {
  color: #fff7cb;
  font-size: 70px;
  text-align: center;
  line-height: 95px;
  padding-top: 21px;
  padding-left: 2px;
  height: 95px;
}

.bifen span,
.bifen em {
  width: 68px;
  display: inline-block;
  vertical-align: top;
  font-weight: bold;
}

.bifen em {
  width: 75px;
}

.teamname {
  color: #d5ab7d;
  font-size: 18px;
  width: 270px;
  margin: 0 auto;
  height: 40px;
  display: flex;
  justify-content: space-between;
}

.teamname li {
  width: 126px;
  line-height: 40px;
  text-align: center;
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.selectbox {
  width: 219px;
  height: 31px;
  margin: 0 auto;
}

.selectbox select {
  width: 100%;
  height: 100%;
  background: #cf7b1c url(../img/sbg.png) no-repeat;
  background-size: 100% 100%;
  border: none;
  color: #8a1e23;
  font-size: 20px;
  padding-left: 5px;
}

* {
  -webkit-appearance: none;
  -webkit-text-size-adjust: none;
}

.tbnum {
  margin-top: 12px;
  height: 35px;
  text-align: center;
  color: #d5ab7d;
  font-size: 18px;
  padding-left: 3px;
  line-height: 35px;
}

.tbnum p,
.tbnum ul {
  display: inline-block;
  vertical-align: top;
}

.tbnum ul {
  height: 35px;
  text-align: center;
  font-size: 0;
}

.tbnum li {
  margin: 0 6px;
  width: 35px;
  height: 35px;
  display: inline-block;
  vertical-align: top;
  color: #d5ab7d;
  font-size: 16px;
  line-height: 35px;
  background: url(../img/tbnum.png) no-repeat;
  cursor: pointer;
}

.tbnum li.cur {
  color: #d5ab7d;
  background-position: top right;
}

.jcbtn {
  margin: 21px auto 0;
  width: 197px;
	height: 55px;
  background-position: 0 -77px;
}

.jcbtn.committed {
  background-position:  right -161px;
}

.tit.t4.mt {
  margin-top: 34px;
  margin-bottom: 20px;
}
.tit.t7{
  margin: 30px auto 0;
  width: 374px;
  height: 53px;
  background-position:0 -587px;
}
.finalbox{
  margin-top: 42px;
  width: 750px;
  height: 288px;
}
.finalbox .selectul{
  width: 100%;
  height: 142px;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  flex-wrap: wrap;
}
.finalbox .selectul li{
  z-index: 2;
  position: relative;
  width: 236px;
  height: 142px;
  background: url("../img/finalcom.png") no-repeat center top;
  background-size: 500px auto;
}

.finalbox .selectul .li0{
  background-position: 0 0;
}
.finalbox .selectul .li1{
  background-position: right 0;
}
.finalbox .selectul .li2{
  background-position: left -200px;
}

.finalbox .selectul li.succeed::before{
  content: '';
  position: absolute;
  top: 10px;
  left: 0;
  width: 58px;
  height: 25px;
  background: url(../img/finalcom.png) no-repeat;
  background-size: 500px auto;
  background-position: 0 -400px;
}

.finalbox .selectul li.fail::before{
  content: '';
  position: absolute;
  top: 10px;
  left: 0;
  width: 58px;
  height: 25px;
  background: url(../img/finalcom.png) no-repeat;
  background-size: 500px auto;
  background-position: 0 -443px;
  z-index: 2;
}
.finalbox .selectul li.fail::after{
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 236px;
  height: 142px;
  background: url(../img/finalcom.png) no-repeat;
  background-size: 500px auto;
  background-position: -264px -200px;
}


.selectul li select{
  display: block;
  width: 198px;
  height: 35px;
  border: none;
  color: #6d2b00;
  font-size: 20px;
  padding-left: 10px;
  margin: 80px auto 0;
  line-height: 35px;
}
.finalbox .selectul .li0 select{
  background:#d2a564 url(../img/finalbox.png) no-repeat;
  color: #c58132;
}
.finalbox .selectul .li1 select{
  background:#d2a564 url(../img/finalbox1.png) no-repeat;
  color: #9f1d22;
}
.finalbox .selectul .li2 select{
  background:#d2a564 url(../img/finalbox2.png) no-repeat;
  color: #5c89bb;
}

.jsjcbtn{
  transition: filter 0.5s linear;
  margin: 50px auto 0;
  width: 197px;
  height: 55px;
  background: url(../img/jsjcbtn.png) no-repeat;
  background-size: 100% 100%;
  display: block;
  text-indent: -9999em;
  overflow: hidden;
}
.jsjcbtn.committed{
  background: url(../img/ytj.png) no-repeat;
}
.matchbktop {
  padding-top: 34px;
}

.matchbktop .saiqubox {
  background: url(../img/saiquboxgj.png) no-repeat;
  width: 308px;
  height: 339px;
  margin: 0 16px;
}

.matchbktop .saiqubox ul {
  padding-top: 91px;
}

.matchbktop .saiqubox li,
.matchbktop .saiqubox li.yixuan::before {
  background: url(../img/sqligj.png?v=3) no-repeat;
}

.matchbktop .saiqubox.saiquboxyj li,
.matchbktop .saiqubox.saiquboxyj li.yixuan::before {
  background: url(../img/sqliyj.png?v=2) no-repeat;
  width: 209px;
  height: 60px;
  padding-top: 0;
  line-height: 60px;
}

.matchbktop .saiqubox li {
  margin-top: 11px;
  color: #ffe2a5;
  width: 209px;
  height: 60px;
  padding-top: 0;
  line-height: 60px;
}

.matchbktop .saiqubox li.yixuan::before,
.matchbktop .saiqubox.saiquboxyj li.yixuan::before {
  background-position: 0 -70px;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.matchbktop .saiquboxyj {
  background: url(../img/saiquboxyj.png?v001) no-repeat;
}

.matchbktop .saiqubox.saiquboxyj li {
  color: #ddefff;
}

.jjjcboxbot .jjjclist {
  margin: 0 15px;
}
.fz_nav_box{
  width: 588px;
  display: flex;
  justify-content: space-between;
  margin: 37px auto 0;
}
.fz_nav{
  width: 282px;
  height: 64px;
  background: url(../img/fz_nav.png) no-repeat;
  background-size: 600px auto;
  display: block;
  text-indent: -9999em;
  overflow: hidden;
}
.win_nav{
  background-position: 0 0;
}
.lose_nav{
  background-position: -300px 0;
}
.win_nav.on{
  background-position: 0 -100px;
}
.lose_nav.on{
  background-position: -300px -100px;
}
.fz_box{
  display: none;
}
.show{
  display: block;
}
.matchbktop .saiqubox li.win {
  background-position: 0 -170px;
  color: #5f4519;
}

.matchbktop .saiqubox.saiquboxyj li.win {
  color: #5f4519;
  background-position: 0 -170px;
}

.tit.t6 {
  background-position:0 -300px;
}

.rulebox {
  margin: 27px auto 0;
  width: 676px;
  height: 1510px;
  background: url(../img/rulebox.png) no-repeat;
  padding-top: 42px;
  background-size: 100% 100%;
  text-align: left;
}

.stit {
  margin-left: 36px;
  color: #fff7e5;
  height: 39px;
  line-height: 39px;
  text-align: center;
  letter-spacing: 1px;
  background: url(../img/ruletit.png) no-repeat;
  display: inline-block;
  vertical-align: top;
  position: relative;
  font-size: 22px;
  background-size: 364px auto;
}
.stit.stit0{
  width: 244px;
  background-position: -10px -69px;
}
.stit.stit1{
  width: 344px;
  background-position: -10px -10px;
}
.stit.stit2{
  width: 184px;
  background-position: -10px -128px;
}
.rulebox ul {
  color: #844610;
  font-size: 20px;
  line-height: 30px;
  padding: 11px 48px 21px;
}

.rulebox span {
  color: #c94006;
}

.rulebox li::before {
  display: inline-block;
  content: '';
  margin: 0 4px 0 1px;
  width: 12px;
  height: 30px;
  vertical-align: top;
  background: url(/act/xtl/guess/20240716/pc/img/dotbg.png) no-repeat center center;
  background-size: 100% auto;
}

.rulebox p {
  color: #844610;
  padding: 5px 30px 10px;
  text-indent: 17px;
}

.rulebox p span {
  color: #c94006;
}

#cy_bot {
  display: none;
}

/*pop*/
.logintype p,
.pop,
.tablebox th,
.tpage {
  font-size: 24px !important;
}

.tablebox td {
  font-size: 22px !important;
}

/* .pop2 .popcont {
  padding-top: 96px !important;
} */

.pop_login .logintype li {
  width: 249px;
  margin: 0;
}
.pop_login .logintype2 li {
  width: 170px;
}

/*nav*/
.navbtn1 {
  display: none;
}

.flobox {
  z-index: 12;
  position: fixed;
  left: 0;
  bottom: 0;
  width: 750px;
  height: 116px;
  background: url(../img/bot_nav.png) no-repeat;
  background-size: 750px auto;
}

.flobox .openbtn {
  display: none;
}

.s_nav,
.snavbox {
  width: 100%;
  height: 100%;  
}
.snavbox a{
    width: 150px;
    height: 116px;
    text-indent: -999em;
}
.snavbox .snav0 {
  position: absolute;
  left: 50%;
  margin-left: -75px;
  background-position:-300px 0;
}

.snavbox .snav1,
.snavbox .snav2,
.snavbox .snav3,
.snavbox .snav4 {
  position: relative;
  height: 102px;
  width: 150px;
  float: left;
  text-indent: -9999em;
  z-index: 1;
  margin-top: 11px;
}
.snavbox .snav3 {
  margin-left: 150px;
}

.pg1 .snavbox a.snav1 {
  background: url(../img/bot_nav.png) no-repeat;
  background-size: 750px auto;
  background-position: -300px -135px;
}
.pg2 .snavbox a.snav1 {
  background: url(../img/bot_nav.png) no-repeat;
  background-size: 750px auto;
  background-position: 0 -135px;
}

.pg3 .snavbox a.snav2 {
  background: url(../img/bot_nav.png) no-repeat;
  background-size: 750px auto;
  background-position: -150px -135px;
}

.pg4 .snavbox a.snav3 {
  background: url(../img/bot_nav.png) no-repeat;
  background-size: 750px auto;
  background-position: -450px -135px;
}

.pg5 .snavbox a.snav4 {
  background: url(../img/bot_nav.png) no-repeat;
  background-size: 750px auto;
  background-position: -600px -135px;
}