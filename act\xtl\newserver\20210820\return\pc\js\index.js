// 奖品信息 (抽奖的顺序就是数组的index)
var prizeList = [
    { img: '1.jpg', txt: '3级秘银', prizeCode: 'Q1', lottIdx: 9 },
    { img: '3.jpg', txt: '虹耀石', prizeCode: 'Q2', lottIdx: 7 },
    { img: '5.jpg', txt: '蓝色心决：坐啸', prizeCode: 'Q3', lottIdx: 5 },
    { img: '7.jpg', txt: '玄灵丹', prizeCode: 'Q4', lottIdx: 3 },
    { img: '9.png', txt: '500元京东购物卡', prizeCode: 'Q5', lottIdx: 1 },
    { img: '8.jpg', txt: '坐骑:天马（30天）', prizeCode: 'Q6', lottIdx: 2 },
    { img: '6.jpg', txt: '时装:藤花正晓晴（永久, 拾取绑定）', prizeCode: 'Q7', lottIdx: 4 },
    { img: '4.jpg', txt: '金蚕丝x5', prizeCode: 'Q8', lottIdx: 6 },
    { img: '2.jpg', txt: '3级棉布', prizeCode: 'Q9', lottIdx: 8 },
    { img: '10.png', txt: 'switch', prizeCode: 'Q10', lottIdx: 11 }
];
//计算字符串长度
String.prototype.strLen = function () {
    var len = 0;
    for (var i = 0; i < this.length; i++) {
        if (this.charCodeAt(i) > 255 || this.charCodeAt(i) < 0) len += 2; else len++;
    }
    return len;
}
//将字符串拆成字符，并存到数组中
String.prototype.strToChars = function () {
    var chars = new Array();
    for (var i = 0; i < this.length; i++) {
        chars[i] = [this.substr(i, 1), this.isCHS(i)];
    }
    String.prototype.charsArray = chars;
    return chars;
}
//判断某个字符是否是汉字
String.prototype.isCHS = function (i) {
    if (this.charCodeAt(i) > 255 || this.charCodeAt(i) < 0)
        return true;
    else
        return false;
}
//截取字符串（从start字节到end字节）
String.prototype.subCHString = function (start, end) {
    var len = 0;
    var str = "";
    this.strToChars();
    for (var i = 0; i < this.length; i++) {
        if (this.charsArray[i][1])
            len += 2;
        else
            len++;
        if (end < len)
            return str;
        else if (start < len)
            str += this.charsArray[i][0];
    }
    return str;
}
//限制输入
$("input[contact][maxlength]").bind("input propertychange", function () {
    var len = $(this).attr("maxlength");
    var val = $(this).val().toString();
    if ($(this).attr("num")) {//如果限制数字，删除非数字
        $(this).val(val.replace(/[^\d]/g, ''));
    }
    var len = $(this).attr("maxlength");
    var val = $(this).val().toString();
    if (val.length > len) {
        $(this).val(val.substr(0, len));

    }
});

//游戏意向选择
$('.yxyix li').on('click', function () {
    if ($(this).hasClass('yxz')) {
        $(this).removeClass('yxz');
    } else {
        if ($(this).siblings('.yxz').length > 2) {
            alert('最多选择三个!');
        } else {
            $(this).addClass('yxz');
        }
    };
});
//联系方式选择
$("#contactType").change(function () {
    if ($(this).val() != 3) {
        $("#creatGroup .onlinew1 input[contact]").hide();
        $("#contactValue_" + $(this).val()).show();
    } else {
        $("#creatGroup .onlinew1 input[contact]").hide();
    }
})
//联系方式选择2
$("#contactType2").change(function () {
    if ($(this).val() != 3) {
        $("#aplayTeam .onlinew1 input[contact]").hide();
        $("#contactValue2_" + $(this).val()).show();
    } else {
        $("#aplayTeam .onlinew1 input[contact]").hide();
    }
})
var sign = 3,
    commonPath = '/xtl/newserver/20210820/common/',
    activity = "backFlowG";
if (location.href.indexOf('longmen') != -1) {
    var version = "2021082001"
    var shareUrl = 'public/longmen'
} else {
    var version = "20210820"
    var shareUrl = 'public'
}

var localJson = '';
$.ajax({
    url: commonPath + 'share.json',
    async: false,
    success: function (res) {
        localJson = res;
    }
})
$('.backbtn').on('click', function () {
    $('html,body').animate({
        scrollTop: 0
    }, 'slow');
});
$(function () {
    var $lottStart = $('#lottStart');
    var re = {
        isLongmen: location.href.indexOf('/longmen/') !== -1,
		isMobile: ja.glob.isMobile,
        base_url: '/changyou/xtl/backFlowG',
        bRotate: true,
        loginFlag: false,
        banFlag: false, // 封停状态标识
        clickFlag: false,
        getDrawStatus: true,
        isWechat: ja.glob.isWechat,
        members: Number, // 队伍成员数量
        shareTeamCode: '', // 分享页队伍编号
        navLink: ['../../appoint/pc/index.shtml', 'javascript:void(0);', '../../peak/pc/index.shtml',
            '../../cover/pc/index.shtml'
        ],
        shareParam: {
            indexLink: ja.utils.url_add_key(location.origin + '/xtl/newserver/20210820/' + shareUrl + '/return/m/index.shtml', "CYSTID", "share"),
            indexTitle: localJson.return.title,
            indexDesc: localJson.return.desc,
            indexImgUrl: localJson.return.imgUrl,
            shareTitle: localJson.share.title,
            shareDesc: localJson.share.desc,
            shareImgUrl: localJson.share.imgUrl
        },
        /**
         * 是否已经在队伍  判断是否可以创建队伍 加入队伍 领取组队礼等
         */
        teamFlag: false,//是否加入队伍了
        myLotCount: 0, //抽奖次数
        isClick: true,
        logId: '',
        oldtime: '',//初始化时间
        purpose: ["外观截图党", "一条龙副本", "卡级", "生活", "打架"],
        init: function () {
            ja.config({
                app: "xtl",
                activity: activity,
                version: version,
                platform: "changyou",
                isWxLogin: true,
                isWxInit: true,
                type: 1,
                ready: function (glob) {
                    glob.code === 1 ? re.loginAfter() : re.loginBefore();
                    setTimeout(function () {
                        re.randomTeam()
                    }, 300)

                    // re.filNavLink()

                    re.isWechat && re.isWxBind();

                    ja.wx.init(function () {
                        ja.wx.share({
                            link: re.shareParam.indexLink,
                            title: re.shareParam.indexTitle,
                            desc: re.shareParam.indexDesc,
                            imgUrl: re.shareParam.indexImgUrl,
                        })
                        if (location.pathname.indexOf('share') != -1) {
                            re.sharePageConfig();
                            $('#backHome').on('click', function () {
                                location.href = re.shareParam.indexLink
                            })
                        }
                    })
                }
            });
            if (re.isLongmen) {
                if (location.pathname.indexOf('share') != -1) {
                    ja.utils.longmen({
                        color: 'white',
                        top: 400,
                        left: 40
                    });
                } else {
                    ja.utils.longmen({
                        color: 'white',
                        top: re.isMobile ? 100 : 170,
                        left: re.isMobile ? 40 : 60
                    });
                }
                alert('亲爱的玩家您好，本次活动为测试，奖励仅在测试服可用，感谢您的支持！');
            }

            $('#login').on('click', function () {
                if (ja.glob.code == 0) {
                    re.tips('网络繁忙，请稍后再试')
                    return
                } else if (ja.glob.code == 1) {
                    re.loginAfter()
                    return
                } else if (ja.glob.code == 2) {
                    re.isWechat ? popup('#pop-loginBox') : ja.login()
                    return
                } else if (ja.glob.code == 1106) {
                    re.tips('活动未开始')
                    return
                } else if (ja.glob.code == 1103) {
                    re.tips('活动已结束')
                    return
                } else if (ja.glob.code == 1102) {
                    re.tips('活动无效');
                    return
                }
            })

            $('#plaLogin').on('click', function () {
                ja.login()
            })

            $('#logout').on('click', function () {
                ja.logout()
            })



            if (re.isWechat) {
                $('.wx_share').text('一键分享');
                $('#teamShare').on('click', function () {
                    re.wxShareConfig($('#teamCode').val());
                })
            } else if (re.IsPC()) {
                $('#teamShare').on('click', function () {
                    var result = clipBordCopy($('#teamCode').val());
                    alert('复制成功，快去粘贴发送给好友吧');
                })
            } else {
                $('#teamShare').on('click', function () {
                    var result = clipBordCopy(ja.utils.url_add_key('http://' + location.host + '/xtl/newserver/20210820/' + (
                        shareUrl || 'public') + '/return/m/share.shtml?teamCode=' + $('#teamCode').val() + '', "CYSTID", "share"));
                    alert('复制成功，快去粘贴发送给好友吧');
                })
            }

            //一键领取回归礼包
            $('#getBackReward').on('click', function () {
                if (!re.loginFlag) {
                    $('#login').click();
                    return;
                } else if (re.banFlag) {
                    popup('#forbidden');
                    return;
                }
                re.backReward()
            })

            // 创建队伍选时间
            $('#creatBtn').on('click', function () {
                if (re.commHandle()) return;
                popup('#creatGroup')
            })
            var list = [];
            $("#creatGroup .yxyix li.yxz").each(function () {
                list.push($(this).index() + 1);
            })
            // 创建队伍提交
            $('#creatTeam').on('click', function () {
                if ($('#creatStartTime').val() == '' || $('#creatEndTime').val() == '') {
                    re.tips('请选择您的在线时间')
                    return
                }
                var obj = re.getContact();
                if (obj) {
                    re.createTeam(obj)
                }
            })

            // 加入小队弹窗
            $('#joinBtn').on('click', function () {
                if (re.commHandle()) {
                    return;
                }
                popup('#joinTeam')
            })

            // 加入小队按钮
            $('#joinTeamBtn').on('click', function () {
                if (!$('#teamCodeInput').val().length) {
                    alert('请输入小队验证码！')
                    return
                }
                if ($('#teamCodeInput').val().length > 9) {
                    alert('小队验证码错误')
                    return
                }
                var obj = {
                    verifyCode: $('#teamCodeInput').val(),
                    device: re.IsPC() ? 'PC' : 'M'
                }
                re.goInTroops(obj)
            })

            // 一键领取奖励
            $('.fl-list>li .lqbtn').on('click', function () {
                if (!re.loginFlag) {
                    $('#login').click();
                    return;
                } else if (re.banFlag) {
                    popup('#forbidden')
                    return;
                } else if ($(this).hasClass("gray")) {
                    $('#pop-get-fail p').text('您已经领取过组队礼包了');
                    popup('#pop-get-fail')
                    return
                }
                var level = [50, 70, 90];
                var ind = $(this).parent("li").index();
                re.troopsReWard(level[ind]);
            })

            // 队伍编号查询
            $('#search').on('click', function () {
                if (!$('#TeamNum').val()) {
                    return;
                } else if ($('#TeamNum').val() > 4294967296) {
                    re.tips('队伍编号错误，请检查后再试')
                    return
                }

                var obj = {
                    troopsSn: $('#TeamNum').val()
                }
                re.search(obj)
            })

            // 我的申请
            $('#myApply').on('click', function () {
                if (re.commHandle()) {
                    return;
                }
                re.getApplyingList()
            })

            // 待审核信息
            $('#applyInfo').on('click', function () {
                if (!re.loginFlag) {
                    $('#login').click();
                    return;
                } else if (re.banFlag) {
                    popup('#forbidden')
                    return;
                }
                re.getAwaitApplyList()
            })

            //换一批
            $('.btn-exchange').click(function () {
                if (!re.clickFlag) {
                    re.randomTeam();
                }
            })

            //列表申请入队
            $('#teamList').on('click', '#applyBtn', function () {
                if (re.commHandle()) {
                    return;
                }
                $("#creatTeam2").get(0).obj = $(this);
                popup($('#aplayTeam'));
            })
            $("#creatTeam2").click(function () {
                var list = [];
                $("#aplayTeam .yxyix li.yxz").each(function () {
                    list.push($(this).index() + 1);
                })
                var apl_bty = $("#creatTeam2").get(0).obj;
                var obj = re.getContact2();
                if (obj) {
                    obj.troopsSn = apl_bty.data('sn');
                    re.applyTeam(obj, apl_bty);
                }
            })
            // 查看详情
            $('#applyInfoBox').on('click', '.icon-select', function () {
                var obj = {
                    id: $(this).data('id'),
                    applyCn: $(this).data('applycn'),
                    roleName: $(this).data('rolename'),
                    purpose: $(this).data('purpose'),
                    status: '1'
                }
                var area, serv, role;
                area = serv = role = '无'
                if (obj.roleName && obj.roleName != 'no' && obj.roleName.split('_').length > 1) {
                    area = obj.roleName.split('_')[0] || '';
                    serv = obj.roleName.split('_')[1] || '';
                    role = obj.roleName.split('_')[2] || '';
                }
                $('.pop-bg6 .applylist li span').eq(0).text(obj.applyCn);
                $('.pop-bg6 .applylist li span').eq(1).text(area)
                $('.pop-bg6 .applylist li span').eq(2).text(serv)
                $('.pop-bg6 .applylist li span').eq(3).text(role)
                $('.pop-bg6 .applylist li span').eq(4).text(re.getPurpose(obj.purpose))
                $(".pop-bg6 .refer-btn").data("id", obj.id)
                popup($(".pop-bg6"))
                //re.examine(obj)
            })
            //同意申请
            $(".pop-bg6").on('click', '.refer-btn', function () {
                var obj = {
                    id: $(this).data('id'),
                    status: '1'
                }
                re.examine(obj);
            })
            // 一键清空
            $('#clear').on('click', function () {
                re.expireAll()
            })

            // 个人中心
            $("#centerBtn").on('click', function () {
                if (re.banFlag) {
                    popup('#forbidden');
                    return;
                }
                re.getComponent(commonPath + 'myCenter/', function () {
                    setTimeout(function () {
                        popup('#pop-mycenter');
                    }, 100)
                });
            });
            // 抽奖
            // re.rota('#rotate01', '#lottStart');
            // var lottObj = new re.LottOBJ({
            //     obj: $('#lottcon'),
            //     cover: $('#lottcon .lott-cover'),
            //     count: 9,
            //     position: ja.glob.isMobile ? '45_108,539_0,257_0,398_0,609_80,468_108,327_104,186_119,116_0' : '122_83,862_15,439_15,651_15,968_55,757_83,545_79,334_94,228_15',
            //     complete: function () {
            //         //抽奖完成
            //         popup($('#dialogGetPrize'));
            //         re.myLotCnt(); //回显抽奖次数
            //         re.getDrawStatus = true;
            //     }
            // });
            // window.lottObj = lottObj
            // 抽奖
            var lottObj = function(num){
                // num = 抽奖序号
                re.LottOBJ(num, {
                    wrap: $('#rotate01'),
                    count: 12,//奖品数量
                    complete: function (e) {
                        //抽奖完成
                        popup($('#dialogGetPrize'));
                        re.myLotCnt(); //回显抽奖次数
                        re.getDrawStatus = true;
                    }
                });
            }
            // 开始抽奖 参数为抽中的序号
            $lottStart.click(function () {
                if (!re.loginFlag) {
                    $('#login').click();
                    return;
                }
                if (re.myLotCount < 1) return; //没有抽奖次数不能交换
                if (re.isClick && re.getDrawStatus) {
                    re.isClick = re.getDrawStatus = false;
                    ja.ajax({
                        url: re.base_url + '/lot',
                        type: 'post',
                        data: {},
                        success: function (res) {
                            re.isClick = true;
                            if (res.code == 10000 && res.data) {
                                var data = res.data;
                                if (data.prizeType == 'Q' || data.prizeType == 'N') { //虚拟奖品
                                    $('#dialogGetPrize .pop-sm').html('所有道具奖励仅发放至新服，请在新服开启<br>后前前往<i class="red">大理（172，122）龚彩云</i><br>处领取。')
                                    $('#dialogGetPrize .fillAddress')[0].style.display = 'none'
                                } else if (data.prizeType == 'R') { //实物奖 （可以填写收货地址）
                                    if (data.addressStatus == 0) {
                                        $('#dialogGetPrize .fillAddress')[0].style.display = 'block'
                                    }
                                    re.logId = data.logId;
                                    $('#dialogGetPrize .pop-sm').html('请填写收货地址并保证手机畅通哦~')
                                }
                                var order = 2; // 虹耀石
                                for (var i = 0; i < 10; i++) {
                                    if (prizeList[i].prizeCode == data.prizeCode) {
                                        $('#priceName').html(prizeList[i].txt); //奖品名称
                                        $('#priceIcon').attr('src', '/act/xtl/newserver/20210820/return/pc/img/prizeIcon/' + (prizeList[i].img)) //奖品icon
                                        order = prizeList[i].lottIdx;
                                    }
                                }
                                lottObj(order);
                            } else if (res.code == 2007) {
                                re.tips('没有抽奖资格')
                                re.getDrawStatus = true;
                            } else if (re.commonErrPop(res)) {
                                re.getDrawStatus = true;
                                alert(res.message)
                            }
                        },
                        error: function (res) {
                            re.isClick = true;
                            alert('网络异常')
                        }
                    })
                }
            })

            // 填写收货地址
            $('.btn_addr_submit').click(function () {
                var addressDialog = $(this).parent().attr('id'); // PopAddr 抽奖之后的弹出的填写地址 PopAddrCenter个人中心的填写地址
                var realName = $('#'+addressDialog+' .input_addr_people').val().replace(/\s*/g, ""),
                    phone = $('#'+addressDialog+' .input_addr_phone').val(),
                    address = $('#'+addressDialog+' .input_addr_dec').val().replace(/\s*/g, "");
                if (realName == '') {
                    $('#pop-address-err p').html('还未填写收货人信息呢~ <br />包裹无法准确到达您的手中呢！');
                    popup('#pop-address-err');
                } else if (!re.checkIsChEnNum(realName)) {
                    $('#pop-address-err p').html('请输入正确的姓名！');
                    popup('#pop-address-err');
                } else if (!phone) {
                    $('#pop-address-err p').html('还未填写您的联系方式哦~ <br />包裹无法准确到达您的手中呢！');
                    popup('#pop-address-err');
                } else if (!re.checkPhone(phone)) {
                    $('#pop-address-err p').html('请输入正确的手机号码！');
                    popup('#pop-address-err');
                } else if (!address) {
                    $('#pop-address-err p').html('还未填写收货地址哦~ <br />包裹无法准确到达您的手中呢！')
                    popup('#pop-address-err');
                } else if (!re.checkIsChEnNum(address)) {
                    $('#pop-address-err p').html('请输入正确的地址！');
                    popup('#pop-address-err');
                } else {
                    var obj = {
                        rname: realName,
                        phone: phone,
                        address: address,
                        logId: re.logId
                    }
                    return console.log(re.logId);
                    if (re.isClick && re.preventclick(1000)) {
                        re.saveAddr(obj)
                    }
                }
            })

            // 提交失败 展示收货地址
            $('.pop-address .close').click(function () {
                hideMask();
                setTimeout(function () {
                    popup('#PopAddr')
                })

            })
        },
        getPurpose: function (data) {
            var purpose = '';
            var list;
            if (data && data != 'undefined' && data != 'no') {
                list = data.toString().split("_");
                $(list).each(function (ind) {
                    var tmp = '';
                    if (ind != 0) {
                        tmp = '_'
                    }
                    purpose = purpose + tmp + re.purpose[this - 1]
                })
                // m端 队伍展示卡片游戏意向显示优化备选
                // ja.glob.isMobile && (purpose = '<span style="display: block; text-align: center;">' + purpose + '</span>');
            } else {
                purpose = '无';
            }

            return purpose;
        },
        loginAfter: function () {
            $('.login_box').hide()
            $('.user_name').text(ja.glob.userInfo.openid)
            $('.logout_box').show()
            re.loginFlag = true;
            $('#applyInfo').hide()
            re.myLotCnt() //我的抽奖次数
            re.rewardLog() // 根据记录判读是否已领取过组队礼 禁用领取按钮
            re.isInTeam() // 看下在不在队伍 是不是队长 判断 申请信息按钮得状态
            re.troopsMember() // 看下队伍成员数量  判断可以审批的人数
            re.roleList();//加载角色
        },
        loginBefore: function () {
            // nothing
            re.loginFlag = false;
        },
        /**
         *  填充导航的链接
         */
        filNavLink: function () {
            $($('#nav').children()).each(function (i, v) {
                if (re.navLink[i].indexOf('java') == -1) {
                    if (!ja.glob.isMobile) {
                        v.target = '_blank';
                    }
                }
                v.href = re.navLink[i];
            })
        },
        /**
         * 查询微信是否绑定
         */
        isWxBind: function () {
            function autoLogin() {
                ja.user.wxAutoLogin(function (res) {
                    if (res.code === 10000) {
                        ja.user.getUserInfo(function (res) {
                            if (res.code === 10000) {
                                re.loginAfter();
                                hideMask('.pop')
                            }
                        })
                    } else {
                        alert(res.message);
                    }
                })
            }

            function jumpUrl() {
                location.href = ja.glob.urlWxBind
                // ja.href(ja.glob.urlWxBind);
            }

            ja.user.wxIsBind(function (res) {
                if (res.code === 10000 && res.data) {
                    $('#unBind').hide();
                    $('#isBind').html(res.data + '<br />已绑定').show();
                    $('#isBind').click(autoLogin);
                    $('#isBind').parent().click(autoLogin);
                } else {
                    $('.wxicon').show();
                    $('#unBind,.wxicon').click(jumpUrl);
                }
            })
        },
        /**
         * 微信分享设置
         * @param {*} a 组队码取值
         */
        wxShareConfig: function (a) {
            hideMask($('.pop'))
            addMaskLayer('#000', 0.8);
            var img = new Image();
            img.className = 'share_img';
            img.src = '/act/xtl/newserver/20210820/return/m/img/sharetips.png';
            img.style.cssText = "position: fixed;top: 10px;right: 0; z-index: 999;"
            $('body').append(img);
            $(document).on('touchstart', '#Overlay', function () {
                removeMaskLayer();
                $('body > img[style]').remove()
            })
            ja.wx.share({
                link: ja.utils.url_add_key('http://' + location.host + '/xtl/newserver/20210820/' + (shareUrl || 'public') +'/return/m/share.shtml?teamCode=' + a + '', "CYSTID", "share"),
                title: re.shareParam.shareTitle,
                desc: re.shareParam.shareDesc,
                imgUrl: re.shareParam.shareImgUrl,
                success: function () {
                    ja.wx.share({
                        link: re.shareParam.indexLink,
                        title: re.shareParam.indexTitle,
                        desc: re.shareParam.indexDesc,
                        imgUrl: re.shareParam.indexImgUrl,
                    })
                    removeMaskLayer();
                    $('.share_img').fadeOut().remove();
                }
            })
        },
        sharePageConfig: function () {
            if (ja.glob.urlSearch) {
                var query = ja.glob.urlSearch.substring(1);
                var vars = query.split("&");
                for (var i = 0; i < vars.length; i++) {
                    var pair = vars[i].split("=");
                    if (pair[0] == 'teamCode') {
                        re.shareTeamCode = pair[1];
                    }
                }
                $('#teamCodeInput').val(re.shareTeamCode);
            }
            ja.wx.share({
                link: ja.utils.url_add_key('http://' + location.host + '/xtl/newserver/20210820/' + (shareUrl || 'public') +
                    '/return/m/share.shtml?teamCode=' + re.shareTeamCode + '', "CYSTID", "share"),
                title: re.shareParam.shareTitle,
                desc: re.shareParam.shareDesc,
                imgUrl: re.shareParam.shareImgUrl,
                success: function () {

                }
            })
        },
        /**
         * 通用错误码处理
         * @param {*} data 返回的对象
         */
        commonErrPop: function (data) {
            var flag = false;
            re.clickFlag = false;
            if (data.code == '1202' || data.code == '1209' || data.code == '1207' || data.code == '1007') { //1202请求参数缺失或者为空  1209操作频繁
                re.tips("系统繁忙，请稍后重试！");
                return;
            } else if (data.code == '1019' || data.code == '1012') { //登录状态检测失败
                ja.glob.code = 2;
                //ja.login();
                $("#login").click();
                return;
            } else if (data.code == '3103') { //账号封停
                popup('#forbidden')
                re.banFlag = true;
                return;
            } else if (data.code == '5050') { //已在队伍
                popup('#pop-creat-fail');
                re.teamFlag = true
                return;
            } else if (data.code == '5057') { //已申请过
                $('#pop-join-fail p').html('您已经申请过这支小队了').addClass('pop-txt1');
                popup('#pop-join-fail')
                return;
            } else if (data.code == '5052') { // 队伍已满
                $('#pop-join-fail p').html('您所申请的小队已满员，请换个小队试试吧！').addClass('pop-txt1');
                popup('#pop-join-fail')
                return;
            } else if (data.code == '5051') { // 申请达到上限
                $('#pop-join-fail p').html('您已经申请了3个小队，请等等队长的回复吧！若队长始终未回复，2日后您可重新申请其它小队。').addClass('pop-txt1');
                popup('#pop-join-fail')
                return;
            } else if (data.code == '5056') { //
                $('#pop-join-fail p').html('此队伍等待审核数量已达到上限，请您换一支队伍再试').addClass('pop-txt1');
                popup('#pop-join-fail')
                return;
            } else if (data.code == '5053') { //认证手机或身份相同
                $('#pop-join-fail p').html('您已经有相同身份证或手机号的账号在小队里了，<br>快去邀请其他少侠一起组队吧！')
                popup('#pop-join-fail')
                return;
            } else if (data.code == '1104') { //入队阶段未开始
                re.tips('组队阶段未开始')
                return;
            } else if (data.code == '1105') { //入队阶段一结束
                re.tips('组队阶段已结束')
                return;
            } else {
                flag = true;
            }
            return flag;
        },
        /**
         * 通用的处理方法  处理未登录与封停账号
         */
        commHandle: function () {
            var flag = true;
            if (ja.glob.code == 0) {
                re.tips('网络繁忙，请稍后再试')
                return flag;
            } else if (ja.glob.code == 1106) {
                re.tips('活动未开始')
                return flag;
            } else if (ja.glob.code == 1103) {
                re.tips('活动已结束')
                return flag;
            } else if (ja.glob.code == 1102) {
                re.tips('活动无效')
                return flag;
            } else if (!re.loginFlag) {
                $('#login').click();
                return flag;
            } else if (re.banFlag) {
                popup('#forbidden')
                return flag;
            } else if (re.teamFlag) {
                re.tips('您的账号已经绑定过小队，<br/>请前往个人中心查看组队情况')
                return flag;
            } else {
                flag = false;
                return flag;
            }
        },
        /**
         * 通用提示
         */
        tips: function (msg) {
            $('#tips p').html(msg).addClass('pop-txt1')
            popup('#tips');
        },
        /**
         * 判断设备
         */
        IsPC: function () {
            var userAgentInfo = navigator.userAgent;
            var Agents = new Array("Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod");
            var flag = true;
            for (var v = 0; v < Agents.length; v++) {
                if (userAgentInfo.indexOf(Agents[v]) > 0) {
                    flag = false;
                    break;
                }
            }
            return flag;
        },
        /**
         * 配置角色
         */
        setRole: function (data) {
            var tmp = '';
            if (data.length > 0) {
                for (var i = 0; i < data.length; i++) {
                    tmp += '<option value="' + data[i].roleId + '">' + data[i].serverNme + '_' + data[i].roleName + '</option>';
                }
            } else {
                tmp = '<option value="no">我是新玩家</option>';
            }
            $("#serverName").html(tmp);
            $("#serverName2").html(tmp);
        },
        //组队的时候
        getContact: function () {
            var message = '';
            var sel = $("#contactType option:selected");
            var contact = $.trim($("#contactValue_" + sel.val()).val())
            if (contact) {
                message += sel.html() + "_" + contact;
            } else {
                message = 'no';
            }
            if (sel.val() == 3) {
                message = '我不想填写联系方式';
            }

            var list = [];
            $("#creatGroup .yxyix li.yxz").each(function () {
                if ($(this).index() > 1) {
                    list.push($(this).index());
                } else {
                    list.push($(this).index() + 1);
                }
            })
            //校验
            if (list.length == 0) {
                alert('您未填写游戏意向，请先填写游戏意向方便志同道合的少侠一起勇闯新服吧~')
                return;
            }
            var wxreg = /^[a-zA-Z]{1}[-_a-zA-Z0-9]{5,19}$/
            var qq = /^[1-9]\d{4,10}$/
            if (sel.val() == 0) {
                if (!qq.test(contact)) {
                    alert("请填写正确QQ号")
                    return;
                }
            } else if (sel.val() == 1) {
                if (!wxreg.test(contact) && !re.checkPhone(contact)) {//微信账号允许输入手机号
                    alert("请填写正确微信号")
                    return;
                }
            } else if (sel.val() == 2) {
                if (!re.checkPhone(contact)) {
                    alert("请填写正确手机号")
                    return;
                }
            }
            var obj = {
                onlineTime: $('#creatStartTime').val() + '--' + $('#creatEndTime').val(),
                device: re.IsPC() ? 'PC' : 'M',
                roleName: $("#serverName option:selected").html(),
                roleId: $("#serverName").val(),
                contact: message,
                purpose: list.length > 0 ? list.join('_') : 'no'
            }
            return obj;
        },
        //申请入队的时候
        getContact2: function () {
            var message = '';
            var sel = $("#contactType2 option:selected");
            var contact = $.trim($("#contactValue2_" + sel.val()).val())
            if (contact) {
                message += sel.html() + "_" + contact;
            } else {
                message = 'no';
            }
            if (sel.val() == 3) {
                message = '我不想填写联系方式';
            }

            var list = [];
            $("#aplayTeam .yxyix li.yxz").each(function () {
                if ($(this).index() > 1) {
                    list.push($(this).index());
                } else {
                    list.push($(this).index() + 1);
                }
            })
            //校验
            if (list.length == 0) {
                alert('您未填写游戏意向，请先填写游戏意向方便志同道合的少侠一起勇闯新服吧~')
                return;
            }
            var wxreg = /^[a-zA-Z]{1}[-_a-zA-Z0-9]{5,19}$/
            var qq = /^[1-9]\d{4,10}$/
            var sel = $("#contactType2 option:selected");
            if (sel.val() == 0) {
                if (!qq.test(contact)) {
                    alert("请填写正确QQ号")
                    return;
                }
            } else if (sel.val() == 1) {
                if (!wxreg.test(contact) && !re.checkPhone(contact)) {//微信账号允许输入手机号
                    alert("请填写正确微信号")
                    return;
                }
            } else if (sel.val() == 2) {
                if (!re.checkPhone(contact)) {
                    alert("请填写正确手机号")
                    return;
                }
            }
            var obj = {
                device: re.IsPC() ? 'PC' : 'M',
                roleName: $("#serverName2 option:selected").html(),
                roleId: $("#serverName2").val(),
                contact: message,
                purpose: list.length > 0 ? list.join('_') : 'no'
            }
            return obj;
        },
        /**
         * 角色列表
         */
        roleList: function () {
            ja.ajax({
                url: re.base_url + '/roleList',
                type: 'get',
                success: function (res) {
                    if (res.code == 10000) {
                        //todo初始化角色列表，无角色默认 我是新玩家
                        re.setRole(res.data);
                    }
                }
            })
        },
        /**
         * 是否在队伍中
         */
        isInTeam: function () {
            ja.get(re.base_url + '/checkInTroops', function (res) {
                if (res.code == 10000) {
                    re.teamFlag = true;
                    $('#myApply').hide()
                    if (res.data != 1) { // 设置查看申请信息按钮
                        $('#applyInfo').hide()
                        // $('#applyInfo').on('click', function () {
                        //     re.tips('您不是队长')
                        // })
                    } else if (res.data == 1) {
                        $('#applyInfo').show()
                    }
                } else if (re.commonErrPop(res)) {
                    if (res.code == 5055) {
                        re.teamFlag = false;
                        return
                    } else {
                        alert(res.message)
                    }
                }
            })
        },
        /**
         * 随机展示6队
         */
        randomTeam: function () {
            if (re.clickFlag) return;
            re.clickFlag = true
            ja.get(re.base_url + '/randomTroops', function (res) {
                // console.log(res);
                if (res.code == 10000) {
                    var li = '';
                    $(res.data).each(function (i, v) {
                        var area, serv, role;
                        area = serv = role = '无'
                        if (v[0].roleName && v[0].roleName != 'no' && v[0].roleName.split('_').length > 1) {
                            area = v[0].roleName.split('_')[0] || '';
                            serv = v[0].roleName.split('_')[1] || '';
                            role = v[0].roleName.split('_')[2] || '';
                        }
                        var member = '' // 队员
                        for (var i = 0; i < 6; i++) {
                            if(v[i]){
                                if (!v[i].backFlag) {
                                    member += '<i class="huoy-ico" ></i >'
                                }else {
                                    member += '<i class="huig-ico" ></i >'
                                }
                            } else {
                                member += '<i></i>'
                            }
                        }
                        li += '<li>' +
                            '<span class="rank">NO.' + v[0].troopsSn + '</span>' +
                            '<a href="javascript:;" class="apply-btn icon"\
							data-roleName="' + v[0].roleName + '"\
							data-purpose="' + v[0].purpose + '"\
							data-onlineTime="' + v[0].onlineTime + '"\
							data-roleId="' + v[0].roleId + '"\
							id="applyBtn" data-sn="' + v[0].troopsSn + '">申请入队</a>' +
                            '<div class="team-con">' +
                            '<p class="t-name">队长：<span>' + v[0].cn + '</span></p>' +
                            '' + (v[0].backFlag == 1 ? ('<span class="hg-tag icon" style="display: block;">回归</span>') : '') + '' +
                            '<p class="t-time">在线时间：' + (v[0].onlineTime ? v[0].onlineTime : '') + '</p>' +
                            '<p>原大区：' + area + '</p>\
							<p>原服务器：' + serv + '</p>\
							<p>原ID：' + role + '</p>\
							<p>游戏意向：' + re.getPurpose(v[0].purpose) + '</p>\
							<p>联系方式：入队后可见</p>'+
                            '<div class="team-peo">' + member + '</div>' +
                            '</div>' +
                            '</li>'
                    })

                    $('#teamList').empty()
                    $('#teamList').html(li)

                    // 动画
                    $('.apply-list').addClass('ani-exchangeTeam');
                    setTimeout(function () {
                        $('.apply-list').removeClass('ani-exchangeTeam');
                    }, 1500);
                } else if (re.commonErrPop(res)) {

                }
                // 防止过快点击换一批小队
                setTimeout(function () {
                    re.clickFlag = false;
                }, 1500)
            })
        },
        /**
         * 搜索队伍
         */
        search: function (obj) {
            if (re.clickFlag) return;
            re.clickFlag = true
            ja.get(re.base_url + '/troopsMemberBySn', obj, function (res) {
                // console.log(res);
                if (res.code == 10000) {
                    if (!res.data.length || res.data.length >= 6) {
                        re.clickFlag = false;
                        re.tips('当前队伍已满员或不存在，无法申请加入')
                        return;
                    }
                    var li = '';
                    var v = res.data
                    var area, serv, role;
                    area = serv = role = '无'
                    if (v[0].roleName && v[0].roleName != 'no' && v[0].roleName.split('_').length > 1) {
                        area = v[0].roleName.split('_')[0] || '';
                        serv = v[0].roleName.split('_')[1] || '';
                        role = v[0].roleName.split('_')[2] || '';
                    }
                    var member = '' // 队员
                    for (var i = 0; i < 5; i++) {
                        if(v[i]){
                            if (v[i].backFlag) {
                                member += '<i class="huoy-ico" ></i >'
                            }else {
                                member += '<i class="huig-ico" ></i >'
                            }
                        } else {
                            member += '<i></i>'
                        }
                    }
                    li += '<li>' +
                        '<span class="rank">NO.' + v[0].troopsSn + '</span>' +
                        '<a href="javascript:;" class="apply-btn icon"\
							data-roleName="' + v[0].roleName + '"\
							data-purpose="' + v[0].purpose + '"\
							data-onlineTime="' + v[0].onlineTime + '"\
							data-roleId="' + v[0].roleId + '"\
							id="applyBtn" data-sn="' + v[0].troopsSn + '">申请入队</a>' +
                        '<div class="team-con">' +
                        '<p class="t-name">队长：<span>' + v[0].cn + '</span></p>' +
                        '' + (v[0].backFlag == 1 ? ('<span class="hg-tag icon" style="display: block;">回归</span>') : '') + '' +
                        '<p class="t-time">在线时间：' + (v[0].onlineTime ? v[0].onlineTime : '') + '</p>' +
                        '<p>原大区：' + area + '</p>\
							<p>原服务器：' + serv + '</p>\
							<p>原角色：' + role + '</p>\
							<p>游戏意向：' + re.getPurpose(v[0].purpose) + '</p>\
							<p>联系方式：入队后可见</p>'+
                        '<div class="team-peo">' + member + '</div>' +
                        '</div>' +
                        '</li>'

                    $('#teamList').empty()
                    $('#teamList').html(li)

                    // 动画
                    $('.list-team').addClass('ani-exchangeTeam');
                    setTimeout(function () {
                        $('.list-team').removeClass('ani-exchangeTeam');
                        re.clickFlag = false;
                    }, 1500);
                } else if (re.commonErrPop(res)) {
                    alert(res.message)
                    re.clickFlag = false
                }
            })
        },
        /**
         * 领取回流礼包
         */
        backReward: function () {
            if (re.clickFlag) return;
            re.clickFlag = true
            ja.post(re.base_url + '/backReward', function (res) {
                re.clickFlag = false
                if (res.code == 10000) {
                    popup('#getGiftSuc')
                    $('#cantGet').show().removeClass('gray').addClass('ylq');
                    $('#getBackReward').hide()
                } else if (res.code == 3401) {
                    alert('您不是回流玩家，不能领取该奖励')
                } else if (res.code == 3402) {
                    re.tips('您已经领取过奖励了')
                } else if (re.commonErrPop(res)) {
                    alert(res.message)
                }
            })
        },
        /**
         * 领奖记录
         */
        rewardLog: function() {
        	ja.get(re.base_url + '/prizeInfo', function(res) {
                // console.log('领奖记录',res);
        		if (res.code == 10000) {
        			if(res.data.length > 0){
        				$(res.data).each(function(){
        					if(this.prizeType && this.prizeType.indexOf("TROOPS_50") != -1){
        						$(".fl-list li .lqbtn").eq(0).addClass('gray ylq');
        					}
        					if(this.prizeType && this.prizeType.indexOf("TROOPS_70") != -1){
        						$(".fl-list li .lqbtn").eq(1).addClass('gray ylq');
        					}
        					if(this.prizeType && this.prizeType.indexOf("TROOPS_90") != -1){
        						$(".fl-list li .lqbtn").eq(2).addClass('gray ylq');
        					}
        				})
        			}
        		}
        	})
        },
        /**
         * 创建队伍
         */
        createTeam: function (obj) {
            if (re.clickFlag) return;
            re.clickFlag = true
            ja.post(re.base_url + '/createTroops2', obj, function (res) {
                re.clickFlag = false
                if (res.code == 10000) {
                    $('#teamNo').text(res.data.split('_')[0]);
                    $('#teamCode').val(res.data.split('_')[1]);
                    popup('#creatSuc')
                    re.teamFlag = true;
                    $('#myApply').hide()
                    $('#applyInfo').show()
                } else if (re.commonErrPop(res)) {
                    alert(res.message)
                }
            })
        },
        /**
         * 验证码入队
         */
        goInTroops: function (obj) {
            if (re.clickFlag) return;
            re.clickFlag = true
            ja.post(re.base_url + '/goInTroops2', obj, function (res) {
                re.clickFlag = false

                if (res.code == 10000) {
                    popup('#pop-join-succ');
                    // 调取小队列表接口 回显加入的人数
                    re.randomTeam();
                    re.myLotCnt(); //我的抽奖次数
                    re.teamFlag = true;
                } else if (re.commonErrPop(res)) {
                    if (res.code == 5055) {
                        $('#pop-join-fail p').text('队伍不存在')
                        popup('#pop-join-fail')
                    } else {
                        alert(res.message)
                    }
                }
            })
        },
        /**
         * 列表入队
         */
        applyTeam: function (obj, a) {
            if (re.clickFlag) return;
            re.clickFlag = true
            ja.post(re.base_url + '/applyTroops2', obj, function (res) {
                re.clickFlag = false

                if (res.code == 10000) {
                    a.hide()
                    // a.siblings('.applied').show()
                    popup('#pop-apply-succ')
                } else if (re.commonErrPop(res)) {
                    alert(res.message)
                }
            })
        },
        /**
         * 领取组队礼包
         */
        troopsReWard: function (level) {
            var level = level;
            if (re.clickFlag) return;
            re.clickFlag = true
            ja.post(re.base_url + '/troopsReWard2', { type: level }, function (res) {
                re.clickFlag = false
                if (res.code == 10000) {
                    popup('#getGiftSuc');
                    re.myLotCnt(); //查看抽奖次数,设置领取礼包状态
                    //$('#getTeamGift').hide();
                    //$('#banGetTeamGift').show()
                } else if (res.code == 5059) {
                    $('#pop-get-fail p').text('小队中还有成员未达到' + level + '级哦~\n快去和小队成员们一起升级吧~');
                    popup('#pop-get-fail')
                } else if (res.code == 5055) {
                    $('#pop-get-fail p').text('少侠还未成功组队哦~请先完成组队再来领取奖励吧~！')
                    popup('#pop-get-fail')
                } else if (res.code == 5058) {
                    $('#pop-get-fail p').text('您的小队未满2人，还无法领取奖励哦！');
                    popup('#pop-get-fail')
                } else if (res.code == 3402) {
                    $('#pop-get-fail p').text('您已经领取过组队礼包了');
                    popup('#pop-get-fail')
                } else if (res.code == 1104) {
                    $('#pop-get-fail p').text('礼包领取暂未开放，请先完成组队，并在新服开启后达到指定要求，再来领取。');
                    popup('#pop-get-fail')
                } else if (res.code == 1105) {
                    $('#pop-get-fail p').text('组队奖励领取时间已结束');
                    popup('#pop-get-fail')
                } else if (re.commonErrPop(res)) {
                    alert(res.message)
                }
            })
        },
        /**
         * 查询队伍信息
         */
        troopsMember: function () {
            //if (re.clickFlag) return;
            re.clickFlag = true
            ja.get(re.base_url + '/troopsMember', function (res) {
                re.clickFlag = false

                if (res.code == 10000) {
                    re.members = res.data.length
                } else if (re.commonErrPop(res)) {
                    alert(res.message)
                }
            })
        },
        /**
         * 申请中列表
         */
        getApplyingList: function () {
            ja.get(re.base_url + '/getApplyingList', function (res) {
                if (res.code == 10000) {
                    var tr = ''
                    $(res.data).each(function (i, v) {
                        tr += '<tr><td width="140"> NO.' + v.troopsSn + '</td><td>' + v.troopsOwnerCn + '</td></tr>'
                    })
                    $('#myApplyBox tbody:eq(1)').empty();
                    $('#myApplyBox tbody:eq(1)').html(tr);
                    popup('#myApplyBox')
                } else if (re.commonErrPop(res)) {
                    alert(res.message)
                }
            })
        },
        /**
         * 待审核列表
         */
        getAwaitApplyList: function () {
            ja.get(re.base_url + '/getAwaitApplyList', function (res) {
                if (res.code == 10000) {
                    var tr = ''
                    $(res.data).each(function (i, v) {
                        tr += '<tr>\
							<td width="40%" style="word-break: break-all;">' + v.applyCn + '</td>\
							<td><a href="javascript:;" class="agree-btn icon-select" data-purpose="'+ v.purpose + '" data-applyCn="' + v.applyCn + '" data-roleName="' + v.roleName + '" data-id="' + v.id + '">查看详情</a></td>\
							</tr>'
                    })

                    if (res.data.length != 0) {
                        $('#applyInfoBox').siblings('p').hide()
                        $('#applyInfoBox').empty();
                        $('#applyInfoBox').html(tr);
                    } else {
                        $('#applyInfoBox').empty();
                        $('#applyInfoBox').siblings('p').show()
                    }
                    popup('#applyInfoPop')
                } else if (re.commonErrPop(res)) {
                    alert(res.message)
                }
            })
        },
        /**
         * 审批
         * @param {*} obj
         */
        examine: function (obj) {
            if (re.clickFlag) return;
            re.clickFlag = true
            ja.post(re.base_url + '/examine2', obj, function (res) {
                re.clickFlag = false

                if (res.code == 10000) {
                    $('#pop-agree-fail .pop-tit').removeAttr('id').attr('id', 'suc')
                    $('#pop-agree-fail p').text('该少侠已经成功入队~前往个人中心查看少侠的个人信息哦~')
                    popup('#pop-agree-fail')
                    re.myLotCnt(); //查看抽奖次数
                } else if (res.code == 5050) {
                    $('#pop-agree-fail .pop-tit').removeAttr('id').attr('id', 'fail')
                    $('#pop-agree-fail p').text('该成员已在队伍中')
                    popup('#pop-agree-fail')
                } else if (res.code == 5052) {
                    $('#pop-agree-fail .pop-tit').removeAttr('id').attr('id', 'fail')
                    $('#pop-agree-fail p').text('您的小队已满员，没有办法再加入其他少侠啦~！')
                    popup('#pop-agree-fail')
                } else if (res.code == 5050) {
                    $('#pop-agree-fail .pop-tit').removeAttr('id').attr('id', 'fail')
                    $('#pop-agree-fail p').text('已有认证手机号或身份证相同的成员在队伍中')
                    popup('#pop-agree-fail')
                } else if (res.code == 5060) {
                    $('#pop-agree-fail .pop-tit').removeAttr('id').attr('id', 'fail')
                    $('#pop-agree-fail p').text('很遗憾，该少侠已加入其他小队了！')
                    popup('#pop-agree-fail')
                } else if (re.commonErrPop(res)) {
                    alert(res.message)
                }
            })
        },
        /**
         * 清空列表
         */
        expireAll: function () {
            if (re.clickFlag) return;
            re.clickFlag = true;
            if (!$('#applyInfoBox').children().length) {
                alert('当前暂无申请信息')
                re.clickFlag = false
                return;
            }
            ja.post(re.base_url + '/expireAll', function (res) {
                re.clickFlag = false

                if (res.code == 10000) {
                    $('#applyInfoBox').empty()
                    $('#applyInfoBox').siblings('p').show()
                    alert('清空成功')
                } else if (re.commonErrPop(res)) {
                    alert(res.message)
                }
            })
        },
        // 加载组件
        getComponent: function (path, fun) {
            // 加载js
            function popMycenter() {
                $.getScript(path + 'index.js');
                fun && fun();
                // 移动端和pc端 表格字体间距问题
                if (ja.glob.isMobile) {
                    $('#tab_dis2').addClass('jhtgTable')
                }
            }

            // 加载css
            // if ($('#MycenterCss').length === 0) {
            //     $('<link>').attr({
            //         id: 'MycenterCss',
            //         rel: "stylesheet",
            //         type: 'text/css',
            //         href: '/act/xtl/newserver/20210820/appoint/m/css/common.css',
            //     }).appendTo($('head'));
            // }

            if ($('#pop-mycenter').length === 0) {
                $('<div />').load(path + 'index.html', function () {
                    popMycenter();
                }).appendTo($('body'));
            } else {
                popMycenter();
            }
        },
        // 我的抽奖次数
        myLotCnt: function () {
            ja.get(re.base_url + '/myLotCnt', function (res) {
                // console.log(res);
                if (res.code == 10000) {
                    var data = res.data;
                    // 小队成员是否满足等级
                    if (data.isReceived50 != 0) {
                        $(".fl-list li .lqbtn").eq(0).addClass('gray');
                    }
                    if (data.isReceived70 != 0) {
                        $(".fl-list li .lqbtn").eq(1).addClass('gray');
                    }
                    if (data.isReceived90 != 0) {
                        $(".fl-list li .lqbtn").eq(2).addClass('gray');
                    }
                    re.myLotCount = data.myLotCnt < 0 ? 0 : data.myLotCnt;
                    $('#remaining').html(re.myLotCount);
                    if (re.myLotCount < 1) {
                        $lottStart.addClass('gray')
                    } else {
                        $lottStart.removeClass('gray')
                    }
                } else if (re.commonErrPop(res)) {
                    // alert(res.message)
                }
            });
        },
        //抽奖
        LottOBJ: function (e, config) {
            var opt = $.extend({
                loop: 6,//圈数 默认6
                duration: 6000,//时间 默认6秒
                ease: 5,//启动速度系数 默认5
                complete: function () {
                    //to do
                }
            }, config),
                obj = opt.wrap,
                count = opt.count,
                rotate = 0,
                target = opt.loop * 360 + 360 - 360 / count * e,
                fps = 1000 / 60,
                past = 0,
                state = function (s) {
                    if (s) obj.attr('data-state', s);
                    return obj.attr('data-state');
                },
                run = function () {
                    if (past > opt.duration) {
                        opt.complete(e);
                        state('结束');
                    } else {
                        rotate = Math.sin(past / opt.duration * Math.PI / 2) * target;
                        past += fps;
                        obj.css({
                            'transform': 'rotate(' + rotate + 'deg)',
                            '-webkit-transform': 'rotate(' + rotate + 'deg)'
                        });
                        setTimeout(run, fps);
                    }
                };
            if (state() == '运行' || e >= count) return;
            state('运行');
            run();
        },

        // 检测手机号
        checkPhone: function (str) {
            if (!(/^1(3|4|5|6|7|8|9)\d{9}$/.test(str))) {
                return false;
            }
            return true;
        },
        // 检测收货地址和收货人姓名
        checkIsChEnNum: function (str) {
            var pattern = /^[A-Za-z0-9\u4e00-\u9fa5]+$/gi;
            if (pattern.test(str))
                return true;
            else
                return false;
        },
        // 保存收货地址
        saveAddr: function (obj) {
            re.isClick = false;
            ja.ajax({
                url: this.base_url + '/saveAddr',
                type: 'post',
                data: obj,
                async: false,
                success: function (res) {
                    re.isClick = true;
                    if (res.code == 10000) {
                        popup('#pop-address-suc');
                        $('.fillAddress').hide();
                    } else if (re.commonErrPop(res)) {
                        alert(res.message)
                    }
                },
                error: function () {
                    re.isClick = true;
                    alert('网络异常')
                }

            })
        },
        // 频繁点击
        preventclick: function (msc) {
            var getTime = new Date().getTime();
            if (re.oldtime == '') {
                re.oldtime = getTime;
                return true;
            } else {
                var newtime = getTime;
                if (newtime - re.oldtime > msc) {
                    re.oldtime = getTime;
                    return true;
                } else {
                    return false;
                }
            }
        }

    }

    re.init()
    window.backFlowG = re;
    
});

// setTimeout(function(){
//     popup('#PopAddrCenter');
// }, 3000);