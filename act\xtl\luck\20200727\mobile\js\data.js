var BASE_URL = "/changyou/xtl/declaration/";
var jpList = {
    'Q1':'天河灵鹊(7天)',
    'Q2':'真元精珀*3',
    'Q3':'虹耀石*3',
    'Q4':'缀梦灵石*5',
    'Q5':'时装：愿君共白首(永久)',
    'Q6':'天河灵鹊(30天)',
    'Q7':'云霄羽翼(180天)',
    'R1':'小蛋蛋鸡玩偶',
    'R2':'全门派马克杯'
}
$(function () {
    var app = 'xtl';
    var activity = 'declaration';
    var version = '20200710';
    var platform = 'changyou';
    if(window.location.href.indexOf('20200728') !="-1"){
        version = '2020071001';//龙门版本号
        $(".longmen").show();
        alert('亲爱的玩家您好，本次活动为测试，奖励仅在测试服可用，感谢您的支持！')
    }
    ja.simpleConfig(app, activity, version, platform, function () {
        list(1, '1', '', '');
    },
        //登录
        function (cn) {
            if (!(validate(cn))) {
                loginFlag = false;
            } else {
                loginFlag = true;
                $("#name").text(cn.data.openid);
                $(".login_btm").hide();
                $(".hou_login").show();
                // $('.my_gb a').attr('href','javascript:OpenDialog("pop1");');
                ja.ajax({
                    url: BASE_URL + 'lotNum',
                    type: 'GET',
                    dataType: 'json',
                    data: {
                        data: 1
                    },
                    success: function (data) {
                        if (data.code == 3103) {
                            alert('您的账号已封停');
                            return;
                        }
                        $(".my_login").hide();
                        roleList() //登录后绑定角色
                        $('.my_gb a').attr("href", "javascript:OpenDialog('pop1')");
                        $('.play_sp .play_lott').attr("href", "javascript:OpenDialog('pop1')");
                        personCenter(); //登录后提取个人中心的状态
                        $('#cs_na').html(data.data);
                    }
                })

            }
        });
});
//调用登录
function login() {
    ja.slogin(1);
}
//验证参数
function validate(value) {
    if (value == null || value == 'null' || value == '' || value == undefined) {
        return false;
    } else {
        return true;
    }
}
//登录按钮点击
$(".login_box>a").click(function () {
    // lotNum();
    login();
});
//个人中心点击
$('.myCenter').on('click', function () {
    popup($(".pop1"));
});
//注销
$('.logout').click(function () {
    ja.logout();
});




//角色列表
function roleList() {
    ja.ajax({
        url: BASE_URL + 'roleList',
        type: 'GET',
        dataType: 'json',
        success: function (data) {
            if (data.code == 3103) {
                alert('您的账号已封停');
                return false;
            }
            if (data.code == 5021) {
                // alert('已绑定角色');
                $('.my_gb a').attr('href', 'javascript:OpenDialog("pop2");');
                $('.play_sp a').attr('href', 'javascript:lottStart();');
            } else {
                OpenDialog('pop1');
                var suData = data.data;
                for (var j in suData) {
                    //var result = suData[j].server_name.split("_");
                    $(".js_select").append('<option value="' + suData[j].group_no + '_' + suData[j].role_guid + '">' + suData[j].role_name + '：' + suData[j].server_name + '</option>');
                }
                $('#bang_js').click(function () {
                    if ($('.js_select').val() == 0) {
                        alert('请选择角色');
                        return false;
                    }
                    var gr = $('.js_select').val().split("_");
                    var groupNo = gr[0];
                    var roleGuid = gr[1];
                    ja.ajax({
                        url: BASE_URL + 'saveBindRole',
                        type: 'post',
                        dataType: 'json',
                        data: {
                            groupNo: groupNo,
                            roleGuid: roleGuid
                        },
                        success: function (data) {
                            closeDialog();
                            roleList();
                            if (data.code == 5021) {
                                $('.gr_zx').attr('href', '');
                            }else if (data.code == 2131) {  //等级低于30级,无法参与！
                                alert('等级低于30级,无法参与！')
                            }
                            location.reload();
                        }
                    })
                });
            }
        }
    })
}

// 个人中心
$('.gr_zx').click(function () {
    personCenter1();
});



//上传图片
// function changepic(file) {
//     var reads= new FileReader();
//     f=document.getElementById('vfile').files[0];
//     reads.readAsDataURL(f);
//
//     var fileTypes = [".jpg", ".png"];
//     var filePath = file.value;
//     //当括号里面的值为0、空字符、false 、null 、undefined的时候就相当于false
//     if(filePath){
//         var isNext = false;
//         var fileEnd = filePath.substring(filePath.indexOf("."));
//         for (var i = 0; i < fileTypes.length; i++) {
//             if (fileTypes[i] == fileEnd) {
//                 isNext = true;
//                 break;
//             }
//         }
//         if (!isNext){
//             alert('不接受此文件类型');
//             file.value = "";
//             return false;
//         }
//     }else {
//         return false;
//     }
//     var fileSize = 0;
//     var fileMaxSize = 1024;//1M
//     var filePath = file.value;
//     if(filePath){
//         fileSize =file.files[0].size;
//         var size = fileSize / 1024;
//         if (size > fileMaxSize) {
//             alert("文件大小不能大于1M！");
//             file.value = "";
//             return false;
//         }else if (size <= 0) {
//             alert("文件大小不能为0M！");
//             file.value = "";
//             return false;
//         }
//     }else{
//         return false;
//     }
//
// }


// function changepic1(file) {
//     var reads= new FileReader();
//     f=document.getElementById('vfile1').files[0];
//     reads.readAsDataURL(f);
//
//
//
//     reads.onload=function (e) {
//         if($('#pic_one1').is(":visible") && $('#pic_one2').is(":visible")){
//             alert('奔现认证只允许上传两张图');
//             return false;
//         }
//         if($('#pic_one1').is(":visible")){
//             $('#pic_one2').show();
//             document.getElementById('pic_one2').src=this.result;
//         }else{
//             $('#pic_one1 ').show();
//             document.getElementById('pic_one1').src=this.result;
//         }
//     };
// }
//实时监测特殊表情删除
//限制输入长度
/* $("#name_dx").bind("input",function(event){
    var val = $(this).val();
    var reg = /[^\a-\z\A-\Z0-9\u4E00-\u9FA5\.\,\?\？\、\<\>\。\，\-\——\=\;\@\！\!\+\t\n\$]/g;
    console.log(event.isComposing )
    if(event.isComposing == false){
        $("#name_dx").val(val.replace(reg,''));
    }
    
}); */
/* $("#text_wb").bind("input",function(event){
    var val = $(this).val();
    var reg = /[^\a-\z\A-\Z0-9\u4E00-\u9FA5\.\,\?\？\、\<\>\。\，\-\——\=\;\@\！\!\+\t\n\$]/g;    
    if(event.isComposing == false){
        $("#text_wb").val(val.replace(reg,''));
    }
});
 */
//微软拼音bug，没根据键盘做实时检测，需要改为，中文输入开始和结束监听，输入结束后在响应
var reg = /[^\a-\z\A-\Z0-9\u4E00-\u9FA5\.\,\?\？\、\<\>\。\，\-\——\=\;\@\！\!\+\ \t\n\$]/g;
var typing = false;
$('#name_dx').on('compositionstart', function () {
    typing = true;//中文输入法开始
}).on('compositionend', function () {
    typing = false;//中文输入法结束
    $(this).keyup();
}).on("keyup", function () {
    var val = $(this).val();
    if(typing == false){
        $(this).val(val.replace(reg,''));
    }
}).on("blur", function () {
    var val = $(this).val();
    typing = false;
    $(this).keyup();
    $(this).val(val.replace(reg,''));
});
$('#text_wb').on('compositionstart', function () {
    typing = true;//中文输入法开始
}).on('compositionend', function () {
    typing = false;//中文输入法结束
    $(this).keyup();
}).on("keyup", function () {
    var val = $(this).val();
    if(typing == false){
        $(this).val(val.replace(reg,''));
    }
}).on("blur", function () {
    var val = $(this).val();
    typing = false;
    $(this).keyup();
    $(this).val(val.replace(reg,''));
});

//纸短情长
function save1(save) {
    var confession_name = $.trim($("#name_dx").val()); //对象名字
    var confession_content = $.trim($("#text_wb").val());
    var vfile = $('#pic_one').src;
    if ("" == confession_name || null == confession_name) {
        alert("请填写告白对象~");
        return;
    }
    if ($('#pic_one').is(":hidden") && $('#two_pic').is(":hidden") && $('#san_pic').is(":hidden")) {
        alert('请上传游戏截图或照片');
        return false;
    }
    var sizeMb = 0;
    $('.one_updata img').each(function (index, item) {
        var base64url = $('.one_updata img')[index].src;

        var str = base64url.replace('data:image/png;base64,', '');
        var equalIndex = str.indexOf('=');
        if (str.indexOf('=') > 0) {
            str = str.substring(0, equalIndex);
        }
        var strLength = str.length;
        var fileLength = parseInt(strLength - (strLength / 8) * 2);
        // 由字节转换为MB
        var size = "";
        size = (fileLength / 1024).toFixed(2);
        Number(size); // 234
        sizeMb += parseInt(size);
        if (sizeMb >= 5000) {
            alert('请重新上传！全部图加起来不能超过5M');
            location.reload();
            return false;
        }
    });

    if (confession_name.length > 15) {
        alert("您填写的对象名称太长啦~");
        return;
    }
    if ("" == confession_content || null == confession_content) {
        alert("请填写告白内容~")
        return;
    }
    if (confession_content.length > 200) {
        alert("您填写的内容名称太长啦~");
        return;
    }
    var pic1 = $('#pic_one').attr('src');
    var two_pic = $('#two_pic').attr('src');
    var san_pic = $('#san_pic').attr('src');
    //根据手机号判断是否可填奔现
    var vfile1 = $('#vfile1').val();//另一半奔现认证证明，如结婚照、合影等 皆可
    var phone = $.trim($("#phone").val());  //手机号码
    if (phone) {
        if ($('#pic_one1').is(":hidden") && $('#pic_one2').is(":hidden")) {
            alert('请上传另一半奔现认证证明，如结婚照、合影等');
            return false;
        }

        var sizeMb2 = 0;
        $('.two_updata img').each(function (index, item) {
            var base64url = $('.two_updata img')[index].src;

            var str = base64url.replace('data:image/png;base64,', '');
            var equalIndex = str.indexOf('=');
            if (str.indexOf('=') > 0) {
                str = str.substring(0, equalIndex);
            }
            var strLength = str.length;
            var fileLength = parseInt(strLength - (strLength / 8) * 2);
            // 由字节转换为MB
            var size = "";
            size = (fileLength / 1024).toFixed(2);
            Number(size); // 234
            sizeMb2 += parseInt(size);
            if ((sizeMb2 + sizeMb) >= 5000) {
                alert('请重新上传！全部图加起来不能超过5M');
                location.reload();
                return false;
            }
        });

        if (!(/^1[3456789]\d{9}$/.test(phone))) {
            alert("手机号码有误，请重填");
            return false;
        }
        var code = $("#yam_code").val();//验证码
        if (!validate(code)) {
            alert("验证码不能为空");
            return false;
        } else if (code.length < 6 || code.length > 6) {
            alert("请输入正确的验证码");
            return false;
        }
        var pic2 = $('#pic_one1').attr('src');
        var pic3 = $('#pic_one2').attr('src');
        //奔现数据
        var obj2 = {
            pimg1: pic2,
            pimg2: pic3,
            phone: phone,
            code: code
        }
    }

    var obj = {
        phone:null,
        target: encodeURIComponent(Base64.toBase64(confession_name)),
        declaration: encodeURIComponent(Base64.toBase64(confession_content)),//信 base64编码
        cimg1: pic1,
        cimg2: two_pic,
        cimg3: san_pic
    }

    if (phone) {
        $.extend(obj, obj2)
    }
    //防止频繁重复提交
    if($("#shiyan").attr("wait")==1){
        alert("正在提交中，请稍等！");
        return;
    }else{
        $("#shiyan").attr("wait",1);
    }
    ja.ajax({
        url: BASE_URL + save,
        type: 'post',
        dataType: 'json',
        data: obj,
        success: function (data) {
            $("#shiyan").attr("wait",0);
            if (data.code == 10000) {
                alert('报名成功！');

                /* $('.upBx_bx').find('input').attr("disabled", false);
                $('.upBx_bx').find('textarea').attr("disabled", false);
                $('.upBx_bx').unbind("click");
                $('.upBx_bx #vfile').hide(); */
                $('#shiyan').attr('href', 'javascript:authAlone();');
                closeDialog();
                personCenter();
            } else if (data.code == 5022) {
                alert("未绑定角色");
            } else if (data.code == 3002) {
                alert("验证手机短信验证码失败");
            } else if (data.code == 3305) {
                alert('处于待审核或审核已经通过');
                $('.upBx_gb').removeClass('bk_bjuP');
                tcdj()
            }else if (data.code == 2131) {  //等级低于30级
                alert('等级低于30级,无法参与！')
            }else if (data.code == 0) {  //信息提交失败，请重新检查提交（不支持特殊符号）
                alert('信息提交失败，请重新检查提交（不支持特殊符号）')
            }
        },
        error:function (err) {
            $("#shiyan").attr("wait",0);
            alert("系统繁忙，请稍后再试！");
        }
    })
}



var offon = true;
//发送短信
$(".yzm_box a").on('click', function () {
    getCode();
});

//获取验证码
function getCode() {
    if (!offon) {
        alert('请过会儿再点~');
        return false;
    }
    var phone = $("#phone").val();
    if (!validate(phone)) {
        alert("请先输入手机号");
        return false;
    }
    if (!(/^1[3456789]\d{9}$/.test(phone))) {
        alert("手机号码有误，请重填");
        return false;
    }

    ja.ajax({
        type: 'POST',
        url: ja.options.host + "/phone/core/zhpt/sendcode",
        data: { 'phone': phone },
        success: function (data) {
            if (data.code == "10000") {
                alert('验证码已发送')
            } else {
                alert(data.message);
                return false;
            }
        }
    });

    offon = false;
    var num = 60;
    $(".yzm_box a").html(num + '秒');
    var timer = setInterval(function () {
        num--;
        $(".yzm_box a").html(num + '秒');
        if (num <= 0) {
            $(".yzm_box a").html('获取验证码');
            offon = true;
            clearInterval(timer);
        }
    }, 1000)
};



var currentPage = 1;//当前页
//上一页
$('.page_prev').click(function () {
    if (currentPage <= 1) {
        alert('已经是第一页了');
    } else {
        currentPage--;
        var zone = $('#province option:selected').html();
        if (zone == '所在大区') {
            zone = "";
        }
        var server = $('#city option:selected').html();
        if (server == '所在服务器') {
            server = "";
        }
        if (zone == '所在大区' && server == '所在服务器') {
            list(currentPage);
        } else {
            list(currentPage, '', zone, server);
        }
    }
});
// 下一页
$(".page_next").click(function () {
    var zong_page = $('#page_ys').html();
    if (zong_page == currentPage) {
        alert('已经是最后一页了')
    } else {
        currentPage++;
        var zone = $('#province option:selected').html();
        if (zone == '所在大区') {
            zone = "";
        }
        var server = $('#city option:selected').html();
        if (server == '所在服务器') {
            server = "";
        }
        if (zone == '所在大区' && server == '所在服务器') {
            list(currentPage);
        } else {
            list(currentPage, '', zone, server);
        }
    }
});
var listType = '1'; // 记录排序方式
//列表展示
function list(page, zx, zone, server) {
    if (zx != '') {
        listType = zx;
    } else if (zx == '') {
        zx = listType;
    }
    var keywords = $("#idsearch").val();
    var idsearch = $("#key_words").val();
    ja.ajax({
        url: BASE_URL + 'list',
        type: 'GET',
        dataType: 'json',
        data: {
            type: zx,
            sn: keywords,
            keyword: idsearch,
            zone: zone,
            server: server,
            pageSize: 4,
            pageNum: page, //第几页
        },
        success: function (data) {
            if (data.code == 10000) {
                var Tdata = data.data.data;
                $('#page_ys').html(data.data.pages); //页数
                $('#page_w').html(data.data.pageNum);
                var tpl = '';

                for (var i in Tdata) {
                    tpl += '<li auth="' + Tdata[i].auth + '">'
                    tpl += '<div class="bt_list c">'
                    tpl += '<p class="g-fl">' + Tdata[i].rolename + '</p>'
                    tpl += '<p class="g-fr">编号：<span class="bh_ty">' + Tdata[i].sn + '</span></p>'
                    tpl += '</div>'
                    tpl += '<div class="xin_dia" sign="' + Tdata[i].sn + '">'
                    tpl += '<i class="sur"></i><span class="signtimes">' + Tdata[i].vote + '</span>'
                    tpl += '</div>'
                    tpl += '<div class="tu_list">'
                    tpl += '<div class="bx_bg sur t">奔现认证</div>'
                    tpl += '</div>'
                    tpl += '<p class="list_p">' + Tdata[i].declaration + '</p>'
                    tpl += '<p class="list_name">' + Tdata[i].server + '</p>'
                    tpl += '</li>'

                }
                $('.list_con ul').html(tpl);

                for (var i in Tdata) {
                    for (var img in Tdata[i].images) {
                        $('.tu_list').eq(i).append('<img src="' + Tdata[i].images[img] + '" alt="">');
                    }
                }

                $('.list_con li').each(function () {
                    if ($(this).attr('auth') == 2) {
                        $(this).find('.bx_bg').show();
                    }
                });

                function vote(a) {
                    voteFlag = true;
                    var bSign = a.getAttribute("sign");
                    t = $(a);
                    ja.ajax({
                        type: "POST",
                        url: BASE_URL + 'vote',
                        data: {
                            sn: bSign
                        },
                        success: function (data) {
                            voteFlag = false;
                            if (data.code == 3103) {
                                alert('账号封停！');
                                return false;
                            }else if (data.code == 2131) {  //等级低于30级
                                alert('等级低于30级,无法参与！')
                            } else if (data.code == 5022) {
                                OpenDialog('pop1');
                                return;
                            }else if (data.code == 2005) {  //超出每天的限制
                                alert('少侠，今日已完成投票，请明日再来！')
                            } else if (data.code == 10000) {
                                t.find('.signtimes').html(parseInt(t.find('.signtimes').html()) + 1);
                                alert('点赞成功');
                            }else if (data.code == 2111) {
                                alert(data.message);
                            }
                        },
                        error: function () {
                        }
                    });
                }
                $(".xin_dia").off();
                var voteFlag = false;
                $(".xin_dia").on('click', function () {
                    if (!voteFlag) {
                        vote(this)
                    }
                })
            } else {
                alert(data.message);
            }
        }
    })
}

//所在大区筛选
$("#province").change(function () {
    var zone = $('#province option:selected').html();
    if (zone == '所在大区') {
        zone = "";
    }
    list(1, '1', zone);
});
$('#city').change(function () {
    var zone = $('#province option:selected').html();
    var server = $('#city option:selected').html();
    list('1', '1', zone, server);
});

//筛选
//编号搜索
$('.btn_search_04').click(function () {
    if ($('#idsearch').val() == '') {
        alert('请输入参赛编号');
        return false;
    }
    list(1, '0');
})
//玩家昵称搜索
$('.btn_search_01').click(function () {
    if ($('#key_words').val() == '') {
        alert('请输入玩家昵称');
        return false;
    }
    list(1, '0');
});


//按时间
$('.btn_search_02').click(function () {
    $("#province option").eq(0).attr("selected", true);
    $("#city option").eq(0).attr("selected", true);
    $("#idsearch").val("");
    $("#key_words").val("");
    $("#city option:not(:first)").remove();
    currentPage = 1;
    list(1, '0');
})
//按人气
$('.btn_search_03').click(function () {
    $("#province option").eq(0).attr("selected", true);
    $("#city option").eq(0).attr("selected", true);
    $("#idsearch").val("");
    $("#key_words").val("");
    $("#city option:not(:first)").remove();
    currentPage = 1;
    list(1, '1');
});


//抽奖
var LottOBJ = function (o) {
    var conf = {
        obj: '',
        cover: '',
        count: 0,
        position: '',
        complete: function () { }
    }
    conf = $.extend(conf, o);
    conf.cover.hide();
    var pos = [], timer = null, spd = 100, curr = 0, round = 0;
    for (var i = 0; i < conf.position.split(',').length; i++) {
        var s = conf.position.split(',')[i];
        pos.push({ left: s.split('_')[0], top: s.split('_')[1] });
    }
    if (pos.length != conf.count) return;
    this.startRoll = function (n) {
        if (typeof (n) != 'number' && n > pos.length) n = 0;
        spd = 100;
        round = Math.ceil(Math.random() * 4) + 1;
        conf.cover.show().css({ left: pos[curr].left + 'rem', top: pos[curr].top + 'rem' });
        timer = setTimeout(function () { roll(n); }, spd);
    };
    function roll(e) {
        clearTimeout(timer);
        if (curr == pos.length - 1) {
            if (round > 0) {
                round--;
                curr = 0;
            }
        } else
            curr++;
        conf.cover.css({ left: pos[curr].left + 'rem', top: pos[curr].top + 'rem' });
        if (curr == e && round == 0) {
            setTimeout(conf.complete, 500);
            return;
        }
        if (round == 0) spd += 80;
        timer = setTimeout(function () { roll(e); }, spd);
    }
};

//开始抽奖 参数为抽中的序号
function lottStart() {
    
    $('.play_lott').attr('href', 'javascript:;');
    ja.ajax({
        url: BASE_URL + 'lot',
        type: 'POST',
        dataType: 'json',
        success: function (data) {
            if (data.code == 3103) {
                alert('您的账号已封停');
                return;
            }
            lotNum();
            if (data.code == 5022) {
                $('.play_lott').attr('href', 'javascript:OpenDialog("pop1");');
            } else if (data.code == 2007) {
                alert('您还没有抽奖资格');
                $('.play_lott').attr('href', 'javascript:lottStart();');
            } else if (data.code == 10000) {
                if ($('#cs_na').html() > 0) {
                    $('#cs_na').html(parseInt($('#cs_na').html()) - 1); //减少一次
                }
                var gx = '恭喜您获得';
                // lottObj.startRoll(0);
                lottObj = new LottOBJ({
                    obj: $('#lottcon'),
                    cover: $('#lottcon .lott-cover'),
                    count: 10,
                    position: '0.07_0,1.6_0,3.15_0,4.7_0,4.7_1.45,4.7_2.9,3.13_2.9,1.6_2.9,0.07_2.9,0.07_1.45',
                    complete: function () {
                        var jp = data.data;
                        if (data.data == 'Q1' || data.data == 'Q2' || data.data == 'Q3' || data.data == 'Q4' || data.data == 'Q5' || data.data == 'Q6' || data.data == 'Q7') {
                            personCenter();
                            $('.jpFont-zi').show();
                            OpenDialog("pop17");
                        }
                        if (data.data == 'Q1') {
                            $('.jpFont-zi span').html(jpList['Q1']);
                            $(".jp_show").html(gx +'【' + jpList['Q1'] + '】<br />请在<a href="javascript:OpenDialog(\'pop4\');" class="lookPrize">个人中心</a>查看');
                        } else if (data.data == 'Q2') {
                            $('.jpFont-zi span').html(jpList['Q2']);
                            $(".jp_show").html(gx +'【' + jpList['Q2'] + '】<br />请在<a href="javascript:OpenDialog(\'pop4\');" class="lookPrize">个人中心</a>查看');
                        } else if (data.data == 'Q3') {
                            $('.jpFont-zi span').html(jpList['Q3']);
                            $(".jp_show").html(gx +'【' + jpList['Q3'] + '】<br />请在<a href="javascript:OpenDialog(\'pop4\');" class="lookPrize">个人中心</a>查看');
                        } else if (data.data == 'Q4') {
                            $('.jpFont-zi span').html(jpList['Q4']);
                            $(".jp_show").html(gx +'【' + jpList['Q4'] + '】<br />请在<a href="javascript:OpenDialog(\'pop4\');" class="lookPrize">个人中心</a>查看');
                        } else if (data.data == 'Q5') {
                            $('.jpFont-zi span').html(jpList['Q5']);
                            $(".jp_show").html(gx +'【' + jpList['Q5'] + '】<br />请在<a href="javascript:OpenDialog(\'pop4\');" class="lookPrize">个人中心</a>查看');
                        } else if (data.data == 'Q6') {
                            $('.jpFont-zi span').html(jpList['Q6']);
                            $(".jp_show").html(gx +'【' + jpList['Q6'] + '】<br />请在<a href="javascript:OpenDialog(\'pop4\');" class="lookPrize">个人中心</a>查看');
                        } else if (data.data == 'Q7') {
                            $('.jpFont-zi span').html(jpList['Q7']);
                            $(".jp_show").html(gx +'【' + jpList['Q7'] + '】<br />请在<a href="javascript:OpenDialog(\'pop4\');" class="lookPrize">个人中心</a>查看');
                        }
                        $('.play_lott').attr('href', 'javascript:lottStart();');


                        if (jp == "R1" || jp == "R2" ) {
                            OpenDialog('pop11');
                            $('.lay_mc_list ul li a').css('display', 'block');
                        }
                        if (jp == "R1") {
                            $('.shiwu_box').html(jpList['R1']);
                            $('.shiwu').append('<p>'+ jpList['R1'] +'</p>');
                        }else if (jp == "R2") {
                            $('.shiwu_box').html(jpList['R2']);
                            $('.shiwu').append('<p>'+ jpList['R2'] +'</p>')
                        }else if (jp == "N") {
                            $(".jp_show").html("感谢参与")
                            OpenDialog("pop17");
                        }
                        //抽奖完成
                        // popup($('#dialogGetPrize'));
                    }
                });
                if (data.data == 'Q1') {
                    lottObj.startRoll(0);
                } else if (data.data == 'Q2') {
                    lottObj.startRoll(8);
                } else if (data.data == 'Q3') {
                    lottObj.startRoll(7);
                } else if (data.data == 'Q4') {
                    lottObj.startRoll(6);
                } else if (data.data == 'Q5') {
                    lottObj.startRoll(4);
                } else if (data.data == 'Q6') {
                    lottObj.startRoll(3);
                } else if (data.data == 'Q7') {
                    lottObj.startRoll(1);
                } else if (data.data == 'R1') {
                    lottObj.startRoll(9);
                } else if (data.data == 'R2') {
                    lottObj.startRoll(2);
                }else if (data.data == 'N') {
                    lottObj.startRoll(5);
                }
                var lottObj;  //变量提升

            }

        }
    })

}




var pop15Flag = false;
// 个人中心
function personCenter() {
    ja.ajax({
        url: BASE_URL + 'personCenter',
        type: 'GET',
        dataType: 'json',
        success: function (data) {
            if (data.code == 3103) {
                alert('您的账号已停封！');
                $('.gr_zx').attr('href', 'javascript:;');
                return false;
            }
            var data_t = data.data;
            if (data.code == 10000) {    //需报名后 才不显示用户不存在
                $('.xuni').empty();
                $('.shiwu').empty();
                if (data_t.addrStatus == 0) {
                    $('.lay_mc_list ul li a').attr("href", "javascript:OpenDialog('pop11')");
                } else if (data_t.addrStatus == 1) {
                    $('.lay_mc_list ul li a').attr("href", "javascript:OpenDialog('pop13')");
                }
                function isxt() {
                    var _arr = data_t.prizes;
                    var _res = []; //
                    _arr.sort();
                    for (var i = 0; i < _arr.length;) {
                        var count = 0;
                        for (var j = i; j < _arr.length; j++) {
                            if (_arr[i] == _arr[j]) {
                                count++;
                            }
                        }
                        _res.push([_arr[i], count]);
                        i += count;
                    }
                    //_res 二维数维中保存了 值和值的重复数
                    var _newArr = [];
                    for (var i = 0; i < _res.length; i++) {
                        if (_res[i][0] == 'Q1') {
                            $('.xuni').append('<p>' + jpList['Q1'] + 'x' + _res[i][1]+'</p>'+'</p>');
                        }
                        if (_res[i][0] == 'Q2') {
                            $('.xuni').append("<p>真元精珀* " +3 * (_res[i][1])+'</p>');
                        }
                        if (_res[i][0] == 'Q3') {
                            $('.xuni').append("<p>虹耀石* " + 3 * (_res[i][1])+'</p>');
                        }
                        if (_res[i][0] == 'Q4') {
                            $('.xuni').append("<p>缀梦灵石* " + 5 * (_res[i][1])+'</p>');
                        }
                        if (_res[i][0] == 'Q5') {
                            $('.xuni').append("<p>" + jpList['Q5']+ 'x' + _res[i][1]+'</p>');
                        }
                        if (_res[i][0] == 'Q6') {
                            $('.xuni').append("<p>" + jpList['Q6'] + 'x' + _res[i][1]+'</p>');
                        }
                        if (_res[i][0] == 'Q7') {
                            $('.xuni').append("<p>" + jpList['Q7'] + 'x' + _res[i][1]+'</p>');
                        }
                        _newArr.push(_res[i][0] + 'x' + _res[i][1]);
                    }

                }
                isxt();

                for (var i in data_t.prizes) {
                    var jp = data_t.prizes[i];

                    // if(jp=="Q1"){
                    //     $('.xuni').append('<p>天河灵鹊（7天）</p>')
                    // }
                    // if(jp=="Q2"){
                    //     $('.xuni').append('<p>真元精珀*3</p>');
                    // }
                    // if(jp=="Q3"){
                    //     $('.xuni').append('<p>虹耀石*3</p>');
                    // }
                    // if(jp=="Q4"){
                    //     $('.xuni').append('<p>缀梦灵石*5</p>');
                    // }
                    // if(jp=="Q5"){
                    //     $('.xuni').append('<p>天河灵鹊（30天）</p>');
                    // }


                    if (jp == "R1" || jp == "R2") {
                        $('.lay_mc_list ul li a').css('display', 'block');
                    }
                    if (jp == "R1") {
                        $('.shiwu').append('<p>'+ jpList['R1'] +'</p>')
                        $('.shiwu_box').text(jpList['R1'])
                    }
                    if (jp == "R2") {
                        $('.shiwu').append('<p>'+ jpList['R2'] +'</p>')
                        $('.shiwu_box').text(jpList['R2'])
                    }
                }
                $('#pop4 .layer_com > h3+div').empty()
                switch (data_t.status) {
                    case 0:
                        //$('#pop4 .layer_com > h3+div').append('<p style="font-size: 22px;margin-left: 23px;">表白审核中，请耐心等待！</p>')
                        $('#pop4 .layer_com > h3+div').append('<div class="zy_renz c"><p>审核中，请耐心等待！</p></div>')
                        break;
                    case 1:
                        //$('#pop4 .layer_com > h3+div').append('<p style="font-size: 22px;margin-left: 23px;">您已参与表白成功！</p>')
                        $('#pop4 .layer_com > h3+div').append('<div class="zy_renz c"><p>您已参与表白成功！</p></div>')
                        break;
                    case -1:
                        if (data_t.status == -1 && data_t.commitv ==3) {//审核失败三次置灰认证
                            $('#pop4 .layer_com > h3+div').append('<div class="zy_renz c"><p class="g-fl">很抱歉，您参与表白失败！</p><a href="JavaScript:;" class="g-fr hui_gao">我要去告白</a></div>')
                        } else {
                            $('#pop4 .layer_com > h3+div').append('<div class="zy_renz c"><p class="g-fl">很抱歉，您参与表白失败！</p><a href="JavaScript:OpenDialog(\'pop2\');" class="g-fr">我要去告白</a></div>')
                        }
                        //$('#pop4 .layer_com > h3+div').append('<p style="font-size: 22px;margin-left: 23px;">很抱歉，您参与表白失败！</p>')
                        break;
                }
                switch (data_t.authlevel) {
                    case 0:
                        if (data_t.status == -1 && data_t.commitv ==3) {
                            $('#pop4 .layer_com > h3+div').append('<div class="zy_renz c"><p class="g-fl">奔现认证得更多奖励！</p><a href="JavaScript:;" class="g-fr hui_gao">我要去认证</a></div>')
                        } else {
                            $('#pop4 .layer_com > h3+div').append('<div class="zy_renz c"><p class="g-fl">奔现认证得更多奖励！</p><a href="JavaScript:OpenDialog(\'pop2\');" class="g-fr">我要去认证</a></div>')
                        }
                        break;
                    case 1:
                        $('#pop4 .layer_com > h3+div').append('<div class="zy_renz c"><p>奔现认证审核中，请耐心等待！</p></div>')
                        break;
                    case 2:
                        $('#pop4 .layer_com > h3+div').append('<div class="zy_renz c"><p>奔现认证审核成功！</p></div>')
                        break;
                    case -1:
                        if (data_t.status == -1 && data_t.commitv ==3) {
                            $('#pop4 .layer_com > h3+div').append('<div class="zy_renz c"><p class="g-fl">奔现认证得更多奖励！</p><a href="JavaScript:;" class="g-fr hui_gao">我要去认证</a></div>')
                        } else {
                            $('#pop4 .layer_com > h3+div').append('<div class="zy_renz c"><p class="g-fl">奔现认证得更多奖励！</p><a href="JavaScript:OpenDialog(\'pop2\');" class="g-fr">我要去认证</a></div>')
                        }
                        //$('#pop4 .layer_com > h3+div').append('<p style="font-size: 22px;margin-left: 23px;">很抱歉，您参与的奔现认证审核未通过！</p>')
                        break;
                }
                if (data.code == 5022) {
                    $('.my_gb a,.gr_zx,.play_lott').attr("href", "javascript:OpenDialog('pop1')");
                }
                if (data_t.status == 0) {  //纸短情长审核
                    $('.upBx_gb').addClass('bk_bjuP');
                    tcdj();
                    $('#shiyan').attr('href', 'javascript:authAlone();');
                    bxrz_one();  //解锁奔现认证

                } else if (data_t.status == 1) {  // 纸短情长通过
                    $('#shiyan').attr('href', 'javascript:authAlone();');
                    tcdj();
                    bxrz_one();//解锁奔现认证
                } else if (data_t.status == -1 && data_t.commitv ==3) {//告白审核失败超过3次
                    // alert('纸短情长审核失败');
                    //$('.upBx_gb').removeClass('bk_bjuP');
                    tcdj(); //锁定纸短情长
                    bxrz();//锁定奔现认证
                    OpenDialog('pop4');
                    $('.my_gb a').attr('href', 'JavaScript:OpenDialog("pop4");');
                    $('#shiyan').attr('href', 'javascript:void(0);');
                    return false;
                } else if (data_t.status == -1 ) {//告白审核失败不超过3次
                    // alert('纸短情长审核失败');
                    $('.upBx_gb').addClass('bk_bjuP');
                    tcdj_one(); //解锁纸短情长
                    bxrz_one();//解锁奔现认证
                    OpenDialog('pop4');
                    $('.my_gb a').attr('href', 'JavaScript:OpenDialog("pop2")');
                    $('#shiyan').attr('href', 'javascript:save1("update")');
                    return false;
                }
                if (data_t.authlevel == 1) {
                    $('.my_gb a').attr('href', 'javascript:OpenDialog("pop4");')
                }
                if (data_t.authlevel == 2) {
                    $('.my_gb a').attr('href', 'javascript:OpenDialog("pop4");')
                }
                if (data_t.authlevel == -1) {
                    // alert('奔现认证审核失败')
                    bxrz_one();//解锁奔现认证
                    if (!pop15Flag) {
                        OpenDialog('pop15');
                        pop15Flag = true;
                    }
                    $('.my_gb a').attr('href', 'javascript:OpenDialog("pop2");')
                }
            }
            if (data.code == 5022) {
                $('.gr_zx').attr('href', 'javascript:OpenDialog("pop1");')
            }
            //不可编辑
            //未填写纸短情长


        }
    })
}

// 纸短情长在审核中-锁定
function tcdj() {
    $('.upBx_gb').off()
    if ($('.upBx_gb').hasClass("bk_bjuP")) {
        $(".one_updata .del").hide();
        $('.upBx_gb').find('input').attr("disabled", "disabled");
        $('.upBx_gb').find('textarea').attr("disabled", "disabled");
        $('#vfile').css('display', 'none')
        $('.upBx_gb').click(function () {
            alert('纸短情长审核中或已审核过');
        });
        $('.upBx_gb #vfile').hide();
    }
}

//奔现认证锁定
function bxrz() {
    $(".two_updata .del").hide();
    $('.upBx_bx').find('input').attr("disabled", "disabled");
    $('.upBx_bx').find('textarea').attr("disabled", "disabled");
    $('#vfile1').css('display', 'none')
    $('.upBx_bx').click(function () {
        alert('请填写纸短情长后，再来填写奔现认证');
    });
    $('.upBx_bx #vfile').hide();
}
//bxrz();
function bxrz_one() {
    $('.upBx_bx').removeClass('bk_bj');
    $('.upBx_bx').find('input').attr("disabled", false);
    $('.upBx_bx').find('textarea').attr("disabled", false);
    $(".two_updata s img").each(function(){
        if($(this).attr('src')){
            $(this).next(".del").show()
        }
    })
    $('#vfile1').css('display', 'block')
    $('.upBx_bx').unbind("click");
    $('.upBx_bx #vfile').hide();
}
//解锁告白,清空告白
function tcdj_one() {
    $('.upBx_gb').off()
    if ($('.upBx_gb').hasClass("bk_bjuP")) {
        $('.upBx_gb').find('input').attr("disabled", false);
        $('.upBx_gb').find('textarea').attr("disabled", false);
        //$(".one_updata img,.one_updata .del").hide();
        //$(".one_updata s img").attr("src",'');
        $(".one_updata s img").each(function(){
            if($(this).attr('src')){
                $(this).next(".del").show()
            }
        })
        $('#vfile').css('display', 'block')
        $('.upBx_gb #vfile').show();
    }
}
// 个人中心
function personCenter1() {
    ja.ajax({
        url: BASE_URL + 'personCenter',
        type: 'GET',
        dataType: 'json',
        success: function (data) {
            if (data.code == 3103) {
                alert('您的账号已封停！');
                return false;
            }else if (data.code == 1000) {
                //alert('请报名参加告白活动');
                OpenDialog('pop3');
                return false;
            }else if (data.code == 5022) {
                $('.gr_zx').attr('href', 'JavaScript:OpenDialog("pop1");')
            }else if (data.code == 2131) {  //等级低于30级,无法参与！
                alert('等级低于30级,无法参与！')
            } else {
                $('.jpFont-zi').hide()
                personCenter()
                var data_t = data.data;
                OpenDialog('pop4');
            }
        }
    })
}

// 奔现认证
function authAlone() {
    var vfile1 = $('#vfile1').val();//另一半奔现认证证明，如结婚照、合影等 皆可
    var phone = $.trim($("#phone").val());  //手机号码

    if ($('#pic_one1').is(":hidden") && $('#pic_one2').is(":hidden")) {
        alert('请上传另一半奔现认证证明，如结婚照、合影等');
        return false;
    }

    var sizeMb = 0;
    $('.two_updata img').each(function (index, item) {
        var base64url = $('.two_updata img')[index].src;

        var str = base64url.replace('data:image/png;base64,', '');
        var equalIndex = str.indexOf('=');
        if (str.indexOf('=') > 0) {
            str = str.substring(0, equalIndex);
        }
        var strLength = str.length;
        var fileLength = parseInt(strLength - (strLength / 8) * 2);
        // 由字节转换为MB
        var size = "";
        size = (fileLength / 1024).toFixed(2);
        Number(size); // 234
        sizeMb += parseInt(size);
        if (sizeMb >= 5000) {
            alert('请重新上传！全部图加起来不能超过5M');
            location.reload();
            return false;
        }
    });

    if (!(/^1[3456789]\d{9}$/.test(phone))) {
        alert("手机号码有误，请重填");
        return false;
    }
    var code = $("#yam_code").val();//验证码
    if (!validate(code)) {
        alert("验证码不能为空");
        return false;
    } else if (code.length < 6 || code.length > 6) {
        alert("请输入正确的验证码");
        return false;
    }

    var pic2 = $('#pic_one1').attr('src');
    var pic3 = $('#pic_one2').attr('src');
    ja.ajax({
        url: BASE_URL + 'authAlone',
        type: 'POST',
        dataType: 'json',
        data: {
            pimg1: pic2,
            pimg2: pic3,
            phone: phone,
            code: code
        },
        success: function (data) {
            if (data.code == 3002) {
                alert('验证码错误！');
                return false;
            } else if (data.code == 10000) {
                personCenter();
                alert('奔现认证提交成功！待审核')
                closeDialog();
            } else if (code == 3305) {
                alert('已提交审核，请耐心等待')
                closeDialog();
            } else {
                alert(data.message);
            }
        },
        error: function () {
            alert("上传接口错误，请联系管理员！");
        }
    })
}

//抽奖次数查询lotNum
function lotNum() {
    ja.ajax({
        url: BASE_URL + 'lotNum',
        type: 'GET',
        dataType: 'json',
        data: {
            data: 1
        },
        success: function (data) {
            if (data.code == 10000) {
                $('#cs_na').html(data.data);
            }

        }
    })
}


// 信息提交
function xinxi() {

    var name = $.trim($("#name_tj").val());
    var phone = $.trim($("#phone_tj").val());  //手机号码
    var city_tj = $.trim($("#city_tj").val());
    if (name == "" || name == null) {
        alert('请填写姓名');
        return false;
    }
    if (!(/^1[3456789]\d{9}$/.test(phone))) {
        alert("手机号码有误，请重填");
        return false;
    }
    if (city_tj == '' || city_tj == null) {
        alert('请填写地址');
        return false;
    }
    if(city_tj.length>120){
        alert('输入地址过长！');
        return;
    }
    ja.ajax({
        url: BASE_URL + 'updateAddrInfo',
        type: 'POST',
        dataType: 'json',
        data: {
            name: name,
            phone: phone,
            addr: city_tj
        },
        success: function (data) {
            if(data.code == '10000'){
                personCenter();
                OpenDialog('pop12');
            }else if(data.code == '1024'){
                alert('地址已提交，请勿重复提交！')
            }else {
                alert('信息提交失败，请重新检查提交（不支持特殊符号）')
            }
            
        }
    })
}

//点击抽中奖品隐藏
$('.jpFont-zi .gr_zx').click(function () {
    $('.jpFont-zi').hide();
})

