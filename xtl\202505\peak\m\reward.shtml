<!DOCTYPE HTML>
<html>
	<head>
		<meta charset="utf-8">
		<title>第三届新全球争霸赛-新天龙八部官方网站</title>
		<meta name="applicable-device" content="mobile">
		<meta name="viewport"
			content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no">
		<meta content="yes" name="mobile-web-app-capable">
		<meta content="yes" name="apple-mobile-web-app-capable">
		<meta content="telephone=no" name="format-detection">
		<meta name="keywords"
			content="天龙,新天龙,天龙八部,新天龙八部,天龙八部3,tl,tlbb,tl3,xtl,网络游戏,网游,武侠,浪漫武侠,武侠游戏,金庸,MMO,天龙八部OL,萧峰,段誉,虚竹,王语嫣,天龙八部钟汉良版,天龙八部黄日华版,8周年,八周年,周年庆,全民代言,纵情四海" />
		<meta name="description" content="《新天龙八部》十三周年，全新副本观山海即将上线，画面颠覆性升级！唯美山河场景、国韵音乐大作、全新副本玩法登场！海量周年福利上线领取。" />
		<link type="image/x-icon" href="//tl.changyou.com/favicon.ico" rel="icon" />
		<link type="image/x-icon" href="//tl.changyou.com/favicon.ico" rel="shortcut icon" />
		<link type="text/css" rel="stylesheet" href="/all/css/reset.css" />
		<link type="text/css" rel="stylesheet" href="css/swiper.min.css" />
		<link type="text/css" rel="stylesheet" href="css/index.css?v2024" />
		<link type="text/css" rel="stylesheet" href="/xtl/202505/peak/pc/css/pop.css" />
		<script>
			(function() {
				if (!/Android|webOS|iPhone|iPad|iPod|BlackBerry/i.test(navigator.userAgent)) {
					window.location.href = "../pc/reward.shtml"
				};
			})();
			var phoneWidth = parseInt(window.screen.width);
			var phoneScale = phoneWidth / 750;
			var ua = navigator.userAgent;
			if (/Android (\d+\.\d+)/.test(ua)) {
				var version = parseFloat(RegExp.$1);
				// andriod 2.3
				if (version > 2.3) {
					document.write('<meta name="viewport" content="width=750, minimum-scale = ' + phoneScale +
						', maximum-scale = ' +
						phoneScale + ', target-densitydpi=device-dpi">');
					// andriod 2.3
				} else {
					document.write('<meta name="viewport" content="width=750, target-densitydpi=device-dpi">');
				}
			} else {
				document.write('<meta name="viewport" content="width=750, user-scalable=no, target-densitydpi=device-dpi">');
			}
		</script>
		<script type="text/javascript">
			var deviceWidth = document.documentElement.clientWidth;
			if (deviceWidth > 750) {
				deviceWidth = 750
			};
			document.documentElement.style.fontSize = deviceWidth / 750 + 'px';
		</script>
	</head>
	<body class="pg2">
		<h1 class="none">第三届新全球争霸赛</h1>
		<div class="wrap">
			<div class="head">
				<a href="http://tl.changyou.com/main.shtml" title="《新天龙八部》官方网站" target="_blank"
					class="logo">新天龙八部官方网站</a>
					<a href="javascript:;" title="第三届新全球争霸赛" class="logo2">第三届新全球争霸赛</a>
				<p class="arr">向下滑动鼠标浏览更多</p>
			</div>
			<div class="cont">
				<!-- <div class="guanjunbox"></div> -->
				<div class="qiansanbox">
					<a href="javascript:;" title="查看时装" class="gjsz pa"></a>
					<a href="javascript:;" title="查看时装" class="yjsz pa"></a>
					<a href="javascript:;" title="查看时装" class="szlink pa"></a>
					<a href="javascript:;" title="查看详情"  class="pjzrlink lihelink1"> </a>
					<a href="javascript:;" title="查看详情"  class="dfzjlink lihelink2"> </a>
					<a href="javascript:;" title="查看详情"  class="pjzrlink lihelink3"> </a>
					
				</div>
				<div class="dianjunbox">
					<a href="javascript:;" title="查看详情"  class="pjzrlink lihelink1"> </a>
					<a href="javascript:;" title="查看时装" class="szlink pa"></a>
				</div>
				<div class="rewardbox">
					<a href="javascript:;" title="查看详情"  class="pjzrlink lihelink1"> </a>
					<a href="javascript:;" title="查看时装" class="szlink pa"></a>
				</div>
				<div class="rewardbox1">
					<a href="javascript:;" title="查看详情"  class="pjzrlink lihelink1"> </a>
					<a href="javascript:;" title="查看时装" class="szlink pa"></a>
				</div>
				<div class="rewardbox2">
					<a href="javascript:;" title="查看详情"  class="pjzrlink lihelink1"> </a>
					<a href="javascript:;" title="查看时装" class="szlink pa"></a>
				</div>
				<div class="rewardbox3">
					<a href="javascript:;" title="查看时装" class="szlink pa"></a>
				</div>
			</div>
	<!--#include virtual="/xtl/202505/peak/pc/inc/flo.shtml"-->

		</div>
		<div class="pop pop7" id="lihePop">
			<span class="close popclosebtn">x</span>
		</div>
		<div class="pop pop8" id="lihePop1">
			<span class="close popclosebtn">x</span>
		</div>
		<div class="pop pop9" id="szPop1">
			<span class="close popclosebtn">x</span>
		</div>
		<div class="pop pop10" id="szPop2">
			<span class="close popclosebtn">x</span>
		</div>
		<div class="pop pop11" id="szPop3">
			<span class="close popclosebtn">x</span>
		</div>
		<script src="/all/js/jquery-1.8.3.min.js"></script>
		<script src="/all/js/swiper-4.5.0.min.js"></script>
		<script src="/all/js/popout.js"></script>
		<script src="js/index.js"></script>
		<!--#include virtual="/all/nav/cy_public_js_dark.html"-->
		<!--#include virtual="/all/dma/dma_static.html"-->
	</body>
</html>
