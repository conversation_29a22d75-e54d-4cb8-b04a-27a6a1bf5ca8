html {
  font-size: 100px;
}

body,
html {
  height: 100%;
  overflow: hidden;
}

.wrap * {
  box-sizing: border-box;
}

.sp,
.btn {
  background: url(/act/tldc/collection/20250427/pc/img/comm2.png) no-repeat;
}
.sp-tit,
.comm {
  background: url(/act/tldc/collection/20250427/pc/img/title2.png) no-repeat;
}
.full-img img,
.full-img {
  display: block;
}
.full-img img {
  width: 100%;
  height: auto;
}
.bg-head .mid {
  /* position: absolute;
  z-index: 9;
  width: 100%; */
}
.bg-head .mid > div,
.bg-head .mid > a {
  z-index: 9;
  position: absolute;
}
.bg {
  background-size: 100% auto;
  background-position: top center;
  background-repeat: no-repeat;
}
.wrap {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  text-align: center;
}
.hd_top,
.hd_top * {
  will-change: transform;
}
.swiper-container-m {
  width: 100%;
  height: 100%;
}

.swiper-slide-m {
  overflow: hidden;
  opacity: 0;
  transition: opacity 0.5s ease;
}

#cy_bot {
  padding: 0.1rem !important;
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.cyLogo {
  display: inline-flex !important;
}
#cy_bot span {
  display: inline-flex !important;
}
.orientLayer {
  display: none;
  position: fixed;
  z-index: 1000;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.9);
  color: #fff;
  font-size: 0.3rem;
}
.bufflybox {
  pointer-events: none;
}

/* page */
.page {
  width: 100%;
  height: 100%;
  position: relative;
  top: 0;
  margin-top: 0;
  transform-origin: center;
}
.page.page0 {
  padding-top: 3rem;
}
.page .tit-sub {
  width: 12rem;
  height: 1.17rem;
  position: relative;
  left: 50%;
  margin-left: -6.02rem;
  transform: scale(0.9);
}
.content {
    margin: 0 auto;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
}
.page-bg {
  width: 7.5rem;
  height: auto;
  position: absolute;
  top: 0;
  display: block;
  z-index: -1;
}


.bg-head .mid .logo {
  width: 1.3rem;
  height: 0.6rem;
  background: url(/act/tldc/collection/20250427/pc/img/logo.png) no-repeat 50% 0 / 100%;
  font-size: 0;
  display: block;
  left: 0.41rem;
  top: 0.5rem;
}
.login-box {
  right: 0;
  top: 0.7rem;
  color: #fff;
  text-align: center;
  font-size: 0.24rem;
  display: flex;
  justify-content: center;
  flex-direction: column;
  width: 100%;
}
.login-box a {
  color: #3a6c9f;
}
.login-box a:hover {
  text-decoration: underline;
}

.scroll-down {
  width: 0.39rem;
  height: 0.26rem;
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  bottom: 0;
  position: absolute;
}
.scroll-down.page0 {
  background-image: url(/act/tldc/collection/20250427/pc/img/arrow1.png);
  background-size: 1.82rem auto;
  width: 1.82rem;
  height: 0.92rem;
}
.scroll-down .arrow-box {
  width: 0.39rem;
  height: 0.26rem;
  margin: 0 auto;
  box-sizing: border-box;
}
.scroll-down.page0 .arrow-box {
  width: 1.82rem;
  height: 0.92rem;
}
.scroll-down .arrow-box img {
  width: 0.39rem;
  position: absolute;
  animation: bounce 1s ease-in-out infinite alternate;
  display: none;
}
.scroll-down.page0 .arrow-box img {
  width: 1.82rem;
}

.scroll-down.page0 .arrow-box .arrow2 {
  display: block;
}
.scroll-down.page0 .arrow-box .arrow3 {
  display: none;
}

.scroll-down .arrow-box .arrow2 {
  display: none;
}
.scroll-down .arrow-box .arrow3 {
  width: 0.67rem;
  display: block;
  bottom: 0;
}

.nav_list {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  top: 2.1rem;
  right: 0;
  background: none;
  position: relative;
  padding-top: 0.2rem;
}
.nav_list.on {
  width: 2.03rem;
  height: 4.02rem;
  background-image: url(/act/tldc/collection/20250427/m/img/nav_bg.png);
  background-repeat: no-repeat;
}
.nav_list .nav_item:nth-child(6),
.nav_list.on .nav_item:nth-child(6),
.nav_list .nav_item:nth-child(7),
.nav_list.on .nav_item:nth-child(7) {
  display: none;
}
.nav_list .nav_item {
  width: 1.42rem;
  height: 0.38rem;
  background: none;
  border-radius: 0;
  margin: 0.08rem 0;
  display: none;
}

.nav_list.on .nav_item {
  display: block;
}

.nav_list .nav_item.swiper-pagination-bullet-active {
  background: url(/act/tldc/collection/20250427/m/img/nav_item_bg.png) no-repeat;
}
.nav_list .nav_item > a,
.btn-activate-go {
  font-size: 0.22rem;
  color: #457bb9;
  line-height: 0.38rem;
  text-align: center;
  display: block;
}
.nav_list .swiper-pagination-bullet {
  opacity: 1;
}
.btn-off,
.btn-on,
.btn-activate-go {
  position: absolute;
  width: 0.32rem;
  height: 0.55rem;
  top: 0.23rem;
  z-index: 99;
  display: none;
}
.btn-activate-go {
  position: absolute;
  width: 1.42rem;
  height: 0.38rem;
  top: 3.02rem;
}
.btn-activate-go.active {
  background-image: url(/act/tldc/collection/20250427/m/img/nav_item_bg.png);
  background-repeat: no-repeat;
  display: block;
}
.btn-on {
  background-image: url(/act/tldc/collection/20250427/m/img/nav_on.png);
  background-repeat: no-repeat;
  display: block;
  right: 0;
}
.btn-off {
  background-image: url(/act/tldc/collection/20250427/m/img/nav_off.png);
  background-repeat: no-repeat;
  display: none;
  right: 1.84rem;
}
.nav_list.on .btn-on {
  display: none;
}
.nav_list.on .btn-off {
  display: block;
}
.nav_list.on .btn-activate-go {
  display: block;
}

@keyframes bounce {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-30%);
  }
}
@keyframes Light {
  0%,
  100% {
    filter: brightness(100%);
  }

  50% {
    filter: brightness(120%);
  }
}

.btn-music {
  background-position: -9.61rem -0.89rem;
  width: 0.39rem;
  height: 0.39rem;
  right: 0.18rem;
  top: 0.16rem;
  transform: scale(1.3);
}
.btn-music.on {
  background-position: -9.61rem -0.5rem;
}

.home_btn_main {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.home_btn_main .slogan {
  width: 7.12rem;
  height: 4.11rem;
  background-position: -3.91rem -9.26rem;
}
.home_btn_main .peopleNum {
  background-image: url(/act/tldc/collection/20250427/pc/img/page01-peopleNumBg.png);
  background-repeat: no-repeat;
  width: 5.52rem;
  height: 0.48rem;
  font-size: 0.24rem;
  color: #fff;
  line-height: 0.48rem;
  margin-top: 0.68rem;
}
.home_btn_main .peopleNum > p > span {
  color: #ffe3ad;
}
.btn_home_order {
  background-image: url(/act/tldc/collection/20250427/pc/img/btn_home_order.png);
  background-repeat: no-repeat;
  width: 2.8rem;
  height: 1.2rem;
}

.btn-order {
  background-position: 0 0;
  width: 3.58rem;
  height: 0.91rem;
  margin-top: 0.22rem;
}
.btn-order.gray {
  background-position: -3.58rem 0;
}

.btn-home-play {
  width: 0.5rem;
  height: 0.5rem;
  right: 0.72rem;
  top: 1.38rem;
  background-position: -9.5rem 0;
  animation: scale_video_btn 3s 0s linear infinite;
  transform: scale(1.2);
}

.btn-box {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

/* p1 */
.page1 .tit-sub {
  background-position: -1.5rem -3.37rem;
  margin-bottom: 0.24rem;
}
.page1 .swiper-container {
  position: relative;
  padding-bottom: 3rem;
}
.page1 .swiper-container.swiper-thumbs {
  padding-bottom: 0;
}
.swiper-page1 .swiper-slide {
  width: 6.98rem;
  height: 3.56rem;
}
.swiper-page1 .img-box {
  width: 6.98rem;
  height: 3.56rem;
  overflow: hidden;
  position: relative;
}
.swiper-page1 .img-box > img {
  width: 6.24rem;
  height: 3.34rem;
  overflow: hidden;
  object-fit: cover;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.swiper-page1 .img-box::after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: url(/act/tldc/collection/20250427/pc/img/lb-bg.png) center no-repeat;
  background-size: contain;
  pointer-events: none;
  width: 100%;
  height: 100%;
}
.page1 .swiper-pagination,
.page4 .swiper-pagination {
  position: relative;
  margin-top: -2.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.page1 .swiper-pagination-bullet,
.page4 .swiper-pagination-bullet {
  background: url(/act/tldc/collection/20250427/pc/img/comm2.png) no-repeat;
  width: 0.16rem;
  height: 0.16rem;
  background-position: -9.58rem -1.78rem;
  margin: 0 0.12rem 0;
  opacity: 1;
}
.page1 .swiper-pagination-bullet-active,
.page4 .swiper-pagination-bullet-active {
  width: 0.24rem;
  height: 0.24rem;
  background-position: -9.76rem -1.74rem;
}
.page1 .swiper-button-prev {
  width: 0.4rem;
  height: 0.62rem;
  background-position: -8.65rem -2.05rem;
  margin-left: 0.08rem;
  margin-top: 2.04rem;
}
.page1 .swiper-button-next {
  width: 0.4rem;
  height: 0.62rem;
  background-position: -8.27rem -2.05rem;
  margin-right: 0.08rem;
  margin-top: 2.04rem;
}
.swiper-thumbs {
  margin-top: 1rem;
  width: 6.1rem;
  margin-top: 0.9rem !important;
}

.swiper-thumbs .swiper-slide {
  width: 1.95rem;
  height: 1.23rem;
  opacity: 0.6;
  transition: opacity 0.3s ease;
  cursor: pointer;
}
.swiper-thumbs .swiper-slide::after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: url(/act/tldc/collection/20250427/m/img/lb-sBg.png) center no-repeat;
  background-size: 100%;
  pointer-events: none;
  width: 100%;
  height: 100%;
}
.swiper-thumbs .swiper-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0.18rem;
}

.swiper-thumbs .swiper-slide-thumb-active,
.swiper-thumbs .swiper-slide:hover {
  opacity: 1;
}

/* p2 */
.page2 .tit-sub {
  background-position: -1.5rem -4.54rem;
}
.page2 .top-box {
  width: 7.5rem;
  height: 0.5rem;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 0 0.4rem;
}
.page2 .top-box > p {
  font-size: 0.22rem;
  color: #fff;
}
.page2 .top-box .btn-rule,
.page3 .btn-rule {
  width: 1.24rem;
  height: 0.34rem;
  background-position: -8.89rem -2.73rem;
  transform: scale(1.3);
}
.page2 .lottery {
  width: 7.5rem;
  height: 6.38rem;
  margin:  0 auto 0.86rem;
}
.page2 .btn-lottery {
  width: 2.62rem;
  height: 0.74rem;
  background-position: -7.38rem -6.52rem;
  margin: 0 auto;
}
.page2 .btn-lottery.gray {
  width: 2.62rem;
  height: 0.74rem;
  background-position: -7.38rem -7.26rem;
}
.page2 .lottery-task {
  background-image: url(/act/tldc/collection/20250427/m/img/page02-taskBg.png);
  background-repeat: no-repeat;
  width: 7.38rem;
  height: 1.71rem;
  font-size: 0.22rem;
  color: #6e9ccb;
  line-height: 0.45rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding-left: 0.5rem;
}
.page2 .lottery-task > p {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 0 0.15rem;
  width: 100%;
}
.page2 .lottery-task > p:nth-child(1) {
  font-size: 0.22rem;
  font-weight: bold;
}
.page2 .lottery-task > p > span.num {
  width: 0.22rem;
  height: 0.22rem;
  background: url(/act/tldc/collection/20250427/pc/img/comm2.png) no-repeat;
  background-position: -8.67rem 0;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.24rem;
  font-size: 0.18rem;
}
.page2 .lottery-task > p:nth-child(3) > span:nth-child(2) {
  line-height: 0.34rem;
  text-align: start;
  width: 50%;
}
.page2 .lottery-task .red {
  color: #b15c35;
  font-weight: bold;
  margin: 0 0.08rem;
}
.page2 .lottery-task .red:nth-child(3) {
  margin-right: 0.46rem;
}
.page2 .btn-invite {
  width: 1.28rem;
  height: 0.34rem;
  background-position: -8.72rem -3.07rem;
  transform: scale(1.3);
}

/* p3 */
.page3 .tit-sub {
  background-position: -1.5rem -5.71rem;
}
.page3 .btn-rule {
margin: 0 auto;
}
.page3 .invite-box {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  height: 1.3rem;
  position: relative;
  margin-bottom: 0.24rem;
}
.page3 .invite-box .number {
  font-size: 0.2rem;
  color: #b15c35;
  font-weight: bold;
  position: absolute;
  bottom: -0.1rem;;
  display: none;
}
.page3 .invite-box .item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 0 0.5rem;
  position: relative;
  width: 1rem;
  height: 100%;
}
.page3 .invite-box .item .btn-invite {
  width: 0.94rem;
  height: 0.94rem;
  background-position: -9.12rem -3.41rem;
  position: relative;
  transform: scale(1.1);
}
.page3 .invite-box .item .btn-invite > img {
  width: 0.72rem;
  height: 0.72rem;
  border-radius: 50%;
  overflow: hidden;
  position: absolute;
  left: 0.07rem;
  top: 0.1rem;
}
.page3 .invite-box .item .btn-invite.active::after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0.05rem;
  bottom: 0;
  background: url(/act/tldc/collection/20250427/pc/img/comm2.png) no-repeat;
  background-position: -8.15rem -3.41rem;
  width: 0.94rem;
  height: 0.94rem;
  display: none;
}
.page3 .invite-box .item .btn-inviteChange {
  width: 0.88rem;
  height: 0.35rem;
  background-position: -9.12rem -4.65rem;
  bottom: 0;
  right: 0.08rem;
  position: absolute;
  display: none;
}
.page3 .invite-box .item .btn-inviteChange.out {
  background-position: -8.24rem -4.65rem;
}
.page3 .btn-box > a {
  width: 2.33rem;
  height: 0.54rem;
}
.page3 .btn-box > a:nth-child(2) {
  margin-left: 0.36rem;
}
.page3 .btn-createSteam {
  background-position: 0 -0.91rem;
}
.page3 .btn-createSteam.gray {
  background-position: 0 -1.45rem;
}
.page3 .btn-join {
  background-position: -2.33rem -0.91rem;
}
.page3 .btn-join.gray {
  background-position: -2.33rem -1.45rem;
}
.page3 .btn-check {
  background-position: -4.66rem -0.91rem;
  display: none;
}
.page3 .prize-box {
  width: 7.19rem;
  height: 3.7rem;
  position: relative;
  background: url(/act/tldc/collection/20250427/m/img/page02-prizeLine.png) no-repeat;
  background-size: 7.19rem 0.84rem;
  background-position: top center;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 0.28rem;
  margin-top: 0.3rem;
}
.page3 .prize-item {
  width: 1.6rem;
  height: 2.79rem;
  background: url(/act/tldc/collection/20250427/m/img/prize-bg.png) no-repeat;
  background-size: 100% auto;
  background-position: center;
  position: absolute;
  top: 0.52rem;
}
.prize-item.done::after{
    content: "";
    position: absolute;
    top: -0.2rem;
    right: -0.2rem;
    background: url(/act/tldc/collection/20250427/pc/img/done-icon.png) no-repeat;
    background-size: 100% auto;
    width: .64rem;
    height: .65rem;
}
.page3 .prize-item:nth-child(1) {
  left: 0.06rem;
  top: 0.86rem;
}
.page3 .prize-item:nth-child(2) {
  left: 1.88rem;
}
.page3 .prize-item:nth-child(3) {
  left: 3.68rem;
  top: 0.86rem;
}
.page3 .prize-item:nth-child(4) {
  left: 5.5rem;
}
.page3 .prize-item>img {
    width: 1rem;
    height: 1rem;
    overflow: hidden;
    margin: 0.36rem auto 0;
}
.page3 .prize-item > p {
  position: absolute;
  text-align: center;
  width: 100%;
  user-select: none;
}
.page3 .prize-item .name,
.page3 .prize-pop .item > p,
.page3 .name-box .item > p {
  font-size: 0.2rem;
  color: #3a6c9f;
  text-shadow: 2px 0 0 #fff, -2px 0 0 #fff, 0 2px 0 #fff, 0 -2px 0 #fff;
  top: 1.58rem;
  line-height: 0.3rem;
}
.page3 .prize-item .name{
    line-height: 0.22rem;
    top: 1.46rem;
}
.page3 .prize-item .title {
  font-size: 0.2rem;
  color: #fff;
  line-height: 0.44rem;
  top: 2.24rem;
}
.page3 .prize-item .title > span {
  font-size: 0.24rem;
  color: #fff4a4;
}
.page3 .prize-pop-box {
  background-image: url(/act/tldc/collection/20250427/pc/img/hover-prize.png);
  background-repeat: no-repeat;
  width: 6.5rem;
  height: 3.5rem;
  position: absolute;
  bottom: 0.5rem;
  display: none;
  transform: scale(0.7);
  pointer-events: none;
}
.page3 .prize-pop {
  display: flex;
  flex-direction: row;
  justify-content: center;
  position: absolute;
  top: 0.5rem;
  left: 50%;
  transform: translateX(-50%);
}
.page3 .prize-pop .item {
  width: 1.5rem;
  height: 1.8rem;
  position: relative;
  background: url(/act/tldc/collection/20250427/pc/img/page02-avatarBg.png) no-repeat;
  background-size: 1.45rem 1.4rem;
  background-position: top center;
  margin: 0.5rem 0.12rem 0;
}
.page3 .prize-pop .item > img {
    width: 0.8rem;
    height: 0.8rem;
    overflow: hidden;
    margin-top: 0.3rem;
}
.page3 .prize-pop .item > p {
  position: absolute;
  top: 1.4rem;
  width: 100%;
  text-align: center;
}
.page3 .get-name {
  width: 6.19rem;
  height: 1.67rem;
  position: relative;
  background: url(/act/tldc/collection/20250427/m/img/page02-itemBg.png) no-repeat;
  background-size: 100% auto;
  background-position: center;
  left: 50%;
  transform: translateX(-50%);
}
.page3 .get-name .user-status{
    width: 2.06rem;
    position: absolute;
    top: 0.34rem;
    left: 50%;
    transform: translateX(-50%);
    font-size: .2rem;
    color: #fff;
    text-align: center;
    line-height: .34rem;
}
.page3 .get-name .user-status>span{color: #fff4a4;}
.page3 .name-box {
  width: 5.8rem;
  height: 1.24rem;
  right: 0.18rem;
  position: absolute;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-evenly;
  bottom: 0;
}
.page3 .name-box .item {
  width: 100%;
  height: 1rem;
  position: relative;
  background: url(/act/tldc/collection/20250427/pc/img/page02-avatarBg.png) no-repeat;
  background-size: 1.06rem 1rem;
  background-position: top center;
}
.page3 .name-box .item > img {
  width: 0.7rem;
  height: 0.7rem;
  border-radius: 50%;
  overflow: hidden;
  margin-top: 0.16rem;
  margin-bottom: 0.16rem;
}
.page3 .name-box .item > p {
  top: 0.66rem;
  position: absolute;
  width: 100%;
}

/* p4 */
.page4 .tit-sub {
  background-position: -1.5rem -6.88rem;
  margin-bottom: 0.1rem;
}

.page4 .container {
  width: 7.5rem;
  height: 8.52rem;
  margin-top: 0.3rem;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.page4 .swiper-container {
  width: 6.98rem;
  min-height: 3.9rem;
  margin: 0;
  overflow: hidden;
  padding-bottom: 0.4rem;
}
.page4 .swiper-pagination {
  margin-top: 0.26rem;
}
.page4 .swiper-slide {
  width: 6.98rem;
  height: 3.56rem;
  overflow: hidden;
}
.swiper-page4 .swiper-slide > a {
  height: 3.56rem;
  overflow: hidden;
  display: block;
  position: relative;
}
.swiper-page4 .swiper-slide > a > img {
  width: 6.74rem;
  height: 3.34rem;
  overflow: hidden;
  object-fit: cover;
  position: absolute;
  left: 0.12rem;
  top: 0.11rem;
}
.swiper-page4 .swiper-slide::after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: url(/act/tldc/collection/20250427/pc/img/lb-bg.png) center no-repeat;
  background-size: 100% 3.56rem;
  pointer-events: none;
  width: 100%;
  height: 100%;
}
.page4 .news-list {
  width: 6.88rem;
  height: 4.97rem;
  position: relative;
  background: url(/act/tldc/collection/20250427/m/img/page04-newsBg.png) no-repeat;
  background-size: 100% auto;
  background-position: center;
  bottom: -0.1rem;
}
.page4 .news-list .news-buttons {
  height: 0.84rem;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  position: absolute;
  left: 0;
  top: 0.24rem;
}
.page4 .news-list .news-buttons .btn-tab {
  width: 0.96rem;
  height: 0.64rem;
  margin: 0 0.16rem;
  position: relative;
}
.page4 .news-list .news-buttons .btn-tab > span {
  font-size: 0.2rem;
  color: #3a6c9f;
  line-height: 0.54rem;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}
.page4 .news-list .news-buttons .btn-tab.active {
  background: url(/act/tldc/collection/20250427/pc/img/comm2.png) no-repeat;
  background-position: -7.16rem -1.3rem;
}
.page4 .news-list .news-buttons .btn-tab.active > span,
.nav_list .nav_item.swiper-pagination-bullet-active > a,
.btn-activate-go.active > span {
     color: #b34012;
}
.page4 .news-list .btn-more {
  width: 0.92rem;
  height: 0.23rem;
  background-position: -9.08rem -4.42rem;
  right: 0.5rem;
  top: 0.5rem;
}
.page4 .news-list .news-content {
  width: 5.9rem;
  height: 3.5rem;
  margin: 1.3rem auto 0;
}
.page4 .news-list .news-content .news-pane {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 0.1rem;
  display: none;
}
.page4 .news-list .news-content .news-pane.active {
  display: block;
}
.page4 .news-list .news-content .news-pane .title {
  color: #3a6c9f;
  text-align: start;
  margin-left: 0.26rem;
  margin-right: auto;
      width: 3.8rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.page4 .news-list .news-content .news-pane .text {
  height: 0.48rem;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  border-bottom: 0.01rem solid #fff;
  font-size: 0.18rem;
}
.page4 .news-list .news-content .news-pane .text > p {
  font-size: 0.18rem;
}
.page4 .news-list .news-content .news-pane .text .type {
  color: #fff;
  background-color: #87c9f5;
  padding: 0.02rem 0.08rem;
}
.page4 .news-list .news-content .news-pane .text .time {
  color: #769ec4;
}

/* p5 */
.page5 {
  padding-top: 2.2rem;
}
.page5 .tit-sub {
  background-position: -1.5rem -8.09rem;
}
.page5 .key {
  background-image: url(/act/tldc/collection/20250427/m/img/page05-ipt.png);
  background-repeat: no-repeat;
  width: 6.9rem;
  height: 1.25rem;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.3rem;
  color: #6e9ccb;
  line-height: 1.25rem;
  margin: 1rem auto;
  text-indent: 0.5rem;
}
.page5 .btn-activate {
  width: 3.58rem;
  height: 0.9rem;
  background-position: 0 -2.36rem;
  margin-top: 3rem;
  left: 50%;
  transform: translateX(-50%);
}
.page5 .btn-activate.activated {background-position: -3.58rem -2.36rem;}
.page5 .btn-activate.gray {background-position: 0 -3.26rem;}

/* bottom */
.ss_bot {
  height: 0.9rem !important;
  background: #f0f0f0;
}
.ss_bot * {
  box-sizing: content-box;
}

/* ------- pop -------- */
.pop {
  background: url(/act/tldc/collection/20250427/pc/img/pop.png) no-repeat center;
  width: 6.07rem;
  height: 3.83rem;
  font-size: 0.22rem;
  color: #274c70;
  line-height: 0.36rem;
  text-align: center;
  position: relative;
  padding: 1.8rem 0.5rem 0.5rem 0.5rem;
  display: none;
}
.pop.pop1 {
  background: url(/act/tldc/collection/20250427/pc/img/pop1.png) no-repeat;
  width: 6.07rem;
  height: 4.53rem;
  padding: 0;
  padding-top: 0.3rem;
}
.pop.pop-video {
  background: url(/act/tldc/collection/20250427/pc/img/pop-video.png) no-repeat;
  width: 8.37rem;
  height: 4.93rem;
  padding: 0.11rem;
}
.pop .close {
  width: 0.35rem;
  height: 0.34rem;
  background-position: -9.65rem -5rem;
  position: absolute;
  right: 0.2rem;
  top: 0.9rem;
}
.pop .tit-pop {
  background-position: center -4.4rem;
  height: 0.6rem;
  text-align: center;
  margin-bottom: 0.1rem;
}
.pop .tit-pop strong {
  font-size: 0.32rem;
  color: #12569a;
}
.pop p > strong {
  font-size: 0.22rem;
  color: #12569a;
  font-weight: bold;
}
.icon-bind,
.icon-unbind {
  width: 1.33rem;
  height: 1.3rem;
  margin: 0.1rem auto;
}
.icon-bind {
  background-position: 0 -3.1rem;
}
.icon-unbind {
  background-position: -1.34rem -3.1rem;
}
.btn-bind {
  width: 2.04rem;
  height: 0.54rem;
  background-position: 0 -2.5rem;
}
.list-login li {
  width: 50%;
  float: left;
  font-size: 0.2rem;
  line-height: 1.3;
}
.pop2 {
  background-image: url(/act/tldc/collection/20250427/pc/img/pop1.png);
  height: 2.24rem;
}
.pop2 .txt {
  padding-top: 0.1rem;
  line-height: 1.4;
}

.pop .p-tips {
  height: 40%;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.pop.pop-getCode .p-tips {
  height:60%;
	margin-bottom: .2rem;
}
.pop.pop-getCode{
	padding: .6rem .05rem .05rem .05rem;
	height: 3.23rem;
}
.pop .p-tips .copy {
  display: inline-block;
  white-space: normal;
  word-break: break-all;
  max-width: 80%;
}
.pop.confirm {
    padding-top: 0.7rem;
}
.pop.confirm .close {
    right: -0.3rem;
    top: -0.24rem;
}
.pop.confirm .p-tips {
  height: 50%;
}
.pop.confirm .p-tips .red {
  color: #ad2e00;
}
.pop .p-tips > span {
  display: contents;
}
.pop .pop-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.pop .pop-btn > a {
  width: 2.31rem;
  height: 0.64rem;
}
.pop .pop-btn.small > a {
  width: 1.81rem;
  height: 0.56rem;
}
.pop .btn-leaderOut {
  background-position: 0 -7.8rem;
}
.pop .btn-confirmJoin {
  background-position: 0 -8.44rem;
}
.pop .btn-cancel {
  background-position: 0 -9.08rem;
}
.pop .btn-copy {
  background-position: 0 -9.72rem;
}
.pop .btn-userInfo {
  background-position: 0 -10.36rem;
}
.pop .btn-copyCode {
  background-position: 0 -11rem;
}
.pop .btn-goActivate {
  background-position: 0 -11.64rem;
}
.pop .pop-btn.small .btn-copyCode {
  background-position: 0 -5.34rem;
}
.pop .pop-btn.small .btn-goActivate {
  background-position: -1.81rem -5.34rem;
}
.pop .pop-btn.small .btn-userInfo {
  background-position: -3.62rem -5.34rem;
}
.pop .btn-retype {
  background-position: 0 -12.28rem;
}
.pop .btn-activate {
    background-position: -2.43rem -8.48rem;
    text-indent: 0;
    color: #fff;
    display: block;
}
.pop .btn-timeOver {
  background-position: 0 -12.92rem;
}
.pop .btn-confirmOut{background-position: -2.43rem -7.16rem;}
.pop .btn-cancelOut{background-position: -2.43rem -7.84rem;}
.pop .btn-addMsg{background-position: -2.43rem -9.12rem;}
.pop .btn-activate>span{
    display: block;
    line-height: 0.5rem;
    text-align: center;
    margin-left: 1.12rem;
    width: .3rem;
    font-weight: bold;
}
.pop .btn-changeUser {
  background-position: 0 -13.56rem;
}
.pop .btn-confirmTeam {
  background-position: -2.43rem -6.52rem;
}

.pop .video-box {
  width: 8.14rem;
  height: 4.7rem;
  overflow: hidden;
}
.pop .video-box > video {
  width: 100%;
  height: 100%;
}
.pop.pop-video .close {
  right: -0.04rem;
  top: -0.28rem;
}

.pop.pop-rule {
  padding-top: 1rem;
}
.pop-con {
  width: 90%;
  height: 2.7rem;
  overflow-x: hidden;
  overflow-y: auto;
  margin: 0 auto;
  text-align: start;
}
.pop.pop-rule .tit-pop {
  height: 0.36rem;
}

.pop .input > input,
.pop .input > textarea {
  width: 3.78rem;
  height: 0.44rem;
  border: 0.01rem solid #1b83c3;
  background-color: #e7f3ff;
  margin-bottom: 0.1rem;
  font-size: 0.26rem;
  line-height: 0.44rem;
  padding: 0 0.1rem;
}
.pop .form-item {
  display: flex;
    justify-content: flex-start;
    margin-left: 0.2rem;
}
.pop .form-item > label {
    width: 1.1rem;
  text-align: start;
  margin-right: 0.1rem;
}
.pop .form .address-tips {
  margin-left: 1.4rem;
  text-align: left;
  color: red;
  margin-top: -0.1rem;
}
.pop .form-item.verification-code .input > input {
  width: 2.7rem;
}
.pop .btn-code,
.pop .btn-address {
  font-size: 0.2rem;
  color: #1b83c3;
  height: 0.44rem;
  line-height: 0.44rem;
  margin-left: 0.1rem;
  border-bottom: 0.01rem solid;
}
.pop .btn-order {
  background-position: 0 -14.2rem;
}
.pop .form {
  margin-top: 0.2rem;
}

.pop.pop1 .tit-pop {
  width: 1.98rem;
  height: 0.31rem;
  margin: .2rem auto 0.18rem;
}
.pop.userInfo .tit-pop {
  background-position: 0 -13.37rem;
}
.pop .user-buttons {
  display: flex;
  justify-content: center;
}
.pop .btn-tab {
  width: 1.42rem;
  height: 0.4rem;
}
.pop .btn-tab:nth-child(1) {
  background-position: -8.58rem -5.34rem;
}
.pop .btn-tab:nth-child(1).active {
  background-position: -7.12rem -5.34rem;
}
.pop .btn-tab:nth-child(2) {
  background-position: -8.58rem -5.8rem;
}
.pop .btn-tab:nth-child(2).active {
  background-position: -7.12rem -5.8rem;
}
.pop .user-content {
  width: 90%;
  margin: 0 auto;
  height: 2.52rem;
  overflow-x: hidden;
  overflow-y: auto;
  margin: 0 auto;
}
.pop .user-pane {
  display: none;
  position: relative;
}
.pop .user-pane.active {
  display: block;
}
.pop .user-pane > table {
  width: 100%;
  font-size: 0.2rem;
  color: #333333;
  line-height: 0.48rem;
  text-align: center;
  background-color: #e0f1ff;
}
.pop .user-pane > table > tbody > tr > th,
.pop .user-pane > table > tbody > tr > td {
  border: 0.01rem solid #b5d6f3;
}
.pop .btn-address {
  position: fixed;
  margin-bottom: -0.38rem;
  margin-left: 1rem;
}
.pop .addrSuc {
  position: absolute;
  left: 0;
}
.pop .user-pane > table > tbody > tr > td:nth-child(1) {
  max-width: 1.8rem;
  word-wrap: break-word;
  line-height: 0.3rem;
}
.pop.userInfo .close,
.pop.apply .close,
.pop.address .close {
  right: -0.3rem;
  top: -0.28rem;
}
.pop.pop-rule .close {
    top: 0.5rem;
}
.pop.pop-getCode .close {
    right: -0.2rem;
    top: -0.2rem;
}
.pop.apply .tit-pop {
  background-position: 0 -13.98rem;
}
.pop .apply-list > ul {
  width: 94%;
  margin: 0 auto;
  height: 3rem;
  overflow-x: hidden;
  overflow-y: auto;
}
.pop .apply-list > ul > li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.18rem;
  color: #333333;
  line-height: 0.56rem;
  text-align: center;
  height: 0.56rem;
  border-bottom: 0.01rem solid #98c3ec;
}
.pop .apply-list > ul > li > img {
  width: 0.4rem;
  height: 0.4rem;
  overflow: hidden;
  border-radius: 50%;
  border: 0.01rem solid #b5d6f3;
}
.pop .apply-list > ul > li > a {
  width: 0.9rem;
  height: 0.4rem;
}
.pop .btn-agree {
  background-position: 0 -6rem;
}
.pop .btn-ignore {
  background-position: -1rem -6rem;
}

.pop.address .tit-pop {
  width: 2.98rem;
  background-position: 0 -13.68rem;
    margin-left: 1.7rem;
  margin-bottom: 0.5rem;
}
.pop .input > textarea {
  height: 0.86rem;
}
.pop.address .form-item > label {
  text-align: center;
}
.pop .btn-confirm {
  background-position: 0 -6.52rem;
}

.pop .btn-applyJoin {
  background-position: 0 -7.16rem;
}
.pop-home-prize {
  background: url(/act/tldc/collection/20250427/pc/img/home-prize-pop.png) no-repeat center;
  width: 3.81rem;
  height: 1.66rem;
  font-size: 0.16rem;
  line-height: 0.18rem;
  color: #13325b;
  bottom: -1.4rem;
  position: absolute;
  padding-left: 0.3rem;
  display: none;
}
.pop-home-prize > ul {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
    margin: 0.3rem auto 0.2rem;
}
.pop-home-prize > ul > li {
  width: 0.72rem;
  height: 0.72rem;
  background: url(/act/tldc/collection/20250427/pc/img/home-prize.png) no-repeat center;
  position: relative;
  display: flex;
  margin: 0 0.06rem;
}
.pop-home-prize>ul>li>img{
    width: 0.4rem;
    height: 0.4rem;
    bottom: 0.2rem;
    position: absolute;
}
.pop-home-prize > ul > li > span {
  color: #fff;
  position: absolute;
  bottom: -0.2rem;
  width: 1.4rem;
  font-size: 0.14rem;
}

/* animation In&Out */
.toRight,
.toRightSmall,
.toLeft,
.toLeftSmall,
.toBottom,
.toBottomSmall,
.toTop,
.toTopSmall,
.scaleIn {
  -webkit-transition: all 0.6s;
  opacity: 0;
}
.toRight {
  -webkit-transform: translate3d(-100%, 0, 0);
}
.toRightSmall {
  -webkit-transform: translate3d(-20%, 0, 0);
}
.toLeft {
  -webkit-transform: translate3d(100%, 0, 0);
}
.toLeftSmall {
  -webkit-transform: translate3d(20%, 0, 0);
}
.toBottom {
  -webkit-transform: translate3d(0, -100%, 0);
}
.toBottomSmall {
  -webkit-transform: translate3d(0, -30%, 0);
}
.toTop {
  -webkit-transform: translate3d(0, 100%, 0);
}
.toTopSmall {
  -webkit-transform: translate3d(0, 10%, 0);
}
.scaleIn {
  -webkit-transform: scale(1.4, 1.4);
}

.swiper-slide-m.swiper-slide-active .toTopSmall {
  -webkit-transform: translate3d(0, 0, 0);
  opacity: 1;
}
.swiper-slide-m.toTopSmall.swiper-slide-active {
  -webkit-transform: translate3d(0, 0, 0);
  opacity: 1;
}
.swiper-slide.swiper-slide-m.toTopSmall.ss_bot.swiper-slide-next {
  -webkit-transform: translate3d(0, 0, 0);
  opacity: 1;
}

/* transition delay */
.swiper-slide-m.swiper-slide-active {
  -webkit-transition-delay: 0.1s;
}
.swiper-slide-m.delay1.swiper-slide-active {
  -webkit-transition-delay: 0.1s;
}
.swiper-slide.swiper-slide-m.delay1.ss_bot.swiper-slide-next {
  -webkit-transition-delay: 0.1s;
}

.swiper-slide-m.swiper-slide-active .delay3 {
  -webkit-transition-delay: 0.3s;
}

@keyframes sloganAni {
  0% {
    transform: scale(1.8);
    opacity: 0;
  }
  60% {
    transform: scale(1);
    opacity: 1;
  }
  65% {
    transform: translate(-4px, -4px);
  }
  70% {
    transform: translate(0, 0);
  }
  75% {
    transform: translate(4px, 4px);
  }
  80% {
    transform: translate(0, 0);
  }
  85% {
    transform: translate(-4px, 4px);
  }
  90% {
    transform: translate(0, 0);
  }
  95% {
    transform: translate(4px, -4px);
  }
  100% {
    transform: translate(0, 0);
    opacity: 1;
  }
}
@keyframes -webkit-sloganAni {
  0% {
    -webkit-transform: scale(1.8);
    opacity: 0;
  }
  60% {
    -webkit-transform: scale(1);
    opacity: 1;
  }
  65% {
    -webkit-transform: translate(-4px, -4px);
  }
  70% {
    -webkit-transform: translate(0, 0);
  }
  75% {
    -webkit-transform: translate(4px, 4px);
  }
  80% {
    -webkit-transform: translate(0, 0);
  }
  85% {
    -webkit-transform: translate(-4px, 4px);
  }
  90% {
    -webkit-transform: translate(0, 0);
  }
  95% {
    -webkit-transform: translate(4px, -4px);
  }
  100% {
    -webkit-transform: translate(0, 0);
    opacity: 1;
  }
}
/* 适配 375*554*/
@media screen and (max-device-width: 375px) and (max-device-height: 554px) and (orientation: portrait){
  .page-bg0 {
    height: 100vh;
    object-fit: cover;
}
.page {
  padding-top: 0;
}
.page.page0 {
    padding-top: 2rem;
}
.bg-head .mid .logo {
    left: 0.1rem;
    top: 0.1rem;
}
.login-box {
    left: 50%;
    transform: translateX(-50%);
    top: 0.16rem;
    justify-content: center;
}
.page2 .content{
      transform: scale(.9);
    top: 0.2rem;
}
}