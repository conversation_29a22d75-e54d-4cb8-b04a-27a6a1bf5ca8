@import "ani.css";
@charset "utf-8";
html,body,div,p,ul,li,dl,dt,dd,em,i,span,a,img,input,h1,h2,h3,h4,h5{margin:0;padding:0;}
a,img,input{border:none;outline:none;}
a{text-decoration:none;}
img{display:block;}
ul,li{list-style:none;}
table{border-collapse:collapse;border-spacing:0;}
body{font:24px/1.5 -apple-system,"Helvetica Neue",Helvetica,Arial,sans-serif;-webkit-user-select: none;background-color:#fff;}
.none{display:none;}
input::-webkit-outer-spin-button,input::-webkit-inner-spin-button{-webkit-appearance: none !important;}
input[type="number"]{-moz-appearance:textfield;}
*{-webkit-appearance:none;-webkit-text-size-adjust:none;}
html{-webkit-tap-highlight-color:rgba(0,0,0,0);}
#cy_bot{padding:10px!important;position: relative;z-index: 2}
.cyLogo{margin-top:10px;}
.orientLayer {
    display: none;
    position: fixed;
    z-index: 1000;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, .9);
    color: #fff;
    font-size: 30px;
}

/* 页面样式 */
html,body{width:100%;}
.pr{position: relative;}
.pa{position: absolute;}
.hide{font-size: 0; text-indent: -9999em; width: 0; height: 0;}
.t{font-size: 0; text-indent: -9999em;display: block}
.bg{background-repeat: no-repeat; background-size: 100% auto;}

.main{width: 750px;height: 100vh;position: relative;overflow: hidden}
.page{width: 100%;height: 1624px;position: absolute;top: 50%;margin-top: -812px;z-index: -1; opacity: 0;}
.page.show{z-index: 2; animation: ani-fade-left-in .8s 0s both;}
.page_index{background: url("../img/bg.jpg") no-repeat; background-size: 100% auto;}
.page_main, .page_custom{background: url("../img/bg2.jpg") no-repeat; background-size: 100% auto;}
.mid{position: absolute; width: 750px; height: 1200px; margin-top: -600px; top: 50%; left: 0;}
.page_main .mid, .page_custom .mid{margin-top: -550px;}
.btn_rule{right: 0; top: 140px; background-image: url("../img/btn_rule.png"); width: 45px; height: 239px; z-index: 2;}
.login-info{top: 0; right: 0; padding-right: 10px; font-size: 24px; color: #793a01;position: absolute;}
.login-info a{color: #793a01;}
.logo{top: 0; left: 10px; background-image: url("../img/logo.png"); width: 136px; height: 63px;}
.con{width: 612px; position: absolute;top: 372px;left: 68px;}
.info_wrap{width: 100%; height: 733px; display: flex; flex-direction: column; align-items: center; color: #793a01; font-size: 24px;}
.btn_start{background-image: url("../img/btn_start.png"); width: 355px; height: 84px;margin: 20px auto;}
.info_wrap span{color: #3ca427}

.gift-info{padding: 24px 70px;display: flex;width: 100%; box-sizing: border-box}
.gift-attr{flex: 1;margin-left: 24px;color: #6d3802; font-size: 24px;display: flex; flex-direction: column; justify-items: center; line-height: 40px;}
.star-wrap{width: 100%; height: 64px; padding: 0 70px; box-sizing: border-box; display: flex; justify-content: space-between; align-items: center;position: relative;}
.star-wrap::before,.star-wrap::after{content: ''; background: url("../img/line_sp.png") no-repeat; background-size: 100% 100%; width: 599px; height: 3px;position: absolute;left: 50%; margin-left: -300px;}
.star-wrap::before{ top: -3px; }
.star-wrap::after{ bottom: -3px; }
.star-wrap label{font-size: 24px; }
.star_list{width: 250px; height: 25px; display: flex; justify-content: space-between; align-items: center;}
.star_list li{width: 26px; height: 25px; background: url("../img/star.png") no-repeat; background-size: 100% 100%; }
.star_list li.on{background-image: url("../img/star_on.png");}
.base_attr{padding: 24px 70px 18px;width: 100%; box-sizing: border-box}
.list_attr{width: 100%;}
.item_attr{width: 564px; margin: 0 auto 8px; position: relative; background: url("../img/item.png") no-repeat center/100% 100%; height: 40px; padding: 0 70px;box-sizing: border-box; display: flex; justify-content: space-between;}
.main_info{background: url("../img/bg_con.png") no-repeat; background-size: 100% auto; width: 749px;height: 1165px;padding: 197px 0 0 18px; box-sizing: border-box;}
.main_info .info_wrap{width: 526px; height: 750px;}
.main_info .gift-info{padding: 0 0  24px 50px;}
.main_info .star-wrap{padding: 0 50px; width: 534px; box-sizing: border-box;}
.main_info .star-wrap::before, .main_info .star-wrap::after{width: 520px; margin-left: -260px;}
.main_info .base_attr{ padding: 30px 44px 18px;}
.main_info .item_attr{width: 478px; padding: 0 24px;}
.act-info{width: 604px;margin-left: 58px;}
.act-info span{color: #d73800;}
.btn_back{background-image: url("../img/btn_back.png"); width: 146px; height: 51px;position: absolute; top: -44px; left: 10px;}

.page_main .main_info{background-image: url("../img/bg_con2.png"); height: 1165px; padding-top: 164px;}
.page_main .main_info .info_wrap{height: 734px;}
.page_main .act-bot{width: 646px;margin-left: 30px; display: flex; justify-content: space-between; }
.page_main .act-info{width: 414px;margin-top: 30px;margin-left: 0px;}
.qr-box{width: 205px; height: 205px; background-color: #b39854; padding: 5px; box-sizing: border-box;}
.qr-box img, .qr-box canvas{width: 195px; height: 195px; object-fit: contain}

.poster-save-img{
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 9;
    opacity: 0;
}


/* 弹窗 */
.pop{width: 620px;height: 420px;background: url(../img/pop1.png) no-repeat;position: relative;display: none;background-size: 100% 100%; margin-left: -30px;}
.pop2{background-image: url("../img/pop2.png"); height: 349px;}
.pop3{background-image: url("../img/pop3.png"); height: 1072px;}
.pop_close{background: url("../img/close.png") no-repeat center/100%; width: 34px;height: 34px;position: absolute;z-index: 2;top:20px;right: -22px;text-indent:-9999em;cursor: pointer;}
.pop_wrap{width: 556px;margin-left: 62px; padding-top: 74px;}
.pop_con{color: #6d3802; font-size: 22px;}
.pop_msg_con{width: 454px;margin: 0 auto;}
.pop_alert .pop_con{height: 190px; display: flex; align-items: center; text-align: center}
.tits{background: url("../img/tits.png") no-repeat; background-size: 100% auto; width: 414px; height: 41px; margin: 0 auto;}
.pop_get .tits{background-position: 0 0;}
.pop_make_fail .tits{background-position: 0 -100px;}
.pop_account_stop .tits{background-position: 0 -200px;}
.pop_login_type .tits{background-position: 0 -300px;}
.pop_make_success .tits{background-position: 0 -400px;}
.tit-record.tits{background-position: 0 -500px;}
.tit-rule.tits{background-position: 0 -600px;}

body .pop{color: #6d3802}
.pop_login_type .pop_wrap{color: #6d3802}
.login-type-list{display: flex; justify-content: space-between; width: 452px; margin: 20px auto 20px;}
/*.login-type-list li{display: flex;flex}*/
.login-type-list li{font-size: 18px; text-align: center;}
.login-type-list .login-icon{width: 132px;height: 146px; background-size: 100% 100%; margin: 6px auto 0; display: flex; flex-direction: column; justify-content: flex-end; align-items: center; box-sizing: border-box;padding-bottom: .06rem;}
.login-icon span{font-size: 20px; color: #6d3802; line-height: 24px; width: 80%; overflow: hidden;text-overflow: ellipsis; word-break: break-all; white-space: nowrap;}
.login-icon i{display: block; text-align: center; line-height: 24px;font-size: 20px; color: #6d3802; font-style: normal; }
.login-type-list .login-wx{background-image: url("../img/icon_bind_wx.png");}
.login-type-list .btn_bind_wx{background-image: url("../img/btn_bind.png"); width: 114px; height: 33px; margin-bottom: 10px;}
.login-type-list .login-cy{background-image: url("../img/icon_bind_cy.png");}
.login-type-list .login-wegame{background-image: url("../img/icon_bind_wegame.png");}
.login-type-tip{font-size: 20px; text-align: center;}
.is_bind_wx{display: none;}
.un_bind_wx{display: block;}

.pop-gifts{display: flex; justify-content: center; margin: 28px auto 20px;}
.pop-gift-item{margin: 0 50px;}
.pop-gift-item .pop-gift-box{width: 114px; height: 105px; margin: 0 auto 6px; background-image: url("../img/gift_box.png")}
.pop-gift-box img{width: 100%; height: 100%; object-fit: contain}
.pop-gift-name{font-size: 22px;text-align: center;}
.pop-gift-tip{text-align: center; font-size: 24px; padding: 0 60px;}

.pop_rule .pop_con{width: 504px;margin: 16px auto 48px;}
.pop_rule table{width: 100%;}
.pop_rule table td{color: #6d3802; font-size: 24px; text-align: center; height: 56px; border: 2px solid #d6b276;}
.pop_con_rule{height: 587px; overflow: auto; font-size: 20px; color:#6d3802; line-height: 38px; }
.pop_con_rule h3{font-weight: normal; font-size: 22px;}
.pop_con_rule p{text-indent: -30px;margin-left: 30px; }
.pop_con_rule i{width: 22px; height: 22px; font-size: 18px; text-indent: 0; margin-right: 10px; color: #ffffff; background: #c9ac7d; border-radius: 50%; display: inline-block; text-align: center; line-height: 22px;}
.rule-img{width: 414px; margin-left: 36px;margin-top: 30px;}
.rule-img img{width: 352px; height: 174px; object-fit: contain; margin-left: 20px;}
.pop_con_rule span{color: #d73800}

.pop_make_success .main_info{background-image: url("../img/pop_info_box.png"); width: 514px; height: 800px; margin: 20px auto; padding-top: 92px;}
.pop_make_success .main_info .info_wrap{width: 476px;}
.pop_make_success .main_info .base_attr{padding: 30px 22px 18px;}
.pop_make_success .main_info .star-wrap::before, .pop_make_success .main_info .star-wrap::after {width: 486px; margin-left: -243px; }
.pop_make_success .btns{display: flex; justify-content: space-between; width: 518px; margin: 0 auto;}
.pop_make_success .btns a{ width: 251px; height: 53px; display: block}
.btn_share{background-image: url("../img/btn_share.png");}
.btn_retry{background-image: url("../img/btn_retry.png");}
