/***public***/
a { text-decoration: none; }
input[type="text"], input[type="password"], select { -webkit-appearance: none; appearance: none; outline: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); border-radius: 0; box-sizing: border-box; }
html, a, button, input { -webkit-tap-highlight-color: rgba(0, 0, 0, 0); }
i, em { font-style: normal; }
html { font-size: 100px; }
body, html { -webkit-tap-highlight-color: transparent; -webkit-text-size-adjust: none; -webkit-user-select: none; }
a { text-decoration: none; blr: expression(this.onFocus=this.blur()); outline: none; }
a:focus, a:active, a { outline: none; }
body { font-size: 22px; color: #9f6158; text-align: center; position: relative; }
body, html, .wrap, .wrapper { width: 100%; height: 100%; overflow: hidden; }
.pa { position: absolute; }
.wrapper { z-index: 1; background: url(../img/share/sharebg.jpg) no-repeat center center; background-size: 750px 1650px; text-indent: -9999em; animation: bg 6s ease-in-out; }
@keyframes bg {
	0% { transform: scale(1.2) }
	100% { transform: scale(1) }
}
.wrap { position: absolute; z-index: 2; top: 0; left: 0; }
.contbox { height: 1100px; left: 0; top: 50%; margin-top: -550px; position: absolute; width: 750px; }
.huapa { right: 0; top: 50%; z-index: 2; margin-top: -825px; animation: huaskew 10s linear infinite; transform-origin: top right; }
@keyframes huaskew {
	0%, 100%, 20%, 50%, 80% { transition-timing-function: cubic-bezier(0.215, .61, .355, 1); /*贝塞尔曲线 ： X1 Y1 X2 Y2*/ transform: translate3d(0, 0, 0) rotate(0); /*设置只在Z轴上移动*/ }
	40%, 43% { transition-timing-function: cubic-bezier(0.755, 0.50, 0.855, 0.060); transform: translate3d(0, -15px, 0) rotate(15deg); }
	70% { transition-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060); transform: translate3d(0, -8px, 0) rotate(8deg); }
	90% { transform: translate3d(0, -2px, 0) rotate(2deg); }
}
.cirlebg { width: 696px; height: 697px; background: url(../img/share/cirlebg.png) no-repeat; background-size: 100%; top: 160px; left: 53px; z-index: 1; }
.zhubiao { width: 397px; height: 755px; left: -53px; top: -135px; background: url(../img/share/zhhubiao.png) no-repeat; background-size: 100%; }
.zhubiao p { color: #8b3c28; font-size: 20px; line-height: 20px; padding: 420px 0 0 60px; width: 103px; text-align: center; }
.cirlebg img { display: block; }
.cailiao { text-indent: -9999em; }
.cailiao0 { width: 372px; top: -35px; right: 104px; height: 552px; background: url(../img/share/maobi.png) no-repeat; background-size: 100%; }
.cailiao1 { width: 290px; top: 90px; right: 104px; height: 398px; background: url(../img/share/yanliao.png) no-repeat; background-size: 100%; }
.cailiao2 { width: 319px; top: 90px; right: 104px; height: 363px; background: url(../img/share/jinfen.png) no-repeat; background-size: 100%; }
.cailiao3 { width: 354px; top: 153px; right: 87px; height: 337px; background: url(../img/share/buliao.png) no-repeat; background-size: 100%; }
.getbtn, .fhbtn, .loginbtn, .gbbtn, .pop_h2 { background: url(../img/share/com.png) no-repeat; background-size: 332px auto; text-indent: -9999em; z-index: 2; }
.getbtn { width: 309px; height: 93px; top: 840px; left: 50%; margin-left: -155px; }
.fhbtn { width: 135px; height: 41px; background-position: 0 -187px; top: 0; left: 39px; }
.getbtn.dis_btn { background-position: 0 -93px; pointer-events: none; }
.txtbox { width: 100%; left: 0; top: 930px; text-align: center; }
.txtbox div p { line-height: 32px; height: 32px; border-bottom: 2px dotted #f7ccaa; display: inline-block; }
.txtbox div span { color: #ff624d; }
.logo { width: 169px; height: 79px; background: url(../img/logo.png) no-repeat; background-size: 100% auto; text-indent: -9999em; top: 52px; left: 26px; z-index: 2; }
.dlbar { height: 60px; right: 18px; width: 520px; display: flex; display: -webkit-flex; position: absolute; z-index: 2; top:0; font-size: 20px; line-height: 30px; }
.dlbar, .dlbar a { color: #be4335; }
.dlbar div { align-self: center; -webkit-align-self: center; width: 100%; }
.logout_box { text-align: right; }
.loginbtn { width: 45px; height: 28px; background-position: -202px -195px; text-indent: -9999em; display: block; float: right; }

/*hua*/
.flw { position: absolute; top: 0; right: 0; z-index: 10; }
.flw li { position: absolute; top: 0; right: 0; }
.flw span { background: url(../img/share/flw.png) no-repeat; background-size: 423px auto; display: block; }
.flw .sp1 span { width: 56px; height: 32px; background-position: 0 0; }
.flw .sp2 span { width: 35px; height: 47px; background-position: -119px 0; }
.flw .sp3 span { width: 56px; height: 33px; background-position: -206px -1px; }
.flw .sp4 span { width: 55px; height: 26px; background-position: -314px -1px; }
.flw .sp5 span { width: 37px; height: 42px; background-position: -1px -95px; }
.flw .sp6 span { width: 39px; height: 38px; background-position: -104px -99px; }
.flw .sp7 span { width: 62px; height: 40px; background-position: -177px -101px; }
.flw .sp8 span { width: 46px; height: 32px; background-position: -264px -101px; }
.fallani1 { animation: fallAni1 10s linear infinite; }
.fallani2 { animation: fallAni2 10s linear infinite; }
.fallani3 { animation: fallAni3 10s linear infinite; }
@keyframes fallAni1 {
	0% { transform: rotateX(0) rotateY(0) rotateZ(0); }
	100% { transform: rotateX(0) rotateY(0) rotateZ(360deg); }
}
@keyframes fallAni2 {
	0% { transform: rotateX(0) rotateY(0) rotateZ(0); }
	100% { transform: rotateX(0) rotateY(60deg) rotateZ(360deg); }
}
@keyframes fallAni3 {
	0% { transform: rotateX(0) rotateY(0) rotateZ(0); }
	100% { transform: rotateX(60deg) rotateY(0) rotateZ(360deg); }
}
@keyframes fadeInUp {
	0% { opacity: 0; transform: translateY(20px) }
	100% { opacity: 1; transform: translateY(0) }
}
.active .cirlebg, .active .getbtn, .active .txtbox div, .active .logo, .active .dlbar, .active .fhbtn { animation: fadeInUp 1s .2s ease both; }
.active .getbtn { animation-delay: .4s; }
.active .txtbox div:nth-child(1) { animation-delay: .6s; }
.active .txtbox div:nth-child(2) { animation-delay: .7s; }
.active .txtbox div:nth-child(3) { animation-delay: .8s; }
.active .txtbox div:nth-child(4) { animation-delay: .9s; }
.active .txtbox div:nth-child(5) { animation-delay: 1s; }
.active .logo, .active .dlbar, .active .fhbtn { animation-delay: 1.2s; }
.pop { width: 562px; height: 310px; background: url(../img/share/pop.png) no-repeat; background-size: 562px auto; position: relative; display: none; }
.gbbtn { width: 54px; height: 54px; background-position: 0 -234px; display: block; text-indent: -9999em; top: 6px; right: 30px; }
.pop_cont { padding: 82px 34px 0; }
.pop_h2 { width: 332px; height: 80px; background-position: 0 -311px; margin: 0 auto; text-indent: 0; display: flex; display: -webkit-flex; text-align: center; font-weight: bold; }
.pop_h2 span { color: #d6612d; font-size: 42px; align-self: center; -webkit-align-self: center; width: 100%; background-image: -webkit-linear-gradient(top, #d66230, #e59249); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.pop_inf { color: #532410; text-align: center; line-height: 28px; padding-top: 14px; }
.pop_inf a { display: inline-block; border-bottom: 1px solid #532410; color: #532410; }
.pop_h2_failr { background-position: 0 -405px; }
.pop_h2_failr span { background-image: -webkit-linear-gradient(top, #824225, #824225); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.wxicon, .glicon, .bdbtn { background: url(../img/diacom.png) no-repeat; background-size: 750px auto; display: block; text-indent: -9999em; }
.pop_dl { width: 686px; height: 464px; background: url(../img/pop.png) no-repeat; background-size: 686px auto; }
.pop_dl .pop_cont { padding: 30px 34px 0; }
.pop_dl .pop_h2 span { color: #d6612d; font-size: 36px; }
.dllist { width: 556px; margin: 0 auto; text-align: center; color: #494543; }
.dllist li { width: 278px; float: left; padding-top: 25px; }
.wxicon, .glicon { margin: 13px auto 0; width: 183px; height: 184px; }
.wxicon { background-position: 0 -495px; }
.glicon { background-position: -218px -495px; }
.bdbtn { margin: 17px auto 0; width: 241px; height: 67px; background-position: -443px -535px; }
.p2_txt { padding-top: 18px; }
