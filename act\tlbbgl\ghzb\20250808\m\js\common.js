(function () {
    //页面加载完成前调用
    // $(window).load(function () {
    //
    // });

    function App() {
        const that = this


        this.data = {

        }
        this.pageNo1= 1
        this.pageNo2= 1
        this.type= 1

        this.ajax = function (l, d, opt) {
            let loading
            return new Promise((resolve, reject) => {
                const env = 1  // 1-内网IP接口  2-内部服务器接口  3-畅游测试接口  4-畅游正式接口
                const domainMap = {
                    1: "http://changyoumenpai2.com",
                    2: 'https://admin.csfullspeed.com',
                    3: 'https://test-api-tlbbglact.changyou.com',
                    4: 'https://api-tlbbglact.changyou.com'
                }
                const domain = domainMap[env]
                const activeName = 'gonghui'
                const path = `${[3, 4].includes(env) ? '/public' : ''}/index.php${[3, 4].includes(env) ? '/' : '?s='}changyou/${activeName}/`

                const url = domain + path + l

                // console.log('api', l);
                const data = d || {}
                let verifyToken = $.tools.getStorage('GHtoken')
                if (l !== 'login' && verifyToken) {
                    // if (mockToken) verifyToken = mockVerifyToken
                    data.verifyToken = verifyToken
                }
                // console.log('post', data);

                if (opt && opt.loading) loading = lay.load()
                $.ajax({
                    url: url,
                    type: opt?.type ||'POST',
                    data: data,
                    dataType: 'json',
                    success: function (res) {
                        if (opt && opt.loading) lay.close(loading)
                        resolve(res)
                    },
                    error: function (err) {
                        if (opt && opt.loading) lay.close(loading)
                        console.log('post err res', err);
                        lay.msg('接口请求繁忙，请稍后重试')
                        reject(err)
                    }
                })
            })
        }

        this.loginPop = function () {
            if ($('.btn-login').hasClass('timeover')) return that.alert('当前不在活动时间内')
            const base_url = $.tools.getUrl
            const params = $.tools.getRequest()
            params.base_url = base_url
            const ml = $.tools.encodeBase64URLSafeString(JSON.stringify(params))
            const state = $.tools.encodeBase64URLSafeString(encodeURIComponent(location.search))
            let s = location.href.replace(location.search, '')
            const src = 'https://auth.changyou.com/interfaceLogin?project=tlglactivity&s=' + s + '&state=' + state + `&gameType=tlgl&ml=${ml}`;
            $.tools.setStorage('URL_BEFORE_LOGIN', s)
            $('#popLogin .login-box').html(`<iframe src="${src}" frameborder="0" width="100%" height="100%" scrolling="no"></iframe>`);
            lay.open('popLogin')
        }

        this.login = function () {
            const ticket = $.tools.getStorage('ticket')
            if (!ticket) return Promise.resolve({ code: -1, msg: '没有ticket' })
            // 登录
            const urlBeforeLogin = $.tools.getStorage('URL_BEFORE_LOGIN')
            if (!urlBeforeLogin) return Promise.resolve({ code: -1, msg: '没有传登录前的url' })
            const s_url = urlBeforeLogin
            console.log('login接口传参：', s_url)
            const data = {ticket, s_url}
            const uid = $.tools.getParam('uid')
            if (uid) data.shareUid = uid
            const CYSCID = $.tools.getParam('type')
            if (CYSCID) data.CYSCID = CYSCID
            return new Promise(resolve => {
                that.ajax('login', data)
                    .then(res => {
                        $.tools.removeStorage('ticket')

                        if (res.code === -20003) {
                            that.alert('您的账号已停权', that.logout)
                        }

                        console.log('login', res)
                        if (res.code === 0) {
                            //TODO:修改过这里
                            console.log(res.data);
                            // const token = res.data.userData.token
                            const token = res.data.token
                            $.tools.setStorage('GHtoken', token)
                            // 重新拉取初始话数据
                            _checkLogin().then(res => {
                                console.log('_checkLogin', res);
                                resolve(res)
                            })
                        } else{
                            resolve(res)
                        }
                    })
                    .catch(res => {
                        if (res.code === -20003) {
                            that.alert('您的账号已停权', that.logout)
                            return
                        }

                        if (res.code === -21003) {
                            lay.msg('分享参数错误')
                            setTimeout(function () {
                                location.href = $.tools.removeParam(location.href, 'uid')
                            }, 1500)
                            return
                        }
                        resolve(res)
                    })
            })
        }

        function _checkLogin() {
            const uid = $.tools.getParam('uid')
            const data = {}
            if (uid) data.shareUid = uid
            const CYSCID = $.tools.getParam('CYSCID')
            if (CYSCID) data.CYSCID = CYSCID
            const url = uid ? 'pageIndex' : 'index'
            return that.ajax(url, data)
        }

        this.checkLogin = function () {
            let verifyToken = $.tools.getStorage('GHtoken');
            // 缓存是否有token
            //  -无 缓存token，则表示还未登录 =>
            if (!verifyToken) {
                if ($.tools.getParam('ticket')) {
                    $.tools.setStorage('ticket', $.tools.getParam('ticket'))
                    const href = $.tools.removeParam(location.href, 'ticket')
                    return window.location.href = $.tools.removeParam(href, 'state')
                }
                //       判断缓存中是否有 ticket 参数，有则
                let ticket = $.tools.getStorage('ticket')
                if(ticket) {
                    //       -如果有，则调login接口
                    //          -接口正常code=0,拿到token，并缓存
                    //          -接口异常，则登录失败返回错误信息
                    return this.login()
                }
                else {
                    //       -如果无ticket参数，返回未登录
                    return Promise.resolve({code: -1, msg: '未登录'})
                }
            }
                //  -有 缓存token，调初始话接口，拉取登录信息 =>
                //       -接口正常code=0，则登录成功，返回登录信息
            //       -接口异常，登录失败，返回错误信息
            else {
                return new Promise(resolve => {
                    _checkLogin().then(res => {
                        console.log('_checkLogin', res);

                        if (res.code === -20003) {
                            that.alert('您的账号已停权', that.logout)
                        }

                        if (res.code === -21003) {
                            lay.msg('分享参数错误')
                            setTimeout(function () {
                                location.href = $.tools.removeParam(location.href, 'uid')
                            }, 1500)
                            return
                        }
                        console.log('resolve', res);

                        if(res.code === 0) {
                            that.data = Object.assign(that.data, res.data)
                        }
                        resolve(res)
                    }).catch(res => {
                        if (res.responseJSON) res = res.responseJSON
                        resolve(res)
                    })
                })
            }
        }

        this.init = function () {
            console.log('init');
            const ticket = $.tools.getParam('ticket')
            if (ticket) {
                $.tools.setStorage('ticket', ticket)
                const state = $.tools.getParam('state')
                let href = location.href
                const search = decodeURIComponent($.tools.decodeBase64URLSafeString(state))
                href = $.tools.getUrl + search + location.hash
                return location.href = href
            }
            $('.btn-logout').hide()

            tracker.pv_uv()

            setWxShare()

            // console.log('init');
            document.addEventListener('touchstart', function(event) {
                if (event.touches.length > 1) {
                    // console.log('禁止双指放大')
                    event.preventDefault();
                }
            }, { passive: false });

            document.addEventListener('touchmove', function(event) {
                if (event.touches.length > 1) {
                    // console.log('禁止双指放大')
                    event.preventDefault();
                }
            }, { passive: false });



            //默认打开页面
            open('page0');
            this.bind()

            setTimeout(() => {
                that.loading_index = lay.load()

                this.checkLogin()
                    .then(res => {
                        console.log('checkLogin()', res);

                        // 判断ticket参数，如果存在，以及获取登录态信息
                        if (res.code === -40001) {
                            // 客态分享链接uid不是队长
                            if ($.tools.getParam('uid')) {
                                lay.msg(res.msg)
                                setTimeout(() => {
                                    window.location.href = $.tools.removeParam(location.href, 'uid')
                                }, 1500)
                                return
                            }
                        }

                        if (res.code === -90001) {
                            lay.close(that.loading_index)
                            that.alert('当前不在活动时间内')
                            $('.btn-login').addClass('timeover')
                            return;
                        }

                        if (res.code !== 0) {
                            lay.close(that.loading_index)
                            // console.log('此时需要用户点击登录按钮',res)
                            that.login().then(res => {
                                // console.log('服务器登录态信息', res);

                                if (res.code !== 0) {
                                    // console.log('此时需要用户点击登录按钮')
                                } else {
                                    that.refreshUserInfo()
                                }
                            })
                        }

                        if (res.code === 0) {
                            that.data = Object.assign(that.data, res.data)
                            handlerLoginSuccess()
                        }

                    })
                    .catch(e => {
                        lay.close(that.loading_index)
                        console.log(e);
                    })
            }, 100)
        }

        this.refreshUserInfo = function () {
            this.checkLogin().then(res => {
                console.log('checkLogin', res);
                if (res.code === 0) {
                    handlerLoginSuccess()
                }
            })
        }

        // 渲染页面
        function renderPage() {
            console.log('start renderPage')
        }

        function handlerLoginSuccess() {
            $('.ewmtn').show()
            $('.btn-logout').show()
            const uid = $.tools.getParam('uid')
            // 登录信息获取正常，拿到了用户信息
            // console.log('登录信息获取正常，拿到了用户信息', that.data)
            // 判断是否已入队，判断是否为队长
            const data = that.data
            console.log(that.data);
            const { ghId, isLeader,gonghuiData,name,cn,isActive } = data.userData

            if(ghId){
                if(uid){
                    // lay.msg('您已加入公会，进入公会首页~')
                    setTimeout(function () {
                        location.href = $.tools.removeParam(location.href, 'uid');
                    }, 1000)
                    return;
                }

                $('.J_nickname').text(name);//我的昵称
                $('.J_cn').text(cn);//cn
                $('.J_ganghuiName').text(gonghuiData.name);//公会名称
                $('.J_memberNum').text(gonghuiData.memberNum);//公会人数
                if(gonghuiData.qqAccount){
                    $('.J_qqAccount input').val(gonghuiData.qqAccount);
                }else {
                    if(!isLeader){
                        $('.J_qqAccount').hide();
                        $('.guild-code').addClass('guild-code2')
                    }else {
                        $('.J_qqAccount .btn-copy').hide();
                        $('.guild-line').addClass('justify-center');
                    }
                }
                // if(gonghuiData.wxAccount){
                //     $('.J_wxAccount input').val(gonghuiData.wxAccount);
                // }else {
                //     $('.J_wxAccount').hide();
                // }
                if(isLeader){
                    $('.isLeader').text('会长')
                }else {
                    $('.guild-code').addClass('guild-code3');
                    $('.isLeader').text('成员')
                }
                $('.J_ghId input').val(ghId);//公会码
                $('.J_personTotal').text(data.personTotal);//公会有效人数
                $('.J_personRank').text(data.personRank);//公会有效排名
                $('.J_buyRank').text(data.buyRank);//公会消费排名

                let rankData1 = ``;
                for(let i=0;i<data.rankData1.length;i++){
                    rankData1+=`<tr>
                                    <td>${ data.rankData1[i].rank }</td>
                                    <td>${ data.rankData1[i].name }</td>
                                    <td>${ data.rankData1[i].totalNum }</td>
                                </tr>`
                }
                $('#rankData1').html(rankData1 || `<tr><td style="line-height: 2.4rem">暂无数据</td></tr>`)

                let rankData2 = ``;
                for(let i=0;i<data.rankData2.length;i++){
                    rankData2+=`<tr>
                                    <td>${ data.rankData2[i].rank }</td>
                                    <td>${ data.rankData2[i].gonghuiName }</td>
                                </tr>`
                }
                $('#rankData2').html(rankData2 || `<tr><td style="line-height: 2.4rem">暂无数据</td></tr>`)

                let giftRecord = ``;

                if (data.giftRecord.length === 0) {
                    $('.gift-list').addClass('no-data')
                    $('.record-txt').text('您暂未获得奖励，请在页面参加活动领取奖励后查看获奖记录。')
                } else {
                    $('.gift-list').removeClass('no-data')
                    if(isActive) {
//                         $('.record-txt').html(`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您已成功获得以下奖励，游戏道具请在7月25日《天龙八部·归来》公测开服后前往大理龚彩云处领取（角色等级需达到40级）。
//    <br />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您已成为公会有效成员，满足条件的公会会长和有效成员可瓜分百万元宝。`)
$('.record-txt').html(`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您已成功获得以下奖励，请在开服后前往游戏内领取`)
                        $('.pop6 .gift-list').height(310)
                    } else {
                        $('.record-txt').html(`您已成功获得以下奖励，请在开服后前往游戏内领取`)
//                         $('.record-txt').html(`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您已成功获得以下奖励，游戏道具请在7月25日《天龙八部·归来》公测开服后前往大理龚彩云处领取（角色等级需达到40级）。
//   <br />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您暂未成为公会有效成员，公测后达到68级且解锁了高级成长密令即可成为有效成员。
//   <br />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;满足条件的公会会长和有效成员可瓜分百万元宝。`)
                        $('.pop6 .gift-list').height(280)
                    }
                }
                for(let i=0;i<data.giftRecord.length;i++){
                    giftRecord+=`<li>
                                    <div class="col1">${ data.giftRecord[i].receiveTime }</div>
                                    <div class="col2">${ data.giftRecord[i].giftName }</div>
                                </li>`
                }
                $('#giftRecord').html(giftRecord)

                open('page2');
            }else {
                if(uid){
                    open('page4');

                    $('#J_inviterUser').text(data.inviterUser.nickname);//
                    $('#J_inviterGh').text(data.inviterUser.name);//

                }else {
                    open('page1');
                }

            }

            lay.close(that.loading_index)
        }

        // 跳转到指定页面 0登录 1创建/加入 2公会页面 3客态邀请
        function open(page) {
            $('.' + page).addClass('on').siblings().removeClass('on');
        }
        this.open = open;

        // function copyMyLink() {
        //     lay.closeAll()
        //     if (clipBordCopy('《天龙八部》XXXX：' + $('.lianjie').val())) {
        //         that.alert('复制成功')
        //     } else {
        //         that.alert("复制失败");
        //     }
        // }

        //获取验证码
        function sendCode() {
            let phone = $('#phone').val();
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                return lay.msg('请输入正确的手机号')
            }

            that.ajax('sendCode', {
                phone,
            }).then(res => {
                if (res.code !== 0) return that.alert(res.msg)
                lay.msg('验证码发送成功，请注意查收~')
                let time = 60;
                let timer = setInterval(function () {
                    time--;
                    $('.btn-getcode').addClass('disabled').text(time + 's');
                    if(time==0){
                        $('.btn-getcode').removeClass('disabled').text('获取验证码');
                        clearInterval(timer);
                    }
                },1000)
            })
        }

        // 创建公会
        function createTeam() {
            let name = $('#name').val().trim(),
                namePre = $('#namePre').val().trim(),
                nickname = $('#nickname').val().trim(),
                // wxAccount = $('#wxAccount').val().trim(),
                qqAccount = $('#qqAccount').val().trim(),
                phone = $('#phone').val().trim(),
                vCode = $('#vCode').val().trim();
            if (name.length < 2) {
                return lay.msg('请输入公会名称2~8字')
            }
            if (name.length < 1) {
                return lay.msg('请输入公会前缀')
            }
            if (nickname.length < 2) {
                return lay.msg('请输入昵称2~8字')
            }
            if (!/^1[3-9]\d{9}$/.test(phone)) {
                return lay.msg('请输入正确的手机号')
            }
            if (vCode.length !== 6) {
                return lay.msg('请输入正确的验证码')
            }

            that.ajax('createTeam', {
                name,
                namePre,
                nickname,
                // wxAccount,
                qqAccount,
                phone,
                vCode,
            })
                .then(res => {
                if (res.code !== 0) return that.alert(res.msg);
                that.alert(`恭喜您，成功创建公会！
<p class="t-l">获得称号：盟契山河（30天），所有属性+5，请在7月25日《天龙八部·归来》公测开服后前往大理龚彩云处领取。</p>
<p class="t-l">您可以通过分享链接或者发放公会码邀请其他玩家加入公会，参与公会争霸瓜分百万元宝！</p>`, function () {
                    // 队伍创建成功，跳转到队长首页
                    // 需要重新拉取个人信息
                    lay.closeAll()
                    that.refreshUserInfo()
                    that.open('page2')
                })

            })
        }

        // 获得分享链接
        function getShareLink() {
            let link = location.href
            if (that.data?.userData?.ghId) {
                const {ghId} = that.data.userData;
                link = $.tools.replaceParam(link, 'uid', ghId)
            }
            return link
        }
        // 获得微信分享参数
        function getWxShareData() {
            return {
                isWxDebug: false,
                title: '天龙归来，公会争霸赢百万元宝',
                desc: '公会兄弟冲榜，拿限定称号，瓜分百万元宝！',
                link: getShareLink(),
                imgUrl: 'https://' + location.host + '/act/tlbbgl/ghzb/20250407/m/img/share.png',
                success: function () {
                    // alert('分享成功')
                }
            }
        }
        // 设置微信分享参数
        function setWxShare() {
            if ($.tools.browser.versions.weixin) {
                const shareData = getWxShareData()
                console.log('分享数据：', shareData);
                $.wechat.share(shareData)
            }
        }

        //集结小队战友
        this.share = function () {
            tracker.click_share()
            // 预约集结，点击预约集结按钮，如果在微信内，则引导玩家进行分享，微信内分享后显示小卡片；如果在非微信内，则弹出分享链接提示
            if ($.tools.browser.versions.weixin) {
                setWxShare()
                $('.pop-share').show();
            } else {
                $('.copy-text input').val(getShareLink())
                lay.open('popCopy');
            }
        }

        this.updateQQ = function () {
            let qqAccount = $('#qqAccount2').val().trim();
            if (qqAccount.length < 1) {
                return lay.msg('请输入QQ群号')
            }
            that.ajax('updateTeam', {
                ghId: that.data.userData.ghId,
                qqAccount,
            },).then(res => {
                if (res.code !== 0) return that.alert(res.msg);
                lay.closeAll()
                $('.J_qqAccount input').val(qqAccount);
                that.data.userData.gonghuiData.qqAccount = qqAccount;
                $('.guild-line').removeClass('justify-center')
                $('.J_qqAccount .btn-copy').show();
                that.alert('QQ群号修改成功~')
            })
        }

        //获取公会成员列表
        this.ghuserList = function () {
            that.loading_index = lay.load()
            let pageNo = that.type==1 ? that.pageNo1 : that.pageNo2;
            that.ajax('gonghuiUserList', {
                pageNo,
                type: that.type,
            },{type:'GET'}).then(res => {
                lay.close(that.loading_index)
                if (res.code === -1100) return lay.msg('操作频繁，请稍后再试')
                if (res.code !== 0) return that.alert(res.msg);
                let data = res.data.userList;
                let totalNum = Math.ceil(res.data.totalNum/100);
                totalNum = totalNum==0 ? 1 : totalNum;

                let html = ``;
                if(that.type==1){
                    for(let i=0;i<data.length;i++){
                        html+=`<tr>
                                    <td>${ (i+1)+((pageNo-1)*100) }</td>
                                    <td>${ data[i]?.name }</td>
                                    <td>${ data[i]?.isEffective?'是':'否' }</td>
                                </tr>`
                    }
                    $('#userList1').html(html)
                    $('#pageNo1').html(pageNo)
                    that.pageTotal1 = totalNum*1
                    $('#pageTotal1').html(that.pageTotal1)
                    if(totalNum==1){
                        $('.rank-con0').addClass('nonext')
                    }
                }else {
                    for(let i=0;i<data.length;i++){
                        html+=`<tr>
                                    <td>${ data[i].rank }</td>
                                    <td>${ data[i].name }</td>
                                </tr>`
                    }
                    $('#userList2').html(html || `<tr><td style="line-height: 8rem">暂无消费排名</td></tr>`)
                    $('#pageNo2').html(pageNo)
                    that.pageTotal2 = totalNum
                    $('#pageTotal2').html(that.pageTotal2)
                    if(totalNum==1){
                        $('.rank-con1').addClass('nonext')
                    }
                }
            })
        }

        this.getTeam = function () {
            let ghId = $('#ghId input').val();
            if(!ghId) return;
            that.ajax('getTeam', {
                ghId,
            },).then(res => {
                if (res.code !== 0) {
                    $('#nickname2').val('')
                    $('#J_ghName').text('-')
                    $('.J_namePre').text('')
                    if(res.code === -35000) return lay.msg('公会码错误，请检查后重试');
                    return that.alert(res.msg);
                }
                let ghData = res.data.ghData;
                $('#J_ghName').text(`【${ghData.name}】`)
                $('.J_namePre').text(ghData.namePre)
                // $('#nickname2').val(ghData.namePre)
            })
        }
        this.applyJoin = function () {
            let ghId = $('#ghId input').val(),
                nickname = $('#nickname2').val(),
                isPreName = 0;
            if(!ghId) {
                return lay.msg('请输入公会码');
            }
            if (nickname.length < 2) {
                return lay.msg('请输入昵称2~8字')
            }
            that.ajax('applyJoin', {
                ghId,
                nickname,
                isPreName
            },).then(res => {
                if (res.code !== 0) return that.alert(res.msg);
                that.alert(`恭喜您，成功加入公会！
<p class="t-l">获得称号：盟契山河（30天），所有属性+5，请在7月25日《天龙八部·归来》公测开服后前往大理龚彩云处领取。</p>
<p class="t-l">您可以通过分享链接或者发放公会码邀请其他玩家加入公会，参与公会争霸瓜分百万元宝！</p>`, function () {
                    window.location.href = $.tools.removeParam(location.href, 'uid')
                })
            })
        }
        this.logout = function () {
            $.tools.removeStorage('GH_LOGIN_INFO')
            $.tools.removeStorage('GHtoken')
            const b = encodeURIComponent(location.href)
            location.href = `https://auth.changyou.com/logout?b=${b} `
        }

        this.bind = function () {
            //关闭弹窗
            $('.closeAll,.btn-cancel').on('click', function () {
                lay.closeAll()
            })

            $('#popCom .btn-confirm, #popCom .btn-close').on('click', function () {
                lay.close('popCom')
            })
            //登录
            $('.btn-login').on('click', function () {
                that.loginPop()
            })
            //活动规则
            $('.btn-rule').on('click', function () {
                lay.open('popRule')
            })
            //创建公会
            $('.btn-create').on('click', function () {
                lay.open('popCreate')
            })
            //公会前缀填写，输入框失去焦点时，昵称自动填充前缀
            // $('#namePre').on('blur', function () {
            //     $('#nickname').val($(this).val())
            // })
            //获取验证码
            $('.btn-getcode').on('click', $.tools.throttle(sendCode, 1000, true))
            //确认创建
            $('#popCreate .btn-confirm').on('click', $.tools.throttle(createTeam, 1000, true))

            //个人中心
            $('.btn-center').on('click', function () {
                lay.open('popCenter')
            })

            // 退出登录
            $('.btn-logout').click(function () {
                that.logout()
            })

            //集结公会成员
            $('.btn-guild').on('click', function () {
                that.share();
            })
            //查看成员详情
            $('.btn-detail').on('click', function () {
                that.ghuserList();
                open('page3');
            })
            //查看成员详情
            $('.btn-back').on('click', function () {
                that.refreshUserInfo()
                open('page2')
            })
            //复制分享链接
            $('.btn-copy2, .copy-text').on('click', function () {
                lay.closeAll()
                const name = that.data.userData.gonghuiData.name
                const copyText = `【${name}】公会邀请你加入，一起瓜分百万元宝-《天龙八部·归来》！` + getShareLink()
                if (clipBordCopy(copyText)) {
                    lay.msg('复制成功~')
                } else {
                    lay.msg("复制失败~");
                }
            })
            //复制QQ
            $('.J_qqAccount .btn-copy').on('click', function () {
                if (clipBordCopy(that.data.userData.gonghuiData.qqAccount)) {
                    lay.msg('公会QQ群号复制成功~')
                } else {
                    lay.msg("复制失败，请手动复制~");
                }
            })
            //修改QQ
            $('.J_qqAccount .btn-modify').on('click', function () {
                lay.open('modifyQQ')
            })
            $('#modifyQQ .btn-confirm').on('click', $.tools.throttle(that.updateQQ, 1000, true))
            // //复制微信
            // $('.J_wxAccount .btn-copy').on('click', function () {
            //     if (clipBordCopy(that.data.userData.gonghuiData.wxAccount)) {
            //         lay.msg('复制微信号成功~')
            //     } else {
            //         lay.msg("复制失败，请手动复制~");
            //     }
            // })
            // //修改微信
            // $('.J_wxAccount .btn-modify').on('click', function () {
            //     lay.open('modifyWX')
            // })
            // $('#modifyWX .btn-confirm').on('click', function () {
            //     that.updateWX()
            // })
            //复制公会码
            $('.J_ghId .btn-copy').on('click', function () {
                if (clipBordCopy(that.data.userData.ghId)) {
                    lay.msg('公会码复制成功~')
                } else {
                    lay.msg("复制失败，请手动复制~");
                }
            })
            //获取公会码
            $('#ghId input').on('blur',function () {
                that.getTeam()
            })
            //确认加入公会
            $('#popJoin .btn-confirm').on('click', $.tools.throttle(that.applyJoin, 1000, true))
            //加入其他公会
            $('.btn-join').on('click', function () {
                $('#ghId input').val('')
                $('#nickname2').val('')
                $('#J_ghName').text('-')
                $('.J_namePre').text('')
                $('#ghId').show()
                lay.open('popJoin')
            })
            $('.btn-joins').on('click', function () {
                $('#ghId input').val(that.data.inviterUser.ghId)
                $('#J_ghName').text(`【${that.data.inviterUser.name}】`)
                $('.J_namePre').text(that.data.inviterUser.namePre)
                // $('#nickname2').val(that.data.inviterUser.namePre)
                $('#ghId').hide()
                lay.open('popJoin')
            })
            // $('.radio').on('click',function () {
            //     $(this).toggleClass('checked');
            //     if ($(this).hasClass('checked')) {
            //         $('.ipt3 .J_namePre').show()
            //     } else {
            //         $('.ipt3 .J_namePre').hide()
            //     }
            // })

            //关注
            $('.btn-follow').on('click',function () {
                lay.open('popFollow')
            })

            //排名
            $('.rank-tab a').on('click', function () {
                $(this).addClass('on').siblings().removeClass('on');
                $(this).parent().siblings('.rank-con'+ $(this).index()).show().siblings('.rank-con').hide();
            })
            //公会详情
            $('.rank-tab2 a').on('click', function () {
                that.type = $(this).index()+1;
                that.ghuserList()
            })
            $('.rank2 .rank-con0 .btn-prev').on('click', function () {
                if(that.pageNo1==1) return;
                that.pageNo1--;
                that.ghuserList()
            })
            $('.rank2 .rank-con0 .btn-next').on('click', function () {
                if(that.pageNo1==that.pageTotal1) return;
                that.pageNo1++;
                that.ghuserList()
            })
            $('.rank2 .rank-con0 input').on('blur', function () {
                let val = $(this).val()*1;
                val = val<1 ? 1: val>that.pageTotal1 ? that.pageTotal1: val
                $(this).val(val)
                that.pageNo1 = val;
                that.ghuserList()
                let _this = $(this)
                setTimeout(function () {
                    _this.val('')
                },1500)
            })
            //
            $('.rank2 .rank-con1 .btn-prev').on('click', function () {
                if(that.pageNo2==1) return;
                that.pageNo2--;
                that.ghuserList()
            })
            $('.rank2 .rank-con1 .btn-next').on('click', function () {
                if(that.pageNo2==that.pageTotal2) return;
                that.pageNo2++;
                that.ghuserList()
            })
            $('.rank2 .rank-con1 input').on('blur', function () {
                let val = $(this).val()*1;
                val = val<1 ? 1: val>that.pageTotal2 ? that.pageTotal2: val;
                $(this).val(val)
                that.pageNo2 = val;
                that.ghuserList();
                let _this = $(this)
                setTimeout(function () {
                    _this.val('')
                },1500)

            })
            $('.btn-up').on('click',function () {
                $(this).parent().siblings('.rank-table').animate({scrollTop:0})
            })

            $('.btn-close').click(function () {
                const popId = $(this).parents('.pop').attr('id')
                lay.close(popId)
            })

        }


        this.openPop = function (id) {
            lay.closeAll()
            lay.open(id)
        }

        this.ewm = function () {
            that.openPop('ewmPop')
        }

        this.alert = function (msg, cbConfirm, cbCancel) {
            lay.open('popCom')
            $('#popCom .pop-msg-txt').html(msg)
            if (cbCancel && cbConfirm) {
                $('#popCom .btn-confirm').one('click', cbConfirm)
                $('#popCom .btn-close').one('click', cbCancel)
            } else if (cbConfirm) {
                $('#popCom .btn-confirm, #popCom .btn-close').one('click', cbConfirm)
            }
        }

        const versions = $.tools.browser.versions
        if (versions.android || versions.ios) {
            $(window).on('load orientationchange', handlerScreen);
        } else {
            $(window).on('load resize', handlerScreen)
        }

        function handlerScreen() {
            setTimeout(() => {
                // const contbox = $('.contbox')
                const ww = $(window).width()
                const wh = $(window).height()
                const condition = ww / wh >= 375 / 667
                if (condition) {
                    // const {versions} = $.tools.browser
                    // console.log('versions.ios || versions.android', versions.ios || versions.android)
                    // const contHeight = 1650
                    // if (versions.ios || versions.android) {
                    //     contbox.css({'height': contHeight})
                    // }
                    // let scale0 = (wh / contHeight) > 1 ? 1 : wh / contHeight
                    // let scale = 1
                    // if (!((versions.ios || versions.android) && ww / wh < 1)) {
                    //     scale = scale0 > 1 ? 1 : scale0
                    // }
                    const scale1 = wh / 1334
                    // const scaleCss = {
                    //     'transform': ' translateY(-50%) scale(' + scale + ')',
                    // }
                    // if ((versions.ios || versions.android)) {
                    //     if (wh <= 1334) {
                    //         scaleCss.height = contHeight
                    //         const contH = ww / wh > 1 ? contHeight : (wh > 1100 ? 1100 : wh)
                    //         console.log('contH', contH);
                    //         $('.start_box_page').height(contH)
                    //     } else {
                    //         scaleCss.height = 1334
                    //         $('.start_box_page').height(1334)
                    //     }
                    // } else {
                    //     scaleCss.height = contHeight
                    //     $('.start_box_page').height(contHeight)
                    // }
                    const scaleCss2 = {
                        'transform': 'scale(' + scale1 + ')'
                    }
                    // contbox.css(scaleCss);
                    $('.page, .pop').css(scaleCss2)
                } else {
                    const scale = 1
                    const scaleCss2 = {
                        'transform': 'scale(' + scale + ')'
                    }
                    $('.page,.pop').css(scaleCss2)
                }
            }, 550)
        }
    }

    window.app = new App()
    app.init()
}());
// 一峰 二林 三石 四平 五州 六安 七叶 八辰 九行