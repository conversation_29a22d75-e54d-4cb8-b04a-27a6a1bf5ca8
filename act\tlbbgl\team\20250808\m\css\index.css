@charset "utf-8";
html,body,div,p,ul,li,dl,dt,dd,em,i,span,a,img,input,h1,h2,h3,h4,h5{margin:0;padding:0;}
a,img,input{border:none;outline:none;}
a{text-decoration:none;}
img{display:block;}
ul,li{list-style:none;}
table{border-collapse:collapse;border-spacing:0;}
body{font:24px/1.5 -apple-system,"Helvetica Neue",Helvetica,Arial,sans-serif;-webkit-user-select: none;background-color:#fff;}
.none{display:none;}
input::-webkit-outer-spin-button,input::-webkit-inner-spin-button{-webkit-appearance: none !important;}
input[type="number"]{-moz-appearance:textfield;}
*{-webkit-appearance:none;-webkit-text-size-adjust:none;}
html{-webkit-tap-highlight-color:rgba(0,0,0,0);}
#cy_bot{padding:10px!important;position: relative;z-index: 2}
.cyLogo{margin-top:10px;}
.orientLayer {
    display: none;
    position: fixed;
    z-index: 1000;
    width: 100%;
    height: 100%;
    align-items: center;
    justify-content: center;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, .9);
    color: #fff;
    font-size: 30px;
}

html,body{width:100%;touch-action: manipulation;max-width: 750px;margin: 0 auto;}
.pr{position: relative;}
.pa{position: absolute;}
.hide{font-size: 0; text-indent: -9999em; width: 0; height: 0;}
.t{font-size: 0; text-indent: -9999em;display: block}
img{width:100%;}
p{text-align: center;}
/***public***/
i,em{font-style: normal;}
.wrap {
    text-align: center;
    width: 100%;
    height: 100%;
    margin: 0 auto;
    position: fixed;
    overflow: hidden;
    top: 0;
    left: 0;
}

.page_bg{
    width: 750px;
    height: 100%;
    margin: 0 auto 0 -375px;
    position: fixed;
    overflow: hidden;
    top: 0;
    left: 50%;
    background: url(../img/page02.jpg) no-repeat center center/750px auto;
}

.page{
    display: none;
}
.page, .contbox{
    width: 750px;
    margin-left: -375px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateY(-50%);
}
.contbox{
    width: 1350px;
    height: 1650px;
    margin-left: -675px;
    background-size: auto 100%;
    background-position: center center;
    background-repeat: no-repeat;
}

.mid {
    width: 750px;
    margin: 0 auto;
}
/* page1 */
.page1 {
    width: 750px;
    height: 100%;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
    min-height: initial;
    background: url(../img/page01.jpg) no-repeat center center/750px auto;
}
.page_bg::before{
    content: '';
    width: 100%;
    height: 100%;
    background: rgba(16, 12, 9, .77);
    position: absolute;
    left: 0;
    top: 0;
}
.page1 .logo{
    background: url("../img/logo.png") no-repeat 0 0/160px auto;
    width: 160px;
    height: 92px;
    position: absolute;
    left: 50%;
    margin-left: -335px;
    top: 40px;
    z-index: 3;
}
.page1 .start_box_page {
    width: 750px;
    height: 1650px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.page1 .contbox{height: 1650px;}
.page1 .slogan{background-image: url("../img/slogan.png"); width: 750px; height: 418px; position: absolute; left: 50%; margin-left: -375px; bottom: 140px;}
.page1 .login-box{position: absolute;bottom: 0;left: 50%;transform: translateX(-50%);color: #fff;text-align: center;}
.page1 .login-box > div{position: absolute;top: 0;left: 50%;transform: translateX(-50%);color: #fff;text-align: center;}
.page1 .btn-login{width: 431px;height: 178px;background: url(../img/btn-login.png) no-repeat center;background-size: 100% auto;position: absolute;bottom: 34px;left: 50%; margin-left: -215px;}
.page1 .btn-login.timeover{background-image: url("../img/btn-timeover.png");}
.page1 .btn-login:not(.timeover){animation: ani-breath 2s linear infinite both;}
@keyframes ani-breath {
    0%, 100% {transform: scale(1);}
    50% {transform: scale(.9);}
}
.page1 .btn-login.home{background-image: url("../img/btn-getHome.png");}
.page1 .btn-rule{position: absolute;left: 50%;transform: translateX(-50%); bottom: 10px;width: 178px;height: 33px;background: url(../img/btn-rule.png) no-repeat center;background-size: 100% auto; z-index: 2;}
.page .mid{padding-top: 480px;}
/* page2 */
.page2{width: 750px;height: 1650px;}
.page2 .contbox{height: 1650px;background-image: url(../img/page02-mass1.png);}
.page2 .top-box{margin-bottom: 0; padding-top: 174px;}
.page2 .mid{padding-top: 545px;}
/* page3 */
.page3{width: 750px;height: 1650px;}
.page3 .contbox{background-image: url(../img/page02-mass1.png);}
.page3 .mid{padding-top: 545px;}

/* page4 */
.page4{width: 750px;height: 1650px;}
.page4 .contbox{background-image: url(../img/page02-mass1.png);}
.page4 .mid{padding-top: 545px;}

/* page5 */
.page5{width: 750px;height: 1650px;}
.page5 .contbox{background-image: url(../img/page02-mass1.png);}
.page5 .mid{padding-top: 545px;}

/* page6 */
.page6{width: 750px;height: 1650px;}
.page6 .contbox{background-image: url(../img/page02-task.png);}
.task-list{width: 630px;height: 724px;display: flex;flex-direction: column;justify-content: space-between;align-items: center;margin: 180px auto 0;overflow: hidden;}
.task-list li{width: 630px;height: 158px;background: url(../img/task-itemBg.png) no-repeat center;background-size: 100% auto;position: relative;}

.task-list li>img{width: 146px;height: 146;margin-left: 8px;overflow: hidden;margin-top: 6px;}

.task-list li .task-gift{width: 150px;height: 158px;position: absolute;top: 0;left: 160px;display: flex;flex-direction: column;justify-content: space-evenly;align-items: center;} /*background: url(../img/taskBg.png) no-repeat center;*/

.task-list li .task-gift:nth-child(2){left: 336px;}
.task-list li .task-gift>img{width:82px;height: 82px;overflow: hidden;}
.task-list li .task-gift p{font-size: 18px;line-height:18px;font-weight: 500;}
.page6 .btn-taskGet{width: 146px;height:100%;background: url(../img/btn-taskGetUN.png) no-repeat center;background-size: 100% auto;position: absolute;right: 6px;top: 50%;transform: translateY(-50%);}
.page6 .btn-taskGet.get{width: 146px;height:100%;background: url(../img/btn-taskGet.png) no-repeat center;background-size: 100% auto;}
.page6 .btn-taskGet.gray{filter: grayscale(100%); width: 146px;height:100%;background: url(../img/btn-gray.png) no-repeat center;background-size: 100% auto;}
.page6 .btn-backHome{width: 178px;height:43px;background: url(../img/btn-backHome.png) no-repeat center;background-size: 100% auto;margin-bottom: 15px;}
.page6 .btn-member{width: 178px;height:43px;background: url(../img/btn-member.png) no-repeat center;background-size: 100% auto;margin-bottom: 15px;}
.page6 .task-pop{width: 284px;height: 194px;background: url(../img/task-pop.png) no-repeat center;background-size: 100% auto;position: absolute;bottom: 150px;display: none;}
.page6 .task-pop>p{color: #ffeecf;font-size: 20px;text-align: center;align-content: center;margin-top: 46px;line-height: 32px;padding: 0 10px 0 20px;height: 90px;overflow-x: hidden;overflow-y: auto;}
.task-list li:nth-child(4) .task-pop{bottom: 152px;}
/* pop */
.pop{ width:578px; height:auto; color: #fff; font-size: 24px; text-align: center; margin: 0 auto 20px;  position: relative;display:none;}
.btn-close{width: 60px;height: 51px;position: absolute;top: 18px;right: 4px;z-index: 10;background: url(../img/btn-close.png) no-repeat center;background-size: 100% auto;}
/* .pop-home{display: block;} */

.pop-login{width: 608px;height:624px;background: none;background-size: 100% auto;}
.pop-login iframe {width: 600px; height: 630px;}
.pop-login .btn-close{top: 0; right: -60px;}

.pop-common{width: 630px;height:410px;background: url(../img/pop-common.png) no-repeat center;background-size: 100% 100%;}
.pop-common .btn-confirm{width:196px;height:65px;background: url(../img/btn-confirm3.png) no-repeat center;background-size: 100% auto;}
.pop-common .btn-cancel{width:196px;height:65px;background: url(../img/btn-cancel.png) no-repeat center;background-size: 100% auto;margin: 0 auto;}
.pop-common .btn-bind{width:196px;height:65px;background: url(../img/bind.png) no-repeat center;background-size: 100% auto;margin: 0 auto;}
.pop-common .btn-create{width:251px;height:70px;background: url(../img/btn-create.png) no-repeat center;background-size: 100% auto;}
.pop-common .btn-phone{width:251px;height:70px;background: url(../img/btn-phone.png) no-repeat center;background-size: 100% auto;}
.pop-common .btn-cancel-pop{width:251px;height:70px;background: url(../img/btn-cancel-pop.png) no-repeat center;background-size: 100% auto;}

.pop-userinfo1{width: 675px;height:674px;background: url(../img/pop-userinfo1.png) no-repeat center;background-size: 100% auto;}
.pop-userinfo1 .ipt-box{width:580px;height:70px;background: url(../img/ipt-name1.png) no-repeat center;background-size: 100% auto;margin:14px auto;}
.pop-userinfo1 .ipt-name{width:440px;}
.pop-userinfo1 .btn-selectAvatar{width:580px;height:70px;background: url(../img/ipt-avatar.png) no-repeat center;background-size: 100% auto;margin: 0 auto;}
.pop-userinfo1 .btn-confirm{width:322px;height:83px;background: url(../img/btn-confirm1.png) no-repeat center;background-size: 100% auto;margin: 0 auto;}
.pop-userinfo1 .avatar-list{width: 600px;height: 168px;display: flex;flex-direction: row;justify-content: space-between;align-items: center;overflow-x: scroll; overflow-y: hidden; margin:10px auto 0;}
.pop-userinfo1 .avatar-list li{width: 146px;height: 146px;background: url(../img/avatarBg.png) no-repeat center;background-size: 100% auto;position: relative;}
.pop-userinfo1 .avatar-list li.active::after{content: "";display: block;position: absolute;top: 0;right: 0;bottom: 0;left: 0;width: 146px;height: 146px;background: url(../img/avatarBg-ac.png) no-repeat center;background-size: 100% auto;}
.pop-userinfo1 .avatar-list li img{width: 120px;height: 120px;padding-top: 14px;margin-left: 15px;border-radius: 50%; overflow: hidden;}

.pop-userinfo2{width: 675px;height:464px;background: url(../img/pop-userinfo2.png) no-repeat center;background-size: 100% auto;}
.pop-userinfo2 .userinfo-box{height: 146px;margin-left: 54px;position: relative;}
.pop-userinfo2 .userinfo-box .avatar{width: 146px;height: 146px;background: url(../img/avatarBg.png) no-repeat center;background-size: 100% auto;}
.pop-userinfo2 .userinfo-box .avatar>img{width: 120px;height: 120px;top: 14px;margin-left: 14px;border-radius: 50%; overflow: hidden;}
.pop-userinfo2 .ipt-box{width:384px;height:70px;background: url(../img/ipt-name2.png) no-repeat center;background-size: 100% auto;position: absolute;top: 38px;left: 180px;}
.pop-userinfo2 .ipt-name{width:240px;}
.pop-userinfo2 .btn-confirm{width:253px;height:78px;background: url(../img/btn-confirm2.png) no-repeat center;background-size: 100% auto;margin: 0 auto;}
.pop-userinfo2 .btn-cancel{width:253px;height:78px;background: url(../img/btn-cancel.png) no-repeat center;background-size: 100% auto;margin: 0 auto;}
.pop-userinfo2 .txt1{font-size: 26px;}

.pop-shareWX{width:100%;height:100%;background:rgba(0,0,0,0.8) url(../img/pop-shareWX.png) 224px 10px/482px 412px  no-repeat ; position:fixed;top:0;left:0;z-index:999;display:none;}
.pop-shareWX .btn-close{top: 307px;right: 58px;width: 42px;height: 37px;}

.pop-share{width: 630px;height:403px;background: url(../img/pop-share.png) no-repeat center;background-size: 100% auto;}
.pop-share .txt1{width: 500px; line-height: 34px;}
.pop-share .ipt-copy{width: 479px;height:67px;background: url(../img/share-ipt2.png) no-repeat center;background-size: 100% auto;font-size: 22px;color: #fff;line-height: 40px;box-sizing: border-box;padding: 10px 62px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
.pop-share .ipt-copyBtn{width: 479px;height:67px;background: url(../img/share-ipt.png) no-repeat center;background-size: 100% auto;font-size: 30px;color: #fff;line-height: 40px;padding: 10px 0;margin-top: 20px;}
.pop-share .btn-close{top: -45px;right: -10px;}

.pop-apply{width: 627px;height:655px;background: url(../img/pop-apply.png) no-repeat center;background-size: 100% auto;padding-top: 220px;}
.pop-apply .btn-close{top: 70px;right: -10px;}
.pop-apply .btn-boxPop{width: 346px; height: 112px;left: 324px;bottom: 0;}
.pop-apply .btn-agree{width:160px;height:54px;background: url(../img/btn-agree.png) no-repeat center;background-size: 100% auto;margin: 0 auto;}
.pop-apply .btn-refuse{width:160px;height:78px;background: url(../img/btn-refuse.png) no-repeat center;background-size: 100% auto;margin: 0 auto;}


.pop-state{width: 633px;height:622px;background: url(../img/pop-state.png) no-repeat center;background-size: 100% auto;}
.pop-state .btn-unLogin{width:207px;height:54px;background: url(../img/btn-unLogin.png) no-repeat center;background-size: 100% auto;margin: 0 auto;margin-left: 10px;}
.pop-state .btn-unLogin.logined{width:207px;height:54px;background: url(../img/btn-logined.png) no-repeat center;background-size: 100% auto;margin: 0 auto;margin-left: 10px;}
.pop-state .user-level{width:207px;height:54px;background: url(../img/apply-nameBg.png) no-repeat center;background-size: 100% 100%;margin: 0 auto;text-align: center; font-size: 24px; line-height: 54px; color:#fbfbf1; text-shadow: 0 2px 2px #474444;}
.pop-state .btn-boxPop{width: 346px; height: 112px;left: 324px;bottom: 0;}
.pop-state .txt1{padding-top:104px;margin-bottom: 0; line-height: 30px;}
.pop-state .user-list{height: 420px;}

.pop-rule{width: 627px;height: 909px;;background: url(../img/pop-rule.png) no-repeat center;background-size: 100% auto;}
.pop-rule .txt-box{height: 740px;overflow-y: scroll;margin-top: 30px;}
.pop-rule .txt-box p{text-align: start;font-size: 18px;color: #000;line-height: 30px;}
.pop-rule .txt-box p i{text-align: center;font-size: 18px;color: #fff;line-height: 30px;text-align: center;width:30px;height:30px;background: url(../img/numBg.png) no-repeat center;background-size: 100% auto;position: absolute;}
.pop-rule .txt-box p span{padding-left: 32px;}

.pop-home{width: 608px;height:727px;background: url(../img/pop-home.png) no-repeat center;background-size: 100% auto;}
.pop-home .pop-tit{background: url("../img/pop-tit2.png") no-repeat center;background-size: auto 100%; height: 57px; margin-top: 58px;}
.pop-home .record-txt{display: block; text-indent: 0px;}
.pop-home .txt2{padding:0 30px 0 66px; margin-top: 20px;}
.pop-home .txt2,.pop-home ul li{font-size: 22px;color: #000;text-align: start;}
.pop-home h3{font-size: 34px; color: #77664e; line-height: 36px; text-align: center; margin: 0 auto 20px;}
.pop-home .show-gift{margin:0 46.5px;width: 515px;height: 345px;background: url("../img/pop-home-gift-bg.png") no-repeat center; }



.pop-home .gift-list-head {width: 508px;margin: 0 auto;}
.pop-home .gift-list-head div{color: #ffffff;font-size: 24px; line-height: 54px; text-align: center; text-shadow: 0 2px 8px #bd996b;}
.pop-home .gift-list {width: 508px;height:310px;margin: 10px auto 0;overflow-y: scroll;position: relative;}
.pop .no-data::after{content: '暂无数据~'; position: absolute; top: 50%; left: 50%; transform: translate(-50%,-50%); font-size: 24px;color: #4c4537;}
.pop-home ul li{display: flex;flex-direction: row;justify-content: start;align-items: center;margin: 10px 0; border-bottom: 1px dashed #cccccc;}
.pop-home .col1{font-size: 24px;width: 160px;flex-shrink: 0; line-height: 48px;justify-content: center; display: flex; align-items: center;}
.pop-home .col2{font-size: 24px;line-height: 28px;margin-left: 10px; min-height: 48px; display: flex; align-items: center;}
.gift-list-head .col1{width: 156px;height:46px;background: url("../img/th1.png") no-repeat center;background-size: auto 100%; justify-content: center;margin: 5px 0 0 6px;}
.gift-list-head .col2{width: 337px;height:46px;background: url("../img/th2.png") no-repeat center;background-size: 100% 100%;; width: 346px; justify-content: center;margin: 5px 0 0 0;}
.pop-home .btn-close{top: -30px;right: -10px;}

@media screen and (min-width: 1200px) {
    .pop-rule {
        width: 510px;
        height: 887px;
    }
    .pop-rule .btn-close {
    width: 52px;
    top: 10px;
    }

    .pop-rule .txt-box {
        height: 80%;
    }

    .pop .txt-box {
        width:86%;
    }
    .pop-rule .txt-box p {
    line-height: 36px;
    }

   .pop-apply .btn-close {
    top: 128px;
   }
}