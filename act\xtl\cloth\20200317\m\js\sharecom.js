var isLongmen = location.href.indexOf('/longmen/') !== -1,
    isNormal = undefined,       // 是否封停账号
    isClick = false,             // 是否可点击
    isGrade = undefined,       // 是否等级不足
    isBind = undefined,
    $btnGet = $('.getbtn'),
    isStage2 = undefined,
    urlParams = ja.utils.getUrlParams();
var popIsShow = false;
var popDom = null;
var commPath = '/fashion';

// 道具图展示
$('#ClothOrder').addClass('cailiao' + urlParams.clothOrder);
// 添加龙门标识
isLongmen && ja.longmen({ top: 300,left:0 });

ja.config({
    app: 'xtl',              // 必填，应用标识
    activity: 'fashion',     // 必填，活动标识
    version: isLongmen ? '2020031001' : '20200310',     // 必填，活动版本标识
    platform: 'changyou',    // 必填，登陆平台标识
    type: 1,
    isWxLogin: true,
    ready: function (glob) {
        if(glob.isWechat) {
            glob.code === 1 ? loginAfter() : loginBefore();
        }else {
            alert('请在微信中访问');
        }

        ja.wx.init(function () {
            th.shareHtml(urlParams.orderId, urlParams.clothOrder)
        })
    }
});

// 登录前
function loginBefore() {
    $('.login_box').show();
    $('.logout_box').hide();
    isWxBind();
}

// 登录后
function loginAfter() {
    $('.login_box').hide();
    $('.logout_box').show();
    $('#userName').text(ja.glob.userInfo.openid);
    // getUserStatus();
    getAllInfo();
}

// 统一点击
function $click(fun) {
    if(ja.glob.isWechat) {
        switch (ja.glob.code) {
            case 0: alert('网络繁忙，请稍后再试'); break;
            case 1:
                if(!isClick) {
                    if(isNormal) {
                        popShow('forbidden');
                    } else if (isGrade) {
                        popShow('lowGrade');
                    } else {
                        if (fun && typeof fun === 'function') {
                            isClick = true
                            fun()
                        }
                    }
                }
                break;
            case 2: popShow('pop_login'); break;
            case 1106: alert('活动未开始'); break;
            case 1102: alert('活动无效'); break;
            case 1103: alert('活动已结束'); break;
        }
    }else {
        alert('请在微信中访问');
    }
}

// 查询微信是否绑定
function isWxBind() {
    if (ja.glob.isWechat) {
        ja.user.wxIsBind(function (res) {
            isBind = false;
            $('#wxLogin .text').text('');
            if (res.code === 10000 && res.data) {
                $('#wxLogin .bdbtn').hide();
                $('#wxLogin .p2_txt').show().html(res.data + '<br />已绑定');
                isBind = true;
            } else {
                $('#wxLogin .bdbtn').show();
                $('#wxLogin .p2_txt').hide();
                isBind = false;
            }
        })
    }
}

// 获取阶段信息
function getAllInfo() {
    ja.ajax({
        url: '/fashion/userinfo',
        async: false,
        success: function (res) {
            if(res.code === 10000) {
                isStage2 = res.data.STAGE_2_STATUS;
            } else if (res.code == '1202' || res.code == '1209' || res.code == '1207' || res.code == '1007') { //1202请求参数缺失或者为空  1209操作频繁
                th.tips('系统繁忙，请稍后重试！');
                return;
            } else if (res.code == '1019' || res.code == '1012') { // 登录状态检测失败
                $('#login').click();
                return;
            } else if (res.code == '3103') { // 账号封停
                popShow('forbidden')
                isNormal = true;
                return;
            } else if (res.code == '2131') { // 等级不足
                popShow('lowGrade')
                isGrade = true;
                return;
            }
        }
    })
}

/**
*  ================== 事件绑定 ===================
**/
// 登录
$('#loginBtn').click(function () {
    $click();
});

// 账户登录
$('.login_account').click(ja.login);

// 微信登录
$('#wxLogin').click(function () {
    if(typeof isBind !== 'undefined') {
        if (isBind) {
            ja.user.wxAutoLogin(function (res) {
                if (res.code === 10000) {
                    location.reload();
                } else {
                    alert(res.message);
                }
            })
        } else {
            location.href = ja.glob.urlWxBind;
        }
    }
});

// 退出登录
$('#logout').click(ja.logout);

// 领取道具
$btnGet.click(function () {
    $click(function() {
        var orderId = urlParams.orderId;
        if (typeof isStage2 === 'undefined') {
            isClick = false
            return
        };
        if (isStage2 == '2') {
            alert('此阶段已结束')
            isClick = false
            return
        };
        if (isStage2 == '-1') {
            alert('此阶段未开始')
            isClick = false
            return
        };
        if(orderId) {
            ja.ajax({
                type: 'POST',
                url: '/fashion/exchange/receive',
                async: false,
                data: { orderid: orderId },
                success: function (res) {
                    isClick = false
                    switch (res.code) {
                        case 10000:
                            popShow('pop_get');
                            // $btnGet.addClass('dis_btn');
                            break;
                        case 1001: alert('分享人该道具数量不足'); break;
                        case 3304: alert('用户信息更新失败'); break;
                        // 已被他人领取
                        case 5005: popShow('pop_fail_slow'); break;
                        // 领取超出每日限制
                        case 5006: popShow('pop_fail'); break;
                        case 5011: alert('不能领取自己分享的道具'); break;
                        default: alert(res.message);
                    }
                }
            })
        } else {
            th.tips('请点击正确的分享链接！')
        }
    });
});

//#region 花瓣
var flowInit = function (container, flwCss, aniCss) {
    var screenWidth = window.innerWidth,
        screenHeight = window.innerHeight,
        maxDuration = 15,
        createFlow = function () {
            container.append('<li class="' + flwCss[Math.floor(Math.random() * (flwCss.length - 1))] + '"><span class="' + aniCss[Math.floor(Math.random() * (aniCss.length - 1))] + '"></span></li > ');
            var item = container.find('li').last(),
                startPositionLeft = 80 - screenWidth * 1 / 3 * Math.random(),
                startPositionTop = -100,
                endPositionTop = screenHeight + 200,
                endPositionLeft = startPositionLeft - screenWidth * 2 / 3 * Math.random() - screenWidth * 1 / 3,
                duration = maxDuration * 2 / 3 + maxDuration * 1 / 3 * Math.random();
            item.css({
                'transform': 'translate(' + startPositionLeft + 'px,' + startPositionTop + 'px)',
                '-webkit-transform': 'translate(' + startPositionLeft + 'px,' + startPositionTop + 'px)'
            });
            setTimeout(function () {
                item.css({
                    '-webkit-transform': 'translate(' + endPositionLeft + 'px,' + endPositionTop + 'px)',
                    '-webkit-transition': duration + 's ease-out',
                    'transform': 'translate(' + endPositionLeft + 'px,' + endPositionTop + 'px)',
                    'transition': duration + 's ease-out'
                }).one('webkitTransitionEnd transitionend', function () {
                    $(this).remove();
                });
            }, 100);
        };
    if (typeof document.getElementsByTagName("body")[0].style.animation != 'undefined') {
        createFlow();
        setInterval(createFlow, 2000);
    }
};
flowInit($('.flw'),
    ['sp1', 'sp2', 'sp3', 'sp4', 'sp5', 'sp6', 'sp7', 'sp8'],
    ['fallani1', 'fallani2', 'fallani3']
);
//#endregion

// 弹框
function popShow(id) {
    popHide();
    var p = $('#' + id);
    popDom = p;
    if (p) {
        p.show().css({
            position: 'fixed',
            top: '50%',
            left: '50%',
            marginTop: -popDom.height() / 2 + 'px',
            marginLeft: -popDom.width() / 2 + 'px',
            zIndex: 998
        });
        p.attr('for', 'pop');
        popIsShow = true;
        if ($('[for="' + id + '"]').length >= 1) return;
        $('body').append('<div name="overlay" for=' + id + ' style="width:100%;height:100%;position:fixed;top:0;left:0;z-index:997;background:rgba(0,0,0,0.8);"></div>');
    }
}
function popHide() {
    $('[for="pop"]').hide().attr('style', '');
    $('[name="overlay"]').remove();
}

// 屏幕适应
window.addEventListener("resize", function () {
    if (!popIsShow)
        return;
    setTimeout(function () {
        popDom.css({
            marginTop: -popDom.height() / 2 + 'px',
            marginLeft: -popDom.width() / 2 + 'px',
            zIndex: 998
        });
    }, 400)
});
