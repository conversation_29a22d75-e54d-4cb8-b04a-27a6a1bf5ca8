@charset "utf-8";

.pr {
    position: relative;
}

.pa {
    position: absolute;
}

.btn {
    transition: filter 0.5s linear;
    width: 197px;
    height: 55px;
    background: url(../img/com.png) no-repeat;
    display: block;
    text-indent: -9999em;
    overflow: hidden;
}

.btn:not(.gray):hover {
    -webkit-filter: brightness(1.15);
}

.none {
    display: none;
}

.c:before,
.c:after {
    content: "";
    display: table;
}

.c:after {
    clear: both;
}

.c {
    zoom: 1;
}

.bar {
    width: 1200px;
    margin: 0 auto;
    position: relative;
}

body {
    min-width: 1200px;
    padding-top: 55px;
    overflow-x: hidden;
    width: 100%;
    background-color: #ece1d3;
}

.wrap {
    font-size: 16px;
    color: #d0e3ff;
    overflow: hidden;
    background: url(../img/wbg.jpg) no-repeat center top;
}

.head {
    height: 937px;
    background: url(../img/head.jpg) no-repeat center top;
    position: relative;
    z-index: 1;
}

.vbg {
    position: absolute;
    top: -55px;
    width: 1920px;
    height: 1080px;
    left: 50%;
    margin-left: -960px;
    z-index: 1;
}

.vbg video {
    width: 100%;
    height: 100%;
}

.logo {
    width: 186px;
    height: 86px;
    background: url(../img/logo.png) no-repeat;
    display: block;
    text-indent: -9999em;
    position: absolute;
    top: 24px;
    left: 4px;
    z-index: 2;
}

.logo2 {
    pointer-events: none;
    width: 139px;
    height: 109px;
    background: url(../img/logo2.png) no-repeat;
    display: block;
    text-indent: -9999em;
    position: absolute;
    top: 16px;
    left: 219px;
    z-index: 1;
}

.arr {
    position: absolute;
    z-index: 3;
    width: 261px;
    height: 66px;
    left: 50%;
    margin-left: -130px;
    text-align: center;
    top: 835px;
    text-indent: -9999em;
    background: url(../img/arr.png) no-repeat;
    animation: flow 2s linear infinite both;
    -webkit-animation: flow 2s linear infinite both;
}

@keyframes flow {
    0% {
        transform: translateY(0px);
    }

    50% {
        transform: translateY(10px);
    }

    100% {
        transform: translateY(0px);
    }
}

@-webkit-keyframes flow {
    0% {
        -webkit-transform: translateY(0px);
    }

    50% {
        -webkit-transform: translateY(10px);
    }

    100% {
        -webkit-transform: translateY(-0px);
    }
}

.cont, .cont3 .cont4, .cont5 {
    width: 100%;
    height: 5259px;
    background: url(../img/cont.jpg) no-repeat center top;
    position: relative;
    z-index: 2;
}

.cont1 {
    height: 5663px;
    background: url(../img/cont3.jpg) no-repeat center top;
}

.cont6 {
    height: 4700px;
    background: url(../img/cont4.jpg) no-repeat center top;
}

.navbox {
    height: 108px;
    position: relative;
    width: 100%;
}

.navbox div {
    position: absolute;
    width: 100%;
    left: 0;
    top: -8px;
    height: 166px;
    background: url(../img/navbg.png) no-repeat center top;
}

.navbox a {
    background: url(../img/navcom.png) no-repeat;
    text-indent: -9999em;
}

.navbox .nav0 {
    position: absolute;
    top: -10px;
    z-index: 2;
    left: 50%;
    margin-left: -134px;
    width: 268px;
    height: 166px;
    background-position: -500px -270px;
}


.navlist {
    z-index: 1;
    position: absolute;
    top: 1px;
    left: 50%;
    margin-left: -750px;
    width: 1500px;
    height: 166px;
}

.navlist li {
    cursor: pointer;
    float: left;
    display: inline;
    display: flex;
    align-items: center;
    justify-content: center;
}

.navlist li.li0, .navlist li.li3 {
    margin-top: -7px;
}

.navlist li.li1 {
    margin-left: -2px;
}

.navlist li.li2 {
    margin-left: 264px;
}

.navlist li a {
    width: 100%;
    height: 100%;
    text-indent: -9999em;
    display: block;
    z-index: 1;
    position: relative;
}

.navlist li.li0 a {
    width: 383px;
    height: 116px;
    background-position: -500px 0;
}

.navlist li.li1 a {
    width: 238px;
    height: 102px;
    background-position: -500px -140px;
}

.navlist li.li2 a {
    width: 238px;
    height: 102px;
    background-position: -500px -464px;
}

.navlist li.li3 a {
    width: 375px;
    height: 116px;
    background-position: -500px -590px;
}

.pg1 .nav_box .nav0 {
    background-position: 0 -270px;
}

.pg2 .navlist li.li0 a {
    background-position: 0 0;
}

.pg3 .navlist li.li1 a {
    background-position: 0 -140px;
}

.pg4 .navlist li.li2 a {
    background-position: 0 -464px;
}

.pg5 .navlist li.li3 a {
    background-position: 0 -590px;
}

.flobox {
    z-index: 5;
    position: fixed;
    width: 226px;
    height: 667px;
    background: url(../img/flo.png) no-repeat left top;
    top: 50%;
    margin-top: -333px;
    display: none;
    right: 0px;
}

.s_nav {
    position: relative;
    padding: 43px 5px 0;
    z-index: 6;
    box-sizing: border-box;
}

.snavbox {
    margin-top: 18px;
}

.s_nav a {
    background: url(../img/flocom.png) no-repeat;
    text-indent: -9999em;
    overflow: hidden;
    display: block;
}

.navbtn1 {
    width: 91px;
    height: 88px;
    background-position: -49px -412px !important;
    margin: 0 auto;
}

.snavbox a {
    width: 215px;
    height: 70px;
    margin-bottom: 15px;
}

.snavbox a.snav0 {
    background-position: 13px -1px;
}

.snavbox a.snav1 {
    background-position: 13px -81px;
}

.snavbox a.snav2 {
    background-position: 13px -161px;
}

.snavbox a.snav3 {
    background-position: 13px -241px;
}

.snavbox a.snav4 {
    background-position: 13px -321px;
}

.pg1 .snavbox a.snav0 {
    background-position: -210px -1px;
}

.pg2 .snavbox a.snav1 {
    background-position: -210px -81px;
}

.pg3 .snavbox a.snav2 {
    background-position: -210px -161px;
}

.pg4 .snavbox a.snav3 {
    background-position: -210px -241px;
}

.pg5 .snavbox a.snav4 {
    background-position: -210px -321px;
}

.cont1nr {
    padding-top: 80px;
}

.tit {
    margin: 0 auto;
    width: 928px;
    height: 129px;
    background: url(../img/tit.png) no-repeat;
    text-indent: -9999em;
    overflow: hidden;
}

.tit.tit0 {
    background-position: -36px 0;
}

.jcb {
    position: relative;
    text-align: center;
    color: #ffefcc;
    font-size: 22px;
    line-height: 40px;
    margin: 18px auto 33px;
    width: 542px;
    height: 44px;
    background: url(../img/jcbbox.png) center no-repeat;
    font-weight: bold;
}

.recordbtn {
    z-index: 3;
    position: absolute;
    color: #ffde98;
    font-size: 20px;
    right: 0;
    top: 228px;
    line-height: 27px;
    text-indent: -99999em;
    width: 101px;
    height: 37px;
    background: url(../img/recordbtn.png) no-repeat;
}

.dhlist {
    width: 1320px;
    margin-left: -60px;
}

.dhlist li {
    display: inline;
    float: left;
    width: 213px;
    height: 382px;
    background: url(../img/libg.png) no-repeat;
    padding-top: 30px;
    box-sizing: border-box;
}

.dhlist li.li1 {
    margin-top: 30px;
}

.dhlist li.li3 {
    margin-top: 30px;
}

.dhlist li.li5 {
    margin-top: 30px;
}

.dhlist div,
.getlist div {
    width: 120px;
    height: 120px;
    margin: 30px auto 0;
    text-align: center;
    line-height: 106px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.getlist div {
    width: 98px;
    height: 97px;
    background: url(../img/djbox1.png) no-repeat;
    line-height: 100px;
}

.dhlist div img,
.getlist div img {
    display: inline-block;
    vertical-align: middle;
}

.djname {
    color: #d6ae82;
    font-size: 16px;
    text-align: center;
    line-height: 18px;
    padding: 2px 0;
    height: 44px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.dhbtn {
    margin: 4px auto;
    background-position: 0 0;
}

.dhbtn.disabled {
    filter: grayscale(100%);
    pointer-events: none;
}

.unused_counts {
    color: #d6ae82;
    font-size: 16px;
    text-align: center;
}


.infp {
    color: #ffefcc;
    font-size: 18px;
    text-align: center;
    line-height: 1;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
}

.tit.t1 {
    background-position: -36px -150px;
}


.tit.t2 {
    width: 542px;
    height: 44px;
    background-position: 0 -300px;
    margin-top: 47px;
}

.textbox {
    margin: 22px auto 0;
    text-align: center;
    width: 630px;
    height: 42px;
}

.textbox .jcb {
    margin: 0;
    width: auto;
    height: auto;
    float: left;
    text-align: left;
    font-size: 24px;
    font-weight: normal;
}

.textbox .jcb {
    background: none;
}

.textbox .jcb.fr {
    float: right;
}

.textbox .jcb span {
    font-size: 40px;
}

.stepsbox {
    margin: 20px -0 0 0;
    position: relative;
}

.steps {
    position: relative;
    width: 100%;
}

.steps li {
    float: left;
    position: relative;
    height: 216px;
}

.steps li.li0 {
    width: 185px;
}

.steps li.li1 {
    width: 274px;
}

.steps li.li2 {
    width: 274px;
}

.steps li.li3 {
    width: 467px;
}

.steps li .numdiv {
    position: absolute;
    top: 0;
    right: -109px;
    width: 216px;
    height: 217px;
    background: url(../img/stepicon.png) no-repeat;
    padding-top: 83px;
    z-index: 2;
    box-sizing: border-box;
}

.steps li .numdiv p {
    text-align: center;
    font-size: 24px;
    color: #6c422b;
    background-image: -webkit-linear-gradient(top, #9a3f16, #ae0d0c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: bold;
    line-height: 27px;
    /* text-shadow: 0 0 10px #fadda4; */
}

.steps li.li3 .numdiv {
    right: 84px;
}

.steps li.cur .numdiv {
    background-position: -284px -2px;
}

.steps li.cur .numdiv p {
    color: #d58544;
    background-image: -webkit-linear-gradient(top, #d58544, #6c422b);

}

.jindu,
.jindu p {
    background: url(../img/jindu.png) no-repeat;
}

.jindu {
    width: 100%;
    position: absolute;
    left: 0;
    height: 23px;
    top: 95px;
    z-index: 1;
}

.jindu p {
    position: absolute;
    top: 8px;
    left: 32px;
    height: 9px;
    background-position: -29px -41px;
}

.jindu.jindu0 p {
    width: 133px;
}

.jindu.jindu1 p {
    width: 455px;
}

.jindu.jindu2 p {
    width: 777px;
}

.jindu.jindu3 p {
    width: 1228px;
}

/* .jindu p::after {
  content: '';
  position: absolute;
  width: 0px;
  height: 30px;
  background: url(../img/active.png) no-repeat top right;
  top: 0;
  right: 20px;
  z-index: 2;
  display: none;
}

.jindu.jindu1 p::after,
.jindu.jindu2 p::after,
.jindu.jindu3 p::after {
  width: 239px;
}

.jindu.jindu0 p::after {
  width: 100px;
}

.jindu.jindu3 p::after {
  right: 15px;
}

.jindu::after,
.jindu::before {
  width: 35px;
  height: 30px;
  position: absolute;
  content: '';
  background: url(../img/icon.png) no-repeat;
  z-index: 3;
  top: 0;
}

.jindu::before {
  left: 0;
}

.jindu::after {
  right: 0;
  background-position: left bottom;
} */

.getlist {
    margin-left: 20px;
}

.getlist li {
    width: 218px;
    height: 294px;
    background: url(../img/divbg.png) no-repeat;
    padding-top: 24px;
    float: left;
    display: inline;
    margin-left: 56px;
    margin-top: -22px;
    box-sizing: border-box;
}

.getlist dl {
    text-align: center;
    font-size: 0;
}

.getlist dd {
    display: inline-block;
    vertical-align: top;
    width: 108px;
}

.getlist dd.ful {
    width: 100%;
}

.getlist .djname {
    height: 56px;
    padding: 3px 0 6px;
}

.getbtn {
    margin: 0 auto;
    background-position: 0 -60px;
}


.getbtn.ylq {
    background: url(../img/ylq.png) no-repeat;
}

.stepsboxbot {
    margin-top: 37px;
}

.stepsboxbot .steps li.li0 {
    width: 284px;
}

.stepsboxbot .steps li.li1 {
    width: 312px;
}

.stepsboxbot .steps li.li2 {
    width: 604px;
}

.stepsboxbot .steps li.li2 .numdiv {
    right: 184px;
}

.stepsboxbot .jindu.jindu0 p {
    width: 291px;
}

.stepsboxbot .jindu.jindu1 p {
    width: 618px;
}

.stepsboxbot .jindu.jindu2 p {
    width: 1228px;
}

.stepsboxbot .jindu.jindu0 p::after {
    width: 239px;
}

.getlistbot {
    margin-left: 0;
    padding-left: 83px;
}

.getlistbot li {
    margin-left: 93px;
}

.tit.t3 {
    margin: 88px auto 25px;
    width: 542px;
    height: 44px;
    background-position: 0 -372px;
}


.jclist {
    margin-left: -50px;
    width: 1300px;
    height: 78px;
    background: url("../img/jclist_bg.png") no-repeat;
    padding-left: 50px;
    box-sizing: border-box;
}

.jclist li {
    transition: filter 0.5s linear;
    float: left;
    display: inline;
    background: url(../img/jclist.png) no-repeat;
    text-indent: -9999em;
    width: 172px;
    height: 79px;
    margin-top: 3px;
    cursor: pointer;
    overflow: hidden;
    margin-left: -1px;
}

.jclist li:not(.gray):hover {
    -webkit-filter: brightness(1.15);
}

.jclist li.li0 {

    background-position: -4px -1px;
}

.jclist li.li1 {
    background-position: -4px -101px;
}

.jclist li.li2 {
    background-position: -4px -201px;
}

.jclist li.li3 {
    background-position: -4px -301px;
}

.jclist li.li4 {
    background-position: -4px -401px;
}

.jclist li.li5 {
    background-position: -4px -501px;
}

.jclist li.li6 {
    background-position: -4px -601px;
}


.jclist li.li0.cur {
    background-position: -250px 0;
}

.jclist li.li1.cur {
    background-position: -250px -100px;
}

.jclist li.li2.cur {
    background-position: -250px -200px;
}

.jclist li.li3.cur {
    background-position: -250px -300px;
}

.jclist li.li4.cur {
    background-position: -250px -400px;
}

.jclist li.li5.cur {
    background-position: -250px -500px;
}

.jclist li.li6.cur {
    background-position: -250px -600px;
}


.distab {
    display: none;
}

.distab.dis {
    display: block;
}

.matchbk {
    margin: 0 -9px 0 -22px;
}

.saiqubox {
    float: left;
    display: inline;
    margin: 60px 0 0 23px;
    width: 279px;
    height: 415px;
    background: url(../img/saiqubox.png) no-repeat;
}

.saiqubox1 {
    background: url(../img/saiqubox1.png) no-repeat;
}

.saiqubox2 {
    background: url(../img/saiqubox2.png) no-repeat;
}

.saiqubox3 {
    background: url(../img/saiqubox3.png) no-repeat;
}

.saiqubox4 {
    margin: 30px 0 0 23px;
    background: url(../img/saiqubox4.png) no-repeat;
}

.saiqubox5 {
    margin: 30px 0 0 23px;
    background: url(../img/saiqubox5.png) no-repeat;
}

.saiqubox6 {
    margin: 30px 0 0 23px;
    background: url(../img/saiqubox6.png) no-repeat;
}

.saiqubox7 {
    margin: 30px 0 0 23px;
    background: url(../img/saiqubox7.png) no-repeat;
}

.saiqubox ul {
    padding-top: 57px;
}

.saiqubox li,
.saiqubox li.yixuan::before,
.saiqubox li.win::after {
    background: url(../img/sqli.png) no-repeat;
    background-size: 300px auto;
}


.ttsai .saiqubox li,
.ttsai .saiqubox li.yixuan::before,
.ttsai .saiqubox li.win::after {
    background: url(../img/sqli2.png) no-repeat;
    background-size: 300px auto;
}

.saiqubox li {
    margin: 10px auto 0;
    color: #ddb689;
    font-size: 22px;
    text-align: center;
    width: 226px;
    height: 50px;
    padding: 3px 6px 0;
    line-height: 45px;
    cursor: pointer;
    position: relative;
    white-space: nowrap;
    text-overflow: ellipsis;
    box-sizing: border-box;
    background-position: 0 -60px;
}


.saiqubox li.yixuan {
    color: #fef398;
    background-position: -0 -120px;
}

.saiqubox li.yixuan::before {
    content: '';
    z-index: 2;
    width: 58px;
    height: 25px;
    position: absolute;
    right: 0;
    top: -5px;
    background-position: -242px -63px;
}


.saiqubox li.win {
    color: #793a0a;
    background-position: 0 0;
}

.saiqubox li.win::after {
    width: 58px;
    height: 25px;
    position: absolute;
    content: '';
    top: -5px;
    left: 0;
    background-position: -242px -8px;
    z-index: 3;
}

.tjbtn {
    margin: 12px auto 0;
    background-position: -200px -60px;
}

.tjbtn.committed {
    background-position: -200px -60px;
}


.tip {
    text-align: center;
    color: #ffde98;
    font-size: 18px;
    margin-top: 64px;
    font-weight: bold;
}

.tip.mt {
    margin-top: 39px;
}

.tit.t4 {
    margin: 30px auto 0;
    width: 374px;
    height: 53px;
    background-position: -626px -300px;
}


.ttsai {
    padding: 9px 0 2px;
}

.sc_fz_box2 .ttsai, .sc_fz_box3 .ttsai {
    display: flex;
    justify-content: space-evenly
}

.flex_jcse {
    display: flex;
    justify-content: space-evenly
}


.ttsai.pt {
    padding-top: 0px;
}

.ttsai .saiqubox {
    background: none;
    width: 263px;
    height: auto;
}

.ttsai .saiqubox ul {
    padding-top: 0;
    background: url(../img/ttsai.png) no-repeat center right;
    background-size: 60px auto;
}

.ttsai .saiqubox li {
    width: 215px;
    height: 48px;
    background-position: 0 -50px;
    margin: 9px 0 0 0;
}

.ttsai .saiqubox li.yixuan {
    background-position: 0 -100px;
}

.ttsai .saiqubox li.yixuan::before {
    width: 58px;
    height: 25px;
    background-position: -242px -63px;
}

.ttsai .saiqubox li.win {
    width: 215px;
    height: 48px;
    background-position: 0 0;
}

.ttsai .saiqubox li.win::after {
    width: 58px;
    height: 25px;
    background-position: -242px -7px;
}

.tip.ttsaitip {
    margin-top: 23px;
}

.tit.t5 {
    margin-top: 35px;
    width: 374px;
    height: 53px;
    background-position: -626px -372px;
}


.tit.t5.mt {
    margin-top: 42px;
}

.jjjcbox {
    margin: -4px -44px 0px;
}

.jjjclist {
    margin: 22px 0 0 33px;
    float: left;
    display: inline;
    width: 282px;
    height: 368px;
    background: url(../img/jjjclist.png) no-repeat;
}

.sc_fz_box2 .jjjcbox, .sc_fz_box3 .jjjcbox {
    display: flex;
    justify-content: space-evenly;
}

.sc_fz_box2 .jjjclist, .sc_fz_box3 .jjjclist {
    margin-left: 0;
}

.bifen {
    color: #fff7cb;
    font-size: 60px;
    text-align: center;
    line-height: 80px;
    padding-top: 25px;
    padding-left: 2px;
}

.bifen span,
.bifen em {
    width: 44px;
    display: inline-block;
    vertical-align: top;
    font-weight: bold;
}

.bifen em {
    width: 65px;
}

.teamname {
    color: #d5ab7d;
    font-size: 16px;
    width: 218px;
    margin: 0 auto;
    height: 34px;
    display: flex;
    justify-content: space-between;
}

.teamname li {
    width: 100px;
    float: left;
    line-height: 34px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.selectbox {
    width: 182px;
    height: 25px;
    margin: 0 auto;
}

.selectbox select {
    width: 100%;
    height: 100%;
    background: #6a0d11 url(../img/sbg.png) no-repeat;
    border: none;
    color: #d5ab7d;
    font-size: 14px;
    padding-left: 5px;
}

* {
    -webkit-appearance: none;
    -webkit-text-size-adjust: none;
}

.tbnum {
    margin-top: 16px;
    height: 30px;
    text-align: center;
    color: #d5ab7d;
    font-size: 16px;
    padding-left: 4px;
    line-height: 30px;
}

.tbnum p,
.tbnum ul {
    display: inline-block;
    vertical-align: top;
}

.tbnum ul {
    height: 30px;
    text-align: center;
    font-size: 0;
}

.tbnum li {
    margin: 0 6px;
    width: 28px;
    height: 28px;
    display: inline-block;
    vertical-align: top;
    color: #b38f68;
    text-align: center;
    font-size: 14px;
    line-height: 28px;
    box-sizing: border-box;
    border: 1px solid #b38f68;
    cursor: pointer;
}

.tbnum li.cur {
    color: #ffe9c2;
    border-color: #ffe9c2;
    text-shadow: 0 0 5px #a62c59;
}

.jcbtn {
    margin: 36px auto 0;
    background-position: -200px 0;
}


.jcbtn.committed {
    background-position: -10px -162px;
}

.tit.t4.mt {
    margin-top: 50px;
}

.tit.t7 {
    margin-top: 32px;
    width: 374px;
    height: 53px;
    background-position: 0 -655px;
}

.finalbox {
    margin-top: 42px;
    margin-bottom: 86px;
    width: 1200px;
    height: 220px;
}

.finalbox .selectul {
    width: 100%;
    height: 167px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.finalbox .selectul li {
    z-index: 2;
    position: relative;
    width: 297px;
    height: 167px;
    background: url(../img/finalcom.png) no-repeat;
}

.finalbox .selectul .li0 {
    background-position: 0 0;
}

.finalbox .selectul .li1 {
    background-position: -300px 0;
}

.finalbox .selectul .li2 {
    background-position: left bottom;
}

.finalbox .selectul li.succeed::after {
    background: url(../img/succeedicon.png) no-repeat;
}

.finalbox .selectul li.succeed select {
    background: #f9c35e url(../img/finalbox1.jpg) no-repeat;
}

.finalbox .selectul li.fail::after {
    background: url(../img/failicon.png) no-repeat;
}

.finalbox .selectul li.fail select {
    color: #94400b;
    background: #d2a564 url(../img/finalbox2.jpg) no-repeat;
}

.selectul li select {
    display: block;
    cursor: pointer;
    width: 253px;
    height: 40px;
    border: none;
    color: #6d2b00;
    font-size: 20px;
    padding-left: 28px;
    margin: 103px auto 0;
}

.finalbox .selectul .li0 select {
    background: #d2a564 url(../img/finalbox.jpg) no-repeat;
    color: #c58132;
}

.finalbox .selectul .li1 select {
    background: #d2a564 url(../img/finalbox1.jpg) no-repeat;
    color: #9f1d22;
}

.finalbox .selectul .li2 select {
    background: #d2a564 url(../img/finalbox2.jpg) no-repeat;
    color: #5c89bb;
}

.jsjcbtn {
    transition: filter 0.5s linear;
    margin: 19px auto 0;
    width: 197px;
    height: 55px;
    background: url(../img/jsjcbtn.png) no-repeat;
    display: block;
    text-indent: -9999em;
    overflow: hidden;
}

.jsjcbtn.committed {
    background: url(../img/ytj.png) no-repeat;

}

.matchbktop {
    margin: 0 0 0 -31px;
}

.matchbktop .saiqubox {
    margin: 38px 0 0 232px;
    background: url(../img/saiquboxgj.png) no-repeat;
    width: 280px;
    height: 307px;
}

.matchbktop .saiqubox ul {
    padding-top: 81px;
}

.matchbktop .saiqubox li,
.matchbktop .saiqubox li.yixuan::before {
    background: url(../img/sqligj.png?v=2) no-repeat;
}

.matchbktop .saiqubox.saiquboxyj li,
.matchbktop .saiqubox.saiquboxyj li.yixuan::before {
    background: url(../img/sqliyj.png?v=2) no-repeat;
}

.matchbktop .saiqubox li {
    margin-top: 11px;
    color: #ffe2a5;
}

.matchbktop .saiqubox li.yixuan::before,
.matchbktop .saiqubox.saiquboxyj li.yixuan::before {
    background-position: 0 -70px;
}

.matchbktop .saiquboxyj {
    background: url(../img/saiquboxyj.png?001) no-repeat;
}

.matchbktop .saiqubox.saiquboxyj li {
    color: #ddefff;
}

.matchbktop .saiqubox.saiquboxyj li.win {
    color: #5f4519;
    background-position: 0 -126px;
}

.jjjcboxbot {
    margin: 0 0 0 -31px;
}

.jjjcboxbot .jjjclist {
    margin: 35px 0 0 233px;
}

.fz_nav_box {
    width: 588px;
    display: flex;
    justify-content: space-between;
    margin: 37px auto 0;
}

.fz_nav {
    width: 282px;
    height: 64px;
    background: url(../img/fz_nav.png) no-repeat;
    display: block;
    text-indent: -9999em;
    overflow: hidden;
}

.win_nav {
    background-position: 0 0;
}

.lose_nav {
    background-position: -300px 0;
}

.win_nav.on {
    background-position: 0 -100px;
}

.lose_nav.on {
    background-position: -300px -100px;
}

.fz_box {
    display: none;
}

.show {
    display: block;
}

.tit.t6 {
    width: 928px;
    height: 129px;
    background-position: -36px -471px;
    margin-top: 65px;
}

.cont1 .tit.t6 {
    margin-top: 68px;
}

.cont2 .tit.t6 {
    margin-top: 93px;
}

.cont3 .tit.t6 {
    margin-top: 28px;
}

.rulebox {
    margin-top: 22px;
    margin-left: -120px;
    width: 1441px;
    height: 1473px;
    background: url(../img/rulebox.png) no-repeat;
    padding: 100px 60px 0;
    box-sizing: border-box;
}

.stit {
    margin-left: 38px;
    color: #fff7e5;
    height: 39px;
    line-height: 39px;
    text-align: center;
    padding: 0 28px;
    letter-spacing: 1px;
    vertical-align: top;
    position: relative;
    font-size: 20px;
    box-sizing: border-box;
    background: url(../img/stitbox.png) no-repeat;
}

.stit.stit0 {
    width: 244px;
    background-position: -10px -69px;
}

.stit.stit1 {
    width: 344px;
    background-position: -10px -10px;
}

.stit.stit2 {
    width: 184px;
    background-position: -10px -128px;
}

.rulebox ul {
    color: #844610;
    font-size: 16px;
    line-height: 24px;
    padding: 14px 60px 16px 66px;
}

.rulebox span {
    color: #cd4b13;
}

.rulebox li::before {
    display: inline-block;
    content: '';
    margin: 6px 4px 0 1px;
    width: 13px;
    height: 13px;
    vertical-align: top;
    background: url(../img/dotbg.png) no-repeat;
}

.rulebox p {
    color: #844610;
    line-height: 25px;
    padding: 2px 68px 10px 53px;
    text-indent: 17px;
}

.rulebox p span {
    color: #e1ab80;
}

.login_cn {
    position: absolute;
    padding: 0 50px;
    line-height: 36px;
    right: 30px;
    /* width: 500px; */
    text-align: center;
    font-size: 20px;
    top: 48px;
    z-index: 2;
    background-image: linear-gradient(to right, transparent, rgba(96, 41, 58, 1), transparent);
}

.login_cn,
.login_cn a {
    color: #fff1d7;
}

.openbtn {
    width: 56px;
    height: 218px;
    background: url(../img/zk.png) no-repeat;
    position: absolute;
    z-index: 5;
    top: 50%;
    margin-top: -109px;
    cursor: pointer;
    right: 214px;
    background-position: 0 0;
}

.openbtn.sq {
    background-position: -60px 0;
    right: 0;
    left: auto;
}

.flobox.none {
    background: none;
    width: 56px;
}

.flobox.none .s_nav {
    display: none;
}

.disabled{
    pointer-events: none;
}
.gray{
    filter: grayscale(100%);
}