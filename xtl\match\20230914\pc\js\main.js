$(function() {
  var isLongmen = location.href.indexOf('/longmen/') > -1,
      isDetail = location.href.indexOf('detail.shtml') > -1,
      generalRankList = undefined,
      generalPage = { current: 1, size: 15 },
      activeRankList = undefined,
      activePage = { current: 1, size: 20 },
      shineFirst = undefined,
      shineRankList = undefined,
      shinePage = { current: 1, size: 6 },
      shineFilter = { orderByType: 1, zone: '', groupNo: '' },    // 闪耀列表筛选信息
      rolesList = undefined,    // 角色列表
      selectRole = undefined,   // 选中的角色信息
      selectSect = undefined,   // 角色下的帮会信息
      initFinish = false,
      initMessage = undefined,  // 初始化异常信息
      popName = undefined,    // 弹出的弹窗名称
      isFirstRender = false,
      myInfo = {
        shineNumber: undefined,  // 可用闪耀值
        sectId: undefined,     // 我的帮会ID
        groupNo: undefined,    // 我的帮会 groupNo
        isBindRole: undefined,   // 是否绑定角色
        isHasSect: undefined,    // 是否有帮会 
      };

  var utils = {
    tips: function(msg, fn, hideClose) {
      $('#pop7 .pop-text').html(msg)
      utils.popup('#pop7')
      if(fn) {
        $('#pop7 .pop_close').on('click', function() {
          fn && fn()
          $('#pop7 .pop_close').off('click')
        })
      }
      if(hideClose) {
        $('#pop7 .pop_close').hide()
      }else {
        $('#pop7 .pop_close').show()
      }
    },
    imgUrl: function(url) {
      if(url.indexOf('http://') > -1 || url.indexOf('https://') > -1) {
        return url
      }else {
        return 'https://static1.changyou.com/xtl/sectrank/' + ja.version + '/' + url
      }
    },
    imgIndex: function(n) {
      var $img = $('.img-list img');
      if($img.eq(n).attr('src')) {
        return n
      }
      for(var i = 0; i < $img.length; i++) {
        if(!$img.eq(i).attr('src')) {
          return i
        }
      }
    },
    verify: function(name) {
      var whiteList = ['Login', 'Logout']
      if(ja.code !== 10000) {
        this.tips(ja.message || '活动异常')
        return;
      }
      if(!ja.isLogin) {
        ja.login()
        return;
      }
      if(whiteList.indexOf(name) === -1) {
        if(!initFinish) {
          alert('服务繁忙，请稍后')
          return;
        }
        if(initMessage) {
          alert(initMessage)
          return;
        }
      }
      return true
    },
    verifyForm: function(recommendType) {
      var formData,
          $img = $('.img-list img'),
          imgArr = [];
      
      if(recommendType === 6) {
        formData = {
          sectStory: $('#pop1 .story').val(),
          recommendReason: $('#pop1 .pop-tjly').val(),
          anonymous: $('#pop1 .btn-tjfs').index($('#pop1 .btn-tjfs.act')),
        }
        for(var i = 0; i < $img.length; i++) {
          var url = $img.eq(i).attr('src')
          if(url) {
            imgArr.push(url)
          }
        }
        formData.image1 = imgArr[0]
        formData.image2 = imgArr[1]
        formData.image3 = imgArr[2]
        if(!formData.image1 && !formData.image2 && !formData.image3) {
          alert('请至少上传1张照片')
          return;
        }
        if(!formData.sectStory) {
          alert('请填写帮会故事')
          return;
        }
        if(formData.sectStory.length < 20) {
          alert('帮派故事要至少要20个字');
          return;
        }
      }else {
        formData = {
          recommendReason: $('#pop2 .pop-tjly').val(),
          anonymous: $('#pop2 .btn-tjfs').index($('#pop2 .btn-tjfs.act')),
          sectStory: '',
        }
      }

      if(!formData.recommendReason) {
        alert('请填写推荐理由')
        return;
      }
      formData.recommendType = recommendType
      $('#BtnSubmit').attr('recommend-type', recommendType)

      return formData
    },
    error: function(data) {
      if(data.code === 0) {
        alert('请求异常')
      }else {
        alert(data.message)
      }
    },
    popup: function(name, fn) {
      this.hideMask()
      popName = name;
      ja.popup($(name))
      if(fn) {
        $(name + ' .pop_close').on('click', function() {
          fn && fn()
          $(name + ' .pop_close').off('click')
        })
      }
    },
    hideMask: function() {
      popName = ''
      ja.hideMask($('.pop'))
    },
    tipsBox: function (options) {
      options = $.extend({
          obj: null,  //jq对象，要在那个html标签上显示
          str: "<span style='display:block;width:80px;text-align:center;font-weight:bold'>+1</span>",  //字符串，要显示的内容;也可以传一段html，如: "<b style='font-family:Microsoft YaHei;'>+1</b>"
          startSize: "18px",  //动画开始的文字大小
          endSize: "36px",    //动画结束的文字大小
          interval: 600,  //动画时间间隔
          color: "#f00",    //文字颜色
          callback: function () { }    //回调函数
      }, options);
      $("body").append("<span class='nums'>" + options.str + "</span>");
      var box = $(".nums");
      var left = options.obj.offset().left + options.obj.width() / 2;
      var top = options.obj.offset().top;
      box.css({
          "position": "absolute",
          "left": left + "px",
          "top": top + "px",
          "z-index": 9999,
          "font-size": options.startSize,
          "line-height": options.endSize,
          "color": options.color
      });
      box.animate({
          "font-size": options.endSize,
          "opacity": "0",
          "top": top - parseInt(options.endSize) + "px"
      }, options.interval, function () {
          box.remove();
          options.callback();
      });
    },
    btnJoinShow: function(type) {
      $('#BtnJoin')
        .removeClass('btn-join2 btn-join3 btn-join4 btn-join5 btn-join6')
        .addClass(type === 1 ? '' : ('btn-join' + type))
      if(type == 4 || type == 5) {
        $('#BtnJoin').css({ 'cursor': 'auto' })
      }else {
        $('#BtnJoin').css({ 'cursor': 'pointer' })
      }
    },
    imgSwiper: function(imgArr) {
      var imgHtml = '';
      $('.bh-box').hide()
      for(var i = 0; i < imgArr.length; i++) {
        imgHtml += '<div class="swiper-slide">' + 
          '<img src="' + utils.imgUrl(imgArr[i]) + '" width="' + (ja.isMobile ? 630 : 589) +  '" height="' + (ja.isMobile ? 285 : 321) + '" />' + 
        '</div>'
      }
      $('.img-swiper-wrapper').html(imgHtml)
      setTimeout(function() {
        $('.bh-box').show()
        new Swiper('.bh-swiper', {
          navigation: {
            nextEl: '.bh-swiper-next',
            prevEl: '.bh-swiper-prev',
          },
          pagination: {
            el: '.swiper-pagination',
          },
        })
      }, 500)
    },
    subServerName: function(str) {
      var n = str.lastIndexOf('-')
      var m = str.lastIndexOf('_')
      var index = Math.max(n, m)
      return str.substring(index ? index + 1 : 0, str.length)
    }
  }

  /**
   * 接口
  */
  var http = {
    corePath: '/xtl/sectrank/',
    // 闪耀值排行榜
    getShineRankList: function() {
      $.ajax({
        url: this.corePath + 'getShineRankList',
        data: {
          pageNum: shinePage.current,
          pageSize: shinePage.size,
          orderByType: shineFilter.orderByType,
          zone: shineFilter.zone,
          groupNo: shineFilter.groupNo
        },
        success: function(res) {
          if(res.code === 10000 && res.data) {
            var pageInfo = res.data.pageInfo || {}
            shineFirst = res.data.first;
            shineRankList = pageInfo.list || [];
            shinePage.total = pageInfo.total ? pageInfo.total - 1 : 0;
            shinePage.number = Math.ceil(shinePage.total / shinePage.size) || 1
          }else {
            shineFirst = undefined
            shineRankList = undefined
          }
          xtl.renderShineFirst()
          xtl.renderShineList(res.message)
        }
      })
    },
    // 活力榜单
    getActiveRankList: function() {
      $.ajax({
        url: this.corePath + 'getActiveRankList',
        success: function(res) {
          if(res.code === 10000 && res.data) {
            activeRankList = res.data || [];
            activePage.total = activeRankList.length;
            activePage.number = Math.ceil(activePage.total / activePage.size) || 1
          }else {
            activeRankList = undefined
          }
          xtl.renderActiveList(res.message)
        }
      })
    },
    // 风云总榜
    getGeneralRankList: function() {
      var url = isLongmen ? this.corePath + 'getGeneralRankList' : '/xtl/match/20220909/pc/json/getGeneralRankList.json'
      $.ajax({
        url: url,
        success: function(res) {
          if(res.code === 10000 && res.data) {
            generalRankList = res.data;
            generalPage.total = generalRankList.length;
            generalPage.number = Math.ceil(generalPage.total / generalPage.size) || 1
          }else {
            generalRankList = undefined
          }
          xtl.renderGeneralList(res.message)
        }
      })
    },
    // 用户信息初始化
    init: function() {
      $.ajax({
        type: 'POST',
        url: this.corePath + 'init',
        success: function(res) {
          initFinish = true;
          if(res.code === 10000) {
            var info = res.data;
            myInfo.isBindRole = !!info.isBind
            myInfo.isHasSect = !!info.sectId || info.sectId == 0
            myInfo.sectId = info.sectId
            myInfo.groupNo = info.groupNo
            myInfo.shineNumber = info.shineNumber
            myInfo.recommend = info.recommend
            if(!info.isBind) {
              utils.popup('#pop5')
              http.getRoles()
            }
            
            !isDetail && xtl.renderPageIndex(info)
          }else if(res.code === 2131) {
            initMessage = '等级不足'
            $('.sect-name, .sect-rank, .server-name, .active-rank, .shine-number, .active-number').text(initMessage);
            utils.tips('您的账户无角色或角色不满足等级要求')
          }else {
            initMessage = res.message
            $('.sect-name, .sect-rank, .server-name, .active-rank, .shine-number, .active-number').text(initMessage);
            utils.error(res)
          }
        }
      })
    },
    // 获取帮派排名
    getSectRank: function() {
      $.ajax({
        url: this.corePath + 'getSectRank',
        success: function(res) {
          if(res.code === 10000) {
            var info = res.data;
            $('.sect-rank').text(info.shineRank || "尚无人推荐")
            $('.active-rank').text(info.activeRank && info.activeRank <= 50 ? info.activeRank : '期待上榜')
            $('.active-number').text(info.activeNumber || 0)
            $('.btn-share-self').attr('rank', info.shineRank)
          }else {
            $('.sect-rank, .active-rank').text('获取排名失败')
            $('.active-number').text(0)
          }
        }
      })
    },
    // 查询帮会审核状态
    getRecommendStatus: function(fun) {
      $.ajax({
        url: this.corePath + 'getRecommendStatus',
        success: function(res) {
          if(res.code === 10000) {
            // 0 已报名，审核中 1 已报名，审核通过  2 已报名审核拒绝 3 已报名,换帮派 
            // 4 无帮派 5 可以推荐初级 6 可以推荐高级 7 可推荐

            // 帮会自荐btn-join 请重新自荐btn-join2 查看我的帮会btn-join3 自荐书审核中btn-join4 自荐已用完btn-join5 请先加入帮派btn-join6 
            var status = res.data;
            $('#BtnJoin').attr('status', status)
            if(status === 0) {
              utils.btnJoinShow(4)
            } else if(status === 1) {
              utils.btnJoinShow(3)
            } else if(status === 2) {
              utils.btnJoinShow(2)
            } else if(status === 3) {
              utils.btnJoinShow(myInfo.recommend == 5 ? 3 : 5)
            } else if(status === 4) {
              utils.btnJoinShow(6)
            } else {
              utils.btnJoinShow(1)
            }
            http.getSectRank()
          }else {
            $('#BtnJoin').attr('status', 9)
          }
          fun && fun(res)
        }
      })
    },
    // 获取角色列表
    getRoles: function() {
      $.ajax({
        url: this.corePath + 'getRoles',
        success: function(res) {
          if(res.code === 10000) {
            rolesList = res.data,
            optionsHtml = '<option value="">请选择</option>'
            rolesList.forEach(function(item) {
              optionsHtml += '<option value="' + item.roleId + '">' + item.serverName + '_' + item.roleName + '</option>'
            })
            $('.role-select').html(optionsHtml).val('')
            $('.pop-search-sect').hide()
          }
        }
      })
    },
    // 获取帮派信息
    getSect: function(roleId, groupNo) {
      $.ajax({
        url: this.corePath + 'getSect',
        data: {
          roleId: roleId,
          groupNo: groupNo
        },
        success: function(res) {
          if(res.code === 10000) {
            selectSect = res.data
            if(selectSect) {
              $('.pop5-sect-name').text(selectSect.sectName || '尚无帮会')
            }else {
              selectSect = undefined
              $('.pop5-sect-name').text('尚无帮会')
            }
          }else {
            selectSect = undefined
            utils.error(res)
          }
        }
      })
    },
    // 绑定角色
    bindRole: function() {
      $.ajax({
        type: 'POST',
        url: this.corePath + 'bindRole',
        data: JSON.stringify($.extend({}, selectRole, selectSect)),
        contentType: 'application/json;charset=utf-8',
        success: function(res) {
          if(res.code === 10000) {
            utils.tips('绑定成功')
            http.init()
          }else {
            utils.error(res)
          }
        }
      })
    },
    // 帮会提交
    recommendSect: function(type) {
      var formInfo = utils.verifyForm(type)
      utils.tips('请稍等，正在提交...', function() {}, true)
      $.ajax({
        type: 'POST',
        url: this.corePath + 'recommendSect',
        data: formInfo,
        success: function(res) {
          if(res.code === 10000) {
            utils.tips('提交成功！')
            http.getRecommendStatus()
          }else if(res.code === 1200){
            alert('提交内容可能因为包含特殊字符导致超出字数上限，请重新检查提交')
          }else if(res.code === 5170){
            utils.tips('您所在帮会推荐状态已变更，请重新自荐', function() {
              location.reload()
            })
          }else {
            utils.tips(res.message, function() {
              utils.popup('#pop6')
            })
          }
        },
        error: function() {
          utils.popup('#pop6')
        }
      })
    },
    // 赠送闪耀值
    sendShine: function(sectId, groupNo, fun) {
      $.ajax({
        type: 'POST',
        url: this.corePath + 'sendShine',
        data: {
          sectId: sectId,
          groupNo: groupNo
        },
        success: function(res) {
          if(res.code === 10000) {
            myInfo.shineNumber --
            myInfo.shineNumber >= 0 && $('.shine-number').text(myInfo.shineNumber)
            fun && fun()
          }else if(res.code == 1202) {
            utils.tips('请求参数异常')
          }else {
            utils.error(res)
          }
        }
      })
    },
    // 领取奖励
    receivePrize: function(type) {
      $.ajax({
        type: 'POST',
        url: this.corePath + 'receivePrize',
        data: {
          type: type
        },
        success: function(res) {
          if(res.code === 10000) {
            utils.popup('#pop4')
            $('#BtnGift' + type).addClass('btn-ljlqed')
          }else if(res.code === 3401){
            // $('#BtnGift'  + type).addClass('btn-ljlqno')
            utils.tips(res.message)
          }else {
            utils.error(res)
          }
        }
      })
    },
    // 帮派详情
    sectInfo: function(sectId, groupNo) {
      $.ajax({
        url: this.corePath + 'sectInfo',
        data: { 
          sectId: sectId,
          groupNo: groupNo 
        },
        success: function(res) {
          if(res.code === 10000) {
            xtl.renderPageDetail(res.data)
          }else if(res.code === 5171){
            alert('帮会不存在')
            location.href = './index.shtml'
          }else {
            utils.error(res)
          }
        }
      })
    }
  }


  /**
   * 业务
  */
  var xtl = {
    init: function() {
      var _this = this;

      ja.config({
        app: 'xtl',
        activity: 'sectrank',
        version: isLongmen ? '2022102501' : '20221025',
        platform: 'changyou',
        isVconsole: true,
        excludePort: ['/xtl/sectrank/sendShine']
      })
      

      ja.ready(function(glob) {

        glob.isLogin ? _this.loginAfter() : _this.loginBefore()

        if(isDetail) {
          var params = glob.params
          if(!params.id || !params.groupNo) {
            location.href = 'index.shtml'
            return;
          }

          http.sectInfo(params.id, params.groupNo)

        }else {
          http.getGeneralRankList()   // 风云总榜
          http.getShineRankList()     // 闪耀排行榜
          xtl.renderSelectZone()      // 大区列表渲染
        }

        if(isLongmen) {
          ja.longmen({
            top: 100,
            left: 'auto',
            right: 150
          })
          alert('此活动为龙门测试版本，奖品均发放到龙门服')
          $('.btn-wait').attr('href', '/xtl/backflow/20220907/longmen/pc/index.shtml')
        }

        xtl.wxShare()

      })
    },
    loginBefore: function() {
      $('.login-before').show()
      $('.login-after').hide()
      $('.sect-name, .sect-rank, .server-name, .active-rank, .active-number, .shine-number').text('等待登录')
    },
    loginAfter: function() {
      $('.login-before').hide()
      $('.login-after').show()
      $('.nickname').text(ja.userInfo.openid)
      http.init()
      if(!isDetail) xtl.uploadfileInit()
    },
    renderGeneralList: function(msg) {
      var trHtml = '';
      if(!generalRankList) {
        $('.pagination-general').hide()
        $('.general-rank-list tbody').html('<tr><td colspan="6">' + (msg || '请求异常') + '</td></tr>')
        return;
      }
      if(!generalRankList.length) {
        $('.pagination-general').hide()
        $('.general-rank-list tbody').html('<tr><td colspan="6">暂无数据</td></tr>')
        return;
      }
      for(var i = (generalPage.current - 1) * generalPage.size; i < generalPage.current * generalPage.size; i++) {
        if(i < generalPage.total) {
          var item = generalRankList[i]
          var serverName = utils.subServerName(item.serverName)
          trHtml += '<tr>' +
            '<td>' + (i + 1) + '</td>' +
            '<td><div class="ellipsis" title="' + item.sectName + '" style="max-width:'+ (ja.isMobile ? 90 : 120) +'px;padding:0 5px;">' + item.sectName + '</div></td>' +
            '<td><div class="ellipsis" title="' + serverName + '" style="max-width:'+ (ja.isMobile ? 90 : 120) +'px;padding:0 5px;">' + serverName + '</div></td>' +
            '<td>' + item.shineNumber + '</td>' +
            '<td>' + item.activeNumber + '</td>' +
            '<td>' + item.point + '</td>' +
          '</tr>'
        }
      }
      $('.page-current1').text(generalPage.current)
      $('.page-number1').text(generalPage.number || 1)
      $('.general-rank-list tbody').html(trHtml)
    },
    renderActiveList: function(msg) {
      var trHtml = '';
      if(!activeRankList) {
        $('.pagination-active').hide()
        $('.active-rank-list tbody').html('<tr><td colspan="4">' + (msg || '请求异常') + '</td></tr>')
        return;
      }
      if(!activeRankList.length) {
        $('.pagination-active').hide()
        $('.active-rank-list tbody').html('<tr><td colspan="4">暂无数据</td></tr>')
        return;
      }
      for(var i = (activePage.current - 1) * activePage.size; i < activePage.current * activePage.size; i++) {
        if(i < activePage.total) {
          var item = activeRankList[i];
          var serverName = utils.subServerName(item.serverName);
          trHtml += '<tr>' +
            '<td>' + (i + 1) + '</td>' +
            '<td><div class="ellipsis" title="' + item.sectName + '" style="max-width:200px; margin: 0 auto;">' + item.sectName + '</div></td>' +
            '<td class="ellipsis">' + serverName + '</td>' +
            '<td>' + item.activeNumber + '</td>' +
          '</tr>'
        }
      }
      $('.page-current2').text(activePage.current)
      $('.page-number2').text(activePage.number || 1)
      $('.active-rank-list tbody').html(trHtml)
    },
    renderShineFirst: function() {
      if(!shineFirst) {
        $('.bhnr').hide()
        return;
      }

      var reasonHtml = '',
          reasonsArr = shineFirst.reasons || [],
          imgArr = shineFirst.images ? shineFirst.images.split('@@@@@') : [];
      
      $('.first-sect-name').text(shineFirst.sectName).attr('title', shineFirst.sectName)
      $('.first-server-name').text(shineFirst.serverName)
      $('.shine-num').text(shineFirst.sectShine || 0)
      $('.first-sect-item').attr({
        'sect-id': shineFirst.sectId,
        'group-no': shineFirst.groupNo
      })
     
      for(var i = 0; i < reasonsArr.length; i++) {
        var item = reasonsArr[i],
            txt = item.roleName + '： ' + item.recommendReason;
        reasonHtml += '<li><p title="' + txt + '">' + txt + '</p></li>'
      }
      $('.roll ul').html(reasonHtml)
      
      utils.imgSwiper(imgArr)

      if(isFirstRender) return;

      setTimeout(function() {
        isFirstRender = true;
        listRollInit($('.roll'), { col:1 });
      }, 1500)
      
    },
    renderShineList: function(msg) {
      var trHtml = '';
      var isRank = !(shineFilter.zone || shineFilter.groupNo || shineFilter.orderByType == 2)
      
      $('.page-current3').text(shinePage.current)
      $('.page-number3').text(shinePage.number || 1)

      if(!shineRankList) {
        $('.bh-list').html('<span style="margin: 50px auto;">' + (msg || '请求异常') + '</span>')
        return;
      }
      if(!shineRankList.length) {
        $('.bh-list').html('<span style="margin: 50px auto;">暂无数据</span>')
        return;
      }
      for(var i = 0; i < shineRankList.length; i++) {
        if(i < shinePage.size) {
          var item = shineRankList[i]
          trHtml += 
            '<div class="bh-item shine-sect-item" sect-id="' + item.sectId + '" group-no="' + item.groupNo + '">' +
              '<div class="bh-pm" style="display:' + (isRank ? 'block' : 'none') +'">排名：<span class="rank-num">' + (item.rank || '') + '</span></div>' +
              '<div class="bhpic">' +
                '<img src="' + (item.images ? utils.imgUrl(item.images.split('@@@@@')[0]) : '') + '" />' +
                '<a href="javascript:;" class="bh-ckxq btn-click btn-detail"><span class="dec"></span>查看详情<span class="dec"></span></a>' +
              '</div>' +
              '<div class="bhname ellipsis" style="max-width:240px; margin: 24px auto 5px;">' + item.sectName + '</div>' +
              '<div class="fwname"><span class="dec"></span>' + 
                '<span class="ellipsis" style="display:inline-block;max-width:200px;" title="' + item.serverName + '">服务器: ' + item.serverName + '</span>' +
              '<span class="dec"></div>' +
              '<div class="bh-dzbg com">' +
                '<a href="javascript:;" class="btn bh-dz btn-user">帮会点赞</a>' +
                '<p>闪耀值:<em style="font-size:16px;letter-spacing: -1px;">' + item.sectShine + '</em></p>' +
                '<a href="javascript:;" class="btn-qq2 btn btn-click btn-share"></a>' +
                '<a href="javascript:;" class="btn-wx2 btn btn-click btn-share"></a>' +
              '</div>' +
            '</div>'
        }
      }
      $('.bh-list').html(trHtml)
    },
    renderSelectZone: function() {
      for(var i=0;i<pro.length;i++){
        $(".zone").append($("<option>").val(i + 1).html(pro[i]));
      }
    },
    renderSelectServer: function(index) {
      for(var i=0;i<city[index].length;i++){//重新为市赋值
        var item = city[index][i]
        $(".server").append(
          '<option value="' + item[1] + '">' + item[0] + '</option>'
        );
      }
    },
    renderPageIndex: function(info) {
      // 未绑定角色
      if(!myInfo.isBindRole) {
        $('.sect-name, .sect-rank, .server-name, .active-rank, .shine-number, .active-number').text('请先绑定角色')
        $('.bind-no').show()
        $('.bind-yes').hide()
      }
      // 已绑定角色
      else {
        // 没有帮会
        if(!myInfo.isHasSect) {
          $('.sect-name, .sect-rank, .active-rank').text('尚未加入帮派')
          utils.btnJoinShow(6)
        }
        // 有帮会
        else {
          $('.sect-name').text(info.sectName || '无').attr('title', info.sectName)
          $('.btn-share-self').attr({ 'sect-id': info.sectId, 'group-no': info.groupNo })
          http.getRecommendStatus()
        }
        $('.shine-number').html(info.shineNumber || 0)
        $('.active-number').text(info.activeNumber || 0)
        $('.role-name').text(info.roleName || '无')
        $('.server-name').text(info.serverName || '无')
        $('.bind-no').hide()
        $('.bind-yes').show()
      }
      $('.role-info').show()
      if(info.send == 1) {
        $('#BtnGift1').addClass('btn-ljlqed')
      }
      if(info.shine == 1) {
        $('#BtnGift2').addClass('btn-ljlqed')
      }
      if(info.active == 1) {
        $('#BtnGift3').addClass('btn-ljlqed')
      }else if(info.active == 3) {
        $('#BtnGift3').addClass('btn-ljlqno')
      }
    },
    renderPageDetail: function(info) {
      var reasonHtml = '',
          imgArr = info.images.split('@@@@@');
      $('.sect-name').text(info.sectName)
      $('.server-name').text(info.serverName)
      $('.rect-story').text(info.sectStory)
      $('.shine-num').text(info.sectShine || 0)
      $('.shine-sect-item').attr({
        'sect-id': info.sectId,
        'group-no': ja.params.groupNo
      })
      $('.rank-num').text(info.rank)
      for(var i = 0; i < info.reasons.length; i++) {
        var item = info.reasons[i],
            txt = item.roleName + '：' + item.recommendReason;
        reasonHtml += '<li><p title="' + txt + '">' + txt + '</p></li>'
      }
      $('.reason-list').html(reasonHtml)

      utils.imgSwiper(imgArr)

      clearTimeout(timer)
      var timer = setTimeout(function() {
        listRollInit($('.roll'), {
          col:1//一行1列
        });
      }, 1500)
    },
    uploadfileInit: function() {
      imageCropper = $('#imageCropper');
      var cropperOptions = {
          aspectRatio: 1.8 / 1, //图片裁剪比例
          //preview: '.img-preview',
          crop: function (e) {}
      };
      imageCropper.cropper(cropperOptions);
      $('.img-list input').on('change', function (event) {
        var index = $('.img-list input').index($(this));
        var i = $('.img-list input').index($(this))
        var eve = event || window.event;
        var file = event.currentTarget.files[0];
        var format = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()
        var str = 'jpg,jpeg,png';
        if (!format || str.indexOf(format) == -1) {
            utils.tips('请选择jpeg，jpg，png格式的图片', function() {
              utils.popup('#pop1')
            });
            $('.img-list input').val("");
            return
        }
        var fileSizeBefter = Math.ceil(file.size / 1024);
        console.log(fileSizeBefter)
    
        if (fileSizeBefter > 3072) {
            utils.tips('请选择小于3M的图片', function() {
              utils.popup('#pop1')
            });
            $('.img-list input').val("");
            return;
        }
        var reader = new FileReader();
        reader.onload = function () {
            utils.popup('#pop9')
            $('.img-list input').val("");
            imageCropper.cropper('destroy').attr('src', reader.result).attr('len', i).cropper(cropperOptions);
        };
        if (file) {
            reader.readAsDataURL(file);
        }
      });
      // 删除图片
      $('.delebtn').on('click', function () {
          $(this).removeClass('show')
          $(this).parent().find('img').removeAttr('src')
          $(this).parent().find('input').val('')
      })
    },
    cropperDate: function() {
      var imageSrc = imageCropper.cropper('getCroppedCanvas').toDataURL('image/jpeg');
      // 去掉base64编码中的前缀 data:image/png;base64,
      var tag = "base64,";
      baseStr = imageSrc.substring(imageSrc.indexOf(tag) + tag.length);
      // 去掉base64编码中的“=”号　　
      var eqTagIndex = baseStr.indexOf("=");
      baseStr = eqTagIndex != -1 ? baseStr.substring(0, eqTagIndex) : baseStr;
      // 计算文件流大小
      var strLen = baseStr.length;
      var fileSize = strLen - (strLen / 8) * 2
      console.log(fileSize)
      if (fileSize / 1024 > 3072) {
          alert('图片过大请重新上传')
          return false;
      }
      this.uploadImg(imageSrc)
    },
    uploadImg: function(code) {
      setTimeout(function() {
          var t = imageCropper.attr('len');
          $('.img-list li').eq(t).find('img').attr('src', code).show();
          $('.img-list .delebtn').eq(t).addClass('show')
          utils.popup('#pop1')
      })
    },
    viewDetail: function(sectId, groupNo) {
      var url = 'detail.shtml?' + $.param({id: sectId, groupNo: groupNo})
      var sReturn = window.open(url)
      if(!sReturn) {
        location.href = url
      }
    },
    wxShare: function(url) {
      if(url) {   // 主动分享
        ja.share({
          title: '你的帮会在收集闪耀值',
          link: url,
          desc: '在《新天龙八部》帮会中，我帮就是最牛的！快为本帮添上闪耀值吧！',
          imgUrl: 'https://i0.cy.com/xtl/main/2022/1020/icon1.jpg',
          success: function() {
            xtl.wxShare()
            utils.hideMask()
          }
        })
      }else {
        ja.share({    // 默认分享
          title: '万帮风云榜',
          link: location.href,
          desc: '争当《新天龙八部》第一人气帮会！老友归来助力帮会登顶！',
          imgUrl: 'https://i0.cy.com/xtl/main/2022/1020/icon2.jpg'
        })
      }
    },
    share: function(sectId, groupNo) {
      var url = location.href;
      if(sectId) {
        url = location.origin + location.pathname.replace('index.shtml', 'detail.shtml')
        + '?' + $.param({ id: sectId, groupNo: groupNo })
      }
      
      if(ja.isMobile) {
        if(ja.isWechat) {
          utils.popup('#pop11')
          xtl.wxShare(url)
          $('#Overlay').on('click', function() {
            utils.hideMask()
            xtl.wxShare()
            $(this).off()
          })
        }else {
          utils.popup('#pop10_1')
          $('#pop10_1 .btn-copy').attr('data-clipboard-text', url)
          var btn = $('#pop10_1 .btn-copy')[0];
          var clipboard = new ClipboardJS(btn);
          clipboard.on('success', function(e) {
            alert('复制成功');
          });
          clipboard.on('error', function(e) {
            alert('您的浏览器不支持复制功能，请手动复制');
          });
        }
      }
      else {
        $('#shareQrcode').html('')

        shareQrcode = new QRCode('shareQrcode', {
          text: url,
          width: 256,
          height: 256,
          colorDark : '#000000',
          colorLight : '#ffffff',
          correctLevel : QRCode.CorrectLevel.M
        });

        utils.popup('#pop8')
      }
    }
  }

  xtl.init()


  /**
   * 事件
  */
  $('body')
    // 需要先登录
    .on('click', '.btn-user', function(e) {
      var name = e.target.id;

      if(!utils.verify(name)) return;

      switch(name) {
        case 'Login':       // 登录
          ja.login()
          break;
        case 'Logout':      // 注销
          ja.logout()
          break;
        case 'BtnGoBind':   // 弹出绑定
          utils.popup('#pop5')
          break;
        case 'BtnGift1':    // 3个礼包领取
        case 'BtnGift2':
        case 'BtnGift3':
          var type = parseInt(name.replace(/[^\d]/g, ''))
          http.receivePrize(type)
          break;
        case 'BtnJoin':   // 参与活动
          var status = parseInt($(this).attr('status'))
          if(!myInfo.isBindRole) return utils.popup('#pop5')
          // 0 已报名，审核中 1 已报名，审核通过  2 已报名审核拒绝 3 已报名,换帮派 
          // 4 无帮派 7 可自荐
          if(status === 0 || (status === 3 && myInfo.recommend == 6)) {
            return;
          }
          else if(status === 1 || (status === 3 && myInfo.recommend == 5)) {
            xtl.viewDetail(myInfo.sectId, myInfo.groupNo)
          }
          else if(status === 4) {
            if(!myInfo.isBindRole) utils.popup('#pop5')
          }
          else if(status === 5) {
            utils.popup('#pop2')
          }
          else if(status === 9) {
            utils.tips('帮会状态查询异常')
          }
          else {
            if(myInfo.recommend === 5) {
              utils.popup('#pop2')
            }else {
              utils.popup('#pop1')
            }
          }
          break;

        // 弹窗
        case 'BtnBindRole':     // 绑定角色 
          if(!selectRole) return alert('请先选择角色')
          http.bindRole()
          break;
        case 'BtnCroper':       // 裁剪确认
          xtl.cropperDate()
          break;
        case 'BtnFinish':       // 表单提交验证 多
          if(!utils.verifyForm(6)) return
          utils.popup('#pop6', function() {
            utils.popup(myInfo.recommend == 5 ? '#pop2' : '#pop1')
          })
          break;
        case 'BtnFinish2':      // 表单提交验证 少
          if(!utils.verifyForm(5)) return
          utils.popup('#pop6', function() {
            utils.popup(myInfo.recommend == 5 ? '#pop2' : '#pop1')
          })
          break;
        case 'BtnSubmit':       // 表单提交
          var recommendType = parseInt($(this).attr('recommend-type')) 
          http.recommendSect(recommendType)
          break;
        case 'BtnBackForm':     // 返回表单
          utils.popup(myInfo.recommend == 5 ? '#pop2' : '#pop1')
          break;
        default:
          break;
      }

      // 点赞
      if($(this).hasClass('btn-dz') || $(this).hasClass('bh-dz')) {
        var _this = this;
        var sectId = $(this).parents('.shine-sect-item').attr('sect-id')
        var groupNo = $(this).parents('.shine-sect-item').attr('group-no')
        if(!myInfo.isBindRole) {
          utils.popup('#pop5')
          return;
        }
        if(!myInfo.shineNumber) {
          utils.tips('闪耀值不足')
          return;
        }
        http.sendShine(sectId, groupNo, function() {
          utils.tipsBox({
            obj: $(_this),
            str: "+1",
            callback: function () {
              $(_this).parent().find('em').html(Math.floor($(_this).parent().find('em').text())+1);
            }
          });
        })
      }

    })
    // 不需要登录
    .on('click', '.btn-click', function(e) {
      var name = e.target.id;
      switch(name) {
        case 'BtnCroperClose':
          utils.popup('#pop1')
          break;
        case 'BtnGifts':      // 查看奖励详情
          utils.popup('#pop3')
          break;
        case 'Tab1':
        case 'NavShine':
          $('.track-con').hide();
          $('.track-con').eq(0).show();
          $('.track-tab').removeClass('track-tab1')
          break;
        case 'Tab2':
        case 'NavActive':
          $('.track-con').hide();
          $('.track-con').eq(1).show();
          $('.track-tab').addClass('track-tab1')
          http.getActiveRankList()
          break;

        case 'BtnFirst1':                   // 风云榜 分页事件
          if(!generalPage.number) return;
          if(generalPage.current === 1) return;
          generalPage.current = 1;
          xtl.renderGeneralList()
          break;
        case 'BtnPrev1':
          if(!generalPage.number) return;
          if(generalPage.current === 1) return;
          generalPage.current--
          xtl.renderGeneralList()
          break;
        case 'BtnNext1':
          if(!generalPage.number) return;
          if(generalPage.current === generalPage.number) return;
          generalPage.current++
          xtl.renderGeneralList()
          break;
        case 'BtnLast1':
          if(!generalPage.number) return;
          if(generalPage.current === generalPage.number) return;
          generalPage.current =  generalPage.number;
          xtl.renderGeneralList()
          break;

        case 'BtnFirst2':                   // 活力榜 分页事件
          if(!activePage.number) return;
          if(activePage.current === 1) return;
          activePage.current = 1;
          xtl.renderActiveList()
          break;
        case 'BtnPrev2':
          if(!activePage.number) return;
          if(activePage.current === 1) return;
          activePage.current--
          xtl.renderActiveList()
          break;
        case 'BtnNext2':
          if(!activePage.number) return;
          if(activePage.current === activePage.number) return;
          activePage.current++
          xtl.renderActiveList()
          break;
        case 'BtnLast2':
          if(!activePage.number) return;
          if(activePage.current === activePage.number) return;
          activePage.current =  activePage.number;
          xtl.renderActiveList()
          break;

        case 'BtnFirst3':                   // 闪耀榜 分页事件
          if(!shinePage.number) return
          if(shinePage.current === 1) return;
          shinePage.current = 1;
          http.getShineRankList()
          break;
        case 'BtnPrev3':
          if(!shinePage.number) return
          if(shinePage.current === 1) return;
          shinePage.current--
          http.getShineRankList()
          break;
        case 'BtnNext3':
          if(!shinePage.number) return
          if(shinePage.current === shinePage.number) return;
          shinePage.current++
          http.getShineRankList()
          break;
        case 'BtnLast3':
          if(!shinePage.number) return
          if(shinePage.current === shinePage.number) return;
          shinePage.current =  shinePage.number;
          http.getShineRankList()
          break;
        case 'BtnRank': // 排行查询
          $('.bh-btns a').removeClass('btn-yx');
          $(this).addClass('btn-yx');
          shinePage.current = 1
          shineFilter.orderByType = 1
          http.getShineRankList()
          break;
        case 'BtnNew':  // 最新查询
          $('.bh-btns a').removeClass('btn-yx');
          $(this).addClass('btn-yx');
          shinePage.current = 1
          shineFilter.orderByType = 2
          http.getShineRankList()
          break;
        default:
          break;
      }

      // 查看详情
      if($(this).hasClass('btn-detail')) {
        var $shineSectItem = $(this).parents('.shine-sect-item');
        var sectId = $shineSectItem.attr('sect-id');
        var groupNo = $shineSectItem.attr('group-no');
        xtl.viewDetail(sectId, groupNo)
      }

      // 分享他人（QQ / wechat）
      if($(this).hasClass('btn-share')) {
        var $shineSectItem = $(this).parents('.shine-sect-item');
        var sectId = $shineSectItem.attr('sect-id');
        var groupNo = $shineSectItem.attr('group-no');
        xtl.share(sectId, groupNo)
      }

      // 是否匿名选择
      if($(this).hasClass('btn-tjfs')) {
        $(this).addClass('act').siblings().removeClass('act')
      }

      // 分享个人
      if($(this).hasClass('btn-share-self')) {
        xtl.share()
      }

      // 敬请期待
      // $('.btn-wait').click(function() {
      //   utils.tips('敬请期待')
      // })

    })
    // 下拉选择
    .on('change', '.btn-select', function(e) {
      var name = e.target.id;
      switch(name) {
        case 'SelectRole':    // 选择绑定角色
          var roleId = $('.role-select').val()
          if(roleId) {
            selectRole = rolesList.filter(function(info) { 
              return info.roleId == roleId;
            })[0]
            $('.pop-search-sect').show()
            http.getSect(selectRole.roleId, selectRole.groupNo)
          }else {
            $('.pop-search-sect').hide()
            selectRole = undefined
            selectSect = undefined
          }
          break;
        case 'SelectZone':        // 大区筛选
          var index= $(this).val();
          $(".server").prop("length", 1);//清空原有的数据
          if (!$(this).val()) {
            $(".server").html('<option value="">所在服务器</option>')
          }else {
            xtl.renderSelectServer(index - 1)
          }
          shinePage.current = 1
          shineFilter.zone = index ? pro[index - 1] : '';
          shineFilter.groupNo = ''
          http.getShineRankList()
          break;
        case 'SelectServer':     // 服务器筛选
          shinePage.current = 1
          shineFilter.groupNo = $(this).val()
          http.getShineRankList()
          break;
        default:
          break;
      }
    })
    // 输入监听
    .on('input', 'textarea', function(e) {
      var limitMax = $(this).attr('limit-max');
      var txt = $(this).val()
      $(this).val(txt.replace(/<|>|\\|\/|&/g,""))
      if(txt.length > limitMax) {
        $(this).val(txt.substring(0, limitMax))
      }
    });
  
  /**
   * 兼容
  */
  (function() {
    // 安卓弹窗
    if(!ja.isIos) {
      var popOffsetTop = undefined;
      const innerHeight = window.innerHeight;
      window.addEventListener('resize', function() {
        const newInnerHeight = window.innerHeight;
        if(!popOffsetTop && $(popName).position()) {
          popOffsetTop = $(popName).position().top
        }
        if (innerHeight > newInnerHeight) {
          // 键盘弹出事件处理
          $(popName).css({
            'top': 'auto',
            'bottom': 0
          })
        } else {
          // 键盘收起事件处理
          $(popName).css('top', popOffsetTop)
        }
      });
    }
  })();

})
