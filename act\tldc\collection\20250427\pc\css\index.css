html {
    font-size: 100px;
}

body, html {
    height: 100%;
    overflow: hidden;
}

.wrap * {
    box-sizing: border-box;
}

.sp,
.btn {
    background: url(../img/comm2.png) no-repeat;
}

.sp-tit,
.comm {
    background: url(../img/title2.png) no-repeat;
}

.full-img img,
.full-img {
    display: block;
}

.full-img img {
    width: 100%;
    height: auto;
}

.bg-head .mid>div,
.bg-head .mid>a {
    z-index: 9;
    position: absolute;
}

.bg {
    background-size: 100% auto;
    background-position: top center;
    background-repeat: no-repeat;
}

.wrap {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    text-align: center;
}
.hd_top, .hd_top * { will-change: transform; }
.swiper-container-m {
    width: 100%;
    height: 100%;
}

.swiper-slide-m {
    overflow: hidden;
}


/* page */
.page {
    width: 19.2rem;
    height: 100%;
    position: relative;
    left: 50%;
    top: 0;
    margin-top: 0;
    margin-left: -9.6rem;
    transform-origin: center;
}

.page .tit-sub {
    width: 12rem;
    height: 1.17rem;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
}

.content {
    margin: 0 auto;
    position: relative;
    top: 50%;
    transform: translateY(-50%);
}

.page-bg {
    width: 19.2rem;
    height: 10.8rem;
    position: absolute;
    top: 0;
    left: 50%;
    margin-left: -9.6rem;
    display: block;
    z-index: -1;
}

.page-bg0 {
    top: -0.55rem;
}

.bg-head .mid .logo {
    width: 1.3rem;
    height: 0.6rem;
    background: url(../img/logo.png) no-repeat 50% 0 / 100%;
    font-size: 0;
    display: block;
    left: 0;
    top: 0.8rem;
}

.login-box {
    right: 0;
    top: 1rem;
    color: #fff;
    text-align: center;
    font-size: .2rem;
    display: flex;
    flex-direction: column;
}
.login-box>p{
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
}

.login-box a {
    color: #3a6c9f;
}

.login-box a:hover {
    text-decoration: underline;
}

.scroll-down {
    width: 0.39rem;
    height: 0.26rem;
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    margin-left: 0;
    bottom: 0;
    position: absolute;
}

.scroll-down.page0 {
    bottom: 0rem;
    margin-left: 3rem;
    background-image: url(../img/arrow1.png);
    background-size: 1.82rem auto;
    width: 1.82rem;
    height: 0.92rem;
}

.scroll-down .arrow-box {
    width: 0.39rem;
    height: 0.26rem;
    margin: 0 auto;
    box-sizing: border-box;
}

.scroll-down.page0 .arrow-box {
    width: 1.82rem;
    height: 0.92rem;
}

.scroll-down .arrow-box img {
    width: 0.39rem;
    position: absolute;
    animation: bounce 1s ease-in-out infinite alternate;
    display: none;
}

.scroll-down.page0 .arrow-box img {
    width: 1.82rem;
}

.scroll-down.page0 .arrow-box .arrow2 {
    display: block;
}

.scroll-down.page0 .arrow-box .arrow3 {
    display: none;
}

.scroll-down .arrow-box .arrow2 {
    display: none;
}

.scroll-down .arrow-box .arrow3 {
    display: block;
}

.nav_list {
    height: 0.5rem;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    right: 2.88rem;
}

.nav_list .nav_item:last-child {
    display: none;
}

.nav_list_activate {
    right: 1rem;
    height: 0.5rem;
    display: flex;
    align-items: center;
    display: block;
    align-content: center;
}

.nav_list .nav_item,
.nav_list_activate .nav_item {
    width: 1.88rem;
    height: 0.42rem;
    background: none;
    border-radius: 0;
}

.nav_list .nav_item.swiper-pagination-bullet-active,
.nav_list_activate.active .nav_item {
    background: url(../img/comm2.png) no-repeat;
    background-position: -8.13rem -1.3rem;
}

.nav_list .nav_item>a,
.nav_list_activate .nav_item>a {
    font-size: 0.2rem;
    color: #fff;
    line-height: 0.42rem;
    text-align: center;
    display: block;
    margin-right: 0.1rem;
}

.nav_list .swiper-pagination-bullet {
    opacity: 1;
}

@keyframes bounce {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(-30%);
    }
}

@keyframes Light {
    0%,
    100% {
        filter: brightness(100%);
    }
    50% {
        filter: brightness(120%);
    }
}

.btn-music {
    background-position: -9.61rem -0.89rem;
    width: 0.39rem;
    height: 0.39rem;
    right: 0.5rem;
    top: 0.06rem;
}

.btn-music.on {
    background-position: -9.61rem -0.5rem;
}

.home_btn_main {
    position: absolute;
    left: 50%;
    top: 0.3rem;
    margin-left: -2.19rem;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
}

.home_btn_main .slogan {
    width: 9.19rem;
    height: 5.09rem;
    background-position: -2.88rem -14.59rem;
    -webkit-animation: sloganAni 1s 0s linear both;
    animation: sloganAni 1s 0s linear both;
}

.home_btn_main .peopleNum {
    background-image: url(../img/page01-peopleNumBg.png);
    background-repeat: no-repeat;
    width: 5.52rem;
    height: 0.48rem;
    font-size: 0.24rem;
    color: #fff;
    line-height: 0.48rem;
    margin-top: -0.68rem;
}

.home_btn_main .peopleNum>p>span {
    color: #ffe3ad;
}

.btn_home_order {
    background-image: url(../img/btn_home_order.png);
    background-repeat: no-repeat;
    width: 2.8rem;
    height: 1.2rem;
}

.btn-order {
    background-position: 0 0;
    width: 3.58rem;
    height: 0.91rem;
    margin-top: 0.22rem;
}

.btn-order.gray {
    background-position: -3.58rem 0;
}

.btn-home-play {
    width: 0.5rem;
    height: 0.5rem;
    left: 1.87rem;
    top: -0.05rem;
    background-position: -9.5rem 0;
    animation: scale_video_btn 3s 0s linear infinite;
}

.btn-box {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

/*p1*/
.page1 .tit-sub {
    background-position: -1.5rem -3.37rem;
    margin-bottom: 0.24rem;
}

.page1 .swiper-container {
    position: relative;
}

.swiper-page1 .swiper-slide {
    width: 9.53rem;
    height: 5.32rem;
}

.swiper-page1 .img-box {
    width: 9.53rem;
    height: 5.32rem;
    overflow: hidden;
    position: relative;
}

.swiper-page1 .img-box>img {
    width: 9.2rem;
    height: 5rem;
    overflow: hidden;
    object-fit: cover;
    position: absolute;
    left: 0.16rem;
    top: 0.16rem;
}

.swiper-page1 .img-box::after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: url(../img/lb-bg.png) center no-repeat;
    background-size: 100%;
    pointer-events: none;
    width: 100%;
    height: 100%;
}

.page1 .swiper-pagination,
.page4 .swiper-pagination {
    position: relative;
    margin-top: 0.14rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.page1 .swiper-pagination-bullet,
.page4 .swiper-pagination-bullet {
    background: url(../img/comm2.png) no-repeat;
    width: 0.16rem;
    height: 0.16rem;
    background-position: -9.58rem -1.78rem;
    margin: 0 0.12rem 0;
    opacity: 1;
}

.page1 .swiper-pagination-bullet-active,
.page4 .swiper-pagination-bullet-active {
    width: 0.24rem;
    height: 0.24rem;
    background-position: -9.76rem -1.74rem;
}

.page1 .swiper-button-prev {
    width: 0.5rem;
    height: 0.74rem;
    background-position: -9.5rem -1.99rem;
    margin-left: 2.64rem;
}

.page1 .swiper-button-next {
    width: 0.5rem;
    height: 0.74rem;
    background-position: -9rem -1.99rem;
    margin-right: 2.64rem;
}

/*p2*/
.page2 .tit-sub {
    background-position: -1.5rem -4.54rem;
}

.page2 .top-box {
    width: 8.18rem;
    height: 0.5rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin: 0 auto;
}

.page2 .top-box>p {
    font-size: 0.16rem;
    color: #fff;
}

.page2 .top-box .btn-rule,
.page3 .btn-rule {
    width: 1.11rem;
    height: 0.34rem;
    background-position: -8.89rem -2.73rem;
    z-index: 9;
}

.page2 .lottery {
    width: 8.34rem;
    height: 4.88rem;
    margin:  0 auto 0.86rem;
}

.page2 .btn-lottery {
    width: 2.62rem;
    height: 0.74rem;
    background-position: -7.38rem -6.52rem;
    margin: 0 auto ;
}

.page2 .btn-lottery.gray {
    width: 2.62rem;
    height: 0.74rem;
    background-position: -7.38rem -7.26rem;
}

.page2 .lottery-task {
    background-image: url(../img/page02-taskBg.png);
    background-repeat: no-repeat;
    width: 14.15rem;
    height: 0.45rem;
    font-size: 0.14rem;
    color: #6e9ccb;
    line-height: 0.45rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
}

.page2 .lottery-task>p {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 0 0.15rem;
}

.page2 .lottery-task>p:nth-child(1) {
    font-size: 0.16rem;
    font-weight: bold;
}

.page2 .lottery-task>p>span.num {
    width: 0.22rem;
    height: 0.22rem;
    background: url(../img/comm2.png) no-repeat;
    background-position: -8.67rem 0;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.24rem;
}

.page2 .lottery-task .red {
    color: #b15c35;
    font-weight: bold;
    margin: 0 0.08rem;
}

.page2 .btn-invite {
    width: 1.28rem;
    height: 0.34rem;
    background-position: -8.72rem -3.07rem;
}

/* page3 */
.page3 .tit-sub {
    background-position: -1.5rem -5.71rem;
    margin-bottom: 0.1rem;
}

.page3 .btn-rule {
    left: 12.58rem;
    position: absolute;
}

.page3 .invite-box {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: flex-start;
    width: 100%;
    height: 1.3rem;
    position: relative;
    margin-bottom: 0.1rem;
}

.page3 .invite-box .number {
    font-size: 0.2rem;
    color: #b15c35;
    font-weight: bold;
    position: absolute;
    bottom: -0.04rem;
    display: none;
}

.page3 .invite-box .item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 0 0.5rem;
    position: relative;
}

.page3 .invite-box .item .btn-invite {
    width: 0.94rem;
    height: 0.94rem;
    background-position: -9.12rem -3.41rem;
    position: relative;
}

.page3 .invite-box .item .btn-invite>img {
    width: 0.72rem;
    height: 0.72rem;
    border-radius: 50%;
    overflow: hidden;
    margin-top: 0.1rem;
    left: 0.06rem;
    position: absolute;
}

.page3 .invite-box .item .btn-invite.active::after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    right: 0.05rem;
    bottom: 0;
    background: url(../img/comm2.png) no-repeat;
    background-position: -8.15rem -3.41rem;
    width: 0.94rem;
    height: 0.94rem;
    display: none;
}

.page3 .invite-box .item .btn-inviteChange {
    width: 0.88rem;
    height: 0.35rem;
    background-position: -9.12rem -4.65rem;
    bottom: -0.11rem;
    right: 0.08rem;
    position: absolute;
    display: none;
}

.page3 .invite-box .item .btn-inviteChange.out {
    background-position: -8.24rem -4.65rem;
}

.page3 .btn-box>a {
    width: 2.33rem;
    height: 0.54rem;
}

.page3 .btn-box>a:nth-child(2) {
    margin-left: 0.36rem;
}

.page3 .btn-createSteam {
    background-position: 0 -0.91rem;
}

.page3 .btn-createSteam.gray {
    background-position: 0 -1.45rem;
}

.page3 .btn-join {
    background-position: -2.33rem -0.91rem;
}

.page3 .btn-join.gray {
    background-position: -2.33rem -1.45rem;
}

.page3 .btn-check {
    background-position: -4.66rem -0.91rem;
    display: none;
}

.page3 .prize-box {
    width: 10.6rem;
    height: 3.1rem;
    position: relative;
    background: url(../img/page02-prizeLine.png) no-repeat;
    background-size: 10.6rem 0.48rem;
    background-position: top center;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 0.1rem;
    margin-top: 0.1rem;
}

.page3 .prize-item {
    width: 1.71rem;
    height: 2.53rem;
    background: url(../img/prize-bg.png) no-repeat;
    background-size: 100% auto;
    background-position: center;
    position: absolute;
    top: 0.52rem;
}
.prize-item.done::after{
    content: "";
    position: absolute;
    top: -0.2rem;
    right: -0.2rem;
    background: url(../img/done-icon.png) no-repeat;
    background-size: 100% auto;
    width: .64rem;
    height: .65rem;
}

.page3 .prize-item:nth-child(1) {
    left: 0.24rem;
}

.page3 .prize-item:nth-child(2) {
    left: 3.03rem;
}

.page3 .prize-item:nth-child(3) {
    left: 5.84rem;
}

.page3 .prize-item:nth-child(4) {
    left: 8.63rem;
}

.page3 .prize-item>img {
    width: 1rem;
    height: 1rem;
    overflow: hidden;
    margin: 0.36rem auto 0;
}

.page3 .prize-item>p {
    position: absolute;
    text-align: center;
    width: 100%;
    user-select: none;
}

.page3 .prize-item .name,
.page3 .prize-pop .item>p,
.page3 .name-box .item>p {
    font-size: 0.16rem;
    color: #3a6c9f;
    text-shadow: 0.02rem 0 0 #fff, -0.02rem 0 0 #fff, 0 0.02rem 0 #fff, 0 -0.02rem 0 #fff;
    top: 1.58rem;
    line-height: 0.3rem;
}
.page3 .prize-item .name{
    line-height: 0.22rem;
    top: 1.38rem;
}

.page3 .prize-item .title {
    font-size: 0.2rem;
    color: #fff;
    line-height: 0.44rem;
    top: 2.05rem;
}

.page3 .prize-item .title>span {
    font-size: 0.24rem;
    color: #fff4a4;
}

.page3 .prize-pop-box {
    background-image: url(../img/hover-prize.png);
    background-repeat: no-repeat;
    width: 6.5rem;
    height: 3.5rem;
    position: absolute;
    bottom: 0.5rem;
    display: none;
    pointer-events: none;
}

.page3 .prize-pop {
    display: flex;
    flex-direction: row;
    justify-content: center;
    position: absolute;
    top: 0.5rem;
    left: 50%;
    transform: translateX(-50%);
}

.page3 .prize-pop .item {
    width: 1.5rem;
    height: 1.8rem;
    position: relative;
    background: url(../img/page02-avatarBg.png) no-repeat;
    background-size: 1.45rem 1.4rem;
    background-position: top center;
    margin: 0.5rem 0.12rem 0;
}

.page3 .prize-pop .item>img {
    width: 0.8rem;
    height: 0.8rem;
    overflow: hidden;
    margin-top: 0.3rem;
}

.page3 .prize-pop .item>p {
    position: absolute;
    top: 1.4rem;
    width: 100%;
    text-align: center;
}

.page3 .get-name {
    width: 8.87rem;
    height: 1.33rem;
    position: relative;
    background: url(../img/page02-itemBg.png) no-repeat;
    background-size: 100% auto;
    background-position: center;
    left: 50%;
    transform: translateX(-50%);
}

.page3 .get-name .user-status{
    width: 2.06rem;
    position: absolute;
    top: 0;
    right: 1.8rem;
    font-size: .2rem;
    color: #fff;
    text-align: center;
    line-height: .34rem;
}
.page3 .get-name .user-status>span{color: #fff4a4;}

.page3 .name-box {
    width: 5.3rem;
    height: 1.24rem;
    right: 0.18rem;
    position: absolute;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-around;
    margin-top: 0.14rem;
}

.page3 .name-box .item {
    width: 1.1rem;
    height: 1rem;
    position: relative;
    background: url(../img/page02-avatarBg.png) no-repeat;
    background-size: 1.06rem 1rem;
    background-position: top center;
}

.page3 .name-box .item>img {
    width: 0.7rem;
    height: 0.7rem;
    border-radius: 50%;
    overflow: hidden;
    margin-top: 0.16rem;
    left: 0.18rem;
    position: absolute;
}

.page3 .name-box .item>p {
    top: 0.66rem;
    position: absolute;
    width: 100%;
    text-align: center;
}

/* page4 */
.page4 .tit-sub {
    background-position: -1.5rem -6.88rem;
}

.page4 .container {
    width: 15rem;
    height: 4.18rem;
    margin-top: 0.6rem;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    margin-left: 3rem;
}

.page4 .swiper-container {
    width: 8.6rem;
    overflow: hidden;
}

.page4 .swiper-slide {
    width: 8.6rem;
    height: 3.8rem;
    overflow: hidden;
}

.swiper-page4 .swiper-slide>a {
    height: 3.8rem;
    overflow: hidden;
    display: block;
    position: relative;
}

.swiper-page4 .swiper-slide>a>img {
    width: 7.44rem;
    height: 3.6rem;
    overflow: hidden;
    object-fit: cover;
    position: absolute;
    left: 0.13rem;
    top: 0.1rem;
}

.swiper-page4 .swiper-slide::after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: url(../img/lb-bg.png) center no-repeat;
    background-size: 100% 3.8rem;
    pointer-events: none;
    width: 100%;
    height: 100%;
}

.page4 .news-list {
    width: 8.16rem;
    height: 4.18rem;
    position: relative;
    background: url(../img/page04-newsBg.png) no-repeat;
    background-size: 100% auto;
    background-position: center;
    right: 1.92rem;
    bottom: 0.12rem;
}

.page4 .news-list .news-buttons {
    height: 0.84rem;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 1.92rem;
    top: 0.4rem;
}

.page4 .news-list .news-buttons .btn-tab {
    width: 0.96rem;
    height: 0.64rem;
    margin: 0 0.16rem;
    position: relative;
}

.page4 .news-list .news-buttons .btn-tab>span {
    font-size: 0.2rem;
    color: #3a6c9f;
    line-height: 0.54rem;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.page4 .news-list .news-buttons .btn-tab.active {
    background: url(../img/comm2.png) no-repeat;
    background-position: -7.16rem -1.3rem;
}

.page4 .news-list .news-buttons .btn-tab.active>span,
.nav_list .nav_item.swiper-pagination-bullet-active>a,
.nav_list_activate.active .nav_item>a {
     color: #b34012;
}

.page4 .news-list .btn-more {
    width: 0.92rem;
    height: 0.23rem;
    background-position: -9.08rem -4.42rem;
    right: 0.5rem;
    top: 0.66rem;
}

.page4 .news-list .news-content {
    width: 4.7rem;
    height: 2.46rem;
    right: 0.3rem;
    top: 1.22rem;
    position: absolute;
}

.page4 .news-list .news-content .news-pane {
    width: 4.7rem;
    height: 2.46rem;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 0.1rem;
    display: none;
}

.page4 .news-list .news-content .news-pane.active {
    display: block;
}

.page4 .news-list .news-content .news-pane .title {
    color: #3a6c9f;
    margin: 0 auto;
    width: 2.7rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.page4 .news-list .news-content .news-pane .text {
    height: 0.48rem;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    border-bottom: 0.01rem solid #fff;
    font-size: 0.18rem;
}

.page4 .news-list .news-content .news-pane .text>p {
    font-size: 0.18rem;
}

.page4 .news-list .news-content .news-pane .text .type {
    color: #fff;
    background-color: #87c9f5;
    padding: 0.02rem 0.08rem;
}

.page4 .news-list .news-content .news-pane .text .time {
    color: #769ec4;
}

/*p5*/
.page5 {
    padding-top: 2.2rem;
}

.page5 .tit-sub {
    background-position: -1.5rem -8.09rem;
}

.page5 .key {
    background-image: url(../img/page05-ipt.png);
    background-repeat: no-repeat;
    width: 9.8rem;
    height: 1rem;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.3rem;
    color: #6e9ccb;
    line-height: 1rem;
    margin: 1rem auto;
    text-indent: 0.5rem;
}

.page5 .btn-activate {
    width: 3.58rem;
    height: 0.9rem;
    background-position: 0 -2.36rem;
    margin-top: 3rem;
    left: 50%;
    transform: translateX(-50%);
}
.page5 .btn-activate.activated {background-position: -3.58rem -2.36rem;}
.page5 .btn-activate.gray {background-position: 0 -3.26rem;}

.box_p1 {
    display: flex;
    justify-content: center;
    flex-direction: column;
}

.p1_top {
    position: relative;
    display: flex;
    justify-content: center;
}

.swiper_news {
    width: 8.64rem;
    height: 4.23rem;
    position: relative;
    margin-right: 0.18rem;
}

.sc_news {
    width: 8.64rem;
    height: 4.23rem;
}

.sc_news .swiper-slide {
    position: relative;
}

.sc_news .swiper-slide a {
    display: block;
    border: 0.02rem solid #c79f54;
    width: 8.64rem;
    height: 4.23rem;
    overflow: hidden;
}

.sc_news .swiper-slide a img {
    width: 100%;
    height: 100%;
}

.sc_news .swiper-slide::after {
    content: '';
    width: 8.43rem;
    height: 4.05rem;
    border: 0.01rem solid rgba(199, 159, 84, 0.6);
    position: absolute;
    top: 0.08rem;
    left: 0.08rem;
    pointer-events: none;
}

.sp_swiper {
    position: relative;
    margin-top: -0.46rem;
    width: 100%;
    display: flex;
    justify-content: center;
    z-index: 3;
}

.sp_swiper .swiper-pagination-bullet {
    width: 0.25rem;
    height: 0.25rem;
    background: url(../img/page1/slide_d.png) no-repeat 50% 0/100%;
    border-radius: 0;
    margin: 0 0.15rem;
    opacity: 1;
    transition: .3s;
}

.sp_swiper .swiper-pagination-bullet-active {
    background: url(../img/page1/slide_d_on.png) no-repeat 50% 0/100%;
}

.p1_xf {
    width: 6.38rem;
    height: 4.23rem;
    position: relative;
    display: block;
}

.p1_xf img {
    display: block;
    width: 6.38rem;
    height: 3.83rem;
    border: 0.02rem solid #c79f54;
}

.p1_xf .tit_xf {
    width: 6.38rem;
    height: 0.4rem;
    border: 0.01rem solid #c79f54;
    margin-top: 0;
    background: url(../img/page1/tit_xf.jpg) no-repeat 50% 0/6.38rem auto;
}

/*news*/
.p1_bot {
    display: flex;
    justify-content: space-between;
    width: 15.2rem;
    margin: 0.4rem auto 0;
}

.news_box {
    position: relative;
    width: 7.4rem;
}

.news_tit {
    height: 0.44rem;
    border-bottom: 0.02rem solid rgba(184, 201, 225, 0.7);
    display: flex;
    justify-content: flex-start;
}

.news_tit a:not(.more) {
    margin-right: 0.3rem;
    width: 0.96rem;
    text-align: center;
    position: relative;
    height: 0.44rem;
    white-space: nowrap;
}

.news_tit a:nth-child(5) {
    margin-right: 0;
}

.news_tit a:not(.more):before {
    content: '';
    width: 0.96rem;
    height: 0.7rem;
    position: absolute;
    bottom: 0;
    left: 0;
    background: url(../img/page1/tab_on.png) no-repeat 50% 0/100%;
    opacity: 0;
    transition: .3s;
    pointer-events: none;
}

.news_tit.news_tit_main a {
    width: 1.76rem;
}

.news_tit.news_tit_main a:not(.more):before {
    background: url(../img/page2/tab2_on.png) no-repeat 50% 0/100%;
    width: 1.76rem;
}

.news_tit a:not(.more).on:before {
    opacity: 1;
}

.news_tit a:not(.more) span {
    font-family: 'title';
    font-size: 0.26rem;
    color: #596674;
    line-height: 0.44rem;
    transition: .3s;
    position: relative;
}

.news_tit a:not(.more):hover span,
.news_tit a:not(.more).on span {
    color: #b8986c;
}

.news_tit .more {
    margin-left: auto;
    margin-right: 0;
    background: url(../img/page1/btn_more.png) no-repeat 50% 100%/100%;
    width: 1.91rem;
    height: 0.44rem;
}

.news_list {
    width: 7.4rem;
    position: relative;
    height: 1.52rem;
    background: url(../img/page1/news_list_bg.png) no-repeat 50% 0/100% 100%;
    margin-top: 0.18rem;
}

.news_list ul {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 7.4rem;
    height: 1.42rem;
    align-content: center;
}

#newsList ul {
    align-content: normal;
    overflow: hidden;
    top: 0.05rem;
}

.news_list ul.on {
    display: block;
}

.news_list ul li,
.news_list ul li a {
    width: 7.45rem;
    height: 0.45rem;
    transition: .3s;
    box-sizing: border-box;
}

.news_list ul li a {
    width: 100%;
    display: flex;
    align-items: center;
    font-size: 0.22rem;
    color: #596674;
    padding: 0 0.15rem;
}

.news_list ul li a em {
    flex-shrink: 0;
    color: #c79f5f;
}

.news_list ul li a b {
    flex-grow: 1;
    margin-left: 0.05rem;
    margin-right: 0.12rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: normal;
}

.news_list ul .impnews b {
    color: #FF0000;
}

.news_list ul li a i {
    flex-shrink: 0;
    font-size: 0.24rem;
    font-style: normal;
}

.news_list ul li:hover a {
    color: #c79f5f;
}

.news_list ul li a .impnew {
    color: #f00;
}

.news_list ul li a .titBold {
    font-weight: bold;
}

/* ------- pop -------- */
.pop {
    background: url(../img/pop.png) no-repeat center;
    width: 6.07rem;
    height: 3.83rem;
    font-size: 0.22rem;
    color: #274c70;
    line-height: 0.36rem;
    text-align: center;
    position: relative;
    padding: 1.8rem 0.5rem 0.5rem 0.5rem;
    display: none;
}

.pop.pop1 {
    background: url(../img/pop1.png) no-repeat center;
    width: 6.07rem;
    height: 4.53rem;
    padding: 0.5rem 0.5rem 0.5rem 0.5rem;
    padding: 0;
    padding-top: 0.3rem;
}

.pop.pop-video {
    background: url(../img/pop-video.png) no-repeat center;
    width: 8.37rem;
    height: 4.93rem;
    padding: 0.11rem;
}

.pop .close {
    width: 0.35rem;
    height: 0.34rem;
    background-position: -9.65rem -5rem;
    position: absolute;
    right: 0.2rem;
    top: 0.9rem;
}

.pop .tit-pop {
    background-position: center -4.4rem;
    height: 0.6rem;
    text-align: center;
    margin-bottom: 0.1rem;
}

.pop .tit-pop strong {
    font-size: 0.32rem;
    color: #12569a;
}

.pop p>strong {
    font-size: 0.22rem;
    color: #12569a;
    font-weight: bold;
}

.icon-bind,
.icon-unbind {
    width: 1.33rem;
    height: 1.3rem;
    margin: 0.1rem auto;
}

.icon-bind {
    background-position: 0 -3.1rem;
}

.icon-unbind {
    background-position: -1.34rem -3.1rem;
}

.btn-bind {
    width: 2.04rem;
    height: 0.54rem;
    background-position: 0 -2.5rem;
}

.list-login li {
    width: 50%;
    float: left;
    font-size: 0.2rem;
    line-height: 1.3;
}
/*bottom*/
.ss_bot {
    height: 1.3rem !important;
    background: #f0f0f0;
}

.ss_bot * {
    box-sizing: content-box;
}

.pop2 {
    background-image: url(../img/pop1.png);
    height: 2.24rem;
}

.pop2 .txt {
    padding-top: 0.1rem;
    line-height: 1.4;
}

.pop .p-tips {
    height: 40%;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.pop.pop-getCode .p-tips {
  height:60%;
	margin-bottom: .2rem;
}
.pop.pop-getCode{
	padding: .6rem .05rem .05rem .05rem;
	height: 3.23rem;
}
.pop .p-tips .copy {
    display: inline-block;
    white-space: normal;
    max-width: 80%;
    word-break: break-all;
}
.pop.confirm {
    padding-top: 0.7rem;
}
.pop.confirm .p-tips {
    height: 50%;
    margin-top: 0.5rem;
}

.pop.confirm .p-tips .red {
    color: #ad2e00;
}
.pop.confirm  .close {
    right: -0.3rem;
    top: -0.1rem;
}
.pop.pop-getCode .close {
    right: -0.2rem;
    top: -0.2rem;
}

.pop .p-tips>span {
    display: contents;
}

.pop .pop-btn {
    display: flex;
    flex-direction: row;
    justify-content: center;
}

.pop .pop-btn>a {
    width: 2.31rem;
    height: 0.64rem;
}

.pop .pop-btn.small>a {
    width: 1.81rem;
    height: 0.56rem;
}

.pop .btn-leaderOut {
    background-position: 0 -7.8rem;
}

.pop .btn-confirmJoin {
    background-position: 0 -8.44rem;
}

.pop .btn-cancel {
    background-position: 0 -9.08rem;
}

.pop .btn-copy {
    background-position: 0 -9.72rem;
}

.pop .btn-userInfo {
    background-position: 0 -10.36rem;
}

.pop .btn-copyCode {
    background-position: 0 -11rem;
}

.pop .btn-goActivate {
    background-position: 0 -11.64rem;
}

.pop .pop-btn.small .btn-copyCode {
    background-position: 0 -5.34rem;
}

.pop .pop-btn.small .btn-goActivate {
    background-position: -1.81rem -5.34rem;
}

.pop .pop-btn.small .btn-userInfo {
    background-position: -3.62rem -5.34rem;
}

.pop .btn-retype {
    background-position: 0 -12.28rem;
}

.pop .btn-activate {
    background-position: -2.43rem -8.48rem;
    text-indent: 0;
    color: #fff;
    display: block;
}
.pop .btn-timeOver {
    background-position: 0 -12.92rem;
}

.pop .btn-activate>span{
    display: block;
    line-height: 0.54rem;
    text-align: center;
    margin-left: 1.12rem;
    width: .3rem;
    font-weight: bold;
}

.pop .btn-changeUser {
    background-position: 0 -13.56rem;
}

.pop .btn-confirmTeam {
    background-position: -2.43rem -6.52rem;
}
.pop .btn-confirmOut{background-position: -2.43rem -7.16rem;}
.pop .btn-cancelOut{background-position: -2.43rem -7.84rem;}
.pop .btn-addMsg{background-position: -2.43rem -9.12rem;}
.pop .video-box {
    width: 8.14rem;
    height: 4.7rem;
    overflow: hidden;
    margin: 0.1rem auto 0;
}

.pop .video-box>video {
    width: 100%;
    height: 100%;
}

.pop.pop-video .close {
    right: -0.04rem;
    top: -0.28rem;
}

.pop.pop-rule {
    padding-top: 1rem;
}


.pop.pop-rule .tit-pop {
    width: 1.98rem;
    height: 0.36rem;
    background-position: 0 -14.29rem;
    margin: 0 auto;
}
.pop.pop-rule .close {
    top: 0.5rem;
}
.pop-con {
    width: 90%;
    height: 2.7rem;
    overflow-x: hidden;
    overflow-y: auto;
    margin: 0 auto;
    text-align: start;
}

.pop .input>input,
.pop .input>textarea {
    width: 3.78rem;
    height: 0.44rem;
    border: 0.01rem solid #1b83c3;
    background-color: #e7f3ff;
    margin-bottom: 0.1rem;
    font-size: 0.26rem;
    line-height: 0.44rem;
    padding: 0 0.1rem;
}

.pop .form-item {
    display: flex;
    justify-content: flex-start;
    margin-left: 0.2rem;
}

.pop .form-item>label {
    width: 1.1rem;
    text-align: start;
    margin-right: 0.1rem;
}

.pop .form .address-tips {
    margin-left: 1.4rem;
    text-align: left;
    color: red;
    margin-top: -0.1rem;
}

.pop .form-item.verification-code .input>input {
    width: 2.7rem;
}

.pop .btn-code,
.pop .btn-address {
    font-size: 0.2rem;
    color: #1b83c3;
    height: 0.44rem;
    line-height: 0.44rem;
    margin-left: 0.1rem;
    border-bottom: 0.01rem solid;
}

.pop .btn-order {
    background-position: 0 -14.2rem;
}

.pop .form {
    margin-top: 0.2rem;
}

.pop.pop1 .tit-pop {
    width: 1.98rem;
    height: 0.31rem;
    margin: .2rem auto 0.18rem;
}

.pop.userInfo .tit-pop {
    background-position: 0 -13.37rem;
}

.pop .user-buttons {
    display: flex;
    justify-content: center;
}

.pop .btn-tab {
    width: 1.42rem;
    height: 0.4rem;
}

.pop .btn-tab:nth-child(1) {
    background-position: -8.58rem -5.34rem;
}

.pop .btn-tab:nth-child(1).active {
    background-position: -7.12rem -5.34rem;
}

.pop .btn-tab:nth-child(2) {
    background-position: -8.58rem -5.8rem;
}

.pop .btn-tab:nth-child(2).active {
    background-position: -7.12rem -5.8rem;
}

.pop .user-content {
    width: 90%;
    margin: 0 auto;
    height: 2.52rem;
    overflow-x: hidden;
    overflow-y: auto;
    margin: 0 auto;
}

.pop .user-pane {
    display: none;
    position: relative;
}

.pop .user-pane.active {
    display: block;
}

.pop .user-pane>table {
    width: 100%;
    font-size: 0.2rem;
    color: #333333;
    line-height: 0.48rem;
    text-align: center;
    background-color: #e0f1ff;
}

.pop .user-pane>table>tbody>tr>th,
.pop .user-pane>table>tbody>tr>td {
    border: 0.01rem solid #b5d6f3;
}

.pop .btn-address {
    position: fixed;
    margin-bottom: -0.38rem;
    margin-left: 1rem;
}

.pop .user-pane>table>tbody>tr>td:nth-child(1) {
    max-width: 1.8rem;
    word-wrap: break-word;
    line-height: 0.3rem;
}
.pop .addrSuc {
    position: absolute;
    left: 0;
}
.pop.userInfo .close,
.pop.apply .close,
.pop.address .close {
    right: -0.3rem;
    top: -0.28rem;
}
.pop.apply .tit-pop {
    background-position: 0 -13.98rem;
}

.pop .apply-list>ul {
    width: 94%;
    margin: 0 auto;
    height: 3rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.pop .apply-list>ul>li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.18rem;
    color: #333333;
    line-height: 0.56rem;
    text-align: center;
    height: 0.56rem;
    border-bottom: 0.01rem solid #98c3ec;
    position: relative;
}

.pop .apply-list>ul>li>img {
    width: 0.4rem;
    height: 0.4rem;
    overflow: hidden;
    border-radius: 50%;
    border: 0.01rem solid #b5d6f3;
}

.pop .apply-list>ul>li>a {
    width: 0.9rem;
    height: 0.4rem;
}

.pop .btn-agree {
    background-position: 0 -6rem;
}

.pop .btn-ignore {
    background-position: -1rem -6rem;
}
.pop.address .tit-pop {
    width: 2.98rem;
    background-position: 0 -13.68rem;
    margin-left: 1.7rem;
    margin-bottom: 0.5rem;
}

.pop .input>textarea {
    height: 0.86rem;
}

.pop.address .form-item>label {
    text-align: center;
}

.pop .btn-confirm {
    background-position: 0 -6.52rem;
}

.pop .btn-applyJoin {
    background-position: 0 -7.16rem;
}
.pop-home-prize {
    background: url(../img/home-prize-pop.png) no-repeat center;
    width: 5.21rem;
    height: 2.05rem;
    font-size: 0.16rem;
    line-height: 0.18rem;
    color: #13325b;
    bottom: -1.8rem;
    position: absolute;
    display: none;
}

.pop-home-prize>ul {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    align-items: center;
    margin: 0.3rem auto 0;
    padding-left: 0.3rem;
}

.pop-home-prize>ul>li {
    width: 0.62rem;
    min-height: 1.1rem;
    background: url(../img/home-prize.png) no-repeat top center;
    position: relative;
    display: flex;
    margin: 0 0.06rem;
    justify-content: center;
}
.pop-home-prize>ul>li>img{
    width: 0.34rem;
    height: 0.34rem;
    top: 0.1rem;
    position: absolute;
}
.pop-home-prize>ul>li:nth-child(1)>img{
    width: 0.6rem;
    height: 0.6rem;
    top: 0rem;
}
.pop-home-prize>ul>li>span {
    color: #fff;
    position: absolute;
    top: 0.6rem;
    width: 1.4rem;
    font-size: 0.14rem;
}
.home-tips{
    font-size: .14rem;
    color: #ddf3ff;
    height: .6rem;
    align-content: center;
    text-align: start;
    padding-left: 0.14rem;
    line-height: .2rem;
}
.home-tips .yellow{color:#ffebbb}
.home-tips>span{
    position: relative;
    padding-left: 0.1rem;
}
.home-tips>span::before {
  content: ''; 
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  width: 0.06rem;
  height: 0.06rem;
  background-color: #ddf3ff; 
}
.pop .applyNew{
    top: 0.1rem;
    width: .1rem;
    height: .1rem;
    background-color: red;
    border-radius: 50%;
    overflow: hidden;
    position: absolute;
}

/* animation In&Out */
.toRight,
.toRightSmall,
.toLeft,
.toLeftSmall,
.toBottom,
.toBottomSmall,
.toTop,
.toTopSmall,
.scaleIn {
    -webkit-transition: all .6s;
    opacity: 0;
}

.toRight {
    -webkit-transform: translate3d(-100%, 0, 0);
}

.toRightSmall {
    -webkit-transform: translate3d(-20%, 0, 0);
}

.toLeft {
    -webkit-transform: translate3d(100%, 0, 0);
}

.toLeftSmall {
    -webkit-transform: translate3d(20%, 0, 0);
}

.toBottom {
    -webkit-transform: translate3d(0, -100%, 0);
}

.toBottomSmall {
    -webkit-transform: translate3d(0, -30%, 0);
}

.toTop {
    -webkit-transform: translate3d(0, 100%, 0);
}

.toTopSmall {
    -webkit-transform: translate3d(0, 10%, 0);
}

.scaleIn {
    -webkit-transform: scale(1.4, 1.4);
}

.swiper-slide-m.swiper-slide-active .toTopSmall {
    -webkit-transform: translate3d(0, 0, 0);
    opacity: 1;
}

.swiper-slide-m.toTopSmall.swiper-slide-active {
    -webkit-transform: translate3d(0, 0, 0);
    opacity: 1;
}

.swiper-slide-m.swiper-slide-active {
    -webkit-transition-delay: .1s;
}

.swiper-slide-m.delay1.swiper-slide-active {
    -webkit-transition-delay: .1s;
}

.swiper-slide-m.swiper-slide-active .delay3 {
    -webkit-transition-delay: .3s;
}


