var sign = 2;
$(function () {
    var isLongmen = location.href.indexOf('/longmen/') !== -1,
        popIsShow = false,
        popDom = null,
        qqNumber = isLongmen ? '**********' : '一群：********* / 二群：*********',
        date = '10月30日19:00',
        payNumber = 'X',
        percentNum = '0',
        sloganImg = new Image(),
        startTime = 0,//预充值开放时间
        endTime = 0,//预充值结束时间
        preChargeUrl = '',//预充值地址
        isLogin = false,
        isClick = true,
        typeName = isLongmen ? 'longmen' : 'kaji',
        accountNumberStatus = true,
		commonPath = '/xtl/newserver/********/common/',
        keyPrecharge = {},
        url = {
            cover: ja.utils.urlAddSearch('./../../cover/pc/index.shtml'),   // 首页
            appoint: ja.utils.urlAddSearch('./../../appoint/pc/index.shtml'),  // 新服预约
            return: ja.utils.urlAddSearch('./../../return/pc/index.shtml'),   // 江湖同归
            dayday: ja.utils.urlAddSearch('./../../dayday/pc/index.shtml') //天天抽奖
        };
    // ja.config({
    //     app: 'xtl',                     // 必填，应用标识
    //     activity: 'newservertopic',     // 必填，活动标识
    //     version: actVersion,            // 必填，活动版本标识
    //     type: 1,
    //     platform: 'changyou',           // 必填，登陆平台标识
    //     ready: function (glob) {         // 非必填，初始化完成回调
    //         getAssembleData();
    //         glob.code === 1 ? loginAfter() : loginBefore();
    //     }
    // });
    
	// ja 初始化
	(function() {
        $.get(commonPath + 'key_kaji.json', function(resKey) {
            keyPrecharge = resKey['precharge'][typeName];
            var json = resKey.precharge[typeName];
            ja.config({
                app: json.APP,
                activity: json.ACTIVITY,
                version: json.VERSIONCODE,
                platform: json.PLAT,
                type: 1,
                ready: function(glob) {
                    getAssembleData();
                    glob.code === 1 ? loginAfter() : loginBefore();
                }
            });
        });
	})();

    init();
    /**
     * ************* 函数封装 **************
     **/
    function init() {
        $('.qq_number').text(qqNumber);
        $('.act_date').text(date);        // 当前预充点数

        $('.url_cover').attr('href', url.cover);      // 首页链接
        $('.url_appoint').attr('href', url.appoint);  // 首页链接
        $('.url_return').attr('href', url.return);    // 江湖同归
        $('.url_dayday').attr('href', url.dayday);    // 江湖同归

        if (isLongmen) {
            var obj = {
                top: "170px",
                left: "60px",
                color: 'white'
            }
            ja.utils.longmen(obj, $(".head"));
            alert('亲爱的玩家您好，本次活动为测试，奖励仅在测试服可用，感谢您的支持！');
        }
    }

    // 登录前
    function loginBefore() {
        $('.login_box').show();
        $('.logout_box').hide();
    }

    // 登录后
    function loginAfter() {
        isLogin = true;
        $('.login_box').hide();
        $('.logout_box').show();
        $('#userName').text(ja.glob.userInfo.openid);
        getUserInfo();
        getMyHasCnt();
        getPreCharge();
    }

    // 统一点击
    function $click(fun) {
        if (accountNumberStatus) {
            switch (ja.glob.code) {
                case 0:
                    alert('网络繁忙，请稍后再试');
                    break;
                case 1:
                    if (fun && ja.utils.objType(fun) === 'function') fun();
                    break;
                case 2:
                    ja.login();
                    break;
                case 1106:
                    alert('活动未开始');
                    break;
                case 1102:
                    alert('活动无效');
                    break;
                case 1103:
                    alert('活动已结束');
                    break;
            }
        } else {
            alert('账户被封')
        }

    }
    // 获取预充值开放时间&地址
    function getPreCharge() {
        ja.post('/changyou/xtl/precharge/preCharge', function (res) {
            if (res.code === 10000 && res.data) {
                var data = res.data;
                startTime = data.beginTime;
                endTime = data.endTime;
                preChargeUrl = data.preChargeUrl;
            } else if (res.code && res.code == 3103) {
                accountNumberStatus = false;
                $('#btn_myCenter').off("click");
                $('#btn_myCenter').click(function () {
                    alert(res.message);
                })
            }
        })
    }
    // 获取用户信息
    function getUserInfo() {
        ja.post('/changyou/xtl/precharge/chargeCount', function (res) {
            if (res.code === 10000 && res.data) {
                var data = res.data,
                    payNum = data.chargecount;//充值点数
                    // uName = data.cn,//用户cn
                    // uMaster = data.cnmaster;//用户cnmaster
                //充值点数
                $('#chargecount').html(payNum);
                if (payNum >= 12000 && payNum < 30000) {
                    $('.fir_box .icon').show()
                } else if (payNum >= 30000 && payNum < 60000) {
                    $('.fir_box .icon,.sec_box .icon').show()
                } else if (payNum >= 60000) {
                    $('.lb_cont .icon').show()
                }
            } else if (res.code && res.code == 3103) {
                accountNumberStatus = false;
                $('#btn_myCenter').off("click");
                $('#btn_myCenter').click(function () {
                    alert(res.message);
                })
            }
        })
    }
    // 个人拼团状态（次数，拼团状态）
    function getMyHasCnt() {
        ja.get('/changyou/xtl/precharge/myHasCnt', function (res) {
            if (res.code == 10000 && res.data) {
                var data = res.data;
                $('#ptCount').html(data.hasCnt);
                var $myPtBtn = $('.pint-box .my-pt-btn');
                if (data.groupno_1) {
                    $myPtBtn.eq(0).addClass('gray').attr('data-pt', '1');
                }
                if (data.groupno_2) {
                    $myPtBtn.eq(1).addClass('gray').attr('data-pt', '1');
                }
                if (data.groupno_3) {
                    $myPtBtn.eq(2).addClass('gray').attr('data-pt', '1')
                }
            }
        })
    }
    // 初始化拼团数据
    function getAssembleData() {
        ja.get('/changyou/xtl/precharge/groupInit', function (res) {
            if (res.code == 10000 && res.data) {
                var data = res.data;
                $('.currentCount_1').html(data.currentCount_1);
                $('.targetCount_1').html(data.targetCount_1);
                $('.currentCount_2').html(data.currentCount_2);
                $('.targetCount_2').html(data.targetCount_2);
                $('.currentCount_3').html(data.currentCount_3);
                $('.targetCount_3').html(data.targetCount_3);
            } else if (res.code == 3103) {
                accountNumberStatus = false;
            }
        })
    }
    // 拼团
    $('.pint-box').on('click', '.my-pt-btn', function () {
        if (isClick) {
            isClick = false;
            //一秒内不能重复点击
            setTimeout(function () {
                isClick = true;
            }, 1000);
            var groupno = Number($(this).attr('data-order')),
                isPinTuan = $(this).attr('data-pt');
            if (isPinTuan && isPinTuan == 1) return;
            $click(function () {
                ja.post('/changyou/xtl/precharge/group?groupno=' + groupno + '', function (res) {
                    isClick = false;
                    if (res.code == 10000) {
                        getMyHasCnt();//获取个人信息
                        getAssembleData();
                        popShow('pop-success');
                        var clipboard = new ClipboardJS('.copy-btn', {
                            text: function () {
                                shareHref = location.href + '#cont2';
                                return shareHref;
                            }
                        });
                        clipboard.on('success', function (e) {
                            alert("复制成功");
                        })
                        $('.pint-box .my-pt-btn').eq(groupno - 1).addClass('gray').attr('data-pt', '1')
                    } else if (res.code == 2121) {
                        popShow('pop-fail');
                    } else if (res.code == 3103) {
                        accountNumberStatus = false;
                        alert('您的账号已停权，请您更换参与活动的账号，或解权本账号后再来参与活动~');
                    } else if (res.code == 1105) {
                        $('.tit').hide();
                        $('#pop-fail .pop-txt').html('拼团活动已结束').css({ 'fontSize': '32px', 'marginTop': '50px' })
                        popShow('pop-fail');
                    } else {
                        alert(res.message)
                    }
                    isClick = true;
                })
            })
        }


    })
	/**
     * ************* 事件绑定 **************
     * */

    // 加载组件
    function getComponent(path, fun) {
        // 加载js
        function popMycenter() {
            $.getScript(path + 'index.js');
            fun && fun();
        }

        if ($('#pop-mycenter').length === 0) {
            $('<div />').load(path + 'index.html', function () {
                popMycenter();
            }).appendTo($('body'));
        } else {
            popMycenter();
        }
    }

    // 登录
    $('#login').click($click);


    // 登出
    $('#logout').click(ja.logout)

    //某个时间段内跳转有效  活动开始时间：1月28日10：00  预充值结束：2月19 00:00
    //结束 预充活动已结束，新服开启后可进游戏内消费领奖!
    //未开始 预充值尚未开始！
    $('#IWantRecharge').click(function (e) {
        $click(function () {
            var curTime = new Date().getTime();
            var start = new Date(startTime.replace(/-/g, "/")).getTime();
            var end = new Date(endTime.replace(/-/g, "/")).getTime();
            if (curTime > start && curTime < end) {
                // 跳转充值页面
                window.open(preChargeUrl);
            } else if (curTime > end) {
                alert("预充活动已结束，新服开启后可进游戏内消费领奖!");
            } else if (curTime < start) {
                alert("预充值尚未开始！");
            }
            e.preventDefault();
        })

    })

    // 按钮“个人中心”
    $('#centerBtn').click(function () {
        $click(function () {
            getComponent('/xtl/newserver/********/common/myCenter/', function () {
                popShow('pop-mycenter');
                var $mycenter = $('#pop-mycenter');
                var mTop = ($(window).height() - ($mycenter.height())) / 2;
                $mycenter.css('top', mTop + 'px!important')
            });
        })
    });
		$('.thi_box .lf_lh .dj-bg').hover(function () {
		        $('.lf_lh').addClass('maskShow')
		        $(this).siblings('.li3-pop').show();
		    }, function () {
		        $('.lf_lh').removeClass('maskShow')
		         $(this).siblings('.li3-pop').hide();
		    });
    $('.pint-box .li1 .pt-bg').hover(function () {
        $('.li1').addClass('maskShow')
        $('.li1-pop').show()
    }, function () {
        $('.li1').removeClass('maskShow')
        $('.li1-pop').hide()
    });
    $('.pint-box .li2 .pt-bg').hover(function () {
        $('.li2').addClass('maskShow')
        $('.li2-pop').show()
    }, function () {
        $('.li2').removeClass('maskShow')
        $('.li2-pop').hide()
    });
    $('.pint-box .li3 .pt-bg').hover(function () {
        $('.li3').addClass('maskShow')
        $('.li3-pop').show()
    }, function () {
        $('.li3').removeClass('maskShow')
        $('.li3-pop').hide()
    });
    window.addEventListener("resize", function () {
        if (!popIsShow)
            return;
        setTimeout(function () {
            popDom.css({
                marginTop: -popDom.height() / 2 + 'px',
                marginLeft: -popDom.width() / 2 + 'px',
                zIndex: 9998
            });
        }, 400)
    });

    //关闭弹窗
    $(".pop .closed , .close").on('click', function () {
        popHide();
    });

})
function popShow(id, msg) {
    msg = msg || '';
    popHide();
    popup($('#' + id));
    $('#' + id + ' .mesg_con').text(msg);
}
function popHide() {
    hideMask($('.pop'));
}
