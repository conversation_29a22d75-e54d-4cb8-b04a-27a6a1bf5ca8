﻿@charset "utf-8";
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, font, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td {
	margin: 0;
	padding: 0;
	border: 0;
	outline: 0;
	font-size: 100%;
	background: transparent;
}
table {
	border-collapse:collapse;
	border-spacing:0;
}
fieldset, img {	border:0; }
address, caption, cite, code, dfn, em, strong, th, var {
	font-style:normal;
	font-weight:normal;
}
ol, ul ,li{ list-style:none; }
caption, th { text-align:left; }
h1, h2, h3, h4, h5, h6 {
	font-size:100%;
	font-weight:normal;
}
:focus { outline: 0;}
a{ text-decoration:none;outline: none;}
a{ text-decoration:none;color:#333; }
a:link,a:visited{color:#444;text-decoration:none;}
a:hover img{ border:none;}

/*清除浮动*/
.clearfix:after {
	content: ".";
	display: block;
	height: 0;
	clear: both;
	visibility: hidden;
}
.clearfix {display: inline-block;}
/* Hides from IE-mac \*/
* html .clearfix { height: 1%;}
.clearfix {display: block;}
/* End hide from IE-mac */

/*png css hack for ie6*/
*html img.png{
    _background-image: expression(this.runtimeStyle.backgroundImage = "none",this.runtimeStyle.filter = "progid:DXImageTransform.Microsoft.AlphaImageLoader(src='" + this.src + "', sizingMethod='image')",this.src = "http://i0.itc.cn/20101019/848_0a785a7b_1118_4825_85dc_e8696988c94b_0.gif");
}
/*Extra CSS File For Change on All The Websites*/
@import url(http://www1.changyou.com/styles/extra.css);